## Pre-Seed Financing – Architecture and Code Flow

This document explains how the Pre‑Seed Financing UI renders and how data/state flows through all connected components. It covers the entry point from `src/pages/Dashboard.tsx` to `src/components/dashboard/PreSeedFinancingCard.tsx` and all child tabs/sections under `src/components/financing/`.

### High-level hierarchy

```mermaid
graph TD
  A[Dashboard.tsx] --> B[PreSeedFinancingCard]
  B --> C{Tabs: Convertible Note | SAFEs | MFN | SAFE Repurchase}
  C --> CN[ConvertibleNoteTab]
  C --> SF[SafeTab]
  C --> MFN[MfnTab]
  C --> SR[SafeRepurchaseTab]

  CN --> CNA[AuthorizeNotesSection (Form + Board Approval)]
  CN --> CNC[CurrentNotesSection (Table)]

  SF --> SFA[AuthorizeSafesSection (Form + Board Approval)]
  SF --> SFC[CurrentSafesSection (Table)]

  MFN --> MFNCN[MfnConvertibleNotesSection]
  MFN --> MFNSF[MfnSafesSection]
  MFN --> MFNSign[MfnSignDocumentsSection]

  SR --> SR1[SafeRepurchaseSection]

  CNA --> CNA_D[Dialog: Board Approval]
  SFA --> SFA_D[Dialog: Board Approval]
  MFNCN --> MFNCN_D[Dialog: Notify Holders]
  MFNSF --> MFNSF_D[Dialog: Notify Holders]
  MFNSign --> MFNSign_D[Dialog: Sign]
  SR1 --> SR1_C[Dialog: Confirm Repurchase]
  SR1 --> SR1_B[Dialog: Board Consent]
```

### Entry point and section selection

- `src/pages/Dashboard.tsx`
  - Chooses which dashboard section to render via local state `activeSection`.
  - When `activeSection === "pre-seed-financing"`, it renders `PreSeedFinancingCard`.

### Container: `PreSeedFinancingCard`

- File: `src/components/dashboard/PreSeedFinancingCard.tsx`
- Renders a `Card` with a `Tabs` control across four main tabs:
  - `convertible-note` → `ConvertibleNoteTab`
  - `safes` → `SafeTab`
  - `mfn` → `MfnTab`
  - `safe-repurchase` → `SafeRepurchaseTab`
- Maintains local `activeTab` via `useState`. Styling is Tailwind via shared `Card` and `Tabs` components.

### Shared UI primitives

- Cards: `src/components/common/Card.tsx` (title, description, content, footer variants)
- Tabs: `src/components/ui/tabs.tsx` (Radix Tabs wrappers: `Tabs`, `TabsList`, `TabsTrigger`, `TabsContent`)
- Form, Inputs, Select, Dialogs, Tooltip, Calendar, etc.: shadcn-style components under `src/components/ui/*`
- Types: `src/types/financing.ts` provides `ConvertibleNote`, `Safe`, `SafeRepurchase`, `InterestType`

---

## Tabs and sections

### 1) Convertible Note

#### `ConvertibleNoteTab` – container sub-tabs
- File: `src/components/financing/ConvertibleNoteTab.tsx`
- Local `activeSubTab` with two tabs:
  - `authorize-notes` → `AuthorizeNotesSection`
  - `current-notes` → `CurrentNotesSection`

#### `AuthorizeNotesSection` – round details form + board approval
- File: `src/components/financing/convertiblenote/AuthorizeNotesSection.tsx`
- Composition:
  - `Form` via `react-hook-form` with default values: `authorizedAmount`, `interestRate`, `interestType`, `maturityDate`, `valuationCap`, `discount`, `mfn`.
  - Inputs: currency fields (with `$`/`%` adorners), `Select` for interest type and MFN, date picker (`Popover` + `Calendar`).
  - MFN warnings: on change of `interestRate`, `valuationCap`, `discount`, it toggles local booleans to show `Tooltip` with an MFN advisory.
  - Board approval panel: shows local `boardApproved` status and a primary submit button.
- Submit flow:
  1. `form.handleSubmit(onSubmit)`
  2. Emits a toast via `useToast()` and opens the board approval dialog.
  3. Dialog shows a mock “Form of Convertible Note” preview using current form state.
  4. Dialog footer actions: `Save as Draft` (close) and `Finish` → `handleBoardApprovalSubmit` (toast + close).

#### `CurrentNotesSection` – table of current notes
- File: `src/components/financing/convertiblenote/CurrentNotesSection.tsx`
- Displays a `Table` of mocked `ConvertibleNote` rows.
- MFN trigger indicator per row using `AlertCircle` + `Tooltip` with guidance.
- Provides a small `Link` to `/captable` for “Issued and Outstanding Convertible Notes”.

### 2) SAFEs

#### `SafeTab` – container sub-tabs
- File: `src/components/financing/SafeTab.tsx`
- Local `activeSubTab` with two tabs:
  - `authorize-safes` → `AuthorizeSafesSection`
  - `current-safes` → `CurrentSafesSection`

#### `AuthorizeSafesSection` – round details form + board approval
- File: `src/components/financing/safe/AuthorizeSafesSection.tsx`
- Very similar to notes authorization but with SAFE-specific fields: `authorizedAmount`, `valuationCap`, `discount`, `mfn`.
- MFN warnings on `valuationCap`/`discount` with `Tooltip` advisory.
- Submit flow identical to notes: toast → open dialog → mock “Form of SAFE” preview → `Finish` posts a toast and closes.

#### `CurrentSafesSection` – table of current SAFEs
- File: `src/components/financing/safe/CurrentSafesSection.tsx`
- Displays a `Table` with mocked `Safe` rows.
- MFN trigger indicator similar to notes.
- Link to `/captable` for “Issued and Outstanding SAFEs”.

### 3) MFN

#### `MfnTab` – container sub-tabs
- File: `src/components/financing/MfnTab.tsx`
- Three sub-tabs:
  - `convertible-notes` → `MfnConvertibleNotesSection`
  - `safes` → `MfnSafesSection`
  - `sign-documents` → `MfnSignDocumentsSection`

#### `MfnConvertibleNotesSection` – compare and notify
- File: `src/components/financing/mfn/MfnConvertibleNotesSection.tsx`
- Shows two `Table`s:
  - Current convertible note round (mocked `ConvertibleNote`)
  - New note terms (mocked with more favorable terms)
- Primary action: `Notify Holders` → opens dialog with mock “Amended and Restated Convertible Note” → `Send Notifications` triggers toast and closes.

#### `MfnSafesSection` – compare and notify
- File: `src/components/financing/mfn/MfnSafesSection.tsx`
- Mirrors the notes flow but for SAFEs; dialog presents “Amended and Restated SAFE”.

#### `MfnSignDocumentsSection` – select and sign documents
- File: `src/components/financing/mfn/MfnSignDocumentsSection.tsx`
- Renders a list of mock documents (notes and SAFEs). Clicking a document selects it, shows a preview, and enables `Sign Document`.
- `Sign Document` triggers a toast; dialog closes.

### 4) SAFE Repurchase and Cancel

#### `SafeRepurchaseTab`
- File: `src/components/financing/SafeRepurchaseTab.tsx`
- Thin wrapper rendering `SafeRepurchaseSection`.

#### `SafeRepurchaseSection` – select, confirm, and board consent
- File: `src/components/financing/saferepurchase/SafeRepurchaseSection.tsx`
- Displays mocked `SafeRepurchase[]` rows with:
  - `Checkbox` to select items
  - `Input` for `repurchaseAmount` when selected
  - Read-only columns for board approved, investor, dates, cap, discount, MFN
- Flow:
  1. When at least one SAFE is selected, a top `Repurchase SAFE` button appears.
  2. Click opens `Confirm SAFE Repurchase` dialog summarizing selected rows with amounts.
  3. Confirm opens `Board Consent` dialog with mock consent and agreement sections.
  4. `Sign and Submit` triggers toast and closes.

---

## State and data

- All state is currently local to each component using `useState` and `react-hook-form`.
- Data is mocked; there are placeholders indicating where persistence/integration would occur (e.g., comment in `onSubmit`).
- Formatting uses `date-fns` for dates and locale string for currency display.
- Notifications use `useToast()`; actions show success toasts. When integrating, consider standardizing to `toast.success` / `toast.error` per project conventions.

## Styling and UX

- Styling is entirely Tailwind via shared UI primitives; there is no standalone CSS for these views.
- `Card` provides consistent padding/headers; `Tabs` give nested navigation.
- MFN risk hints are implemented with `AlertCircle` icons and `Tooltip` for contextual guidance.
- Dialogs are used for document previews, approvals, notifications, and signatures to keep users in-flow.

## How the screenshot maps to components

- Page title and subtitle: `PreSeedFinancingCard` header (`CardTitle`, `CardDescription`).
- Top tab row: `PreSeedFinancingCard` → `TabsList` with 4 triggers.
- Convertible Note sub-tabs row: `ConvertibleNoteTab` → `TabsList` with 2 triggers.
- “Round Details” form and fields: `AuthorizeNotesSection`.
- “Current Notes” table: `CurrentNotesSection`.
- Similar structure applies to SAFEs via `SafeTab`, and MFN/Repurchase via their respective sections.

## Extension points / integration notes

- Replace mock arrays and demo thresholds with real data from services, e.g., a financing service layer and/or React Query hooks.
- Centralize MFN trigger logic (share thresholds/logic between notes and SAFEs) to avoid duplication.
- Persist board approvals and document generation to the backend; integrate with an e-signature module already used by other parts of the app if available.
- Unify toasts across the app’s preferred notification system.

## File index (quick links)

- Dashboard entry: `src/pages/Dashboard.tsx`
- Container card: `src/components/dashboard/PreSeedFinancingCard.tsx`
- Convertible Note:
  - `src/components/financing/ConvertibleNoteTab.tsx`
  - `src/components/financing/convertiblenote/AuthorizeNotesSection.tsx`
  - `src/components/financing/convertiblenote/CurrentNotesSection.tsx`
- SAFEs:
  - `src/components/financing/SafeTab.tsx`
  - `src/components/financing/safe/AuthorizeSafesSection.tsx`
  - `src/components/financing/safe/CurrentSafesSection.tsx`
- MFN:
  - `src/components/financing/MfnTab.tsx`
  - `src/components/financing/mfn/MfnConvertibleNotesSection.tsx`
  - `src/components/financing/mfn/MfnSafesSection.tsx`
  - `src/components/financing/mfn/MfnSignDocumentsSection.tsx`
- SAFE Repurchase/Cancel:
  - `src/components/financing/SafeRepurchaseTab.tsx`
  - `src/components/financing/saferepurchase/SafeRepurchaseSection.tsx`
- Shared primitives: `src/components/common/Card.tsx`, `src/components/ui/tabs.tsx`
- Types: `src/types/financing.ts`


