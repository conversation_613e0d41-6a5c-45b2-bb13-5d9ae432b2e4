# Service Providers Terminate Feature Implementation

## Overview
This document outlines the implementation of the Service Providers Terminate feature, which allows users to terminate employees, consultants, and advisors. The feature includes displaying pending and terminated counts on the terminate row, opening dialogs to view terminated service providers, and an enhanced termination dialog with comprehensive form fields for termination details.

## Implementation Summary

### 1. API Client Integration
**File**: `src/integrations/legal-concierge/client.ts`

Added new API methods and updated existing termination method:

**Updated terminate service provider method**:
```typescript
// PUT: Terminate service provider
async terminateServiceProvider(id: string, payload: {
  terminationDate: string;
  serviceProviderTerminationType: string;
  severanceAmount: number;
  reasonForTermination: string;
}) {
  try {
    const response = await this.put(
      `/api/p2/serviceproviders/${id}/terminate`,
      payload
    );
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

**Added new API methods**:
```typescript
// GET: Get terminated service providers list
async getTerminatedServiceProviders(companyId: string) {
  try {
    const response = await this.get(`/api/p2/serviceproviders/${companyId}/terminatedlist`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// GET: Get pending terminated service providers list
async getPendingTerminatedServiceProviders(companyId: string) {
  try {
    const response = await this.get(`/api/p2/serviceproviders/${companyId}/pendingterminatedlist`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

### 2. Service Layer
**File**: `src/services/service-providers/terminateServiceProvider.service.ts`

**Updated terminate service function**:
```typescript
export async function terminateServiceProvider(payload: {
  id: string;
  terminationDate: string;
  serviceProviderTerminationType: string;
  severanceAmount: number;
  reasonForTermination: string;
}): Promise<boolean> {
  const response = await api.terminateServiceProvider(payload.id, payload);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return true;
}
```

**Added service functions with proper error handling**:
```typescript
export async function getTerminatedServiceProviders(companyId: string): Promise<TerminatedServiceProvider[]> {
  const response = await api.getTerminatedServiceProviders(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as TerminatedServiceProvider[];
}

export async function getPendingTerminatedServiceProviders(companyId: string): Promise<TerminatedServiceProvider[]> {
  const response = await api.getPendingTerminatedServiceProviders(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as TerminatedServiceProvider[];
}
```

**Type Definition**:
```typescript
interface TerminatedServiceProvider {
  id: string;
  name: string;
  serviceProviderStatus: "advisor" | "employee" | "independent-contractor-consultant";
  shares: number;
  typeOfGrant: string;
  vestedAmount: number;
  unvestedAmount: number;
  vestingProgress: number;
  severanceAmount: number | null;
}
```

### 3. React Query Hooks
**File**: `src/hooks/service-providers/useTerminate.hooks.ts`

Added hooks for data fetching and caching:
```typescript
export const useTerminatedServiceProviders = (companyId: string, options = {}) => {
  return useQuery({
    queryKey: ["terminatedServiceProviders", companyId],
    queryFn: () => getTerminatedServiceProviders(companyId),
    enabled: !!companyId,
    ...options,
  });
};

export const usePendingTerminatedServiceProviders = (companyId: string, options = {}) => {
  return useQuery({
    queryKey: ["pendingTerminatedServiceProviders", companyId],
    queryFn: () => getPendingTerminatedServiceProviders(companyId),
    enabled: !!companyId,
    ...options,
  });
};
```

**Updated terminate mutation** to handle new payload structure:
```typescript
export const useTerminateServiceProvider = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (payload: {
      id: string;
      terminationDate: string;
      serviceProviderTerminationType: string;
      severanceAmount: number;
      reasonForTermination: string;
    }) => terminateServiceProvider(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pendingServiceProviders"] });
      queryClient.invalidateQueries({ queryKey: ["serviceProviderStatus"] });
      queryClient.invalidateQueries({ queryKey: ["terminatedServiceProviders"] });
      queryClient.invalidateQueries({ queryKey: ["pendingTerminatedServiceProviders"] });
    },
  });
};
```

### 4. Column Definitions
**File**: `src/components/service-providers/terminateColumns.tsx`

Created comprehensive column definitions for the terminated service provider dialogs:
```typescript
export const terminatedServiceProviderColumns: ColumnDef<TerminatedServiceProvider>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "serviceProviderStatus",
    header: "Type",
    cell: ({ row }) => {
      const status = row.getValue("serviceProviderStatus") as string;
      const displayStatus = status
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
      return <div className="capitalize">{displayStatus}</div>;
    },
  },
  // ... additional columns for shares, grant type, vested amount, etc.
];
```

### 5. Service Provider Items Configuration
**File**: `src/components/dashboard/maintenance/ServiceProviderItems.tsx`

Updated interface to include terminate counts:
```typescript
interface ServiceProviderItemsProps {
  // ... existing props
  terminateCounts: { pending: number; terminated: number };
  onTerminatePendingClick?: () => void;
  onTerminateTerminatedClick?: () => void;
}
```

Updated terminate row configuration:
```typescript
{
  icon: UserX,
  title: "Terminate",
  description: "Terminate employees, consultants, or advisors.",
  buttonText: "Start",
  onClick: onTerminateClick,
  pendingCount: terminateCounts.pending,
  terminatedCount: terminateCounts.terminated,
  onPendingClick: onTerminatePendingClick,
  onTerminatedClick: onTerminateTerminatedClick,
}
```

### 6. Dialog Configuration
**File**: `src/hooks/service-providers/useServiceProvider.hooks.ts`

Added terminate type support:
```typescript
type ServiceProviderType = 'advisor' | 'employee' | 'contractor' | 'terminate';
```

Added terminate counts extraction:
```typescript
const terminateCounts = (statusData as any)?.terminated || { pending: 0, terminated: 0 };
```

Added terminate dialog configuration:
```typescript
if (type === 'terminate') {
  return {
    title: `${status.charAt(0).toUpperCase() + status.slice(1)} Terminated Service Providers`,
    data: terminateData.data,
    columns: terminatedServiceProviderColumns,
    loading: terminateData.loading
  };
}
```

Added `useTerminateData` helper function:
```typescript
function useTerminateData(companyId: string, statusDialog: StatusDialogState) {
  const { data: rawTerminatedProviders, isLoading: loadingTerminated } = useTerminatedServiceProviders(companyId, {
    enabled: statusDialog?.type === 'terminate' && statusDialog?.status === 'terminated' && !!companyId,
  });
  const { data: rawPendingTerminatedProviders, isLoading: loadingPendingTerminated } = usePendingTerminatedServiceProviders(companyId, {
    enabled: statusDialog?.type === 'terminate' && statusDialog?.status === 'pending' && !!companyId,
  });
  
  // ... data processing logic
}
```

### 7. Enhanced Terminate Dialog
**File**: `src/components/service-providers/TerminateDialog.tsx`

**New Form Fields Added**:
- Termination Date picker with future date prevention
- Termination Type radio buttons (3 options)
- Severance Amount number input
- Enhanced Reason text area

**Form Validation**:
```typescript
const isFormValid = () => {
  if (!selectedProviderId || !terminationDate || !terminationType) {
    return false;
  }
  
  // Validate that termination date is not in the future
  const today = new Date();
  const selectedDate = new Date(terminationDate);
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);
  
  return selectedDate <= today;
};

const getValidationError = () => {
  if (!selectedProviderId) return "Please select a service provider";
  if (!terminationDate) return "Please select a termination date";
  if (!terminationType) return "Please select a termination type";
  
  const today = new Date();
  const selectedDate = new Date(terminationDate);
  today.setHours(0, 0, 0, 0);
  selectedDate.setHours(0, 0, 0, 0);
  
  if (selectedDate > today) return "Termination date cannot be in the future";
  
  return null;
};
```

**Termination Types**:
```typescript
const terminationTypes = [
  { value: 'involuntary_termination_without_cause', label: 'Involuntary Termination (Without Cause)' },
  { value: 'involuntary_termination_with_cause', label: 'Involuntary Termination (With Cause)' },
  { value: 'resignation', label: 'Resignation' },
];
```

**Enhanced API Call**:
```typescript
const handleTerminate = async () => {
  if (selectedProviderId && terminationDate && terminationType) {
    try {
      await terminateMutation.mutateAsync({ 
        id: selectedProviderId, 
        terminationDate,
        serviceProviderTerminationType: terminationType,
        severanceAmount: parseFloat(severanceAmount) || 0,
        reasonForTermination: reason || "No reason provided" 
      });
      toast.success("Termination has been sent for approval.");
      resetAndClose();
    } catch (error) {
      toast.error("Failed to terminate service provider");
      console.error("Termination error:", error);
    }
  }
};
```

**UI Enhancements**:
- Real-time validation feedback with error messages
- Disabled states for incomplete forms
- Enhanced confirmation step showing all termination details
- Date input with max attribute to prevent future dates
- Visual error indicators for validation failures

### 8. Service Providers Page Integration
**File**: `src/pages/ServiceProviders.tsx`

Updated to use terminate counts and handlers:
```typescript
const { 
  advisorCounts,
  contractorCounts,
  employeeCounts,
  terminateCounts, // Added
  handleStatusDialogOpen,
  handleStatusDialogClose,
  getDialogConfig,
} = useServiceProviderDialogs();

const serviceProviderItems = getServiceProviderItems({
  // ... existing props
  terminateCounts, // Added
  onTerminatePendingClick: () => handleStatusDialogOpen('terminate', 'pending'),
  onTerminateTerminatedClick: () => handleStatusDialogOpen('terminate', 'terminated'),
});
```

## API Endpoints Used

1. **Status Counts**: `GET /api/p2/companies/{companyId}/serviceproviders/status`
   - Returns counts for all service provider types including `terminated.pending` and `terminated.terminated`

2. **Terminated List**: `GET /api/p2/serviceproviders/{companyId}/terminatedlist`
   - Returns list of terminated service providers with vesting information

3. **Pending Terminated List**: `GET /api/p2/serviceproviders/{companyId}/pendingterminatedlist`
   - Returns list of service providers pending termination

4. **Terminate Service Provider**: `PUT /api/p2/serviceproviders/{id}/terminate`
   - Terminates a service provider with enhanced payload:
     ```json
     {
       "terminationDate": "2025-01-15",
       "serviceProviderTerminationType": "involuntary_termination_without_cause",
       "severanceAmount": 5000.00,
       "reasonForTermination": "Performance issues"
     }
     ```

## Data Flow

1. **Initial Load**: Status API fetches counts for all service provider types
2. **Count Display**: Terminate row shows pending and terminated counts from `data.terminated`
3. **Icon Clicks**: 
   - Pending icon → Opens dialog with pending terminated service providers
   - Terminated icon → Opens dialog with terminated service providers
4. **Start Button**: Opens enhanced terminate selection dialog with comprehensive form fields
5. **Termination Process**: User fills form with date, type, severance, and reason
6. **Auto Updates**: Query invalidation ensures counts update after termination

## Key Features

- **Enhanced Form Validation**: Comprehensive validation with real-time feedback
- **Date Validation**: Prevents future termination dates both in UI and validation logic
- **Visual Consistency**: Uses existing `ServiceProviderStatusDialog` component
- **Type Safety**: Full TypeScript support with proper interfaces
- **Performance**: Efficient React Query caching and invalidation
- **Error Handling**: Comprehensive error handling at all levels
- **Real-time Updates**: Counts update automatically after termination actions
- **Rich Data Display**: Shows comprehensive service provider information including vesting details
- **User Experience**: Clear validation feedback, disabled states, and enhanced confirmation step

## Testing

The implementation has been tested for:
- ✅ TypeScript compilation without errors
- ✅ Proper API integration with enhanced payload
- ✅ Dialog opening and data display
- ✅ Enhanced form validation and error handling
- ✅ Date validation preventing future dates
- ✅ Count updates after termination
- ✅ Error handling scenarios
- ✅ Form state management and reset functionality

## Future Enhancements

- Add filtering and sorting capabilities to the terminated service provider dialogs
- Implement bulk termination actions
- Add export functionality for terminated service provider lists
- Add audit trail for termination actions
