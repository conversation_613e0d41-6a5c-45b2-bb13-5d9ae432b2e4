# Create Stock Option Plan - Implementation Documentation

## Overview
Implements a new Maintenance action and modal to create the initial Stock Option Plan when a company has no SOP yet. The UX has been simplified to match the Increase Stock Option Plan: a single submit button, status-driven display, per-button loaders, an exceed-authorized warning with CTA to open Increase Authorized Shares, and fetch-on-open behavior.

**UI Changes (Completed):**
✅ Single "Submit" button that handles board approval
✅ Remove "Submit for Stockholder Approval" button - show status only
✅ Backend handles entire approval workflow automatically after initial submission
✅ Submit button always visible but conditionally disabled with hover tooltips
✅ Submit button positioned in footer (left side)
✅ Clean status display for both Board and Stockholder approval (no buttons)
✅ Loading spinner only on submit button (no modal-wide loading)
✅ Proper tooltip implementation with hover messages
✅ Spinner integration for button loading states

## Files Added
- `src/services/create-stock-option-plan/createStockOptionPlan.service.ts`
- `src/hooks/create-stock-option-plan/useCreateStockOptionPlan.hooks.ts`
- `src/components/shares/CreateStockOptionPlanDialog.tsx`
- `src/components/shares/CreateStockOptionPlanInitialState.tsx`

## Files Updated
- `src/integrations/legal-concierge/client.ts`: added Create SOP endpoints
- `src/components/dashboard/maintenance/MaintenanceItems.tsx`: show Create vs Increase SOP based on `includeStockOptionPlan`
- `src/components/dashboard/MaintenanceCard.tsx`: fetch `includeStockOptionPlan`, wire Create SOP dialog and IAS CTA
- `src/components/dashboard/maintenance/MaintenanceDialogs.tsx`: already provides IAS wiring used by CTA

## API Methods
- `GET /api/p2/companies/{companyId}/createstockoptionplan/currentpending`
- `POST /api/p2/companies/{companyId}/createstockoptionplan`
- `PUT /api/p2/createstockoptionplan/{companyId}/submitforstockholderapproval`
- `PUT /api/p2/companies/{companyId}/createstockoptionplan/abandoned`
- `GET /api/p2/companies/{companyId}/stockoptionsplansummary` (limits/CTA)

## Service Contract
```ts
getCurrentPending(companyId): Promise<CreateSopState | null>
submitForBoardApproval(companyId, shareAmount): Promise<void>
// Note: submitForStockholderApproval and abandonProcess methods remain for API completeness
// but are not used in the simplified UI flow
submitForStockholderApproval(companyId): Promise<void>
abandonProcess(companyId): Promise<void>
```

## Hook Contract
```ts
{
  data,                 // CreateSopState | null
  summary,              // StockOptionPlanSummary | null
  isLoading,            // excludes board approval loading to avoid modal-wide spinner
  isBoardApprovalLoading, // Button-level loading only
  isStockholderApprovalLoading,
  isAbandonLoading,
  error,
  refetch,
  submitForBoardApproval,
  // Note: Other methods remain for API completeness but may not be used in UI
  submitForStockholderApproval,
  abandonProcess,
}
```

## Dialog Integration
- Fetch on open; spinner shown while loading (only for initial data fetch)
- Computes exceed-authorized using summary and entered/API share amount
- CTA closes Create SOP modal and opens IAS when exceeding authorized
- Status-driven display; refetch after each mutation
- **No modal-wide loading during submit** - only button shows loading state

## UI
- Share Amount
  - Read-only and shows API value when `additionalShare > 0`
  - Editable when `additionalShare === 0` (or no pending)
- Status text renders: Yes | Pending | No from API values
- Submit Button (in footer)
  - Always visible but conditionally disabled
  - Enabled when Share Amount > 0 and `isBoardApproved === "no"` and not exceeding authorized
  - Shows spinner + "Submitting..." when loading
  - Hover tooltips explain disabled states
  - **No modal refresh during submit** - content stays stable
- Stockholder Approval: Shows status only (no button) - backend handles workflow automatically
- IAS CTA: shown when exceed-authorized; opens IAS via callback

## Button State Logic
```typescript
const canSubmit = hasShareAmount && boardApprovalStatus === "no" && !exceedsAuthorized;

const getSubmitButtonTooltip = () => {
  if (!hasShareAmount) return "Please enter share amount first";
  if (boardApprovalStatus === "yes") return "Board approval already completed";
  if (boardApprovalStatus === "pending") return "Board approval is in progress";
  if (exceedsAuthorized) return "Exceeds authorized shares - increase authorized shares first";
  return "Submit for board approval";
};
```

## Loading State Management
```typescript
// Hook ensures only button shows loading, not entire modal
// Global isLoading excludes board approval loading
isLoading: pendingQuery.isLoading || summaryQuery.isLoading || stockholderApprovalMutation.isPending

// Button-level loading for submit action
isBoardApprovalLoading: boardApprovalMutation.isPending

// Result: Modal stays stable, only submit button shows spinner
```

## Simplified Workflow
1. User enters share amount (only when editable)
2. Click "Submit" button (enabled only when `canSubmit` is true)
3. Call POST `/createstockoptionplan` with payload `{ shareAmount }`
4. On success: `toast.success("Board Approval Submitted Successfully")`
5. Refetch GET and update UI
6. **Backend automatically handles** board → stockholder approval workflow

## Toasts
- Success: board approval submitted
- Errors: `toast.error(message)`

## Notes
- No GET on Maintenance navigation; only on modal open
- The same query invalidation pattern as Increase SOP is used after mutations
- UI now matches Increase SOP exactly - consistent user experience
- Backend handles entire approval workflow automatically after initial submission
- **Loading states are button-level only** - no modal-wide loading during submit
- **Tooltips provide clear feedback** for disabled button states
- **Spinner integration** ensures smooth loading UX on submit button
