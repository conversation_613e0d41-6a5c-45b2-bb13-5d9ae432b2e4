# Stock Option Plan Refetch Feature

## Overview
Added automatic refetch of stock option plan summary data after equity grant operations to ensure UI consistency.

## Problem
When equity grants were issued, modified, or deleted through the ModifyGrantsDialog, only the promised grants list was refetched. The stock option plan summary (total pool, allocated shares, remaining shares) was not updated, causing the Equity Overview section to show stale data.

## Solution
Updated the mutation hooks to invalidate both the `promisedGrants` and `stockOptionInformation` queries after successful operations.

## Changes Made

### 1. Updated useUpdatePromisedGrant Hook
- **File**: `src/hooks/service-providers/usePromisedGrants.hooks.ts`
- **Change**: Added `stockOptionInformation` query invalidation to `onSuccess` callback
- **Before**: Only invalidated `promisedGrants` query
- **After**: Invalidates both `promisedGrants` and `stockOptionInformation` queries

```typescript
onSuccess: () => {
  queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
  queryClient.invalidateQueries({ queryKey: ["stockOptionInformation"] });
},
```

### 2. Updated useDeletePromisedGrant Hook
- **File**: `src/hooks/service-providers/usePromisedGrants.hooks.ts`
- **Change**: Added `stockOptionInformation` query invalidation to `onSuccess` callback
- **Reason**: Deleting grants also affects the allocated shares count

## API Flow After Changes

### Before (Incomplete):
1. PUT to update/delete grant
2. Only `promisedGrants` query invalidated
3. `stockOptionInformation` remains stale
4. UI shows outdated equity overview

### After (Complete):
1. PUT to update/delete grant
2. Both `promisedGrants` and `stockOptionInformation` queries invalidated
3. Both data sources refetched automatically
4. UI shows current equity overview

## Benefits
- **Data Consistency**: Equity Overview always reflects current state
- **User Experience**: Users see real-time updates to share allocations
- **Automatic**: No manual refresh required
- **Performance**: React Query handles caching and background updates efficiently

## Technical Implementation
- Uses React Query's `queryClient.invalidateQueries()` for cache invalidation
- Maintains existing functionality while adding necessary refetch
- No breaking changes to existing API calls or UI components
