### Feature: 409A – Set Fair Market Value (FMV)

This document explains how the FMV set flow was implemented end‑to‑end, aligned with `docs/plan-docs/409a-set-fair-market-value-req.md`.

---

### API Client
File: `src/integrations/legal-concierge/client.ts`

- Added method to call the backend endpoint:
```ts
async set409aFairMarketValue(companyId: string, payload: { fairMarketValue: number }) {
  return this.put(`/api/p2/companies/${companyId}/409avaluation/setfairmarketvalue`, payload);
}
```

Why: Centralizes API access; reuses existing error handling/token refresh.

---

### Service Layer
File: `src/services/service-providers/promisedGrants.service.ts`

- Added service function:
```ts
export async function setFairMarketValue(companyId: string, value: number) {
  return api.set409aFairMarketValue(companyId, { fairMarketValue: value });
}
```

Why: Keeps UI decoupled from the API client; keeps 409A functions co‑located with promised-grants context used by the page.

---

### Hook
File: `src/hooks/service-providers/usePromisedGrants.hooks.ts`

- Added `useSetFairMarketValue` mutation with react‑query invalidation and sonner toasts:
```ts
export function useSetFairMarketValue(companyId: string) {
  const queryClient = useQueryClient();
  const mutation = useMutation({
    mutationFn: (value: number) => setFairMarketValue(companyId, value),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["active409AValuation", companyId] });
      toast.success("Fair market value updated");
    },
    onError: (err: any) => {
      const message = err?.response?.data?.data?.[0]?.description || err?.message || "Failed to update FMV";
      toast.error(message);
    },
  });
  return { setFMV: (v: number) => mutation.mutateAsync(v), isSettingFMV: mutation.isPending };
}
```

Why: Single responsibility; consistent error handling; auto refresh of UI after success.

---

### UI Integration
File: `src/components/service-providers/dialog/Dialog409Valuation.tsx`

- Wired in the hook and companyId from `useAuth()`; validated numeric `pricePerShare` and used it as FMV:
```ts
const { user } = useAuth();
const companyId = user?.companyId || "";
const { setFMV, isSettingFMV } = useSetFairMarketValue(companyId);

if (mode === 'set-fair-market-value') {
  if (!formData.pricePerShare) { /* toast + return */ }
  await setFMV(Number(formData.pricePerShare));
  onClose();
}
```

- CTA/button disabled and shows “Sending…” while pending.

Why: Matches the requirement doc’s loading, validation, and toast behaviors.

---

### Files Changed
- `src/integrations/legal-concierge/client.ts` – added `set409aFairMarketValue`
- `src/services/service-providers/promisedGrants.service.ts` – added `setFairMarketValue`
- `src/hooks/service-providers/usePromisedGrants.hooks.ts` – added `useSetFairMarketValue`
- `src/components/service-providers/dialog/Dialog409Valuation.tsx` – integrated hook and UI states
- Docs added: `docs/plan-docs/409a-set-fair-market-value-req.md`, `docs/feature-docs/set-fair-market-value-feature.md`

---

### Behavior
- Enter FMV in dialog → submit → button shows “Sending…”, inputs disabled.
- On success: toast.success, dialog closes, and `active409AValuation` query refetch updates the UI.
- On error: toast.error, dialog remains open.

---

### Next
- If needed, add input helper text and numeric formatting. The current flow is intentionally minimal and consistent with existing patterns.
