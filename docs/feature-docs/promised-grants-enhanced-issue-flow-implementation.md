# Enhanced Promised Grants Issue Flow - Implementation Document

## Overview

This document outlines the implementation of an enhanced issue flow for promised grants, including 409A valuation handling, bulk editing capabilities, and improved user experience for issuing equity grants.

## Table of Contents

1. [Feature Summary](#feature-summary)
2. [Technical Architecture](#technical-architecture)
3. [Implementation Details](#implementation-details)
4. [API Changes](#api-changes)
5. [Component Updates](#component-updates)
6. [User Flow](#user-flow)
7. [Testing Considerations](#testing-considerations)
8. [Future Enhancements](#future-enhancements)

## Feature Summary

The enhanced promised grants issue flow introduces:

- **Intermediate 409A Selection Modal**: A new modal that appears when "Issue" is clicked, offering three 409A valuation options
- **Enhanced 409A Valuation Dialog**: Updated dialogs with "Next" button functionality for seamless flow
- **Bulk Grant Editing**: Refactored modify grants dialog for bulk editing without individual row actions
- **Read-Only Review Mode**: Table view mode for reviewing grants before issuance
- **Concurrent Grant Issuance**: New API endpoint and hook for issuing multiple grants simultaneously
- **Smart Row Selection**: Checkbox selection limited to grants with "Board Approved: NO" status
- **409A Valuation Error Handling**: Proper handling of null appraisal dates with "Not Found" error messages
- **Unrounded Number Display**: All numeric values displayed exactly as received from API without rounding

## Technical Architecture

### New Components

1. **`IssueGrants409AModal`**: Intermediate modal for 409A option selection
2. **Enhanced `Dialog409Valuation`**: Updated with "Next" button and flow integration
3. **Enhanced `ModifyGrantsDialog`**: Supports both edit and view modes
4. **Enhanced `PromisedGrantsTable`**: Conditional editing and smart selection

### New Hooks

1. **`useIssuePromisedGrants`**: Handles concurrent grant issuance
2. **Enhanced `useGrantIssuance`**: Bulk editing with sequential API calls

### New Services

1. **`issuePromisedGrant`**: API endpoint for issuing individual grants

## Implementation Details

### 1. 409A Valuation Error Handling

**File**: `src/pages/PromisedGrants.tsx`

Enhanced the 409A Valuation card to properly handle null appraisal dates:

```typescript
// Updated condition to check for null appraisalDate
{active409AValuation?.data?.appraisalDate ? (
  // Show valuation details
) : (
  // Show "Not Found" error message
)}

// Error message for null appraisal date
{!active409AValuation.appraisalDate ? (
  <div className="bg-red-500/20 border border-red-500/30 rounded-md p-3 mb-4">
    <p className="text-red-400 text-sm font-medium">⚠️ 409A Valuation Not Found</p>
    <p className="text-red-300 text-xs mt-1">409A valuation not found. Please upload in the maintenance section.</p>
  </div>
) : // ... existing expired logic
```

**File**: `src/services/service-providers/promisedGrants.service.ts`

Updated TypeScript interface to handle null values:

```typescript
export interface Active409AValuation {
  appraiserName: string | null;
  appraisalDate: string | null; // Updated to allow null
  fairMarketValue: number;
  confirmationStatus: string;
  isExpired: boolean;
}
```

### 2. Unrounded Number Display

**File**: `src/pages/PromisedGrants.tsx`

Removed currency formatting to show exact API values:

```typescript
// Before: Formatted currency
{new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
}).format(active409AValuation.fairMarketValue)}

// After: Raw value display
${active409AValuation.fairMarketValue}
```

**File**: `src/components/service-providers/PromisedGrantsTable.tsx`

Removed number rounding from all numeric fields:

```typescript
// Before: Rounded display
{`$${grant.pricePerShare.toFixed(2)}`}

// After: Raw value display
{`$${grant.pricePerShare}`}
```

### 3. State Management Bug Fix

**File**: `src/pages/PromisedGrants.tsx`

Fixed dialog state management issue where modify dialog would open in review mode after closing 409A flow:

```typescript
const handleModifySelected = () => {
  setIsReviewModeActive(false); // Reset to edit mode
  openModifyDialog();
};
```

### 4. New API Endpoint

**File**: `src/integrations/legal-concierge/client.ts`

```typescript
// POST: Issue promised grant
async issuePromisedGrant(companyId: string, promiseGrantId: string) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/promisegrants/issue`, {
      promiseGrantId
    });
    return response;
  } catch (error) {
    throw error;
  }
}
```

**File**: `src/services/service-providers/promisedGrants.service.ts`

```typescript
export async function issuePromisedGrant(
  companyId: string,
  promiseGrantId: string
): Promise<any> {
  const response = await api.issuePromisedGrant(companyId, promiseGrantId);
  return response;
}
```

### 2. New Hook: useIssuePromisedGrants

**File**: `src/hooks/service-providers/usePromisedGrants.hooks.ts`

```typescript
export function useIssuePromisedGrants() {
  const [isIssuing, setIsIssuing] = useState(false);
  const queryClient = useQueryClient();

  const handleIssueGrants = async (grants: PromisedGrant[], companyId: string) => {
    setIsIssuing(true);
    const results: Array<{ grantId: string; success: boolean; error?: any }> = [];
    
    try {
      // Process grants sequentially for better error handling and status tracking
      for (const grant of grants) {
        try {
          await issuePromisedGrant(companyId, grant.id);
          results.push({ grantId: grant.id, success: true });
        } catch (error) {
          results.push({ grantId: grant.id, success: false, error });
        }
      }
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      // Only refetch after ALL grants have been processed
      if (successCount > 0) {
        queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
        queryClient.invalidateQueries({ queryKey: ["stockOptionInformation"] });
      }
      
      if (failureCount === 0) {
        toast.success(`All ${successCount} grants issued successfully!`);
        return { success: true, results };
      } else {
        toast.error(`${successCount} grants issued, ${failureCount} failed. Check details.`);
        return { success: false, results };
      }
    } catch (error) {
      console.error("Error issuing grants:", error);
      toast.error("Failed to issue grants. Please try again.");
      return { success: false, results };
    } finally {
      setIsIssuing(false);
    }
  };

  return {
    isIssuing,
    handleIssueGrants,
  };
}
```

### 3. Enhanced Grant Issuance Hook

**File**: `src/hooks/service-providers/usePromisedGrants.hooks.ts`

```typescript
export function useGrantIssuance() {
  const [isIssuing, setIsIssuing] = useState(false);
  const updateGrantMutation = useUpdatePromisedGrant();
  const queryClient = useQueryClient();

  const handleSaveAndClose = async (
    grants: PromisedGrant[],
    localEdits: Record<string, Partial<PromisedGrant>> = {}
  ) => {
    setIsIssuing(true);
    const results: Array<{ grantId: string; success: boolean; error?: any }> = [];

    try {
      for (const grant of grants) {
        try {
          const editedData = localEdits[grant.id];
          const payload = editedData ? { ...grant, ...editedData } : grant;

          await updateGrantMutation.mutateAsync({
            id: grant.id,
            payload: {
              name: payload.name,
              grantType: payload.grantType,
              shares: payload.shares,
              vestingSchedule: payload.vestingSchedule,
              pricePerShare: payload.pricePerShare,
            },
          });
          results.push({ grantId: grant.id, success: true });
        } catch (error) {
          results.push({ grantId: grant.id, success: false, error });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      // Only refetch after ALL grants have been processed
      if (successCount > 0) {
        queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
        queryClient.invalidateQueries({ queryKey: ["stockOptionInformation"] });
      }

      if (failureCount === 0) {
        toast.success(`All ${successCount} grants saved successfully!`);
        return { success: true, results };
      } else {
        toast.error(`${successCount} grants saved, ${failureCount} failed. Check details.`);
        return { success: false, results };
      }
    } catch (error) {
      console.error("Error saving grants:", error);
      toast.error("Failed to save grants. Please try again.");
      return { success: false, results };
    } finally {
      setIsIssuing(false);
    }
  };

  return {
    isIssuing,
    handleSaveAndClose,
  };
}
```

### 4. New Component: IssueGrants409AModal

**File**: `src/components/service-providers/dialog/IssueGrants409AModal.tsx`

```typescript
interface IssueGrants409AModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGrants: PromisedGrant[];
  onOptionSelect: (option: 'use-current' | 'set-fmv' | 'update-409a') => void;
}

const IssueGrants409AModal: React.FC<IssueGrants409AModalProps> = ({
  isOpen,
  onClose,
  selectedGrants,
  onOptionSelect,
}) => {
  const handleOptionSelect = (option: 'use-current' | 'set-fmv' | 'update-409a') => {
    onOptionSelect(option);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5] max-w-md">
        <DialogHeader>
          <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
            Select 409A Valuation Method
          </DialogTitle>
          <DialogDescription className="text-[#F2F2F5] opacity-80">
            Choose how to handle fair market value for {selectedGrants.length} selected grant{selectedGrants.length > 1 ? 's' : ''}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <Button
            onClick={() => handleOptionSelect('use-current')}
            className="w-full h-16 bg-gradient-to-r from-[#9B51E0] to-[#E1467C] text-[#F2F2F5] hover:opacity-90 flex items-center justify-start space-x-3 px-4"
          >
            <FileText className="h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">Use Current 409A Valuation</div>
              <div className="text-sm opacity-80">Use existing 409A without changes</div>
            </div>
          </Button>

          <Button
            onClick={() => handleOptionSelect('set-fmv')}
            className="w-full h-16 bg-[#2E2F3A] border border-[#9B51E0] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 flex items-center justify-start space-x-3 px-4"
          >
            <DollarSign className="h-5 w-5 text-[#9B51E0]" />
            <div className="text-left">
              <div className="font-medium">Set Fair Market Value</div>
              <div className="text-sm opacity-80">Set a new fair market value manually</div>
            </div>
          </Button>

          <Button
            onClick={() => handleOptionSelect('update-409a')}
            className="w-full h-16 bg-[#2E2F3A] border border-[#9B51E0] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 flex items-center justify-start space-x-3 px-4"
          >
            <Upload className="h-5 w-5 text-[#9B51E0]" />
            <div className="text-left">
              <div className="font-medium">Update 409A Valuation</div>
              <div className="text-sm opacity-80">Upload new 409A valuation report</div>
            </div>
          </Button>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
```

### 5. Enhanced ModifyGrantsDialog

**File**: `src/components/service-providers/dialog/ModifyGrantsDialog.tsx`

Key changes:
- Added `isViewMode` prop for read-only mode
- Added `onIssueGrants` prop for issuance functionality
- Button text changes based on mode ("Save and Close" vs "Issue Grants")
- Status display for both edit and issue operations

```typescript
interface ModifyGrantsDialogProps {
  // ... existing props
  isViewMode?: boolean;
  onIssueGrants?: (grants: PromisedGrant[]) => Promise<{ success: boolean; results: any[] }>;
  isIssuing?: boolean;
  issueResults?: Array<{ grantId: string; success: boolean; error?: any }>;
}
```

### 6. Enhanced PromisedGrantsTable

**File**: `src/components/service-providers/PromisedGrantsTable.tsx`

Key changes:
- Added `isViewMode` prop to disable editing
- Smart checkbox selection based on board approval status
- Conditional editing for all fields
- Removed number rounding for all numeric values
- Made price per share read-only in dialog mode

```typescript
// Smart checkbox selection
<Checkbox
  checked={selectedGrants.has(grant.id)}
  onCheckedChange={(checked) => onSelectGrant?.(grant.id, checked === true)}
  disabled={grant.boardApproved !== 'no'} // Updated to lowercase
/>

// Conditional editing
{actionType === 'edit' && !isViewMode ? (
  <Input ... />
) : (
  grant.name
)}

// Unrounded number display
<TableCell className="text-[#F2F2F5]">
  {`$${grant.pricePerShare}`} // Removed .toFixed(2)
</TableCell>
```

## API Changes

### New Endpoint

```
POST /api/p2/companies/{companyId}/promisegrants/issue
Body: { "promiseGrantId": "uuid" }
```

### Updated Endpoints

- **PUT** `/api/p2/promisegrants/{id}` - Enhanced for bulk operations
- **GET** `/api/p2/companies/{companyId}/promisegrants` - Refetched after operations
- **GET** `/api/p2/companies/{companyId}/stockoptionsplansummary` - Refetched after operations

## Component Updates

### Main Component: PromisedGrants.tsx

- Added new state management for review mode
- Integrated all three 409A flows
- Added handlers for each 409A option
- Connected to new issue grants functionality
- Enhanced 409A Valuation card with proper error handling for null appraisal dates
- Removed number rounding from fair market value display
- Added "409A Valuation Not Found" error message when appraisalDate is null

### Dialog Updates

- **Dialog409Valuation**: Added `onNext` prop and updated button text
- **ModifyGrantsDialog**: Added view mode and issue functionality, fixed state management bug
- **PromisedGrantsTable**: Added conditional editing and smart selection, removed number rounding

## User Flow

### Complete Issue Flow

1. **Select Grants**: User selects grants with "Board Approved: NO" status
2. **Click Issue**: Opens intermediate 409A selection modal
3. **Choose 409A Option**:
   - **Use Current 409A**: Shows current FMV → Click "Next" → Review Table → Issue
   - **Set Fair Market Value**: Input FMV → Click "Save and Continue" → Review Table → Issue
   - **Update 409A Valuation**: Upload + Form → Click "Save and Continue" → Review Table → Issue
4. **Review Table**: Read-only table showing final grant details
5. **Issue Grants**: Click "Issue Grants" → Concurrent API calls → Real-time status → Success/Close

### Bulk Edit Flow

1. **Select Grants**: User selects multiple grants
2. **Click Modify**: Opens bulk edit dialog
3. **Edit Fields**: All selected grants editable simultaneously
4. **Save Changes**: Click "Save and Close" → Sequential API calls → Real-time status → Success/Close

## Testing Considerations

### Unit Tests

- Test checkbox selection logic for board approval status
- Test bulk editing functionality
- Test 409A flow integration
- Test concurrent API calls

### Integration Tests

- Test complete issue flow end-to-end
- Test bulk edit flow end-to-end
- Test error handling and partial success scenarios

### User Acceptance Tests

- Verify checkbox behavior for different board approval statuses
- Verify 409A flow navigation
- Verify bulk editing and issuance functionality

## Future Enhancements

### Potential Improvements

1. **Batch Operations**: Support for bulk operations beyond individual API calls
2. **Advanced Validation**: Pre-issuance validation checks
3. **Audit Trail**: Track all changes and issuance actions
4. **Template Support**: Save and reuse common grant configurations
5. **Notification System**: Email/SMS notifications for grant issuance

### Performance Optimizations

1. **Pagination**: Handle large numbers of grants efficiently
2. **Caching**: Implement smart caching for frequently accessed data
3. **Background Processing**: Move heavy operations to background jobs

## Conclusion

The enhanced promised grants issue flow provides a comprehensive, user-friendly experience for managing equity grants. The implementation follows React best practices, maintains code consistency, and provides robust error handling and user feedback.

Key benefits:
- **Improved UX**: Clear flow with intermediate steps and review opportunities
- **Better Error Handling**: Partial success support and real-time status updates
- **Flexible 409A Handling**: Three distinct paths for different valuation scenarios
- **Smart Selection**: Prevents user errors by limiting selection to eligible grants
- **Bulk Operations**: Efficient handling of multiple grants simultaneously
- **Robust Data Display**: Proper handling of null values and unrounded number display
- **State Management**: Fixed dialog state issues for consistent user experience

The implementation is production-ready and provides a solid foundation for future enhancements.
