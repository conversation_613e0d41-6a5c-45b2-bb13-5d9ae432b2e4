# Increase Authorized Shares Feature - Implementation Documentation

## Overview
The "Increase Authorized Shares" feature allows users to increase their company's authorized share capital through a **simplified single-submit workflow** that integrates with backend APIs for real-time data management and state synchronization. The UX has been simplified to match the Stock Option Plan pattern: single submit button, status-driven display, and automated backend workflow.

**UI Changes (Completed):**
✅ Single "Submit" button that handles entire workflow
✅ Remove all individual approval buttons (Board, Stockholder, File Amendment)
✅ Backend handles entire approval workflow automatically after initial submission
✅ Submit button always visible but conditionally disabled with hover tooltips
✅ Submit button positioned in footer (left side)
✅ Clean status display for all approval types (no action buttons)
✅ Loading spinner only on submit button (no modal-wide loading)
✅ Proper tooltip implementation with hover messages
✅ Spinner integration for button loading states

## Feature Architecture

### 1. API Integration Layer
**File**: `src/integrations/legal-concierge/client.ts`

**New Methods Added**:
```typescript
// GET: Get current pending authorized shares
async getCurrentPendingAuthorizedShares(companyId: string)

// POST: Submit for board approval (triggers entire workflow)
async submitForBoardApproval(companyId: string, additionalShares: number)

// PUT: Submit for stockholder approval (backend handles automatically)
async submitForStockholderApproval(companyId: string)

// PUT: Abandon process (API endpoint remains available)
async abandonAuthorizedSharesProcess(companyId: string)
```

**Purpose**: Centralized API client methods that handle all authorized shares operations with proper error handling and authentication.

### 2. Service Layer
**File**: `src/services/authorized-shares/authorizedShares.service.ts`

**Key Components**:
- `AuthorizedSharesResponse` interface for API response typing
- `AuthorizedSharesService` interface defining service contract
- `AuthorizedSharesServiceImpl` class implementing the service
- Error handling and type safety

**Responsibilities**:
- Interface with API client
- Provide type-safe service methods
- Handle error transformation
- Maintain service layer abstraction

### 3. Hook Layer
**File**: `src/hooks/authorized-shares/useAuthorizedShares.hooks.ts`

**Key Features**:
- React Query integration for data fetching and caching
- Mutation handling for POST/PUT operations
- Loading and error state management
- Toast notifications for user feedback
- Automatic data refetching after mutations
- **Button-level loading states only** - no modal-wide loading

**Hook Interface**:
```typescript
interface UseAuthorizedSharesReturn {
  data: AuthorizedSharesResponse['data'] | null;
  isLoading: boolean;                    // Excludes board approval loading
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (additionalShares: number) => Promise<void>; // Single submit
  submitForStockholderApproval: () => Promise<void>; // API completeness only
  abandonProcess: () => Promise<void>;   // API completeness only
}
```

**Loading State Management**:
```typescript
// Hook ensures only button shows loading, not entire modal
// Global isLoading excludes board approval loading
isLoading: queryLoading || stockholderApprovalLoading || abandonLoading || fileUploadLoading

// Button-level loading for submit action
isBoardApprovalLoading: boardApprovalMutation.isPending

// Result: Modal stays stable, only submit button shows spinner
```

### 4. Component Layer

#### Main Dialog Component
**File**: `src/components/shares/IncreaseSharesDialog.tsx`

**Key Changes**:
- **Simplified to single-state dialog** - removed step-based navigation
- **Single submit handler** - triggers entire backend workflow
- **Automated progression** - backend handles Board → Stockholder → File Amendment
- **No manual step navigation** - user submits once, backend handles everything
- **Real-time status updates** - UI shows progress as backend processes

**State Management**:
- Uses `useAuthorizedShares` hook for data fetching
- Handles loading states with spinner (button-level only)
- Displays error states with retry functionality
- Manages dialog state and user interactions

#### Initial State Component
**File**: `src/components/shares/IncreaseSharesInitialState.tsx`

**Key Updates**:
- **Single submit button** in footer (left side)
- **Status-only display** for all approval types (no action buttons)
- **Tooltip integration** - hover shows why button is disabled
- **Spinner integration** - loading state on submit button only
- **Clean status rendering** - Yes | No | Pending for approvals, Submitted | Not Submitted for file amendment

**Button Logic**:
```typescript
const canSubmit = hasAdditionalShares && boardApprovalStatus === "no";

const getSubmitButtonTooltip = () => {
  if (!hasAdditionalShares) return "Please enter additional shares first";
  if (boardApprovalStatus === "yes") return "Board approval already completed";
  if (boardApprovalStatus === "pending") return "Board approval is in progress";
  return "Submit for board approval";
};
```

**Status Display**:
```typescript
// All statuses show text only, no action buttons
Board Approved: Yes | No | Pending
Stockholders Approved: Yes | No | Pending  
File Amendment: Submitted | Not Submitted
```

### 5. Integration Points

#### Maintenance Card Integration
**File**: `src/components/dashboard/MaintenanceCard.tsx`

**Changes**:
- Added `useAuth` hook to get `companyId`
- Updated `MaintenanceDialogs` props to pass `companyId`
- Removed mock `currentAuthorizedShares` data

#### Maintenance Dialogs Integration
**File**: `src/components/dashboard/maintenance/MaintenanceDialogs.tsx`

**Changes**:
- Updated interface to accept `companyId` instead of `currentAuthorizedShares`
- Updated `IncreaseSharesDialog` props to pass `companyId`

## API Response Structure

### GET `/api/p2/companies/{companyId}/authorizedshares/currentpending`
```typescript
interface AuthorizedSharesResponse {
  message: string;
  data: {
    authorizedShare: number;
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    isFileOfCertificateOfAmendmentUploaded: "yes" | "no";
  };
  error: null | string;
  validationErrors: null | string[];
}
```

## Simplified User Flow Implementation

### 1. Initial Load Flow
1. User clicks "Start" button on "Increase Authorized Shares" maintenance item
2. Dialog opens and calls `GET /api/p2/companies/{companyId}/authorizedshares/currentpending`
3. Display data based on response:
   - If `additionalShare > 0`: Show existing pending increase (read-only)
   - If `additionalShare === 0`: Show editable form for new increase

### 2. Single Submit Flow (Automated Backend Workflow)
1. User enters additional shares (only if `additionalShare === 0`)
2. User clicks single "Submit" button
3. Call `POST /api/p2/companies/{companyId}/authorizedshares`
4. **Backend automatically handles entire workflow**:
   - Submits for Board approval
   - Waits for Board approval completion
   - Submits for Stockholder approval
   - Waits for Stockholder approval completion
   - Sends email to agent for File Amendment
5. UI shows real-time status updates as backend progresses
6. **No manual user intervention required**

### 3. Status Display (Read-Only)
- **Board Approved**: Yes | No | Pending (updates automatically)
- **Stockholders Approved**: Yes | No | Pending (updates automatically)
- **File Amendment**: Submitted | Not Submitted (updates automatically)

## Error Handling

### 1. API Error Handling
- Service layer catches and transforms API errors
- Hook layer provides error states to components
- Components display user-friendly error messages
- Retry functionality for failed requests

### 2. Loading States
- **Button-level loading only** - submit button shows spinner
- **No modal-wide loading** - content stays stable during submit
- Disabled buttons during API operations
- Loading indicators for mutations

### 3. User Feedback
- Toast notifications for successful operations
- Error messages for failed operations
- Clear status indicators for approval states

## State Management Logic

### Submit Button Rules
```typescript
// Single submit button logic
const canSubmit = hasAdditionalShares && boardApprovalStatus === "no";

// Button is always visible but conditionally disabled
// Hover tooltips explain disabled states
```

### Field Editability Rules
```typescript
// Additional shares field
const isAdditionalSharesEditable = data.additionalShare === 0;
```

### Status Display Logic
```typescript
// All statuses show text only, no action buttons
const formatStatus = (s: "yes" | "no" | "pending") => 
  s === "yes" ? "Yes" : s === "pending" ? "Pending" : "No";

const formatFileStatus = (s: "yes" | "no") => 
  s === "yes" ? "Submitted" : "Not Submitted";
```

## File Structure

```
src/
├── integrations/
│   └── legal-concierge/
│       └── client.ts (Updated with new API methods)
├── services/
│   └── authorized-shares/
│       └── authorizedShares.service.ts (New)
├── hooks/
│   └── authorized-shares/
│       └── useAuthorizedShares.hooks.ts (New)
├── components/
│   ├── dashboard/
│   │   ├── MaintenanceCard.tsx (Updated)
│   │   └── maintenance/
│   │       └── MaintenanceDialogs.tsx (Updated)
│   └── shares/
│       ├── IncreaseSharesDialog.tsx (Updated - Simplified)
│       └── IncreaseSharesInitialState.tsx (Updated - Single Submit)
```

## Key Features Implemented

### 1. Simplified User Experience
- **Single submit button** - user clicks once, backend handles everything
- **Automated workflow** - no manual step navigation required
- **Real-time status updates** - UI shows progress automatically
- **Consistent with Stock Option Plans** - same UX pattern across features

### 2. Real-time Data Synchronization
- API-driven data fetching with react-query
- Automatic data refetching after mutations
- Real-time status updates

### 3. Dynamic UI States
- Conditional button visibility based on API status
- Field editability based on current state
- Loading and error states (button-level only)

### 4. User Experience Enhancements
- Toast notifications for all operations
- **No abandon functionality in UI** (API endpoint remains available)
- Retry mechanisms for failed requests
- Clear status indicators for approval states
- **Hover tooltips** for disabled button states

### 5. Type Safety
- Full TypeScript implementation
- Proper interface definitions
- Type-safe API responses

## Benefits of Simplified Approach

### 1. User Experience
- **Simpler workflow** - single action instead of multiple steps
- **Fewer user errors** - no manual progression through steps
- **Consistent interface** - matches Stock Option Plan features
- **Professional appearance** - modern, clean design

### 2. Development
- **Easier maintenance** - single submit logic instead of complex step management
- **Better testing** - simpler component logic
- **Reduced bugs** - fewer user interaction points
- **Consistent patterns** - same approach across similar features

### 3. Business Logic
- **Automated workflow** - backend ensures proper progression
- **Reduced user confusion** - clear single action
- **Better compliance** - backend controls approval sequence
- **Efficiency** - faster user completion

## Testing Considerations

### 1. Unit Tests
- Service layer methods
- Hook functionality
- Component rendering logic
- Submit button states and tooltips

### 2. Integration Tests
- API integration flows
- Error handling scenarios
- State management
- Automated workflow progression

### 3. User Acceptance Tests
- Complete workflow testing
- Edge case handling
- Error recovery
- Tooltip functionality

## Future Enhancements

### 1. Additional Features
- Document generation for approvals
- Email notification integration
- Progress tracking indicators
- Enhanced status visualization

### 2. Performance Optimizations
- Request caching strategies
- Optimistic updates
- Background data synchronization

### 3. User Experience
- Enhanced error messaging
- Progress indicators for long-running workflows
- Status change notifications

## Success Metrics

- [x] API integration working seamlessly
- [x] Real-time status updates functional
- [x] Error handling robust and user-friendly
- [x] Performance maintained with API calls
- [x] All existing functionality preserved
- [x] Code follows existing patterns and standards
- [x] Type safety implemented throughout
- [x] User experience enhanced with proper feedback
- [x] **UI simplified to match Stock Option Plan pattern**
- [x] **Single submit button with automated backend workflow**
- [x] **Button-level loading states only**
- [x] **Tooltip integration for disabled states**

## Bug Fixes

### Issue: Dialog Not Displaying Content
**Problem**: When clicking the "Start" button, the dialog would open but show no content.

**Root Cause**: 
1. **Data Access Issue**: The API client automatically extracts the `data` property from responses, but the hook was returning the full response structure.
2. **Type Mismatch**: Components expected the extracted data object but were receiving the full response.
3. **Conditional Rendering Issue**: The dialog only rendered content when both `step === "initial" && data`, but didn't handle the loading state properly.

**Solution**:
1. **Fixed Hook Interface**: Updated `useAuthorizedShares.hooks.ts` to return `AuthorizedSharesResponse['data']` since the API client already extracts the data
2. **Updated Component Data Access**: Modified `IncreaseSharesDialog.tsx` to pass `data` directly to child components
3. **Fixed Data Display**: Updated `IncreaseSharesInitialState.tsx` to use optional chaining for safe data access
4. **Improved Dialog Rendering**: Updated `IncreaseSharesDialog.tsx` to show loading spinner when data is not yet available

**Files Modified**:
- `src/hooks/authorized-shares/useAuthorizedShares.hooks.ts` - Updated return type to `AuthorizedSharesResponse['data']`
- `src/components/shares/IncreaseSharesDialog.tsx` - Fixed data access to use `data.propertyName` directly
- `src/components/shares/IncreaseSharesInitialState.tsx` - Used optional chaining for safe data access

### Issue: Modal-Wide Loading During Submit
**Problem**: When clicking submit, the entire modal would show a loading spinner instead of just the button.

**Root Cause**: The hook included `boardApprovalMutation.isPending` in the global `isLoading` state.

**Solution**: Updated the hook to exclude board approval loading from global loading state.

**Files Modified**:
- `src/hooks/authorized-shares/useAuthorizedShares.hooks.ts` - Fixed `isLoading` logic

## Maintenance Notes

### 1. API Changes
- Monitor API endpoint changes
- Update service layer accordingly
- Maintain backward compatibility

### 2. Component Updates
- Follow existing patterns for new features
- Maintain type safety
- Update documentation
- **Ensure consistency with Stock Option Plan features**

### 3. Testing
- Regular integration testing
- Monitor error rates
- User feedback collection
- **Test automated workflow progression**

This implementation provides a robust, scalable, and user-friendly solution for managing authorized shares increases with full API integration, real-time state management, and a **simplified single-submit workflow that matches the Stock Option Plan pattern**.
