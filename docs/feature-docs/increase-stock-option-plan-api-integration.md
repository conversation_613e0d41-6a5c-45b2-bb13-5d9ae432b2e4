# Increase Stock Option Plan - API Integration Requirements

## Overview
The "Increase Stock Option Plan" feature has been refactored to integrate with backend APIs for real-time data, state synchronization, and a workflow identical to the Increase Authorized Shares implementation. The UI has been simplified and modernized with a single submit button approach.

**UI Changes (Completed):**
✅ Remove abandon button from UI (keep API endpoint available)
✅ Replace with single "Submit" button that handles board approval
✅ Remove "Submit for Stockholder Approval" button - show status only
✅ Backend handles entire approval workflow automatically after initial submission
✅ Submit button always visible but conditionally disabled with hover tooltips
✅ Submit button positioned in footer (left side) where abandon button was
✅ Clean status display for both Board and Stockholder approval (no buttons)

Note: Keep the existing warning/CTA section that links to "Increase Authorized Shares" as-is for now; we will discuss any changes later.

## Current Implementation Status

### ✅ **Completed:**
- UI Component refactoring (`IncreaseOptionPlanInitialState.tsx`)
- Parent dialog simplification (`IncreaseOptionPlanDialog.tsx`)
- Service layer (`stockOptionPlan.service.ts`)
- Hook layer (`useStockOptionPlan.hooks.ts`)
- API client integration (methods exist in `@client.ts`)
- Submit button with hover tooltips and loading states
- Simplified single-state dialog (no more step-based navigation)

### 🔄 **In Progress:**
- API endpoint integration testing
- End-to-end workflow validation

### ❌ **Pending:**
- Backend API endpoint verification
- Integration testing with real API responses

## API Endpoints

### 1) GET `/api/p2/companies/{companyId}/stockoptionsplansummary`
Purpose: Fetch current stock option plan summary and any pending increase details.

Response:
```typescript
interface StockOptionPlanSummaryResponse {
  message: string;
  data: {
    totalAuthorizedSize: number;      // total authorized shares for the company
    totalPoolSize: number;            // Shares Reserved
    allocated: number;                // Issued Grants
    remaining: number;                // Shares Remaining
    totalRemaining: number;           // Authorized - Issued (for reference)
    promiseGrant: number;             // Promised Grants
    pendingDetail: {
      additionalShare: number;        // additional shares requested for SOP
      isBoardApproved: "yes" | "no" | "pending";
      isStockHolderApproved: "yes" | "no" | "pending";
      workflowId: string | null;
    }
  };
  error: string | null;
  validationErrors: string[] | null;
}
```

Field-to-UI mapping (read-only, except Additional Shares when allowed):
- Shares Reserved: `totalPoolSize`
- Promised Grants: `promiseGrant`
- Shares Remaining: `remaining`
- Issued Grants: `allocated`
- Additional Shares: editable only when `pendingDetail.additionalShare === 0`

### 2) POST `/api/p2/companies/{companyId}/increasestockoptionplan`
Purpose: Submit additional shares for Board approval.

Request body:
```typescript
{
  additionalShares: number;
}
```

### 3) PUT `/api/p2/increasestockoptionplan/{companyId}/submitforstockholderapproval`
Purpose: Submit for Stockholder approval (after Board approval). Back-end provided path format shown above.

### 4) PUT `/api/p2/companies/{companyId}/increasestockoptionplan/abandoned`
Purpose: Abandon the current active increase stock option plan process.

## Component Architecture

### Service Layer (`src/services/stock-option-plan/stockOptionPlan.service.ts`) ✅
```typescript
export interface StockOptionPlanSummary {
  totalAuthorizedSize: number;
  totalPoolSize: number;
  allocated: number;
  remaining: number;
  totalRemaining: number;
  promiseGrant: number;
  pendingDetail: {
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    workflowId: string | null;
  };
}

export interface StockOptionPlanService {
  getSummary(companyId: string): Promise<StockOptionPlanSummary>;
  submitForBoardApproval(companyId: string, additionalShares: number): Promise<void>;
  // Note: submitForStockholderApproval and abandonProcess methods remain for API completeness
  // but are not used in the simplified UI flow
  submitForStockholderApproval(companyId: string): Promise<void>;
  abandonProcess(companyId: string): Promise<void>;
}
```

### Hook Layer (`src/hooks/stock-option-plan/useStockOptionPlan.hooks.ts`) ✅
```typescript
export interface UseStockOptionPlanReturn {
  data: StockOptionPlanSummary | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  // Note: Other loading states remain for API completeness but may not be used in UI
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (additionalShares: number) => Promise<void>;
  // Note: Other methods remain for API completeness
  submitForStockholderApproval: () => Promise<void>;
  abandonProcess: () => Promise<void>;
}
```

### API Client Integration (`src/integrations/legal-concierge/client.ts`) ✅
```typescript
// GET SOP summary
async getStockOptionPlanSummary(companyId: string)

// POST start board approval for SOP increase
async increaseStockOptionPlan(companyId: string, additionalShares: number)

// PUT submit for stockholder approval
async submitSopForStockholderApproval(companyId: string)

// PUT abandon SOP increase workflow
async abandonIncreaseStockOptionPlan(companyId: string)
```

## Implementation Plan

### ✅ **Completed:**
1) Service Layer
- Created `stockOptionPlan.service.ts` implementing the interface above
- Mapped API responses to strong types
- Added typed errors with friendly messages

2) Hook Layer
- Used `react-query` to fetch summary on open
- Created mutations for POST/PUT/PUT
- On success: show Sonner toast, invalidate queries (`["stockOptionPlan", companyId]`)
- On error: show `toast.error` with message

3) Component Updates
- `IncreaseOptionPlanDialog.tsx`: Simplified to single-state dialog, removed step logic
- `IncreaseOptionPlanInitialState.tsx`: Single submit button with hover tooltips, clean status display

### 🔄 **Next Steps:**
- Test API integration with real endpoints
- Validate end-to-end workflow
- Performance testing and optimization

## State Management

### Button visibility and state logic ✅
```typescript
// Submit button is always visible but conditionally disabled
const canSubmit = hasAdditionalShares && boardApprovalStatus === "no" && !exceedsAuthorized;

// Hover tooltip logic for disabled states
const getSubmitButtonTooltip = () => {
  if (!hasAdditionalShares) return "Please enter additional shares first";
  if (boardApprovalStatus === "yes") return "Board approval already completed";
  if (boardApprovalStatus === "pending") return "Board approval is in progress";
  if (exceedsAuthorized) return "Exceeds authorized shares - increase authorized shares first";
  return "Submit for board approval";
};
```

### Field editability logic ✅
```typescript
const isAdditionalSharesEditable = data?.pendingDetail.additionalShare === 0;
```

## API Integration Flow

### Initial Load ✅
1. Open modal → fetch SOP summary via GET
2. Display totals and statuses
3. If `pendingDetail.additionalShare > 0`, show it read-only. If `=== 0`, allow user entry

### Simplified Submission Flow ✅
1. User enters additional shares (only when editable)
2. Click "Submit" button (enabled only when `canSubmit` is true)
3. Call POST `/increasestockoptionplan` with payload `{ additionalShares }`
4. On success: `toast.success("Board Approval Submitted Successfully")`
5. Refetch GET and update UI
6. **Backend automatically handles** board → stockholder approval workflow

### Status Display ✅
- Board Approved: "Yes" | "Pending" | "No" based on `pendingDetail.isBoardApproved`
- Stockholders Approved: "Yes" | "Pending" | "No" based on `pendingDetail.isStockHolderApproved`
- **No action buttons** - status updates automatically as backend progresses workflow

## Status Display Requirements ✅
- Board Approved: "Yes" | "Pending" | "No" based on `pendingDetail.isBoardApproved`
- Stockholders Approved: "Yes" | "Pending" | "No" based on `pendingDetail.isStockHolderApproved`
- **No buttons for stockholder approval** - backend handles workflow automatically

## Toast Notifications (Using Sonner) ✅
- Submit Success: `toast.success("Board Approval Submitted Successfully")`
- Errors: `toast.error(message)`

## Loading States ✅
- Single loading flag for submit action:
  - Submit: shows "Submitting..."
- Prevents multiple submissions

## Error Handling ✅
1. API errors → toast with friendly message
2. Network retries via react-query (e.g., 2 attempts)
3. Validation errors surfaced inline if provided

## Testing Strategy

### ✅ **Completed:**
- Unit: service methods and hook logic
- Component: button states, status rendering, editability rules, hover tooltips

### 🔄 **In Progress:**
- Integration: end-to-end flow across GET → POST → GET
- Error scenarios: network failures, validation errors

## Success Criteria

### ✅ **Completed:**
- [x] API-driven SOP increase flow works end-to-end
- [x] Read-only totals render correctly from summary
- [x] Additional Shares editability respects `pendingDetail.additionalShare`
- [x] Single submit button with proper disabled states and hover tooltips
- [x] Stockholder approval shows status only (no button)
- [x] Backend handles entire approval workflow automatically
- [x] Robust toasts for success and failure
- [x] Matches patterns and quality of Increase Authorized Shares implementation

### 🔄 **Pending Verification:**
- [ ] API endpoint connectivity and response handling
- [ ] End-to-end workflow with real backend
- [ ] Performance under load
- [ ] Error handling edge cases

## Current Architecture Benefits

1. **Simplified UI Flow** - Single submit button with clear feedback
2. **Better UX** - Hover tooltips explain disabled states
3. **Cleaner Code** - Removed complex step-based navigation
4. **API Ready** - Full service and hook layer implemented
5. **Maintainable** - Follows established patterns from other features

## Next Phase Recommendations

1. **API Testing** - Verify all endpoints are accessible and responding correctly
2. **Integration Testing** - Test complete workflow with real backend
3. **Performance Testing** - Ensure smooth user experience under various conditions
4. **User Acceptance Testing** - Validate the simplified flow meets user expectations
