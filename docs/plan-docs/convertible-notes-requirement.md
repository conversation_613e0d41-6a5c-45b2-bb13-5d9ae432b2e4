## Convertible Notes Revamp – Feature Requirements

Author: Frontend | Owners: Product, Legal, Frontend, Backend | Status: Draft

### Goals
- Replace Convertibles sub-tabs with streamlined single Action button with dropdown menu
- Support: Authorize Round, Issue Individual Notes, Increase Authorized Round, Record Authorized Round
- Add Qualified Financing Threshold (QFT) input
- Provide robust tables with clear actions and statuses
- Align with existing document/consent flows

### UX Overview
- **NEW DESIGN**: Single "Action" button positioned in top right of Convertible Notes card
- **NEW DESIGN**: Dropdown menu opens with 4 action options instead of horizontal button toolbar
- **NEW DESIGN**: Table positioned at bottom of card (no left section)
- Authorize Round: inputs minus Round Name, triggers board consent
- Increase/Record: round selection + inputs, board approval/external source
- Issue Individual Notes: modal with round selection table
- Display Aggregate Note Rounds table (no Round Name/Pending Increase columns)

---

## **UPDATED UI DESIGN REQUIREMENTS**

### **New Layout Structure**
The Convertible Notes interface will be redesigned with a **two-section vertical layout**:

#### **Top Section: Action Button**
- **Single "Action" button** positioned in the **top right** of the Convertible Notes card
- **Dropdown menu** opens when clicked, containing the 4 action options:
  1. **Authorize Round** → No Change (existing functionality)
  2. **Issue Individual Notes** → No Change (existing functionality)
  3. **Increase Authorized Round** → No Change (existing functionality)
  4. **Record** → New popup with two radio options

#### **Bottom Section: Aggregate Note Rounds Table**
- **Table positioned at the bottom** of the Convertible Notes card
- **Table remains exactly as it is** (no changes to functionality, filtering, or display)
- **No left section** - the table spans the full width below the action button
- **Filtering controls** remain above the table as currently implemented

### **Visual Design Requirements**
- **Action Button**: 
  - Position: Top right corner of the card
  - Style: Primary button with dropdown arrow
  - Size: Standard button height, auto-width for text
  - Hover: Subtle hover effect with cursor pointer
- **Dropdown Menu**:
  - Width: Auto-width to accommodate longest menu item
  - Position: Dropdown below the button, right-aligned
  - Items: 4 action options with clear labels
  - Hover: Highlight effect on menu items
- **Table Layout**:
  - Position: Full width at bottom of card
  - Spacing: Adequate margin from action button
  - Responsive: Maintains current responsive behavior

### **Interaction Flow**
1. User sees single "Action" button in top right
2. User clicks "Action" button
3. Dropdown menu opens with 4 action options
4. User selects desired action
5. Respective dialog opens (existing functionality)
6. Table remains visible and functional below

---

## Component Architecture

Entry point (replaces old `ConvertibleNoteTab`):

- `components/financing/convertiblenote/ConvertibleNotesFlow.tsx`
  - **UPDATED**: Renders: single Action button with dropdown, dialogs for each workflow, and `RoundsTable` at bottom.
  - **REMOVED**: Four-button toolbar replaced with single dropdown button.
  - Holds `selectedRoundId`, coordinates dialogs (no radio component).

Shared primitives used: `Card`, `Tabs` (if reused for other areas), `Form`, `Input`, `Select`, `Dialog`, `Table`, `Tooltip`, `Calendar`, `Button`, `DropdownMenu`.

Proposed new components:

Deprecated:
- `EntryModeSelector.tsx` (radio) is replaced by a single Action button with dropdown.
- **REMOVED**: Four-button horizontal toolbar.

**NEW COMPONENT**:
- `components/financing/convertiblenote/ActionButton.tsx`
  - **Purpose**: Single Action button with dropdown menu containing 4 action options
  - **Position**: Top right of Convertible Notes card
  - **Dropdown Items**: Authorize Round, Issue Individual Notes, Increase Authorized Round, Record
  - **Integration**: Triggers respective dialogs when actions selected
  - **Styling**: Primary button with dropdown arrow, right-aligned positioning

- `dialogs/AuthorizeRoundDialog.tsx`
  - **Fields**: Total Authorized Amount*, Interest Rate*, Interest Type (Simple|Compound)*, Maturity Date*, Valuation Cap*, Discount*, MFN*, Qualified Financing Threshold*
  - **Validation**: All required, real-time validation, form blocked until valid
  - **States**: Loading (spinner), Success (auto-close + toast), Error (inline messages)
  - **Actions**: Cancel, Submit for Board Approval → POST API → loading → success: close + toast + refresh

- `dialogs/IssueIndividualNotesDialog.tsx` (NEW)
  - **Layout**: 600px dialog with round selection table only
  - **Table**: [Radio] + all Aggregate Note Rounds columns, first row pre-selected, blue highlight, single selection
  - **Data**: Active board-approved rounds only (isActive: true)
  - **Actions**: Cancel, Issue (placeholder)
  - **Purpose**: Round selection interface only

- `dialogs/IncreaseAuthorizedRoundDialog.tsx`
  - **Layout**: 900px dialog with approved rounds table + increase form
  - **Table**: Approved rounds only, [Radio] + columns, first selected, blue highlight
  - **Form**: Single USD input (>0), helper text shows current + new total
  - **API**: POST `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedround`
  - **Behavior**: No mutation until fully approved

- `dialogs/RecordAuthorizedRoundDialog.tsx`
  - Disclaimer: "Use this workflow to record any round authorized outside of the platform."
  - Same inputs as Authorize Round (no Round Name), no board approval
  - Upload: Form of Convertible Note (PDF/DOCX)
  - Creates Round with `source = external`, `boardApproved = true`
  - **NEW**: Popup with two radio button options:
    1. **"Issued Note"** (selected by default) - Same as Issue Individual Notes but with document upload option, no documents sent out
    2. **"Authorized Round"** - Same as existing Record Authorized Round functionality

- `tables/RoundsTable.tsx` (updated)
  - Columns: Date Authorized, Maturity Date, Authorized Amount, Outstanding Principal, Interest Rate, Valuation Cap, Discount, MFN, QFT, Board Approved
  - Removed: Round Name, Pending Increase, Source, Actions
  - Issue Individual Notes moved above table

### Aggregate Note Rounds API

- **Endpoint**: GET `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist`
- **Change**: From `authorizedroundlist` to include active/inactive rounds
- **Filtering**: "Show All", "Approved Only" (isActive=true), "Pending" (isActive=false)
- **Default**: "Approved Only"

- **Updated API Response Structure**:
```json
{
  "message": "Success",
  "data": [
    {
      "id": "08029b64-a43e-41b5-9826-d32a34b4115a",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "qualifiedFinancingThreshold": 100000,
      "totalAuthorizedAmountOfRound": 100000,
      "increaseAuthorizeRoundId": null,
      "interestRate": 10,
      "interestType": "simple-interest",
      "maturityDate": "2025-09-18",
      "valuationCap": 100000,
      "discount": 10,
      "mostFavoredNation": false,
      "boardApprovedDate": null,
      "outstandingPrincipal": 0,
      "isActive": false
    }
  ],
  "error": null,
  "validationErrors": null
}
```


- **Field mapping to table columns**:
  - Date Authorized → `boardApprovedDate`
  - Maturity Date → `maturityDate`
  - Authorized Amount → `totalAuthorizedAmountOfRound`
  - Outstanding Principal → `outstandingPrincipal` (from API data)
  - Interest Rate → `interestRate`
  - Valuation Cap → `valuationCap`
  - Discount → `discount`
  - MFN → `mostFavoredNation`
  - QFT → `qualifiedFinancingThreshold`
  - Board Approved → `isActive` (visual indicator)

**UI**: Radio buttons above table, visual approved/non-approved distinction, responsive design

**Deprecated**:
- `notes/IssueIndividualNotesSection.tsx` → `dialogs/IssueIndividualNotesDialog.tsx`
- `dialogs/BoardConsentDialog.tsx` - board consent preview/signature
- `dialogs/RoundIncreaseConsentDialog.tsx` - round increase consent preview/signature

## Service Contracts

### Service Interfaces

```ts
interface ConvertibleNoteRoundResponse {
  id: string;
  companyId: string;
  qualifiedFinancingThreshold: number;
  totalAuthorizedAmountOfRound: number;
  increaseAuthorizeRoundId: string | null;
  interestRate: number;
  interestType: 'simple-interest' | 'compound-interest';
  maturityDate: string;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null;
  isActive: boolean;
}
```




#### Service Implementation
- Standard APIClient integration with error handling
- Toast notifications for success/error states
- Response validation and type casting
- NEW: Prorata sideletter upload URL support


---

## Hook Contracts




### Hook Interfaces

```ts
interface UseAuthorizedRoundListReturn {
  rounds: ConvertibleNoteRoundResponse[];
  isLoadingRounds: boolean;
  error: string | null;
  refetchRounds: () => void;
  filterType: 'all' | 'approved' | 'notApproved';
  setFilterType: (filter: 'all' | 'approved' | 'notApproved') => void;
  filteredRounds: ConvertibleNoteRoundResponse[];
}
```


---

## Data Model Extensions

Add to `src/types/financing.ts`:
```ts
type RoundSource = "platform" | "external";
type RoundStatus = "draft" | "active" | "closed";
type ApprovalStatus = "not_requested" | "pending" | "approved" | "rejected";
type NoteIssuanceStatus = "saved" | "pending_circulation" | "circulating" | "signed" | "void";

interface ConvertibleNoteRound {
  id: string;
  roundName: string;
  dateAuthorized: Date | null;
  maturityDate: Date | null;
  interestRate: number;
  interestType: InterestType;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  authorizedAmount: number;
  outstandingPrincipal: number;
  qualifiedFinancingThreshold: number;
  boardApproved: boolean;
  approvalStatus: ApprovalStatus;
  pendingIncreaseAmount?: number;
  source: RoundSource;
  status: RoundStatus;
}

interface ConvertibleNoteIssue {
  id: string;
  roundId: string;
  roundName: string;
  principalAmount: number;
  investorName: string;
  investorEmail: string;
  investmentDate: Date | null;
  includeProRataSideLetter: boolean;
  status: NoteIssuanceStatus;
}
```

---

## State Flow

**Authorize**: Create round → request board approval → pending status
**Increase**: Request increase → set pendingIncreaseAmount → approval → persist increase
**Record**: Store external round with source=external, skip approvals
**Issue**: Round selection interface only

---

## Validation

- Currency inputs: numbers only with $ format
- Interest Rate, Discount: 0–100
- QFT: required, ≥ 0
- Increase amount: > 0, no pending increases
- Issue Individual Notes: round selection only, no validation

---

## Accessibility

- Keyboard accessible controls, focus trapping, proper labels, announced errors

---

## Telemetry

- Track flow entries, drop-offs, completion time, issuance rates

---

## API Requirements

### GET Authorized Rounds
**Endpoint**: `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist`
**Response**: `ApiResponse<ConvertibleNoteRoundResponse[]>`

**Field Mapping**:
- Date Authorized → `boardApprovedDate`
- Maturity Date → `maturityDate`
- Authorized Amount → `totalAuthorizedAmountOfRound`
- Outstanding Principal → static placeholder
- Interest Rate → `interestRate`
- Valuation Cap → `valuationCap`
- Discount → `discount`
- MFN → `mostFavoredNation`
- QFT → `qualifiedFinancingThreshold`
- Board Approved → `isActive`

### POST Authorize Round
**Endpoint**: `/api/p2/companies/{companyId}/preseedfinance/authorizedround`
**Request**: `AuthorizeRoundRequest`
**Response**: `ApiResponse<AuthorizeRoundResponse>` (201) or error (400)

**Validation**: All fields required, QFT ≥ 0, amount > 0, interest 0-100, future maturity date
**Business Logic**: Create with `isActive: false`, trigger board consent workflow

### POST API for Increase Authorized Round (NEW Implementation Required)

#### Endpoint
- **POST** `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedround`
- **Purpose**: Request an increase to an existing authorized convertible note round
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### Request Payload
```ts
interface IncreaseAuthorizedRoundRequest {
  authorizedRoundId: string;              // Required, ID of the selected authorized round
  increaseAmount: number;                  // USD, required, > 0
}
```

#### Example Request
```json
{
  "authorizedRoundId": "d30d7e86-4c37-4248-ad76-13d0cae101d6",
  "increaseAmount": 100000
}
```

#### Response Structure
**Success Response (201 Created)**
```ts
interface IncreaseAuthorizedRoundResponse {
  message: string;
  data: {
    id: string;                              // Generated increase request ID
    companyId: string;
    authorizedRoundId: string;
    increaseAmount: number;
    status: "pending_approval";              // Increase is pending board approval
    createdAt: string;                       // ISO timestamp
    boardApprovedDate: string | null;        // null until approved
  };
  error: null;
  validationErrors: null;
}
```

**Error Response (400 Bad Request)**
```ts
interface IncreaseAuthorizedRoundErrorResponse {
  message: string;
  data: null;
  error: string;
  validationErrors: {
    field: string;
    message: string;
  }[] | null;
}
```

#### Validation Rules
- `authorizedRoundId`: Required, must be a valid authorized round ID for the company
- `increaseAmount`: Required, numeric, > 0, reasonable maximum limit (e.g., <= 10x current authorized amount)

#### Business Logic
1. Validate that the specified round exists and is active (`isActive: true`)
2. Check that no previous increase request is pending approval for this round
3. Create increase request with status "pending_approval"
4. Trigger board consent workflow for the increase
5. Return increase request data for immediate UI feedback

### POST API for Issue Individual Notes (NEW Implementation Required)

#### Issue Individual Note Endpoint
- **POST** `/api/p2/companies/{companyId}/preseedfinance/issueindividualnote`  
- The UI first obtains presigned URLs if sideletters are attached, then calls this endpoint with S3 keys included when applicable.
- **Purpose**: Create and issue an individual convertible note for a specific authorized round
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### Request Payload
```ts
interface IssueIndividualNoteRequest {
  authorizedRoundId: string;            // Required, ID of the selected authorized round
  principalAmount: number;              // USD, required, > 0
  investorName: string;                 // Required, max 255 chars
  investorEmail: string;                // Required, valid email format
  investmentDate: string;               // ISO date string, required
  includeProRataSideLetter: boolean;    // Required
  includeSideLetter: boolean;           // NEW: Required
  prorataS3Key?: string;                // S3 key when prorata sideletter is included
  s3Key?: string;                       // NEW: S3 key when side letter is included
}
```

#### Example Request
```json
{
  "authorizedRoundId": "d30d7e86-4c37-4248-ad76-13d0cae101d6",
  "principalAmount": 50000,
  "investorName": "John Smith",
  "investorEmail": "<EMAIL>",
  "investmentDate": "2025-08-15",
  "includeProRataSideLetter": true,
  "includeSideLetter": false,
  "prorataS3Key": "companies/123/others/issue-individual-note/prorata-sideletter-2025-08-15.pdf"
}
```

#### Response Structure
**Success Response (201 Created)**
```ts
interface IssueIndividualNoteResponse {
  message: string;
  data: {
    id: string;                           // Generated note ID
    authorizedRoundId: string;
    companyId: string;
    principalAmount: number;
    investorName: string;
    investorEmail: string;
    investmentDate: string;               // ISO date string
    includeProRataSideLetter: boolean;
    status: "pending_circulation";        // Note is issued and pending circulation
    createdAt: string;                    // ISO timestamp
  };
  error: null;
  validationErrors: null;
}
```


#### Validation Rules
- `authorizedRoundId`: Required, must be a valid authorized round ID for the company
- `principalAmount`: Required, numeric, > 0, <= (authorized amount - outstanding principal of round)
- `investorName`: Required, string, max 255 characters
- `investorEmail`: Required, valid email format
- `investmentDate`: Required, valid ISO date string
- `includeProRataSideLetter`: Required, boolean

#### Business Logic
1. Validate principal amount against remaining authorized capacity for the specified round
2. Create note with status "pending_circulation" (note is immediately issued)
3. Update round's outstanding principal amount
4. Trigger email workflow for investor circulation
5. Return note data for immediate UI feedback

### Future API Endpoints (Outline)

#### Round Management
- POST `/convertible-notes/rounds/:id/approval` – request board approval (authorize)
- POST `/convertible-notes/rounds/:id/increase` – request increase; payload: `amount`
- POST `/convertible-notes/rounds/:id/increase/approve` – finalize increase
- GET `/convertible-notes/rounds/:id` – round detail
- POST `/convertible-notes/rounds/:id/upload` – upload document for recorded rounds

#### Notes Management
- POST `/api/p2/companies/{companyId}/preseedfinance/issueindividualnote` – create and issue individual note
- PATCH `/convertible-notes/notes/:noteId` – update draft (future)
- DELETE `/convertible-notes/notes/:noteId` – delete note (future)
- GET `/convertible-notes/rounds/:id/notes` – list notes by round (future)

#### Document/E-Sign
- POST `/documents/consents` – create consent and send for signature (board)
- POST `/documents/circulate` – circulate investor docs (note + side letter)
- GET `/documents/status/:id` – poll status

#### File Upload API (NEW - Pre-signed URL)
- **POST** `/api/p2/companies/{companyId}/documents/authorized-round/presigned-url`
- **Purpose**: Get pre-signed URL for file upload to S3
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### File Upload Request Payload
```ts
interface FileUploadRequest {
  key: string;                    // Required, filename (e.g., "name.doc")
  contentType: string;            // Required, MIME type of file
}
```

#### Example Request
```json
{
  "key": "name.doc",
  "contentType": "application/msword"
}
```

#### File Upload Response Structure
**Success Response (200 OK)**
```ts
interface FileUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: null;
  validationErrors: null;
}
```

#### File Upload Constraints
- **File Types**: PDF (.pdf) or DOCX (.docx) files only
- **File Size**: Maximum 5 MB
- **Content Types Allowed**:
  - `application/pdf` for PDF files
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document` for DOCX files
- **Key Format**: `companies/{companyId}/others/authorized-round/{filename}-{timestamp}.{extension}`

#### File Upload Business Logic
1. Validate file type and size constraints
2. Generate unique S3 key with timestamp to prevent conflicts
3. Create pre-signed URL with 15-minute expiration
4. Return upload URL and S3 key for immediate file upload
5. File upload to S3 is handled separately by the client

#### **NEW**: Prorata Sideletter Pre-signed URL API
- **POST** `/api/p2/companies/{companyId}/documents/issue-individual-note/prorata-presigned-url`
- **Purpose**: Get pre-signed URL for prorata sideletter document upload to S3
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### Prorata Sideletter File Upload Request Payload
```ts
interface ProrataSideletterUploadRequest {
  key: string;                    // Required, filename (e.g., "prorata-sideletter.doc")
  contentType: string;            // Required, MIME type of file
}
```

#### Example Request
```json
{
  "key": "prorata-sideletter.doc",
  "contentType": "application/msword"
}
```

#### Prorata Sideletter File Upload Response Structure
**Success Response (200 OK)**
```ts
interface ProrataSideletterUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: null;
  validationErrors: null;
}
```

#### Prorata Sideletter File Upload Constraints
- **File Types**: PDF (.pdf) or DOC (.doc) files only
- **File Size**: Maximum 5 MB
- **Content Types Allowed**:
  - `application/pdf` for PDF files
  - `application/msword` for DOC files
- **Key Format**: `companies/{companyId}/others/issue-individual-note/{filename}-{timestamp}.{extension}`

#### Prorata Sideletter File Upload Business Logic
1. Validate file type and size constraints
2. Generate unique S3 key with timestamp to prevent conflicts
3. Create pre-signed URL with 15-minute expiration
4. Return upload URL and S3 key for immediate file upload
5. File upload to S3 is handled separately by the client
6. S3 key is stored and included in final note issuance API call as `prorataS3Key`

#### **NEW**: Side Letter Pre-signed URL API
- **POST** `/api/p2/companies/{companyId}/documents/issue-individual-note/side-letter-presigned-url`
- **Purpose**: Get pre-signed URL for side letter document upload to S3
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### Side Letter File Upload Request Payload
```ts
interface SideLetterUploadRequest {
  key: string;                    // Required, filename (e.g., "side-letter.doc")
  contentType: string;            // Required, MIME type of file
}
```

#### Example Request
```json
{
  "key": "side-letter.doc",
  "contentType": "application/msword"
}
```

#### Side Letter File Upload Response Structure
**Success Response (200 OK)**
```ts
interface SideLetterUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: string | null;
  validationErrors: unknown | null;
}
```

#### Side Letter File Upload Constraints
- **File Types**: PDF (.pdf), DOC (.doc), or DOCX (.docx) files only
- **File Size**: Maximum 5 MB
- **Content Types Allowed**:
  - `application/pdf` for PDF files
  - `application/msword` for DOC files
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document` for DOCX files
- **Key Format**: `companies/{companyId}/others/issue-individual-note/{filename}-{timestamp}.{extension}`

#### Side Letter File Upload Business Logic
1. Validate file type and size constraints
2. Generate unique S3 key with timestamp to prevent conflicts
3. Create pre-signed URL with 15-minute expiration
4. Return upload URL and S3 key for immediate file upload
5. File upload to S3 is handled separately by the client
6. S3 key is stored and included in final note issuance API call as `s3Key`

**Note**: Response shapes should return stable IDs and statuses to support optimistic UI.

#### Record Authorized Round API (NEW)
- **POST** `/api/p2/companies/{companyId}/preseedfinance/recordauthorizedround`
- **Purpose**: Record convertible note rounds authorized outside the platform
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### Record Authorized Round Request Payload
```ts
interface RecordAuthorizedRoundRequest {
  qualifiedFinancingThreshold: number;        // USD, required, >= 0
  totalAuthorizedAmountOfRound: number;       // USD, required, > 0  
  interestRate: number;                       // Percentage, required, 0-100
  interestType: "simple-interest" | "compound-interest"; // Required
  maturityDate: string;                       // ISO date string, required
  valuationCap: number;                       // USD, required, > 0
  discount: number;                           // Percentage, required, 0-100
  mostFavoredNation: boolean;                 // Required
  boardApprovedDate: string;                  // ISO date string, required
  s3Key: string;                              // S3 key from file upload, required
}
```

#### Example Request
```json
{
  "qualifiedFinancingThreshold": 0,
  "totalAuthorizedAmountOfRound": 0,
  "interestRate": 0,
  "interestType": "simple-interest",
  "maturityDate": "2025-08-10",
  "valuationCap": 0,
  "discount": 0,
  "mostFavoredNation": true,
  "boardApprovedDate": "2025-08-10",
  "s3Key": "string"
}
```

#### Record Authorized Round Response Structure
**Success Response (200 OK)**
```ts
interface RecordAuthorizedRoundResponse {
  message: string;
  data: {
    id: string;                               // Generated round ID
    companyId: string;
    qualifiedFinancingThreshold: number;
    totalAuthorizedAmountOfRound: number;
    interestRate: number;
    interestType: "simple-interest" | "compound-interest";
    maturityDate: string;                     // ISO date string
    valuationCap: number;
    discount: number;
    mostFavoredNation: boolean;
    boardApprovedDate: string;                // ISO date string
    isActive: boolean;                        // true since already board approved
    createdAt: string;                        // ISO timestamp
  };
  error: null;
  validationErrors: null;
}
```

#### Record Authorized Round Business Logic
1. Validate all required fields and constraints
2. Verify file upload exists (s3Key validation)
3. Create round record with external source designation
4. Mark as board approved since external authorization
5. Return created round with stable ID and status

---

## File Plan (frontend)

### Components
- `src/components/financing/ConvertibleNoteTab.tsx` → remove usage in PreSeedFinancingCard; replace with:
  - `src/components/financing/convertiblenote/ConvertibleNotesFlow.tsx`
  - **NEW**: `src/components/financing/convertiblenote/ActionButton.tsx` (replaces four-button toolbar)
  - `src/components/financing/convertiblenote/dialogs/AuthorizeRoundDialog.tsx`
  - `src/components/financing/convertiblenote/dialogs/IssueIndividualNotesDialog.tsx` (NEW - Round Selection Only)
  - `src/components/financing/convertiblenote/dialogs/IncreaseAuthorizedRoundDialog.tsx`
  - `src/components/financing/convertiblenote/dialogs/RecordAuthorizedRoundDialog.tsx`
  - `src/components/financing/convertiblenote/tables/RoundsTable.tsx`
  - `src/components/financing/convertiblenote/dialogs/BoardConsentDialog.tsx`
  - `src/components/financing/convertiblenote/dialogs/RoundIncreaseConsentDialog.tsx`

### Deprecated Components
- `src/components/financing/convertiblenote/notes/IssueIndividualNotesSection.tsx` - Remove from main flow
- **REMOVED**: Four-button horizontal toolbar (replaced by ActionButton dropdown)

### Hooks & Services Implementation

#### Hooks (GET API - UPDATED)
- `src/hooks/financing/useAuthorizedRoundList.hooks.ts` (UPDATED)
  - **Purpose**: Fetch and manage authorized rounds data for the Aggregate Note Rounds table (includes both approved and non-approved)
  - **Implementation**: React Query hook with `['authorizedRoundList', companyId]` query key (UPDATED)
  - **Data**: Returns rounds array, loading state, error state, refetch function, and filtering capabilities
  - **NEW**: **Filtering**: Client-side filtering between approved (`isActive: true`) and non-approved (`isActive: false`) rounds
  - **Usage**: Used by `RoundsTable` component to populate data with filter controls
  - **Query**: GET `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist` (UPDATED)

#### Hooks (POST API - NEW Implementation Required)
- `src/hooks/financing/useAuthorizeRound.hooks.ts`
  - **Purpose**: Create new convertible note rounds with board approval workflow
  - **Implementation**: React Query mutation hook with automatic cache invalidation
  - **States**: Loading state for submit button, error state for form display
  - **Integration**: Used by `AuthorizeRoundDialog` component
  - **Mutation**: POST `/api/p2/companies/{companyId}/preseedfinance/authorizedround`

- `src/hooks/financing/useIssueIndividualNotes.hooks.ts` (NOT REQUIRED - Future Implementation)
  - **Purpose**: Create and issue individual convertible notes for selected rounds
  - **Implementation**: React Query mutation hooks with automatic cache invalidation
  - **States**: Separate loading states for create and issue operations
  - **Integration**: Used by `IssueIndividualNotesDialog` component (future)
  - **Mutations**: 
    - Create: POST `/api/p2/companies/{companyId}/rounds/{roundId}/notes`
    - Issue: POST `/api/p2/companies/{companyId}/notes/{noteId}/issue`

- `src/services/financing/authorizedRoundList.service.ts` (UPDATED)
  - **Purpose**: API service for fetching authorized rounds list (includes both approved and non-approved)
  - **Implementation**: Static class with `getAuthorizedRounds` method
  - **Endpoint**: GET `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist` (UPDATED)
  - **Error Handling**: Unified error messaging and response validation
  - **Response Mapping**: Transforms API response to `ConvertibleNoteRoundResponse[]`
  - **Dependencies**: Uses `APIClient` from `@/integrations/legal-concierge/client`

- `src/hooks/financing/useNoteIssuance.hooks.ts`
  - **Purpose**: Manage individual note operations (future implementation)
  - **Implementation**: React Query hook for notes CRUD operations
  - **Query Keys**: `['convertibleNotes', companyId, roundId, 'notes']`

### Types
- Extend `src/types/financing.ts` with:
  - `ConvertibleNoteRoundResponse` interface (matches API response structure)
  - `ApiResponse<T>` generic interface for API responses
  - Existing types: `RoundSource`, `RoundStatus`, `ApprovalStatus`, `ConvertibleNoteRound`, `ConvertibleNoteIssue`

### API Client Updates (NEW Implementation Required)

#### APIClient Method Addition
- **File**: `src/integrations/legal-concierge/client.ts`
- **Method**: `createAuthorizeRound(companyId: string, payload: AuthorizeRoundRequest)`
- **Implementation**:
```ts
async createAuthorizeRound(companyId: string, payload: AuthorizeRoundRequest) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/preseedfinance/authorizedround`, payload);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

- **Method**: `increaseAuthorizedRound(companyId: string, payload: IncreaseAuthorizedRoundRequest)`
- **Implementation**:
```ts
async increaseAuthorizedRound(companyId: string, payload: IncreaseAuthorizedRoundRequest) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/preseedfinance/increaseauthorizedround`, payload);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

- **Method**: `getProrataSideletterUploadUrl(companyId: string, payload: ProrataSideletterUploadRequest)` (NEW)
- **Implementation**:
```ts
async getProrataSideletterUploadUrl(companyId: string, payload: ProrataSideletterUploadRequest) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/documents/issue-individual-note/prorata-presigned-url`, payload);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

### Integration Points
- **Dashboard**: `PreSeedFinancingCard.tsx` → `ConvertibleNotesFlow.tsx`
- **Data Flow**: `ConvertibleNotesFlow.tsx` → `useAuthorizeRound` hook → `AuthorizeRoundService` → API
- **Dialog Integration**: `AuthorizeRoundDialog.tsx` uses `useAuthorizeRound` for form submission
- **Table Rendering**: `RoundsTable.tsx` receives rounds data from `useAuthorizedRoundList` hook (UPDATED)
- **API Client**: Uses existing `APIClient` from legal-concierge integration with new POST method

### UI/UX Requirements Summary

#### Dialog Standards
- **Form Validation**: Real-time validation with inline error messages
- **Loading States**: Spinner on submit, disabled inputs during API calls
- **Success Flow**: Auto-close dialog, show toast, refresh table
- **Error Flow**: Keep dialog open, display error, re-enable form
- **Accessibility**: Full keyboard navigation, ARIA labels, focus management

#### Authorize Round Dialog
- **Size**: 600px max width, responsive design
- **Layout**: Two-column grid for form fields
- **Inputs**: Currency formatting, percentage inputs, date picker
- **Submit**: "Submit for Board Approval" with loading states

#### Increase Authorized Round Dialog  
- **Size**: 900px max width with horizontal scrolling table
- **Layout**: Round selection table + increase amount form
- **Selection**: Radio buttons, first row selected by default
- **Validation**: Amount > 0, reasonable limits, helper text showing totals

---

## Key Requirements Summary

### General Flow
Single Action button with dropdown menu, table positioned at bottom, API integration for all workflows.

### Core Functionality
Core dialogs and API integration completed.


### Issue Individual Notes Dialog (NEW Implementation Required)
- ⭕ Dialog Layout:
  - Medium-sized dialog (600px max width) containing round selection table and note details form
  - **IMPORTANT**: Table shows ONLY approved rounds (isActive: true) by default
  - **NO FILTERING**: Remove any filter controls - table displays approved rounds only
- ⭕ Round Selection Table:
  - Shows only approved rounds (filtered by isActive: true)
  - Same columns as Aggregate Note Rounds table with radio buttons in first column
  - Columns: [Radio] Date Authorized, Maturity Date, Authorized Amount, Outstanding Principal, Interest Rate, Valuation Cap, Discount, MFN, QFT
  - First approved round selected by default
  - Selected row highlighted with blue background color
  - **Horizontal scrolling**: Table scrolls horizontally when columns don't fit screen width
  - **Responsive**: Maintains table readability on all screen sizes
- ⭕ Note Issuance Form (Below Table):
  - **Form Fields**:
    - Principal Amount (USD, required, > 0, <= remaining authorized amount of selected round)
    - Investor Name (required, investor full name)
    - Investor Email (required, valid email format)
    - Date of Investment (required, date picker, can be past/present/future)
    - **NEW**: Include Prorata Sideletter checkbox
    - **NEW**: Include Side Letter checkbox
    - **NEW**: Document Upload fields (shown when respective checkboxes are checked)
  - **Form Layout**: Horizontal grid layout (4 columns on large screens, responsive)
  - **Validation**: Real-time validation with error messages
  - **Principal Amount Validation**:
    - Must not exceed (Authorized Amount - Outstanding Principal) of selected round
    - Show helper text: "Available: $X,XXX of $Y,YYY authorized"
  - **Form State Management**: Form data updates when different round is selected
- ⭕ **NEW**: Prorata Sideletter Functionality:
  - **Checkbox**: "Include prorata sideletter" with clear labeling
  - **Document Upload**: File input field (PDF/DOC/DOCX only, max 5MB)
  - **File Validation**: Real-time file type and size validation
  - **Upload Flow**: 
    1. User selects file
    2. Call pre-signed URL API to get upload URL and S3 key
    3. Upload file to S3 using returned URL
    4. Store S3 key for final API submission
  - **Error Handling**: Display upload errors and validation failures
- ⭕ **NEW**: Side Letter Functionality:
  - **Checkbox**: "Include side letter" with clear labeling (placed after prorata checkbox)
  - **Document Upload**: File input field (PDF/DOC/DOCX only, max 5MB)
  - **File Validation**: Real-time file type and size validation
  - **Upload Flow**: 
    1. User selects file
    2. Call pre-signed URL API to get upload URL and S3 key
    3. Upload file to S3 using returned URL
    4. Store S3 key for final API submission
  - **Error Handling**: Display upload errors and validation failures
  - **Independent State**: Separate file state management from prorata sideletter
- ⭕ **NEW**: Optional Document Upload:
  - **Existing**: "Document Upload (Optional)" section remains unchanged
  - **Purpose**: Internal record keeping only, not sent to investors
- ⭕ Dialog Actions (Bottom-Right):
  - Cancel: Close dialog without saving
  - Issue: Create and issue note with circulation to investor email
  - **Button Layout**: Horizontal alignment at bottom-right of dialog
- ⭕ UI States:
  - Default: Table displays approved rounds with first row pre-selected
  - Selection: Radio button selection updates visual highlighting and form data
  - Loading: Issue button shows loading spinner during API call
  - Success: Dialog closes + success toast + refresh rounds table
  - Error: Show error message, keep dialog open for retry
- ⭕ API Integration:
  - Uses single endpoint: POST `/api/p2/companies/{companyId}/preseedfinance/issueindividualnote`
  - **UPDATED**: Payload includes `authorizedRoundId` from selected table row AND both optional S3 keys:
    ```json
    {
      "authorizedRoundId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "principalAmount": 0,
      "name": "string",
      "email": "string", 
      "dateOfInvestment": "2025-08-21",
      "prorataS3Key": "string",  // optional
      "s3Key": "string"          // optional (new)
    }
    ```
  - **NEW**: Pre-signed URL APIs:
    - Prorata sideletter: POST `/api/p2/companies/{companyId}/documents/issue-individual-note/prorata-presigned-url`
    - Side letter: POST `/api/p2/companies/{companyId}/documents/issue-individual-note/side-letter-presigned-url`
  - Final issuance always calls `/preseedfinance/issueindividualnote`
  - Automatic cache invalidation to refresh rounds data

### RecordPopupDialog - Issued Note API Requirements

#### **Document Upload APIs**
1. **Prorata Side Letter**: POST `/api/p2/companies/{companyId}/documents/issue-individual-note/prorata-presigned-url`
2. **Side Letter**: POST `/api/p2/companies/{companyId}/documents/issue-individual-note/side-letter-presigned-url`
3. **Optional Document**: POST `/api/p2/companies/{companyId}/documents/record-issue-individual-note/optional-presigned-url`

#### **Final API Call**
- **Endpoint**: POST `/api/p2/companies/{companyId}/preseedfinance/recordissueindividualnote`
- **Payload Structure**:
```json
{
  "authorizedRoundId": "string",
  "principalAmount": "number",
  "name": "string",
  "email": "string",
  "dateOfInvestment": "string",
  "approvedDate": "string",
  "prorataS3Key": "string (optional)",
  "s3Key": "string (optional)",
  "optionalS3Key": "string (optional)"
}
```

#### **Upload Flow**
1. User checks respective checkboxes for prorata and/or side letter
2. File upload sections appear with drag & drop interface
3. User selects PDF/DOCX files (max 5MB)
4. System calls respective presigned URL APIs
5. Files upload directly to S3
6. Final API call includes all collected S3 keys

### Other Dialogs (Existing)
- Increase Authorized Round dialog allows choosing a round, entering an amount, and submits for approval (no Pending Increase column in table).
- Record Authorized Round dialog collects same fields (without Round Name), stores upload, and creates a round from `source = external`.

### Increase Authorized Round Dialog (NEW Implementation)
- ⭕ Dialog Layout:
  - Medium-sized dialog (900px max width) containing round selection table and form
  - **IMPORTANT**: Table shows ONLY approved rounds (isActive: true) by default
  - **NO FILTERING**: Remove any filter controls - table displays approved rounds only
- ⭕ Round Selection Table:
  - Shows only approved rounds (filtered by isActive: true)
  - Same columns as Aggregate Note Rounds table with radio buttons in first column
  - Columns: [Radio] Date Authorized, Maturity Date, Authorized Amount, Outstanding Principal, Interest Rate, Valuation Cap, Discount, MFN, QFT
  - First approved round selected by default
  - Selected row highlighted with blue background color
  - **Horizontal scrolling**: Table scrolls horizontally when columns don't fit screen width
  - **Responsive**: Maintains table readability on all screen sizes
- ⭕ Increase Amount Form (Below Table):
  - **Form Fields**:
    - Increase Amount (USD, required, > 0, reasonable maximum limits)
    - Current Amount Display (read-only, shows selected round's current authorized amount)
    - New Total Display (read-only, shows calculated total after increase)
  - **Form Layout**: Single-column form below the table
  - **Validation**: Real-time validation with error messages
  - **Increase Amount Validation**:
    - Must be > 0
    - Cannot exceed reasonable limits (e.g., <= 10x current amount)
    - Show helper text: "Current: $X,XXX → New Total: $Y,YYY"
  - **Form State Management**: Form data updates when different round is selected
- ⭕ Dialog Actions (Bottom-Right):
  - Cancel: Close dialog without saving
  - Submit for Board Approval: Submit increase request for board approval
  - **Button Layout**: Horizontal alignment at bottom-right of dialog
- ⭕ UI States:
  - Default: Table displays approved rounds with first row pre-selected
  - Selection: Radio button selection updates visual highlighting and form data
  - Loading: Submit button shows loading spinner during API call
  - Success: Dialog closes + success toast + refresh rounds table
  - Error: Show error message, keep dialog open for retry
- ⭕ API Integration:
  - Uses single endpoint: POST `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedround`
  - Payload includes `authorizedRoundId` from selected table row and `increaseAmount`
  - Automatic cache invalidation to refresh rounds data
- ⭕ Responsive Design:
  - Dialog scales to 900px max width
  - Table horizontal scrolling ensures all columns remain accessible
  - Form layout adapts to screen size

## Aggregate Note Rounds Filtering
Client-side filtering with approval status options.

## Implementation Summary

### Implementation Status
Core components completed with remaining file upload integrations.

---

## **NEW FEATURE: Side Letter Upload for Issue Individual Notes**

### **Feature Overview**
Add support for uploading side letter documents when issuing individual convertible notes. This feature allows users to include both prorata sideletter and side letter documents as optional attachments to individual note issuances.

### **Business Value**
Complete documentation support with flexible workflows and compliance.

### **Feature Scope**
- **Location**: Issue Individual Notes Dialog
- **Trigger**: Checkbox selection for "Include side letter"
- **Data Source**: File upload with S3 integration
- **Integration**: Works alongside existing prorata sideletter functionality

### **Technical Requirements**

#### **UI/UX Requirements**
- **Checkbox Placement**: "Include side letter" checkbox placed after "Include prorata sideletter" checkbox
- **File Upload Section**: Conditional display when checkbox is checked
- **File Validation**: PDF/DOC/DOCX files only, max 5MB
- **Independent State**: Separate file state management from prorata sideletter
- **Visual Consistency**: Same styling and behavior as prorata upload section

#### **API Integration**
- **Presigned URL Endpoint**: POST `/api/p2/companies/{companyId}/documents/issue-individual-note/side-letter-presigned-url`
- **Final API Payload**: Updated `issueIndividualConvertibleNote` endpoint to include `s3Key` field
- **File Upload Flow**: Same as prorata sideletter with independent S3 key storage

#### **State Management**
- **Form Fields**: Add `includeSideLetter: boolean` and `sideLetterSelectedFile: File | null`
- **Validation**: File required when checkbox is checked
- **Reset Logic**: Proper cleanup when unchecking checkbox or changing rounds

### **Implementation Requirements**

#### **Files to Update**
1. **Component**: `src/components/financing/convertiblenote/dialogs/IssueIndividualNotesDialog.tsx`
2. **Types**: `src/types/financing.ts` - Add side letter interfaces
3. **Service**: `src/services/financing/issueIndividualNote.service.ts` - Add side letter upload method
4. **API Client**: `src/integrations/legal-concierge/client.ts` - Add side letter presigned URL method
5. **Hooks**: `src/hooks/financing/useIssueIndividualNote.hooks.ts` - Add side letter upload hook

#### **New Type Interfaces**
```typescript
interface SideLetterUploadRequest {
  key: string;
  contentType: string;
}

interface SideLetterUploadResponse {
  message: string;
  data: {
    uploadUrl: string;
    key: string;
  };
  error: string | null;
  validationErrors: unknown | null;
}
```

#### **Updated Payload Structure**
```typescript
interface IssueIndividualNoteRequest {
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  includeProRataSideLetter: boolean;
  includeSideLetter: boolean;           // NEW
  prorataS3Key?: string;
  s3Key?: string;                       // NEW
}
```

### **User Experience Flow**
1. **Checkbox Selection**: User checks "Include side letter" checkbox
2. **File Upload**: File upload section appears with drag & drop interface
3. **File Selection**: User selects PDF/DOC/DOCX file (max 5MB)
4. **Validation**: Real-time file validation with error feedback
5. **Upload Process**: File uploads to S3 via presigned URL
6. **Note Issuance**: Side letter S3 key included in final API payload
7. **Success Feedback**: Dialog closes with success toast and table refresh

### **Error Handling**
- **File Type Error**: "Please select a PDF, DOC, or DOCX file"
- **File Size Error**: "File size must be less than 5MB"
- **Upload Error**: "Failed to upload side letter document"
- **Missing File Error**: "Please select a file for the side letter"

---

## **NEW FEATURE: View Individual Notes (Eye Icon)**

### **Feature Overview**
Add an eye icon to each row in the **Aggregate Note Rounds** table that allows users to view all individual notes issued within that specific round. This feature provides transparency into the detailed breakdown of each authorized round.

### **Business Value**
Transparency and audit trail for individual note tracking.

### **Feature Scope**
- **Location**: Only on the Aggregate Note Rounds table (main view)
- **Exclusion**: NOT available on Issue Individual Notes or Increase Authorized Round modals
- **Trigger**: Eye icon click on any table row
- **Data Source**: API call to fetch individual notes for the selected round

---

## **Technical Requirements**

### **API Integration**
- **Endpoint**: `GET /api/p2/companies/{companyId}/preseedfinance/issueindividualnotelist/{authorizedRoundId}`
- **Response**: Array of IndividualNote objects

#### **Updated Response Structure**
```json
{
  "message": "Success",
  "data": [
    {
      "id": "03381333-d8aa-43ff-9cf3-14420d66d5fe",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "authorizedRoundId": "5210f828-9bc6-41b5-bcdc-f156e583cea3",
      "interestType": "simple-interest",
      "principalAmount": 25000,
      "accruedInterest": 15.07,
      "interestRate": 22,
      "name": "Sujan",
      "email": "<EMAIL>",
      "dateOfInvestment": "2025-09-20",
      "approvedDate": "2025-09-20",
      "isActive": true
    }
  ],
  "error": null,
  "validationErrors": null
}
```

### **Updated Data Model**
```typescript
interface IndividualNote {
  id: string;
  companyId: string;
  authorizedRoundId: string;
  interestType: "simple-interest" | "compound-interest";
  principalAmount: number;
  accruedInterest: number;           // NEW: Accrued interest amount
  interestRate: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  approvedDate: string | null;
  isActive: boolean;
}
```

---

## **UI/UX Requirements**

### **Eye Icon Integration**
- **Position**: Rightmost column "Actions" in Aggregate Note Rounds table
- **Icon**: Eye icon with hover states and accessibility support
- **Behavior**: Opens modal with individual notes for selected round

### **Updated UI Design Requirements**
- **Modal**: Large (1000px max width) with responsive table
- **Updated Columns**: 
  - Investor Name
  - Email
  - Principal Amount
  - Date of Investment
  - **NEW**: Accrued Interest (replaces Approval Status)
  - Status (Active/Inactive)
- **Formatting**: Currency with separators, formatted dates, color-coded status badges
- **States**: Loading skeleton, empty state message, error with retry

### **Status Display Changes**
- **REMOVED**: Approval Status column (no longer displayed)
- **ADDED**: Accrued Interest column showing `accruedInterest` value from API
- **UPDATED**: Status column shows Active/Inactive based on `isActive` field

---

## **User Experience Requirements**

### **User Experience**
- **Interaction**: Click eye icon → Modal opens → Data loads → View notes
- **Performance**: Immediate modal open with async data loading and caching
- **Accessibility**: Full keyboard navigation and screen reader support

---

## **Implementation Requirements**

### **New Files to Create**
1. **Service**: `src/services/financing/individualNotesList.service.ts`
2. **Hook**: `src/hooks/financing/useIndividualNotesList.hooks.ts`
3. **Component**: `src/components/financing/convertiblenote/dialogs/ViewIndividualNotesDialog.tsx`
4. **Types**: Add to `src/types/financing.ts`

### **Files to Update**
1. **API Client**: Add `getIndividualNotesList` method to `src/integrations/legal-concierge/client.ts`
2. **Rounds Table**: Add eye icon column to `src/components/financing/convertiblenote/tables/RoundsTable.tsx`
3. **Main Flow**: Integrate dialog in `src/components/financing/convertiblenote/ConvertibleNotesFlow.tsx`

### **Service Layer Requirements**
- **Method**: `getIndividualNotesList(companyId: string, authorizedRoundId: string)`
- **Error Handling**: Proper error propagation and user-friendly messages
- **Response Parsing**: Handle nested API response structure
- **Type Safety**: Strong typing for request/response

### **Hook Layer Requirements**
- **Query Key**: `['individualNotesList', companyId, authorizedRoundId]`
- **Caching**: Cache individual notes per round for better performance
- **Loading States**: Provide loading, error, and success states
- **Refetch**: Ability to manually refresh data

### **Component Requirements**
- **Props**: `open`, `onOpenChange`, `roundData`, `companyId`
- **State Management**: Handle modal open/close, data loading, and error states
- **Table Rendering**: Responsive table with proper column alignment
- **Event Handling**: Close modal, handle errors, and manage focus

---

## **Technical Implementation Details**

### **Key Components Required**
- `IndividualNotesListService` with proper error handling
- `useIndividualNotesList` React Query hook with caching
- `ViewIndividualNotesDialog` component with responsive table
- Eye icon integration in RoundsTable with proper accessibility

---

## **View Individual Notes Feature**

### **Implementation Requirements**
- Add eye icon to Aggregate Note Rounds table
- Create `ViewIndividualNotesDialog` with responsive table
- Implement `useIndividualNotesList` hook with React Query caching
- Add proper loading, error, and empty states
- Ensure full accessibility support

---


---

## **NEW FEATURE: Issue Individual Notes Form API Integration**

### **Overview**
Implement API integration for the Issue Individual Notes form within the RecordPopupDialog, enabling users to issue individual convertible notes with document upload capabilities and proper loading states.

### **API Endpoints Required**

#### **1. Document Upload API**
Presigned S3 URL endpoint for convertible note document uploads.

Standard presigned URL request/response structure for S3 file uploads.

#### **2. Record Issue Individual Note API**
- **Endpoint**: `POST /api/p2/companies/{companyId}/preseedfinance/recordissueindividualnote`
- **Purpose**: Record the issuance of an individual convertible note with document references
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### **Request Payload**
```json
{
  "authorizedRoundId": "string",      // UUID of the selected authorized convertible note round
  "principalAmount": 0,               // Principal amount of the note
  "name": "string",                   // Investor name
  "email": "string",                  // Investor email
  "dateOfInvestment": "2025-08-17",  // Investment date (ISO format)
  "approvedDate": "2025-08-17",      // Approval date (ISO format)
  "prorataS3Key": "string",          // S3 key for prorata sideletter (optional)
  "s3Key": "string"                  // S3 key for main convertible note document
}
```

### **Implementation Requirements**

#### **File Upload Flow**
1. **File Selection**: User selects PDF/DOCX file (max 5MB)
2. **Presigned URL Request**: Call presigned URL API with filename and content type
3. **S3 Upload**: Upload file directly to S3 using returned URL
4. **S3 Key Storage**: Store returned S3 key for final API submission
5. **Note Issuance**: Include S3 key in final convertible note issuance payload

#### **File Validation Rules**
- **File Types**: PDF (.pdf) and DOCX (.docx) files only
- **File Size**: Maximum 5MB
- **Content Types**: `application/pdf`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- **Real-time Validation**: Immediate feedback on file selection
- **Client-side Validation**: File type and size validation before upload

#### **Form Integration**
- **Dynamic File Upload**: File upload section appears in Issue Individual Notes form
- **Required Field**: Document upload becomes required when issuing convertible notes
- **File Preview**: Show selected file name, size, and remove functionality
- **Upload Progress**: Display upload status and progress indication
- **Error Handling**: Clear error messages for upload failures

#### **API Service Layer**
- **New Service Method**: `getIssueIndividualNoteUploadUrl()` in document service
- **Enhanced Note Service**: Update `recordIssueIndividualNote()` method
- **Error Handling**: Proper error handling for both upload and issuance APIs
- **Response Validation**: Validate API responses and handle errors gracefully

### **Technical Implementation Details**

#### **Technical Contracts**
Standard service and hook interfaces for upload and note recording operations.

#### **Component Updates Required**
- **RecordPopupDialog.tsx**: Integrate new API calls for Issue Individual Notes form
- **File Upload Section**: Implement S3 upload flow with progress indication
- **Form Submission**: Update form submission to include document upload
- **Error Handling**: Add proper error handling for upload and issuance failures

### **Business Logic**
- **Document Storage**: Documents stored in S3 for internal record keeping
- **Note Issuance**: Individual convertible note recorded with document references
- **Validation**: All form fields required, document upload mandatory
- **Status Tracking**: Note issuance status tracked and displayed
- **Audit Trail**: Complete audit trail of issued notes with documents

### **User Experience Features**

#### **Complete Workflow**
1. **Note Selection**: User selects approved convertible note round from table
2. **Note Details**: User fills out principal amount, name, email, date
3. **Document Upload**: Optional PDF/DOCX file upload (max 5MB)
4. **File Validation**: Real-time validation with error feedback
5. **Upload Process**: File uploads to S3 via presigned URL
6. **Note Issuance**: Complete convertible note recording with document references
7. **Success Feedback**: Dialog closes with success toast and form reset

#### **Loading and Error States**
- **Upload URL Loading**: Button shows "Getting Upload URL..." with spinner
- **Note Issuance Loading**: Button shows "Issuing Note..." with spinner
- **File Validation Errors**: Immediate feedback for invalid files
- **Upload Errors**: Clear error messages for S3 upload failures
- **API Errors**: Proper error handling with user-friendly messages

#### **Form Validation**
- **Required Fields**: All form fields marked with (*) and validated
- **Email Format**: Real-time email validation with error display
- **File Requirements**: File type and size validation
- **Submission Blocking**: Form submission blocked until all validation passes

### **Files Modified/Created**

#### **New Files Created**
- `src/services/financing/issueIndividualNote.service.ts` - Service layer for API operations
- `src/hooks/financing/useIssueIndividualNote.hooks.ts` - React Query hooks
- `src/utils/s3Upload.ts` - S3 upload utility functions

#### **Files Modified**
- `src/components/financing/convertiblenote/dialogs/RecordPopupDialog.tsx` - Enhanced with API integration and loading states
- `src/integrations/legal-concierge/client.ts` - Added new API methods for document upload and note recording

### **Implementation Summary**
The Issue Individual Notes Form API Integration provides complete functionality for issuing individual convertible notes with document upload capabilities, proper validation, and comprehensive error handling.

---

## **Final Requirements Summary**

### **Core Components**
1. **ActionButton** - Single dropdown menu replacing four-button toolbar
2. **AuthorizeRoundDialog** - Complete form with validation and API integration
3. **IssueIndividualNotesDialog** - Round selection with dual document upload support
4. **IncreaseAuthorizedRoundDialog** - Round selection with increase amount form
5. **RecordPopupDialog** - Two radio options with embedded forms
6. **ViewIndividualNotesDialog** - Eye icon functionality for viewing individual notes

### **Key Features**
- Single Action button with dropdown menu positioned in top right
- Table positioned at bottom with full width
- Complete API integration for all workflows
- File upload capabilities with S3 integration
- Dual document upload support (prorata sideletter and side letter)
- Real-time validation and error handling
- Professional UX with loading states and toast notifications

### **API Endpoints**
- GET `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist`
- POST `/api/p2/companies/{companyId}/preseedfinance/authorizedround`
- POST `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedround`
- POST `/api/p2/companies/{companyId}/preseedfinance/issueindividualnote`
- POST `/api/p2/companies/{companyId}/preseedfinance/recordauthorizedround`
- POST `/api/p2/companies/{companyId}/preseedfinance/recordissueindividualnote`
- Various presigned URL endpoints for document uploads

### **Implementation Status**
All core functionality has been implemented with comprehensive validation, error handling, and professional UX. The feature provides complete convertible note management capabilities with document upload support and audit trail functionality.

---

## **UPDATED FEATURE: Outstanding Principal Display & Status Changes**

### **Feature Overview**
Update the Convertible Notes interface to display real outstanding principal data from the API and change status terminology from "Not Approved" to "Pending" for better user experience.

### **Business Value**
- **Accurate Financial Data**: Display real outstanding principal amounts instead of static placeholders
- **Improved UX**: Use "Pending" instead of "Not Approved" for more positive user experience
- **Enhanced Transparency**: Show actual accrued interest in individual notes view

### **Technical Requirements**

#### **1. Outstanding Principal Display**
- **Data Source**: Use `outstandingPrincipal` field from `/api/p2/companies/{companyId}/preseedfinance/authorizedroundlist` API
- **Current Implementation**: Currently shows static `$0` placeholder
- **Updated Implementation**: Display actual `outstandingPrincipal` value from API response
- **Formatting**: Currency formatting with proper separators (e.g., `$24,000`, `$200,000`)

#### **2. Status Terminology Update**
- **Current**: "Not Approved" for `isActive: false` rounds
- **Updated**: "Pending" for `isActive: false` rounds
- **Approved**: "Approved" for `isActive: true` rounds (unchanged)
- **Filter Labels**: Update filter radio buttons from "Not Approved" to "Pending"

#### **3. Individual Notes Dialog Updates**
- **Remove Column**: "Approval Status" column (no longer needed)
- **Add Column**: "Accrued Interest" column showing `accruedInterest` from API
- **Data Source**: Updated API response includes `accruedInterest` field
- **Formatting**: Currency formatting for accrued interest amounts

### **API Response Updates**

#### **Authorized Rounds API Response**
```json
{
  "message": "Success",
  "data": [
    {
      "id": "08029b64-a43e-41b5-9826-d32a34b4115a",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "qualifiedFinancingThreshold": 100000,
      "totalAuthorizedAmountOfRound": 100000,
      "increaseAuthorizeRoundId": null,
      "interestRate": 10,
      "interestType": "simple-interest",
      "maturityDate": "2025-09-18",
      "valuationCap": 100000,
      "discount": 10,
      "mostFavoredNation": false,
      "boardApprovedDate": null,
      "outstandingPrincipal": 0,        // NOW DISPLAYED IN UI
      "isActive": false                 // NOW SHOWS AS "Pending"
    },
    {
      "id": "811e7e55-e82e-4aae-90de-e83e47366966",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "qualifiedFinancingThreshold": 200000,
      "totalAuthorizedAmountOfRound": 210000,
      "increaseAuthorizeRoundId": null,
      "interestRate": 12,
      "interestType": "simple-interest",
      "maturityDate": "2025-09-20",
      "valuationCap": 200000,
      "discount": 2,
      "mostFavoredNation": false,
      "boardApprovedDate": "2025-09-20",
      "outstandingPrincipal": 24000,    // NOW DISPLAYED IN UI
      "isActive": true                  // SHOWS AS "Approved"
    }
  ],
  "error": null,
  "validationErrors": null
}
```

#### **Individual Notes API Response**
```json
{
  "message": "Success",
  "data": [
    {
      "id": "03381333-d8aa-43ff-9cf3-14420d66d5fe",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "authorizedRoundId": "5210f828-9bc6-41b5-bcdc-f156e583cea3",
      "interestType": "simple-interest",
      "principalAmount": 25000,
      "accruedInterest": 15.07,         // NEW: Displayed in Accrued Interest column
      "interestRate": 22,
      "name": "Sujan",
      "email": "<EMAIL>",
      "dateOfInvestment": "2025-09-20",
      "approvedDate": "2025-09-20",
      "isActive": true
    }
  ],
  "error": null,
  "validationErrors": null
}
```

### **UI/UX Changes**

#### **Aggregate Note Rounds Table**
- **Outstanding Principal Column**: Display actual values instead of `$0` placeholder
- **Status Badge**: Change "Not Approved" to "Pending" for `isActive: false`
- **Filter Controls**: Update radio button label from "Not Approved" to "Pending"

#### **View Individual Notes Dialog**
- **Column Changes**:
  - **REMOVED**: "Approval Status" column
  - **ADDED**: "Accrued Interest" column
- **Data Display**: Show `accruedInterest` value with currency formatting
- **Status Column**: Continue showing Active/Inactive based on `isActive` field

#### **Issue Individual Notes Dialog - Principal Amount Validation**
- **NEW**: Real-time validation for Principal Amount field
- **Validation Rule**: Principal Amount must not exceed (Authorized Amount - Outstanding Principal)
- **Available Amount Display**: Shows available amount below input field
- **Error Messages**: Clear error messages when amount exceeds available amount
- **Visual Feedback**: Red border on input field when validation fails
- **Form Submission**: Prevents submission if amount is invalid

### **Implementation Requirements**

#### **Files to Update**
1. **`src/types/financing.ts`**:
   - Update `ConvertibleNoteRoundResponse` to include `outstandingPrincipal: number`
   - Update `IndividualNote` to include `accruedInterest: number`

2. **`src/components/financing/convertiblenote/tables/RoundsTable.tsx`**:
   - Display `outstandingPrincipal` instead of static `$0`
   - Change status badge text from "Not Approved" to "Pending"
   - Update filter radio button label

3. **`src/components/financing/convertiblenote/dialogs/ViewIndividualNotesDialog.tsx`**:
   - Remove "Approval Status" column
   - Add "Accrued Interest" column
   - Update table headers and data mapping

4. **`src/components/financing/convertiblenote/dialogs/IssueIndividualNotesDialog.tsx`**:
   - Update outstanding principal display in round selection table
   - **NEW**: Add Principal Amount validation logic
   - **NEW**: Add available amount calculation and display
   - **NEW**: Add real-time validation with error messages
   - **NEW**: Add visual feedback for validation errors

5. **`src/components/financing/convertiblenote/dialogs/IncreaseAuthorizedRoundDialog.tsx`**:
   - Update outstanding principal display in round selection table

### **User Experience Improvements**
- **Accurate Financial Data**: Users see real outstanding principal amounts
- **Positive Terminology**: "Pending" is more user-friendly than "Not Approved"
- **Enhanced Interest Tracking**: Accrued interest provides better financial visibility
- **Consistent Data**: All tables show the same outstanding principal values
- **NEW**: **Smart Validation**: Real-time Principal Amount validation prevents invalid submissions
- **NEW**: **Clear Feedback**: Available amount display helps users understand limits
- **NEW**: **Error Prevention**: Form submission blocked until validation passes

### **Validation & Error Handling**
- **Null/Undefined Values**: Handle cases where `outstandingPrincipal` might be null
- **Currency Formatting**: Ensure proper formatting for all currency values
- **API Error Handling**: Graceful fallback if API doesn't return expected fields
- **Loading States**: Maintain loading states during API calls

### **Testing Requirements**
- ✅ Outstanding principal displays correctly from API data
- ✅ Status shows "Pending" for `isActive: false` rounds
- ✅ Status shows "Approved" for `isActive: true` rounds
- ✅ Filter controls updated to show "Pending" instead of "Not Approved"
- ✅ Individual notes dialog shows "Accrued Interest" column
- ✅ Individual notes dialog removes "Approval Status" column
- ✅ Currency formatting works correctly for all amounts
- ✅ Error handling for missing or invalid data
- ✅ **NEW**: Principal Amount validation works correctly
- ✅ **NEW**: Available amount calculation is accurate
- ✅ **NEW**: Real-time validation provides immediate feedback
- ✅ **NEW**: Form submission blocked when amount exceeds available
- ✅ **NEW**: Error messages are clear and helpful
- ✅ **NEW**: Visual feedback (red border) appears on validation errors

---

## **NEW FEATURE: Principal Amount Validation**

### **Feature Overview**
Add real-time validation to the Principal Amount input field in the Issue Individual Notes dialog to ensure users cannot enter amounts that exceed the available authorized amount for the selected round.

### **Business Value**
- **Prevents Invalid Submissions**: Users cannot submit amounts that exceed available authorized capacity
- **Clear User Guidance**: Available amount display helps users understand their limits
- **Improved Data Integrity**: Ensures all note issuances are within authorized bounds
- **Better User Experience**: Real-time feedback prevents form submission errors

### **Technical Requirements**

#### **Validation Logic**
- **Formula**: Principal Amount ≤ (Authorized Amount - Outstanding Principal)
- **Real-time Validation**: Validation occurs as user types in the input field
- **Available Amount Display**: Show calculated available amount below input field
- **Error Prevention**: Block form submission if validation fails

#### **UI/UX Requirements**
- **Available Amount Display**: "Available: $X,XXX of $Y,YYY authorized"
- **Error Messages**: "Amount cannot exceed available amount of $X,XXX"
- **Visual Feedback**: Red border on input field when validation fails
- **Helper Text**: Clear indication of available vs. total authorized amounts

#### **Form Integration**
- **Real-time Validation**: Validate on every input change
- **Form Submission Blocking**: Prevent submission until validation passes
- **Error State Management**: Clear errors when user corrects input
- **Round Selection Reset**: Clear validation when switching rounds

### **Implementation Details**

#### **State Management**
```typescript
const [principalAmountError, setPrincipalAmountError] = useState<string>("");

const getAvailableAmount = (round: ConvertibleNoteRoundResponse | undefined): number => {
  if (!round) return 0;
  return round.totalAuthorizedAmountOfRound - round.outstandingPrincipal;
};

const validatePrincipalAmount = (amount: number, round: ConvertibleNoteRoundResponse | undefined): string => {
  if (!round) return "";
  
  const availableAmount = getAvailableAmount(round);
  if (amount > availableAmount) {
    return `Amount cannot exceed available amount of $${availableAmount.toLocaleString()}`;
  }
  return "";
};
```

#### **UI Implementation**
```typescript
<div className="space-y-3">
  <Label htmlFor="principalAmount" className="text-sm font-medium">
    Principal Amount ($) <span className="text-red-500">*</span>
  </Label>
  <Input
    id="principalAmount"
    type="number"
    placeholder="0.00"
    value={formData.principalAmount || ""}
    onChange={(e) => handleInputChange("principalAmount", parseFloat(e.target.value) || 0)}
    min="0"
    step="0.01"
    className={`h-11 ${principalAmountError ? "border-red-500" : ""}`}
  />
  {selectedRound && (
    <p className="text-sm text-gray-600">
      Available: ${getAvailableAmount(selectedRound).toLocaleString()} of ${selectedRound.totalAuthorizedAmountOfRound.toLocaleString()} authorized
    </p>
  )}
  {principalAmountError && (
    <p className="text-sm text-red-600">{principalAmountError}</p>
  )}
</div>
```

### **User Experience Flow**
1. **User selects round**: Available amount is calculated and displayed
2. **User enters amount**: Real-time validation occurs on every keystroke
3. **Amount exceeds available**: Red border appears, error message shows
4. **User corrects amount**: Error clears, validation passes
5. **Form submission**: Only allowed when validation passes

### **Error Handling**
- **Validation Errors**: Clear, specific error messages
- **Edge Cases**: Handle null/undefined round data gracefully
- **Form Reset**: Clear validation errors when switching rounds
- **User Feedback**: Immediate visual and textual feedback

### **Testing Requirements**
- ✅ Available amount calculation is accurate for all rounds
- ✅ Real-time validation works on every input change
- ✅ Error messages are clear and helpful
- ✅ Visual feedback (red border) appears on validation errors
- ✅ Form submission is blocked when validation fails
- ✅ Validation errors clear when user corrects input
- ✅ Available amount updates when switching rounds
- ✅ Edge cases handled (null/undefined data)

### **Implementation Status**
The Principal Amount validation feature is **complete and production-ready**. The implementation provides:
- **Real-time Validation**: Immediate feedback as users type
- **Clear User Guidance**: Available amount display helps users understand limits
- **Error Prevention**: Form submission blocked until validation passes
- **Professional UX**: Visual feedback and clear error messages
- **Robust Logic**: Handles all edge cases and data scenarios

### **Production Readiness**
Ready for production use with comprehensive validation, clear user feedback, and robust error handling.