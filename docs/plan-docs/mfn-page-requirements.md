## MFN Page Revamp – Feature Requirements

Author: Frontend | Owners: Product, Legal, Frontend, Backend | Status: Draft

### Goals
- Transform MFN page to unified view without radio button selection
- Implement new API endpoint for MFN summary data
- Display current vs updated terms in stacked comparison format
- Add single "Notify Holder" button in bottom right corner
- Show triggering note details in bottom table
- Implement proper service layer and custom hooks for new data structure

### UX Overview
- **NEW DESIGN**: Unified view showing all MFN-triggered investments (convertible notes and SAFEs)
- **REMOVED**: Radio button selection between Convertible Notes and SAFEs
- **NEW FEATURE**: Stacked comparison showing current vs updated terms for each investor
- **NEW FEATURE**: Single "Notify Holder" button positioned in bottom right corner
- **NEW FEATURE**: Triggering note details in bottom table
- **IMPROVED**: Color-coded differentiation between current and updated terms

---

## **UPDATED UI DESIGN REQUIREMENTS**

### **New Layout Structure**
The MFN interface will be redesigned as a unified view without radio button selection:

#### **Top Section: MFN Summary Table**
- **Single table** showing all investments that have been triggered by MFN provisions
- **Stacked rows**: Each investor appears twice - current terms and updated terms
- **Color coding**: Visual differentiation between current and updated terms
- **Columns**: Round Type, Investor Name, Principal Amount, Date of Investment, Interest Rate, Valuation Cap, Discount Rate, Email

#### **Bottom Section: Triggering Note Details**
- **Single table** showing the triggering note that caused the MFN provisions to activate
- **Columns**: Round Type, Interest Rate, Valuation Cap, Discount Rate, Approved Date

#### **Action Section: Notify Holder Button**
- **Single button** positioned below the bottom table in the main card
- **Functionality**: Send notifications to all affected holders

### **Visual Design Requirements**
- **Table Layout**: 
  - Top table with stacked comparison rows
  - Bottom table with triggering note details
  - Responsive design with horizontal scroll on mobile
- **Color Coding**:
  - Current terms: Light orange background (#fef3c7) with orange border
  - Updated terms: Light green background (#dcfce7) with green border
  - Clear visual distinction between the two
  - Hover effects for better interactivity
- **Color Legend**: 
  - Legend above the table explaining color coding
  - Visual indicators with colored squares
  - Clear labeling for accessibility
- **Button Positioning**: Fixed position in bottom right corner
- **Typography**: Clear hierarchy with proper spacing
- **Row Height**: Optimized row height by removing badges for better readability

---

## **UI WIREFRAME DESIGN**

### **Page Layout Overview**
```
┌─────────────────────────────────────────────────────────────────┐
│                    MFN (Most Favored Nation)                    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              MFN Summary Table                              │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │ Round │ Investor │ Principal │ Date of   │ Interest │   │ │ │
│  │  │ Type  │ Name     │ Amount    │ Investment│ Rate     │   │ │ │
│  │  ├───────┼──────────┼───────────┼───────────┼──────────┤   │ │ │
│  │  │       │          │           │           │          │   │ │ │
│  │  │       │          │           │           │          │   │ │ │
│  │  │       │          │           │           │          │   │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Triggering Note Details                        │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │ Round │ Interest │ Valuation │ Discount │ Approved │   │ │ │
│  │  │ Type  │ Rate     │ Cap       │ Rate     │ Date     │   │ │ │
│  │  ├───────┼──────────┼───────────┼──────────┼──────────┤   │ │ │
│  │  │       │          │           │          │          │   │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    ┌─────────────────────────┐   │
│                                    │    Notify Holders       │   │
│                                    └─────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### **Detailed Table Wireframes**

#### **Top Table: MFN Summary with Stacked Comparison**
```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                    MFN Summary Table                                                    │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│ Round Type │ Investor Name │ Principal Amount │ Date of Investment │ Interest Rate │ Valuation Cap │ Email │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ Convertible│ Sujan 1       │ $2,500          │ Aug 22, 2025       │ 10%           │ $5,000        │ ...   │
│ [Current]  │               │                 │                    │               │               │       │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ Convertible│ Sujan 1       │ $2,500          │ Aug 22, 2025       │ 22%           │ $5,000        │ ...   │
│ [Updated]  │               │                 │                    │               │               │       │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ Convertible│ Sujan 2       │ $2,000          │ Aug 22, 2025       │ 10%           │ $5,000        │ ...   │
│ [Current]  │               │                 │                    │               │               │       │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ Convertible│ Sujan 2       │ $2,000          │ Aug 22, 2025       │ 22%           │ $5,000        │ ...   │
│ [Updated]  │               │                 │                    │               │               │       │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ SAFE       │ Sujan 3       │ $3,000          │ Aug 22, 2025       │ N/A           │ $6,000        │ ...   │
│ [Current]  │               │                 │                    │               │               │       │
├────────────┼───────────────┼─────────────────┼────────────────────┼───────────────┼───────────────┼───────┤
│ SAFE       │ Sujan 3       │ $3,000          │ Aug 22, 2025       │ N/A           │ $6,000        │ ...   │
│ [Updated]  │               │                 │                    │               │               │       │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

#### **Bottom Table: Triggering Note Details**
```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                              Triggering Note Details                                    │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│ Round Type │ Interest Rate │ Valuation Cap │ Discount Rate │ Approved Date              │
├────────────┼───────────────┼───────────────┼───────────────┼────────────────────────────┤
│ Convertible│ 22%           │ $6,500        │ 15%           │ Aug 22, 2025               │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

### **Color Coding Legend**
- **Current Terms**: Light orange background (#fef3c7) with orange border
- **Updated Terms**: Light green background (#dcfce7) with green border
- **Visual Legend**: Color-coded squares above table for clear identification
- **Round Type**: "Convertible" for authorized-round, "SAFE" for authorized-safe
- **Hover Effects**: Enhanced interactivity with color transitions

### **Button Positioning**
- **Notify Holders Button**: Fixed position in bottom right corner
- **Style**: Primary button with notification icon
- **Text**: "Notify Holders"
- **Loading State**: Spinner during API call
- **Success State**: Toast notification on completion

### **Responsive Design Considerations**
- **Desktop**: Full table view with all columns visible
- **Tablet**: Maintain table structure with adjusted spacing
- **Mobile**: Horizontal scroll for tables, stacked layout for better readability

---

## **API INTEGRATION REQUIREMENTS**

### **New API Endpoint**
- **Endpoint**: `GET /api/p2/companies/{companyId}/preseedfinance/mfnsummary`
- **Purpose**: Fetch comprehensive MFN summary including current issues and triggering note
- **Response**: Array of current issues (with current and updated terms) + triggering note details

### **Data Structure**

#### **MFN Summary Response**
```typescript
{
  "message": "Success",
  "data": {
    "currentIssues": [
      {
        "isTriggerUpdateData": boolean,
        "id": string,
        "roundId": string,
        "companyId": string,
        "roundType": "authorized-round" | "authorized-safe",
        "interestRate": number | null,
        "valuationCap": number,
        "discountRate": number,
        "investorName": string,
        "principalAmount": number,
        "dateOfInvestment": string,
        "approvedDate": string,
        "email": string
      }
    ],
    "newTerm": {
      "roundId": string,
      "companyId": string,
      "roundType": "authorized-round" | "authorized-safe",
      "interestRate": number | null,
      "valuationCap": number,
      "discountRate": number,
      "approvedDate": string,
      "oldMfnRoundType": string,
      "oldMfnRoundId": string
    }
  },
  "error": string | null,
  "validationErrors": unknown | null
}
```

---

## **TECHNICAL IMPLEMENTATION REQUIREMENTS**

### **Component Architecture Changes**

#### **Updated Components**
1. **`MfnTab.tsx`** (Major Update)
   - **REMOVED**: Radio button selection interface
   - **NEW**: Unified MFN summary view
   - **NEW**: Single table layout with stacked comparison
   - **NEW**: Notify holder button integration

2. **`MfnSummaryTable.tsx`** (New)
   - **Purpose**: Display current issues with stacked comparison
   - **Features**: Color-coded rows, grouped by investor ID
   - **Columns**: Round Type, Investor Name, Principal Amount, Date of Investment, Interest Rate, Valuation Cap, Discount Rate, Email

3. **`MfnTriggeringNoteTable.tsx`** (New)
   - **Purpose**: Display triggering note details
   - **Features**: Single row table with new term data
   - **Columns**: Round Type, Interest Rate, Valuation Cap, Discount Rate, Approved Date

4. **`MfnNotifyHolderButton.tsx`** (New)
   - **Purpose**: Single action button for notifying all holders
   - **Positioning**: Bottom right corner
   - **Functionality**: Send notifications to all affected investors

### **Service Layer Implementation**

#### **Updated Service Files**
1. **`src/services/mfn/mfn.service.ts`**
   - **Methods**:
     - `getMfnSummary(companyId: string)`
     - `notifyHolders(companyId: string)`
   - **Error Handling**: Consistent error handling and response parsing
   - **Type Safety**: Full TypeScript implementation

2. **`src/types/mfn.ts`** (Updated)
   - **New Interfaces**:
     - `MfnCurrentIssue`
     - `MfnNewTerm`
     - `MfnSummaryResponse`
     - `MfnNotifyResponse`

### **Custom Hooks Implementation**

#### **Updated Hook Files**
1. **`src/hooks/mfn/useMfnSummary.hooks.ts`** (New)
   - **State Management**: Loading, error, data states
   - **Data Fetching**: API call with proper error handling
   - **Return Values**: `{ data, loading, error, refetch }`

2. **`src/hooks/mfn/useMfnNotifyHolders.hooks.ts`** (New)
   - **State Management**: Loading, error states
   - **Data Fetching**: Notification API call
   - **Return Values**: `{ notify, loading, error }`

### **API Client Updates**

#### **New Methods in `client.ts`**
1. **`getMfnSummary(companyId: string)`**
   - **Endpoint**: `/api/p2/companies/{companyId}/preseedfinance/mfnsummary`
   - **Method**: GET
   - **Response Type**: `MfnSummaryResponse`

2. **`notifyMfnHolders(companyId: string)`**
   - **Endpoint**: `/api/p2/companies/{companyId}/preseedfinance/notifymfn`
   - **Method**: POST
   - **Response Type**: `MfnNotifyResponse`

---

## **UI/UX SPECIFICATIONS**

### **Table Design**
- **Top Table**: MFN Summary with stacked comparison
  - **Row Grouping**: Group by investor ID
  - **Color Coding**: 
    - Current terms: Light orange background (#fef3c7) with orange border
    - Updated terms: Light green background (#dcfce7) with green border
  - **Visual Legend**: Color-coded legend above table for clear identification
  - **Hover Effects**: Enhanced interactivity with color transitions
  - **Responsive**: Horizontal scroll on mobile devices
  - **Optimized Height**: Removed badges for better row height and readability

- **Bottom Table**: Triggering Note Details
  - **Single Row**: Display new term information
  - **Highlighted**: Emphasize the triggering note
  - **Compact**: Focused on key information

### **Column Specifications**

#### **Top Table Columns**
1. **Round Type**: 
   - Display "Convertible" for `authorized-round`
   - Display "SAFE" for `authorized-safe`
2. **Investor Name**: Full investor name
3. **Principal Amount**: Formatted currency
4. **Date of Investment**: Formatted date
5. **Interest Rate**: Percentage (show "N/A" for SAFEs)
6. **Valuation Cap**: Formatted currency
7. **Discount Rate**: Percentage
8. **Email**: Investor email address

#### **Bottom Table Columns**
1. **Round Type**: Convertible or SAFE
2. **Interest Rate**: Percentage (show "N/A" for SAFEs)
3. **Valuation Cap**: Formatted currency
4. **Discount Rate**: Percentage
5. **Approved Date**: Formatted date

### **Button Design**
- **Position**: Below the bottom table in the main card
- **Style**: Primary button with clear call-to-action
- **Text**: "Notify Holders"
- **Icon**: Bell or notification icon
- **Loading State**: Show spinner during API call
- **Success State**: Toast notification on completion

### **Responsive Design**
- **Mobile**: Stacked layout with horizontal scroll for tables
- **Tablet**: Maintain table structure with adjusted spacing
- **Desktop**: Full table view with optimal spacing

---

## **IMPLEMENTATION PHASES**

### **Phase 1: API Integration Layer**
1. **Add new API methods to `client.ts`**
2. **Create updated service files and types**
3. **Implement new custom hooks**
4. **Test API integration**

### **Phase 2: UI Component Updates**
1. **Remove radio button selection**
2. **Create new table components**
3. **Implement stacked comparison layout**
4. **Add color coding for current vs updated terms**
5. **Position notify holder button**

### **Phase 3: State Management & Polish**
1. **Implement loading states**
2. **Add error handling**
3. **Manage notification functionality**
4. **Final testing and refinement**

---

## **TESTING REQUIREMENTS**

### **Unit Tests**
- **Service Layer**: Test API calls and error handling
- **Custom Hooks**: Test state management and data flow
- **Components**: Test rendering and user interactions

### **Integration Tests**
- **API Integration**: Test end-to-end data flow
- **User Interactions**: Test notification functionality
- **Error Scenarios**: Test error handling and user feedback

### **UI/UX Tests**
- **Responsive Design**: Test across different screen sizes
- **Accessibility**: Ensure proper ARIA labels and keyboard navigation
- **Performance**: Test loading states and data rendering

---

## **ACCEPTANCE CRITERIA**

### **Functional Requirements**
- [ ] Unified view displays all MFN-triggered investments
- [ ] Stacked comparison shows current vs updated terms clearly
- [ ] Color coding differentiates current and updated terms
- [ ] Round type displays correctly (Convertible/SAFE)
- [ ] Triggering note details display in bottom table
- [ ] Notify holder button functions correctly
- [ ] API data displays correctly in tables
- [ ] Loading states show during API calls
- [ ] Error handling provides clear user feedback

### **Non-Functional Requirements**
- [ ] Page loads within 2 seconds
- [ ] Smooth transitions and interactions
- [ ] Responsive design works on all screen sizes
- [ ] Accessibility standards are met
- [ ] No console errors or warnings

### **Technical Requirements**
- [ ] TypeScript types are properly defined
- [ ] Error boundaries handle unexpected errors
- [ ] Code follows existing patterns and conventions
- [ ] Proper separation of concerns maintained
- [ ] Unit tests cover critical functionality

---

## **DEPENDENCIES**

### **Frontend Dependencies**
- **Existing**: React, TypeScript, Tailwind CSS, shadcn/ui components
- **New**: None (all required components already exist)

### **Backend Dependencies**
- **API Endpoints**: Must be available and functional
- **Data Format**: Response structure must match specifications
- **Authentication**: Proper authentication and authorization

### **External Dependencies**
- **Icons**: Lucide React (already available)
- **Date Handling**: date-fns (already available)

---

## **RISKS AND MITIGATION**

### **Technical Risks**
1. **API Integration Issues**
   - **Risk**: New API endpoint not ready or response format changes
   - **Mitigation**: Coordinate with backend team, implement proper error handling

2. **Performance Issues**
   - **Risk**: Large datasets causing slow rendering
   - **Mitigation**: Implement pagination or virtualization if needed

3. **Complex Data Grouping**
   - **Risk**: Complex logic for grouping current and updated terms
   - **Mitigation**: Use simple, predictable grouping patterns

### **Timeline Risks**
1. **API Development Delays**
   - **Risk**: Backend not ready when frontend is complete
   - **Mitigation**: Implement mock data fallback, coordinate with backend team

2. **Testing Complexity**
   - **Risk**: Integration testing takes longer than expected
   - **Mitigation**: Start testing early, implement comprehensive test coverage

---

## **SUCCESS METRICS**

### **User Experience**
- **Improved Clarity**: Clear comparison between current and updated terms
- **Better Efficiency**: Single view for all MFN-triggered investments
- **Enhanced Usability**: Intuitive notification process

### **Technical Metrics**
- **Code Quality**: Maintained or improved code quality
- **Performance**: Page load time under 2 seconds
- **Test Coverage**: Minimum 80% test coverage
- **Accessibility**: WCAG 2.1 AA compliance

### **Business Metrics**
- **User Adoption**: Increased usage of MFN functionality
- **Support Tickets**: Reduced support requests related to MFN
- **User Satisfaction**: Positive feedback on interface improvements

---

## **FUTURE ENHANCEMENTS**

### **Phase 2 Features**
- **Bulk Actions**: Select multiple investors for targeted notifications
- **Data Export**: Add export functionality for MFN data
- **Advanced Filtering**: Add filtering and search capabilities
- **Real-time Updates**: WebSocket integration for live data updates

### **Long-term Vision**
- **MFN Dashboard**: Comprehensive MFN management interface
- **Automated Notifications**: System-generated MFN notifications
- **Integration**: Connect with other legal and financial systems
- **Analytics**: MFN impact analysis and reporting

---

## **APPROVAL AND SIGN-OFF**

### **Stakeholder Approvals**
- [ ] **Product Owner**: Requirements and scope approval
- [ ] **Legal Team**: Compliance and regulatory approval
- [ ] **Frontend Team**: Technical implementation approval
- [ ] **Backend Team**: API integration approval
- [ ] **UX/UI Team**: Design and user experience approval

### **Document Version History**
- **Version**: 2.0
- **Date**: [Current Date]
- **Author**: Frontend Team
- **Status**: Draft
- **Next Review**: [Date]

---

*This document will be updated as requirements evolve and implementation progresses.*
