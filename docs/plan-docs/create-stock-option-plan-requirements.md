# Create Stock Option Plan — Requirements

## Goal
Add a new Maintenance action, "Create Stock Option Plan", that appears only when the company does not yet have a stock option plan. Clicking Start opens a simple dialog with a single numeric input (share amount) and two buttons. Initial iteration is UI-only; API wiring will be added later.

## Visibility Rule
- Show the row "Create Stock Option Plan" only when `includeStockOptionPlan === false` from the Company GET response.
- Otherwise, show the existing "Increase Stock Option Plan" row (current behavior).

### Data source
- Use the existing company endpoint `GET /api/companies/{companyId}` to read `includeStockOptionPlan`.
- This is already consumed via `useCompanyById(companyId)` which hits the base url (e.g., `https://apidev.foundersform.com/api/companies/{companyId}`) for the example company. See reference: [Company GET example](https://apidev.foundersform.com/api/companies/4dc692cc-5086-4433-8339-6e7b9c0ce572).

## UI Placement
- File to update: `src/components/dashboard/maintenance/MaintenanceItems.tsx`.
- Add a new row item definition:
  - Title: "Create Stock Option Plan"
  - Description: "Create your company’s initial stock option plan."
  - Button text: "Start"
  - Icon: reuse `LineChart` or choose a distinct icon (e.g., `FilePlus2`) for clarity.
  - onClick: opens a new dialog (see below).
- Gating: The function that builds the items should receive a boolean (or raw `includeStockOptionPlan`) and decide to push either the new item or the existing "Increase Stock Option Plan" item, but never both.

### Suggested API for items function
```ts
export const getMaintenanceItems = (props: MaintenanceItemsProps & {
  includeStockOptionPlan?: boolean; // undefined-safe
}): MaintenanceItemType[] => { /* choose row based on includeStockOptionPlan */ };
```

## Dialog (UI-only for v1)
- New component: `src/components/shares/CreateStockOptionPlanDialog.tsx`
- Behavior: opened from the Maintenance row’s Start button; closed via Cancel or success.
- Contents:
  - Title: "Create Stock Option Plan"
  - Description: "Specify the total number of shares to reserve for your plan."
  - Single input field: "Share Amount" (numeric only)
    - Accept only digits (strip non-numeric on input), format with thousands separators for display.
    - Store an internal numeric value derived from the formatted string.
    - Client-side validation: required, integer > 0, max 2,147,483,647 (int32 safe) for first pass.
  - Buttons:
    - Primary: "Create Plan" (disabled until valid numeric input)
    - Secondary: "Cancel"
- Styling: Tailwind classes and existing Shadcn primitives (`Dialog`, `Input`, `Label`, `Button`).
- Toasts: none for v1 (no API). Add on integration pass.

## State & Hooks
- v1: local component state only (no service/hook calls).
- v2 (future): introduce `useCreateStockOptionPlan.hooks.ts` with a `react-query` mutation and service layer.

## Future API (for v2 — to be confirmed)
- POST `/api/p2/companies/{companyId}/stockoptionplan` with payload `{ totalPoolSize: number }` or `{ shares: number }` (exact field name TBD).
- On success:
  - Show `toast.success("Stock option plan created successfully")`.
  - Invalidate `useCompanyById` and any SOP summary queries so Maintenance switches to "Increase Stock Option Plan" on next render.
- On error:
  - Show `toast.error(message)`.
  - Keep input value for retry.
- Button loading: primary button shows "Creating..." while mutation is pending.

## Validation Rules (v1)
- Required, numeric, positive integer.
- Input must not allow leading zeros except single 0 if you decide to allow typing; final normalized value must be > 0.
- Format display with `toLocaleString()`; maintain a raw number in state.

## Accessibility
- Associate `Label` with `Input` via `htmlFor`.
- Ensure dialog traps focus and is closable via Escape and overlay click (matches existing dialog patterns).

## Success Criteria
- When `includeStockOptionPlan === false`, the dashboard shows "Create Stock Option Plan" instead of the increase row.
- Clicking Start opens the dialog with one numeric input and two buttons; basic validation and formatting work.
- No API calls yet; code organized to easily add v2 service/hook integration later.
