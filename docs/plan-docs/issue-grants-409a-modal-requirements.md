# Issue Grants 409A Modal Requirements

## 📋 Overview

This document outlines the requirements for implementing a new intermediate modal that opens when users click the "Issue" button for selected grants. This modal serves as a gateway to different 409A valuation workflows before proceeding to grant issuance.

## 🎯 Business Objective

- **Streamline the grant issuance process** by providing clear 409A valuation options upfront
- **Improve user experience** by showing the path before opening complex dialogs
- **Maintain consistency** between different 409A valuation approaches
- **Provide clear visual feedback** for each grant's fair market value

## 🔄 Current vs. New Flow

### **Current Implementation:**
```
Issue <PERSON><PERSON> → Directly opens Dialog409Valuation with mode selection
Modify But<PERSON> → Opens ModifyGrantsDialog for editing
```

### **New Implementation:**
```
Issue Button → New Intermediate Modal (3 409A options) → Existing Dialogs → Grant Table
Modify Button → Opens ModifyGrantsDialog (unchanged)
```

## 🎨 New Modal Design Requirements

### **Modal Title & Layout:**
- **Title:** "Select 409A Valuation Method"
- **Subtitle:** "Choose how to handle fair market value for {X} selected grants"
- **Theme:** Dark theme consistent with existing UI (`bg-[#1B1D2A]`, `border-[#2E2F3A]`)

### **Three Button Options:**
1. **"Use Current 409A Valuation on File"**
   - **Icon:** 📋 (document icon)
   - **Description:** "Use existing 409A valuation without changes"
   - **Style:** Primary button with purple gradient

2. **"Set Fair Market Value"**
   - **Icon:** 💰 (dollar sign icon)
   - **Description:** "Set a new fair market value manually"
   - **Style:** Secondary button with blue accent

3. **"Update 409A Valuation"**
   - **Icon:** 📤 (upload icon)
   - **Description:** "Upload new 409A valuation report"
   - **Style:** Secondary button with green accent

### **Button Layout:**
- **Vertical stack** for better readability
- **Equal width** buttons
- **Consistent spacing** between options
- **Hover effects** with subtle animations

## 🔄 Detailed Flow Diagrams

### **Flow 1: Use Current 409A Valuation**
```
New Modal → "Use Current 409A" → Dialog409Valuation (review mode) → Grant Table → Issue
```

**Dialog409Valuation Changes Required:**
- **Mode:** `'use-current-409a'`
- **Display:** Show current FMV for each selected grant
- **Layout:** Table format with grant details and FMV per row
- **Action:** "Proceed to Issue" button

### **Flow 2: Set Fair Market Value**
```
New Modal → "Set Fair Market Value" → Dialog409Valuation (FMV input) → Grant Table → Issue
```

**Dialog409Valuation Changes Required:**
- **Mode:** `'set-fair-market-value'`
- **Input:** Single FMV field that applies to all selected grants
- **Preview:** Show how FMV change affects each grant's price per share
- **Action:** "Apply FMV & Proceed" button

### **Flow 3: Update 409A Valuation**
```
New Modal → "Update 409A Valuation" → Dialog409Valuation (full upload) → Grant Table → Issue
```

**Dialog409Valuation Changes Required:**
- **Mode:** `'update-409a'`
- **Process:** Full 409A upload workflow
- **Result:** New FMV applied to all selected grants
- **Action:** "Upload Complete & Proceed" button

## 🎨 UI/UX Requirements

### **New Modal Design:**
```
┌─────────────────────────────────────────┐
│ ✕ Select 409A Valuation Method         │
├─────────────────────────────────────────┤
│ Choose how to handle fair market value │
│ for 3 selected grants                  │
│                                         │
│ [📋 Use Current 409A Valuation on File] │
│    Use existing 409A without changes   │
│                                         │
│ [💰 Set Fair Market Value]              │
│    Set a new fair market value manually│
│                                         │
│ [📤 Update 409A Valuation]             │
│    Upload new 409A valuation report    │
│                                         │
│                    [Cancel] [Continue]  │
└─────────────────────────────────────────┘
```

### **Dialog409Valuation Updates:**
- **Review Mode Layout:** Table showing each grant with current FMV
- **FMV Input Mode:** Single input field with preview of changes
- **Upload Mode:** Existing workflow with final review step

## 🔧 Technical Requirements

### **New Components:**
1. **`IssueGrants409AModal.tsx`** - New intermediate modal
2. **Updated `Dialog409Valuation.tsx`** - Enhanced review modes
3. **New hooks** for managing the intermediate flow

### **State Management:**
- **Modal state** for the new intermediate step
- **Flow tracking** between modal steps
- **Grant selection persistence** across modal transitions

### **Props & Interfaces:**
```typescript
interface IssueGrants409AModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGrants: PromisedGrant[];
  onOptionSelect: (option: 'use-current' | 'set-fmv' | 'update-409a') => void;
}
```

## 📱 Responsive Design

### **Mobile Considerations:**
- **Button stacking** on small screens
- **Touch-friendly** button sizes
- **Readable text** at all screen sizes

### **Desktop Enhancements:**
- **Hover states** for better interactivity
- **Keyboard navigation** support
- **Consistent spacing** across different resolutions

## 🧪 Testing Requirements

### **Unit Tests:**
- Modal rendering and button functionality
- Option selection and callback handling
- State management and transitions

### **Integration Tests:**
- Flow from new modal to existing dialogs
- Grant selection persistence
- FMV application across different modes

### **User Acceptance Tests:**
- Intuitive button placement and labeling
- Clear flow progression
- Consistent UI/UX with existing components

## 📅 Implementation Timeline

### **Phase 1: New Modal (Week 1)**
- Create `IssueGrants409AModal` component
- Implement three-button layout
- Add routing to existing dialogs

### **Phase 2: Dialog Updates (Week 2)**
- Enhance `Dialog409Valuation` for review modes
- Implement grant table display with FMV
- Add flow completion logic

### **Phase 3: Integration & Testing (Week 3)**
- Connect all components
- Test complete user flows
- UI/UX refinements

## 🎯 Success Criteria

- ✅ **Users can clearly see** the three 409A options before proceeding
- ✅ **Flow is intuitive** and follows expected user patterns
- ✅ **Grant information is clearly displayed** in review modes
- ✅ **FMV changes are properly applied** to all selected grants
- ✅ **UI is consistent** with existing design system
- ✅ **Performance is maintained** across all modal transitions

## 🔗 Related Documents

- [409A Upload Report Requirements](./409a-upload-report-requirements.md)
- [409A Valuation FMV Requirements](./409a-valuation-fmv-requirements.md)
- [Promised Grants Feature Implementation](./promised-grants-feature-implementation.md)

## 📝 Notes

- **Modify button functionality** remains unchanged
- **Existing dialogs** are enhanced, not replaced
- **Grant table** is read-only for final review
- **FMV calculations** apply to all selected grants uniformly
