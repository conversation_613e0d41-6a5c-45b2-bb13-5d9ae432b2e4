# Service Providers Terminate Feature Requirements

## Overview
The Service Providers Terminate feature allows users to terminate employees, consultants, and advisors. The terminate row should display pending and terminated counts based on the new API response structure.

## API Response Structures

### 1. Service Provider Status API
The `/api/p2/companies/{companyId}/serviceproviders/status` endpoint returns:

```json
{
    "message": "Success",
    "data": {
        "employees": {
            "active": 1,
            "pending": 2,
            "terminated": 0
        },
        "advisors": {
            "active": 1,
            "pending": 7,
            "terminated": 1
        },
        "contractors": {
            "active": 1,
            "pending": 1,
            "terminated": 0
        },
        "terminated": {
            "active": 0,
            "pending": 3,
            "terminated": 1
        }
    },
    "error": null,
    "validationErrors": null
}
```

### 2. Terminated Service Providers List API
The `/api/p2/serviceproviders/{companyId}/terminatedlist` endpoint returns:

```json
{
    "message": "Success",
    "data": [
        {
            "id": "1837fd84-d05d-440e-8605-c8fcb6c32ec7",
            "name": "Sam",
            "serviceProviderStatus": "advisor",
            "shares": 200000,
            "typeOfGrant": "restricted-stock-grant-inside-of-stock-option-plan",
            "vestedAmount": 200000,
            "unvestedAmount": 0,
            "vestingProgress": 100,
            "severanceAmount": null
        }
    ],
    "error": null,
    "validationErrors": null
}
```

### 3. Pending Terminated Service Providers List API
The `/api/p2/serviceproviders/{companyId}/pendingterminatedlist` endpoint returns:

```json
{
    "message": "Success",
    "data": [
        {
            "id": "978d86b6-a99e-4cfa-812c-a596304f55a4",
            "name": "Outside",
            "serviceProviderStatus": "advisor",
            "shares": 10000,
            "typeOfGrant": "restricted-stock-grant-outside-of-stock-option-plan",
            "vestedAmount": 0,
            "unvestedAmount": 10000,
            "vestingProgress": 0,
            "severanceAmount": null
        },
        {
            "id": "06e2878e-f8bf-4655-9eba-8f4919b89981",
            "name": "Harry",
            "serviceProviderStatus": "employee",
            "shares": 100000,
            "typeOfGrant": "restricted-stock-grant-outside-of-stock-option-plan",
            "vestedAmount": 0,
            "unvestedAmount": 100000,
            "vestingProgress": 0,
            "severanceAmount": null
        },
        {
            "id": "1e50a025-cbbe-40fe-b8df-68ff24cde79d",
            "name": "Tom",
            "serviceProviderStatus": "independent-contractor-consultant",
            "shares": 50000,
            "typeOfGrant": "option",
            "vestedAmount": 50000,
            "unvestedAmount": 0,
            "vestingProgress": 100,
            "severanceAmount": null
        }
    ],
    "error": null,
    "validationErrors": null
}
```

## Terminate Row Requirements

### Visual Display
- **Title**: "Terminate"
- **Description**: "Terminate employees, consultants, or advisors."
- **Button**: "Start" button
- **Icons**: Only show **pending** and **terminated** icons (no active icon)

### Count Display
- **Pending Icon** (Yellow Clock): Shows count from `data.terminated.pending`
- **Terminated Icon** (Red X): Shows count from `data.terminated.terminated`
- **No Active Icon**: The terminate row should not display an active count

### Click Behavior
- **Pending Icon Click**: Opens dialog showing service providers pending termination
- **Terminated Icon Click**: Opens dialog showing already terminated service providers
- **Start Button Click**: Opens the terminate dialog to select and terminate a provider

### Terminate Dialog Fields
The terminate dialog should include the following fields:
- **Service Provider Selection**: Radio button list of active service providers
- **Termination Date**: Date picker for selecting termination date
- **Termination Type**: Radio button selection with options:
  - Involuntary Termination (Without Cause)
  - Involuntary Termination (With Cause)
  - Resignation
- **Severance Amount**: Number input field for severance amount
- **Reason for Termination**: Optional text area for termination reason

## Implementation Requirements

### 1. Update Service Provider Items Configuration
- Modify `ServiceProviderItems.tsx` to include `pendingCount` and `terminatedCount` for the terminate row
- Remove `activeCount` from the terminate row configuration

### 2. Update Count Extraction Logic
- Extract `terminated.pending` and `terminated.terminated` from the API response
- Pass these counts to the terminate row configuration

### 3. Update Click Handlers
- Add `onTerminatePendingClick` handler for pending icon
- Add `onTerminateTerminatedClick` handler for terminated icon
- These should open appropriate dialogs showing the respective service providers

### 4. Dialog Implementation
- **Pending Dialog**: Shows service providers that are pending termination using `/api/p2/serviceproviders/{companyId}/pendingterminatedlist`
- **Terminated Dialog**: Shows service providers that have been terminated using `/api/p2/serviceproviders/{companyId}/terminatedlist`
- Both dialogs should display the service provider data with columns for:
  - Name
  - Service Provider Status (advisor/employee/independent-contractor-consultant)
  - Shares
  - Type of Grant
  - Vested Amount
  - Unvested Amount
  - Vesting Progress
  - Severance Amount

### 5. Terminate Dialog Enhancement
- **Update TerminateDialog.tsx** to include new form fields:
  - Date picker for termination date
  - Radio button group for termination type selection
  - Number input for severance amount
  - Enhanced reason text area
- **Update API payload** to include new fields:
  - `terminationDate`: ISO date string
  - `serviceProviderTerminationType`: termination type enum
  - `severanceAmount`: number value
  - `reasonForTermination`: string value

## Data Flow

1. **Status API Call**: `GET /api/p2/companies/{companyId}/serviceproviders/status`
2. **Count Extraction**: Extract `data.terminated.pending` and `data.terminated.terminated`
3. **UI Display**: Show counts in terminate row with appropriate icons
4. **Click Actions**: 
   - **Pending Icon Click**: Call `GET /api/p2/serviceproviders/{companyId}/pendingterminatedlist` and show dialog
   - **Terminated Icon Click**: Call `GET /api/p2/serviceproviders/{companyId}/terminatedlist` and show dialog
   - **Start Button Click**: Open terminate selection dialog
5. **Termination Process**:
   - User selects service provider from list
   - User fills termination form (date, type, severance, reason)
   - User confirms termination
   - API call: `PUT /api/p2/serviceproviders/{id}/terminate` with enhanced payload

## Acceptance Criteria

- [ ] Terminate row displays pending and terminated counts only
- [ ] No active count is shown for the terminate row
- [ ] Pending icon click opens dialog with pending terminations
- [ ] Terminated icon click opens dialog with terminated providers
- [ ] Start button opens the terminate selection dialog
- [ ] Terminate dialog includes all required form fields:
  - [ ] Service provider selection (radio buttons)
  - [ ] Termination date picker
  - [ ] Termination type selection (radio buttons)
  - [ ] Severance amount input (number)
  - [ ] Reason for termination (text area)
- [ ] Form validation ensures all required fields are completed
- [ ] API payload includes all new fields:
  - [ ] `terminationDate` (ISO date string)
  - [ ] `serviceProviderTerminationType` (enum value)
  - [ ] `severanceAmount` (number)
  - [ ] `reasonForTermination` (string)
- [ ] Counts update automatically after termination actions
- [ ] All dialogs follow the same UI patterns as other service provider dialogs

## API Implementation Requirements

### 1. Client API Methods
Add the following methods to `APIClient`:
- `getTerminatedServiceProviders(companyId: string)` - calls `/api/p2/serviceproviders/{companyId}/terminatedlist`
- `getPendingTerminatedServiceProviders(companyId: string)` - calls `/api/p2/serviceproviders/{companyId}/pendingterminatedlist`

### 2. Service Functions
Create service functions in `terminateServiceProvider.service.ts`:
- `getTerminatedServiceProviders(companyId: string)` - returns terminated service providers list
- `getPendingTerminatedServiceProviders(companyId: string)` - returns pending terminated service providers list

### 3. React Query Hooks
Create hooks in `useTerminate.hooks.ts`:
- `useTerminatedServiceProviders(companyId: string)` - for terminated list
- `usePendingTerminatedServiceProviders(companyId: string)` - for pending terminated list

### 4. Dialog Components
Create dialog components for displaying the lists:
- `TerminatedServiceProvidersDialog.tsx` - shows terminated service providers
- `PendingTerminatedServiceProvidersDialog.tsx` - shows pending terminated service providers

## API Payload Structure

### Enhanced Terminate API Payload
The `PUT /api/p2/serviceproviders/{id}/terminate` endpoint now expects:

```json
{
  "terminationDate": "2025-09-21",
  "serviceProviderTerminationType": "involuntary_termination_without_cause",
  "severanceAmount": 0,
  "reasonForTermination": "string"
}
```

### Termination Type Enum Values
- `involuntary_termination_without_cause` - Involuntary Termination (Without Cause)
- `involuntary_termination_with_cause` - Involuntary Termination (With Cause)
- `resignation` - Resignation

## Technical Notes

- The terminate row should be treated as a special case in the service provider items configuration
- Count extraction should handle the nested `terminated` object in the API response
- Dialog implementations should reuse existing patterns from other service provider status dialogs
- Query invalidation should update both the status counts and the terminate-specific data
- Service provider data includes additional fields like vesting information and severance amounts
- Form validation should ensure termination date is not in the future
- Severance amount should accept decimal values and be properly formatted
- Termination type selection should be required before allowing termination
