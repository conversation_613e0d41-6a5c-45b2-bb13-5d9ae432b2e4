# Enhanced Cap Table Implementation Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Updated Requirements with Terminate Functionality

## Overview

The Enhanced Cap Table feature provides a comprehensive table view with dual modes (Summary/Detailed), Stock Option Plan (SOP) integration, and administrative edit capabilities. This component displays detailed shareholder information including names, roles, share holdings, SOP shares, and fully-diluted ownership percentages in a structured table format with toggle views, edit functionality, and comprehensive termination capabilities.

## Goals

- Display comprehensive shareholder data with Summary/Detailed toggle views
- Integrate Stock Option Plan (SOP) data within the Cap Table tab
- Provide administrative edit capabilities for offline actions
- Support fully-diluted ownership calculations
- Implement comprehensive termination functionality with API integration
- Ensure responsive design for all device sizes
- Maintain consistency with existing cap table components

## UX Overview

- **View Toggle**: Prominent toggle between "Summary" and "Detailed" views
- **Table Layout**: Structured table with columns for Stockholder Name, Common Stock, Stock Option Plan, Total, and Fully-Diluted Ownership %
- **SOP Integration**: Pool summary rows at bottom of table (Outstanding, Promised, Available)
- **Edit Functionality**: Edit buttons for individual rows and SOP pool rows
- **Termination Functionality**: Comprehensive termination workflow with confirmation dialog
- **Responsive**: Horizontal scroll on mobile, full width on desktop
- **Interactive**: Tooltips for column explanations, hover states, edit modals, termination modals

---

## New Requirements Summary

### 1. UI Toggle: Summary vs Detailed View
- **Location**: Prominent toggle in Cap Table tab header
- **Summary View**: Collapses individual stockholders, aggregates Common Stock values
- **Detailed View**: Shows all individual stockholders and SOP pool rows

### 2. Add Grant Button
- **Location**: Left side of Summary/Detailed toggle on the same line
- **Functionality**: Opens empty modal for adding new grants
- **Design**: Standard button styling consistent with app theme

### 3. Remove "Total Shares" Display
- **Action**: Remove "Total Shares: 300,000" from top right of Cap Table header
- **Location**: `CapTableHeader.tsx` or similar component

### 4. Auto-Generated Results with Edit Capability
- **Data Source**: Auto-generated from incorporation, grants, vesting schedules
- **Edit Scope**: Administrative override for offline actions
- **Edit Interface**: Modal-based editing for individual rows and SOP pool rows

### 5. Enhanced Action Menu System
- **Individual Stockholders**: Replace pencil icons with three vertical dots (⋮) dropdown menu
- **Dropdown Options**: Edit Grant, Terminate (with appropriate icons)
- **Pool Summary**: Remove all edit icons (read-only section)

### 6. Simplified Edit Modal
- **Remove Fields**: Terminated and Exercised checkboxes
- **Keep Fields**: Name (read-only), Common Stock, SOP Shares
- **Functionality**: Same modal opens from both pencil icon and dropdown "Edit" option

### 7. Add Vested Shares Column
- **Position**: After "Unvested Shares" column
- **Data Display**: Show "N/A" when no data available
- **Consistency**: Both Vested and Unvested shares show "N/A" for missing data

### 8. Comprehensive Termination Functionality [NEW]
- **API Integration**: POST `/api/p2/companies/{companyId}/captable/terminate`
- **Modal Interface**: Professional termination confirmation dialog
- **Required Fields**: Date picker, termination type selection, confirmation
- **Payload Structure**: Complete API payload with all required fields
- **Error Handling**: Loading states, validation, and user feedback

### 8. Comprehensive Termination Functionality [NEW]
- **API Integration**: POST `/api/p2/companies/{companyId}/captable/terminate`
- **Modal Interface**: Professional termination confirmation dialog
- **Required Fields**: Date picker, termination type selection, confirmation
- **Payload Structure**: Complete API payload with all required fields
- **Error Handling**: Loading states, validation, and user feedback

### 9. Global Filter Integration (New Requirements)
- **Filter System**: Integrate existing CapTableFilters component for real-time filtering
- **Search Functionality**: Real-time shareholder name search with debounced API calls
- **Role Filter Dropdown**: Filter by shareholder roles (All, Founders, Investors, Advisors, Contractors, Employees)
- **Share Class Filter**: Filter by share classes (All, Common, SeriesAPreferred, StockOptionPool)
- **Global State Management**: Use existing GlobalFilterContext
- **API Integration**: Add filter parameters to table data API
- **Debounce Implementation**: 500ms delay on filter changes to prevent excessive API calls
- **Performance Optimization**: Matches Pro Forma table debounce behavior

---

## Enhanced Data Model

### Updated Type Definitions
Update `src/types/capTable.ts`:

```ts
// Actual API Response Types (from getCapTableList endpoint)
interface CapTableListApiResponse {
  message: string;
  data: {
    latestUpdatedDate: string | null;
    data: CapTableListItemApi[];
    outstandingStockOptionPlanShares: SOPPoolItemApi;
    promisedStockOptionPlanShares: SOPPoolItemApi;
    stockOptionPlanSharesAvailable: SOPPoolItemApi;
  };
  error: null;
  validationErrors: null;
}

interface CapTableListItemApi {
  id: string;
  serviceProviderId: string | null;
  officerId: string;
  name: string;
  role: string;
  common: number;                  // API field name (different from UI)
  stockOption: number;             // API field name (different from UI)
  totalShares: number;
  fullyDilutedOwnership: number;
  isTerminated: boolean;
}

interface SOPPoolItemApi {
  stockOptionPlan: number;
  totalShares: number;
  fullyDilutedOwnership: number;
}

// UI Component Types (for Enhanced Cap Table)
interface EnhancedCapTableListItem {
  id: string;
  name: string;
  role: string;
  commonStock: number;             // UI field name (transformed from API)
  stockOptionPlan: number;         // UI field name (transformed from API)
  totalShares: number;
  fullyDilutedOwnership: number;
  isTerminated: boolean;
  unvestedShares: string;          // "No data from API" - placeholder for future
  isExercised: string;             // "No data from API" - placeholder for future
  officerId: string;               // For API operations
  serviceProviderId: string | null; // For API operations
}

interface SOPPoolSummary {
  outstandingSOPShares: number;    // Transformed from API
  promisedSOPShares: number;       // Transformed from API
  availableSOPShares: number;      // Transformed from API
  totalAuthorizedSOP: number;      // Calculated from API data
}

interface EnhancedCapTableData {
  stockholders: EnhancedCapTableListItem[];
  sopPool: SOPPoolSummary;
  lastUpdated: string;             // From API or current date if null
}

// Edit Modal Types
interface EditCapTableItem {
  id: string;
  name: string;
  commonStock: number;
  stockOptionPlan: number;
  isTerminated: boolean;
  unvestedShares: string;          // "No data from API" - placeholder
  isExercised: string;             // "No data from API" - placeholder
}

interface EditSOPPoolItem {
  outstandingSOPShares: number;
  promisedSOPShares: number;
  availableSOPShares: number;
}

// Termination Types [NEW]
interface TerminateCapTableItemPayload {
  officerId: string;
  serviceProviderId: string | null;
  terminationDate: string;
  severanceAmount: number;
  serviceProviderTerminationType: string;
}

interface TerminateCapTableItem {
  id: string;
  name: string;
  role: string;
  officerId: string;
  serviceProviderId: string | null;
}

// View Toggle State
type CapTableViewMode = 'summary' | 'detailed';
```

---

## Enhanced Component Architecture

### Updated Entry Point
- `components/captable/CapTableGrid.tsx` (major updates required)
  - **Purpose**: Main table component with Summary/Detailed toggle, SOP integration, and termination functionality
  - **Props**: API data, view mode, edit callbacks, termination callbacks
  - **State**: Loading, error, view mode, edit modal states, termination modal states
  - **Children**: Toggle, table headers, individual rows, SOP pool rows, edit modals, termination modals
  - **API Integration**: Edit stockholder functionality with POST endpoint, terminate functionality with POST endpoint

### Enhanced Component Structure
```
CapTableGrid.tsx
├── Card (container)
├── CardHeader
│   ├── CardTitle ("Cap Table")
│   ├── Add Grant Button (left side)
│   ├── View Toggle (Summary/Detailed)
│   └── Last Updated Date
├── CardContent
│   ├── Table Container
│   │   ├── TableHeader
│   │   │   ├── Stockholder Name Column
│   │   │   ├── Common Stock Column (with tooltip)
│   │   │   ├── Stock Option Plan Column (with tooltip)
│   │   │   ├── Total Column (with tooltip)
│   │   │   ├── Fully-Diluted Ownership % Column (with tooltip)
│   │   │   ├── Unvested Shares Column (with tooltip)
│   │   │   ├── Vested Shares Column (with tooltip) [NEW]
│   │   │   └── Action Column (dropdown menu)
│   │   └── TableBody
│   │       ├── Individual Stockholder Rows (Detailed view only)
│   │       ├── Aggregated Stockholder Row (Summary view only)
│   │       ├── SOP Pool Summary Rows (both views, read-only)
│   │       │   ├── Outstanding SOP Securities
│   │       │   ├── Promised SOP Securities
│   │       │   └── SOP Securities Available
│   │       └── Totals Row
│   ├── Modals
│   │   ├── Stockholder Edit Modal (simplified)
│   │   ├── Add Grant Modal (empty)
│   │   ├── Terminate Stockholder Modal [NEW]
│   │   └── Action Dropdown Menu
│   └── Empty State (when no data)
```

### New Shared Components
- `Toggle`, `ToggleGroup` from `@/components/ui/toggle` (for Summary/Detailed toggle)
- `Dialog`, `DialogContent`, `DialogHeader`, `DialogTitle` from `@/components/ui/dialog` (for edit modals)
- `Input`, `Label` from `@/components/ui/input` (for edit forms)
- `Button` variants for edit actions and Add Grant button
- `Badge` for role display and status indicators
- `DropdownMenu`, `DropdownMenuTrigger`, `DropdownMenuContent`, `DropdownMenuItem` from `@/components/ui/dropdown-menu` (for action menu)
- `MoreVertical`, `Edit2`, `Trash2` icons from `lucide-react` (for dropdown menu items)
- `RadioGroup`, `RadioGroupItem` from `@/components/ui/radio-group` (for termination type selection)
- `Calendar` icon from `lucide-react` (for date picker)

---

## Enhanced UI/UX Requirements

### API Integration Requirements
- **Data Transformation**: Transform API response fields to match UI expectations
- **Field Mapping**: 
  - API `common` → UI `commonStock`
  - API `stockOption` → UI `stockOptionPlan`
- **Missing Fields**: Display "No data from API" for `unvestedShares` and `isExercised`
- **Last Updated**: Use API `latestUpdatedDate` or current date if null
- **Loading States**: Show skeleton loader during API calls
- **Fallback Strategy**: Use static demo data when API is unavailable or returns invalid structure
- **Error Handling**: Gracefully fallback to static data instead of showing error messages
- **Global Filter Support**: Add filter parameters to `getCapTableList` API call
  - `proFormaRoleFilter`: Filter by shareholder role
  - `proFormaShareClassFilter`: Filter by share class
  - `shareHolderName`: Search by shareholder name

### View Toggle Design
- **Location**: Prominent placement in Cap Table header, right-aligned
- **Style**: Toggle group with "Summary" and "Detailed" options
- **State**: Clear visual indication of current view
- **Accessibility**: Proper ARIA labels and keyboard navigation

### Enhanced Table Design
- **Layout**: Clean, modern table with subtle borders and alternating row colors
- **Headers**: Bold column headers with tooltips for explanations
- **Responsive**: Horizontal scroll on mobile devices
- **SOP Pool Section**: Light gray background to distinguish from individual stockholders
- **Placeholder Text**: "No data from API" for missing fields (visual indicator for future API integration)

### Updated Column Specifications

#### **Stockholder Name Column:**
- **Left-aligned text**
- **Font weight**: Medium
- **Truncation**: For long names with tooltip
- **Summary View**: Shows "Aggregated Stockholders" instead of individual names
- **Data Source**: API `name` field

#### **Common Stock Column:**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Common shares held by this shareholder"
- **Editable**: Yes, via edit modal
- **Data Source**: API `common` field (transformed to `commonStock`)

#### **Stock Option Plan Column (NEW):**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Stock option plan shares held by this shareholder"
- **Editable**: Yes, via edit modal
- **SOP Pool Rows**: Shows calculated values for Outstanding, Promised, Available
- **Data Source**: API `stockOption` field (transformed to `stockOptionPlan`)

#### **Total Column:**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Total shares (Common Stock + Stock Option Plan)"
- **Calculation**: Sum of Common Stock + Stock Option Plan
- **Editable**: No (calculated field)
- **Data Source**: API `totalShares` field

#### **Fully-Diluted Ownership % Column:**
- **Right-aligned percentages**
- **Formatting**: Two decimal places
- **Tooltip**: "Percentage ownership of total company shares"
- **Calculation**: (Total Shares / Total Company Shares) × 100
- **Editable**: No (calculated field)
- **Data Source**: API `fullyDilutedOwnership` field

#### **Vesting Status Columns:**
- **Unvested Shares**: Display "N/A" when no data available
- **Vested Shares**: Display "N/A" when no data available (NEW COLUMN)
- **Position**: Vested Shares column after Unvested Shares column

### SOP Pool Summary Rows
- **Background**: Light gray to distinguish from individual stockholders
- **Outstanding SOP Securities**: Shows issued options minus exercised
- **Promised SOP Securities**: Shows promised grants amount
- **SOP Securities Available**: Shows available SOP shares
- **Editable**: No (read-only section)
- **Action Icons**: Removed edit icons from all pool summary rows
- **Column Data**:
  - **Common Stock**: "-" (not applicable to pool summary)
  - **Stock Option Plan**: Actual API data from respective pool sections
  - **Total**: Same as Stock Option Plan value (for pool summary)
  - **Fully-Diluted Ownership %**: Actual API data from respective pool sections
  - **Unvested Shares**: "-" (not applicable to pool summary)
  - **Vested Shares**: "-" (not applicable to pool summary)
- **Data Sources**:
  - **Outstanding**: 
    - Stock Option Plan: API `outstandingStockOptionPlanShares.stockOptionPlan`
    - Total: Same as Stock Option Plan
    - Fully-Diluted Ownership: API `outstandingStockOptionPlanShares.fullyDilutedOwnership`
  - **Promised**: 
    - Stock Option Plan: API `promisedStockOptionPlanShares.stockOptionPlan`
    - Total: Same as Stock Option Plan
    - Fully-Diluted Ownership: API `promisedStockOptionPlanShares.fullyDilutedOwnership`
  - **Available**: 
    - Stock Option Plan: API `stockOptionPlanSharesAvailable.stockOptionPlan`
    - Total: Same as Stock Option Plan
    - Fully-Diluted Ownership: API `stockOptionPlanSharesAvailable.fullyDilutedOwnership`
  - **Total Authorized**: Calculated sum of all three Stock Option Plan values

### Edit Functionality Design

#### **Action Menu System:**
- **Individual Stockholders**: Three vertical dots (⋮) dropdown menu
- **Dropdown Options**: 
  - Edit Grant (pencil icon)
  - Terminate (trash icon, red text)
- **SOP Pool Rows**: No action icons (read-only)

#### **Edit Modal Design:**
- **Stockholder Edit Modal**:
  - Stockholder name (read-only)
  - Common Stock input field
  - Stock Option Plan input field
  - Save/Cancel buttons
  - **Removed**: Termination status checkbox, Unvested shares input, Exercise status checkbox
  - **API Integration**: POST `/api/p2/companies/{companyId}/captable`
  - **Payload**: 
    - `officerId`: From selected stockholder row data
    - `serviceProviderId`: From selected stockholder row data
    - `commonStock`: From modal input
    - `sopShares`: From modal input (Stock Option Plan value)

- **Add Grant Modal**:
  - Empty modal for adding new grants
  - Same fields as edit modal but empty
  - Save/Cancel buttons

- **SOP Pool Edit Modal**: (REMOVED - no longer needed)

#### **Terminate Modal Design [NEW]:**
- **Modal Title**: "Terminate Stockholder"
- **Modal Description**: Clear explanation of what will happen
- **Stockholder Information Display**:
  - Name (read-only, from selected stockholder)
  - Role (read-only, from selected stockholder)
- **Required Input Fields**:
  - **Termination Date**: Date picker input (default: current date)
    - Type: `date` input
    - Validation: Required, cannot be future date
    - Format: YYYY-MM-DD
  - **Severance Amount**: Number input field (default: 0)
    - Type: `number` input
    - Validation: Required, minimum value 0
    - Format: Currency amount (no currency symbol in input)
    - Step: 0.01 for decimal precision
  - **Termination Type**: Radio button selection (required)
    - Options:
      - `involuntary-termination-without-cause` - "Involuntary Termination (Without Cause)"
      - `involuntary-termination-with-cause` - "Involuntary Termination (With Cause)"
      - `voluntary-termination` - "Voluntary Termination"
      - `resignation` - "Resignation"
      - `retirement` - "Retirement"
      - `death` - "Death"
      - `disability` - "Disability"
- **Confirmation Section**: Warning about irreversible action
- **Action Buttons**:
  - Cancel (outline variant)
  - Terminate (destructive variant, red)
- **Loading States**: Show spinner and "Terminating..." text during API call
- **API Integration**: POST `/api/p2/companies/{companyId}/captable/terminate`
- **Payload Structure**:
  ```json
  {
    "officerId": "from-selected-stockholder.officerId",
    "serviceProviderId": "from-selected-stockholder.serviceProviderId",
    "terminationDate": "from-date-picker-input",
    "severanceAmount": "from-number-input-field",
    "serviceProviderTerminationType": "from-radio-button-selection"
  }
  ```

#### **Terminate Modal Design [NEW]:**
- **Modal Title**: "Terminate Stockholder"
- **Modal Description**: Clear explanation of what will happen
- **Stockholder Information Display**:
  - Name (read-only, from selected stockholder)
  - Role (read-only, from selected stockholder)
- **Required Input Fields**:
  - **Termination Date**: Date picker input (default: current date)
    - Type: `date` input
    - Validation: Required, cannot be future date
    - Format: YYYY-MM-DD
  - **Severance Amount**: Number input field (default: 0)
    - Type: `number` input
    - Validation: Required, minimum value 0
    - Format: Currency amount (no currency symbol in input)
    - Step: 0.01 for decimal precision
  - **Termination Type**: Radio button selection (required)
    - Options:
      - `involuntary-termination-without-cause` - "Involuntary Termination (Without Cause)"
      - `involuntary-termination-with-cause` - "Involuntary Termination (With Cause)"
      - `voluntary-termination` - "Voluntary Termination"
      - `resignation` - "Resignation"
      - `retirement` - "Retirement"
      - `death` - "Death"
      - `disability` - "Disability"
- **Confirmation Section**: Warning about irreversible action
- **Action Buttons**:
  - Cancel (outline variant)
  - Terminate (destructive variant, red)
- **Loading States**: Show spinner and "Terminating..." text during API call
- **API Integration**: POST `/api/p2/companies/{companyId}/captable/terminate`
- **Payload Structure**:
  ```json
  {
    "officerId": "from-selected-stockholder.officerId",
    "serviceProviderId": "from-selected-stockholder.serviceProviderId",
    "terminationDate": "from-date-picker-input",
    "severanceAmount": "from-number-input-field",
    "serviceProviderTerminationType": "from-radio-button-selection"
  }
  ```

#### **Edit Validation:**
- **Numeric Validation**: All share amounts must be non-negative
- **Business Logic**: Unvested shares cannot exceed total shares
- **Real-time Feedback**: Validation messages and field highlighting

#### **Termination Validation [NEW]:**
- **Date Validation**: Termination date cannot be in the future
- **Severance Amount Validation**: Must be a valid number, minimum value 0
- **Required Fields**: All fields must be filled before submission
- **Confirmation**: User must acknowledge the irreversible nature of the action

#### **Termination Validation [NEW]:**
- **Date Validation**: Termination date cannot be in the future
- **Severance Amount Validation**: Must be a valid number, minimum value 0
- **Required Fields**: All fields must be filled before submission
- **Confirmation**: User must acknowledge the irreversible nature of the action

### Interactive Features
- **Hover States**: Row highlighting on hover
- **Tooltips**: Information icons with explanatory text
- **Edit States**: Visual feedback during editing
- **Loading States**: Disable interactions during save operations
- **Termination States**: Visual feedback during termination process

### Responsive Design
- **Mobile**: Horizontal scroll with sticky first column
- **Tablet**: Full table with adjusted spacing
- **Desktop**: Full table with optimal spacing
- **Breakpoints**: 768px, 1024px, 1440px

---

## Enhanced State Management

### API Integration State
```ts
const [capTableData, setCapTableData] = useState<EnhancedCapTableData | null>(null);
const [isLoading, setIsLoading] = useState(true);
const [error, setError] = useState<string | null>(null);
```

### View Mode State
```ts
const [viewMode, setViewMode] = useState<CapTableViewMode>('detailed');
```

### Edit Modal States
```ts
const [editStockholderModal, setEditStockholderModal] = useState<{
  isOpen: boolean;
  data: EditCapTableItem | null;
}>();

const [editSOPModal, setEditSOPModal] = useState<{
  isOpen: boolean;
  data: EditSOPPoolItem | null;
}>();
```

### Termination Modal States [NEW]
```ts
const [terminateModal, setTerminateModal] = useState<{
  isOpen: boolean;
  stockholder: TerminateCapTableItem | null;
}>();

const [terminationForm, setTerminationForm] = useState<{
  terminationDate: string;
  serviceProviderTerminationType: string;
}>({
  terminationDate: new Date().toISOString().split('T')[0],
  serviceProviderTerminationType: 'involuntary-termination-without-cause'
});
```

### Loading States
- **Initial Load**: Skeleton loader for table rows
- **API Data Loading**: Disable interactions, show loading indicator during API calls
- **Edit Operations**: Loading state during save operations
- **Termination Operations**: Loading state during termination operations [NEW]
- **Fallback State**: Use static demo data when API fails or returns invalid structure
- **Empty State**: Show appropriate message when no data from API
- **Missing Data State**: Display "N/A" for fields not yet available

### Error Handling
- **API Errors**: Gracefully fallback to static demo data instead of showing error messages
- **Edit Validation Errors**: Field-specific error highlighting
- **Termination Validation Errors**: Field-specific error highlighting [NEW]
- **Network Errors**: Use static data when API calls fail
- **Fallback UI**: Graceful degradation when table fails to render
- **Data Transformation Errors**: Use static data when API response structure is invalid

---

## Enhanced Accessibility Requirements

### Keyboard Navigation
- **Focus Management**: Proper tab order through table cells and edit modals
- **Table Navigation**: Arrow keys for cell navigation
- **Toggle Navigation**: Space/Enter for view toggle
- **Edit Modal Navigation**: Tab order through form fields
- **Termination Modal Navigation**: Tab order through form fields [NEW]

### Screen Reader Support
- **ARIA Labels**: Descriptive labels for all interactive elements
- **Table Structure**: Proper table headers and row associations
- **Status Announcements**: Loading, error, edit states, termination states
- **Column Descriptions**: Screen reader accessible column explanations
- **View Mode Changes**: Announce view mode changes
- **Termination Actions**: Announce termination confirmation and status [NEW]

### Visual Accessibility
- **Color Contrast**: WCAG AA compliant color ratios
- **Focus Indicators**: Clear visual focus states
- **Text Scaling**: Support for browser text scaling
- **High Contrast**: Compatible with high contrast mode

---

## Enhanced Performance Requirements

### Loading Performance
- **Initial Load**: < 2 seconds for data retrieval
- **Table Rendering**: < 500ms for table display
- **View Toggle**: < 200ms for view mode changes
- **Edit Modal**: < 100ms for modal opening
- **Termination Modal**: < 100ms for modal opening [NEW]

### Optimization
- **Memoization**: React.memo for expensive table calculations
- **Lazy Loading**: Load edit modals and complex components
- **Efficient Rendering**: Optimized table row rendering
- **State Updates**: Minimal re-renders during edit operations
- **Termination Operations**: Efficient state updates during termination [NEW]

---

## Implementation Plan

### Phase 1: API Integration & Data Transformation
1. **Update type definitions** to match actual API response structure
2. **Create data transformation function** to map API fields to UI expectations
3. **Integrate getCapTableList API call** into CapTableGrid component
4. **Handle missing fields** with "N/A" placeholders for vesting data
5. **Implement loading and error states** for API integration

### Phase 2: Enhanced UI Components
1. **Add "Add Grant" button** to header (left side of toggle)
2. **Add Vested Shares column** after Unvested Shares column
3. **Replace pencil icons** with three vertical dots dropdown menu
4. **Remove edit icons** from SOP pool summary rows
5. **Update table structure** to accommodate new column

### Phase 3: Basic Structure Updates
1. **Remove "Total Shares" display** from Cap Table header
2. **Add Summary/Detailed toggle** to Cap Table header
3. **Update table columns** to match new structure (Stockholder Name, Common Stock, Stock Option Plan, Total, Fully-Diluted Ownership, Unvested Shares, Vested Shares)
4. **Implement view mode state management**

### Phase 4: SOP Integration
1. **Add SOP Pool Summary rows** at bottom of table (read-only)
2. **Implement SOP calculations** for Outstanding, Promised, Available shares
3. **Style SOP section** with distinct background
4. **Add SOP-specific tooltips and explanations**

### Phase 5: Edit Functionality
1. **Create simplified edit modal** for stockholders (removed terminated/exercised fields)
2. **Create Add Grant modal** (empty modal)
3. **Implement dropdown menu** with Edit, Terminate options
4. **Add save/cancel functionality** with loading states and toast notifications
5. **Integrate edit API** with POST endpoint for stockholder updates
6. **Create/Update service files** for API integration
7. **Update hooks** for edit functionality

### Phase 6: Termination Functionality [NEW]
1. **Create terminate service** for API operations
2. **Create terminate modal** with all required fields
3. **Implement termination API integration** with POST endpoint
4. **Add validation and error handling** for termination process
5. **Implement loading states and user feedback** for termination operations
6. **Add confirmation workflow** with proper user warnings
7. **Integrate with existing edit hook** for consistent state management

### Phase 6: Termination Functionality [NEW]
1. **Create terminate service** for API operations
2. **Create terminate modal** with all required fields
3. **Implement termination API integration** with POST endpoint
4. **Add validation and error handling** for termination process
5. **Implement loading states and user feedback** for termination operations
6. **Add confirmation workflow** with proper user warnings
7. **Integrate with existing edit hook** for consistent state management

### Phase 7: Global Filter Integration [NEW]
1. **Update API client** to support filter parameters for getCapTableList
2. **Update service layer** to pass filter parameters to API
3. **Update hook layer** to include filters in query keys and API calls with debounce
4. **Integrate CapTableFilters component** into CapTableGrid
5. **Connect global filter state** using useGlobalFilters hook
6. **Test filter functionality** with real API data

### Phase 8: Summary View Implementation
1. **Implement Summary view logic** to aggregate stockholders
2. **Hide individual stockholders** in Summary view
3. **Show aggregated Common Stock** values
4. **Maintain SOP pool rows** in both views

### Phase 8: Testing & Polish
1. **Add comprehensive unit and integration tests**
2. **Performance optimization** and bundle analysis
3. **Accessibility audit** and improvements
4. **User testing** and feedback integration

---

## Success Metrics

### User Experience
- **Load Time**: Table loads in < 2 seconds
- **Toggle Response**: View changes in < 200ms
- **Edit Operations**: Save operations complete in < 1 second
- **Termination Operations**: Termination operations complete in < 1 second [NEW]
- **Error Rate**: < 1% API error rate

### Technical Metrics
- **Bundle Impact**: < 50KB additional bundle size
- **API Performance**: < 500ms average response time
- **Cache Hit Rate**: > 80% cache utilization
- **Edit Success Rate**: > 95% successful edit operations
- **Termination Success Rate**: > 95% successful termination operations [NEW]
- **Termination Success Rate**: > 95% successful termination operations [NEW]

### Accessibility Metrics
- **WCAG Compliance**: AA level compliance achieved
- **Screen Reader**: 100% functionality with screen readers
- **Keyboard Navigation**: Full keyboard accessibility
- **View Mode Accessibility**: 100% accessible view toggle
- **Termination Accessibility**: 100% accessible termination workflow [NEW]
- **Termination Accessibility**: 100% accessible termination workflow [NEW]

---

## Dependencies

### External Dependencies
- `@tanstack/react-query`: Data fetching and caching
- `lucide-react`: Icon library for consistent iconography

### Internal Dependencies
- `@/components/ui/card`: Card components for layout
- `@/components/ui/table`: Table components
- `@/components/ui/tooltip`: Tooltip components
- `@/components/ui/toggle`: Toggle components for view switching
- `@/components/ui/dialog`: Dialog components for edit modals
- `@/components/ui/input`: Input components for edit forms
- `@/components/ui/button`: Button components for actions
- `@/components/ui/badge`: Badge components for status display
- `@/components/ui/radio-group`: Radio group components for termination type selection [NEW]
- `@/integrations/legal-concierge/client`: API client (getCapTableList method, updateCapTableItem method, terminateCapTableItem method [NEW])
- `@/hooks/useAuth`: Authentication context
- `@/hooks/useCapTableList`: Custom hook for cap table data fetching
- `@/hooks/useCapTableEdit`: Custom hook for cap table edit operations (to be created)
- `@/services/cap-table/capTableEdit.service.ts`: Service for edit API operations (to be created)
- `@/services/cap-table/capTableTerminate.service.ts`: Service for termination API operations [NEW]
- `sonner`: Toast notification library

---

## Detailed Implementation Guide

### File Structure & Locations

#### **1. Service File Creation**
**Location**: `src/services/cap-table/capTableEdit.service.ts`
**Purpose**: Handle API operations for editing cap table items

```typescript
// src/services/cap-table/capTableEdit.service.ts
import { apiClient } from '@/integrations/legal-concierge/client';
import { EditCapTableItem } from '@/types/capTable';

export interface UpdateCapTableItemPayload {
  officerId: string;
  serviceProviderId: string | null;
  commonStock: number;
  sopShares: number;
}

export const updateCapTableItem = async (
  companyId: string, 
  payload: UpdateCapTableItemPayload
): Promise<any> => {
  const response = await apiClient.post(
    `/api/p2/companies/${companyId}/captable`,
    payload
  );
  return response;
};
```

#### **2. Termination Service File Creation [NEW]**
**Location**: `src/services/cap-table/capTableTerminate.service.ts`
**Purpose**: Handle API operations for terminating cap table items

```typescript
// src/services/cap-table/capTableTerminate.service.ts
import { apiClient } from '@/integrations/legal-concierge/client';
import { TerminateCapTableItemPayload } from '@/types/capTable';

export const terminateCapTableItem = async (
  companyId: string, 
  payload: TerminateCapTableItemPayload
): Promise<any> => {
  const response = await apiClient.post(
    `/api/p2/companies/${companyId}/captable/terminate`,
    payload
  );
  return response;
};
```

#### **3. Hook File Creation**
**Location**: `src/hooks/cap-table/useCapTableEdit.ts`
**Purpose**: Manage edit operations with loading states and toast notifications

```typescript
// src/hooks/cap-table/useCapTableEdit.ts
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { updateCapTableItem, UpdateCapTableItemPayload } from '@/services/cap-table/capTableEdit.service';
import { terminateCapTableItem, TerminateCapTableItemPayload } from '@/services/cap-table/capTableTerminate.service';
import { EnhancedCapTableListItem } from '@/types/capTable';

export const useCapTableEdit = () => {
  const { user } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isTerminating, setIsTerminating] = useState(false);

  const updateStockholder = async (
    stockholder: EnhancedCapTableListItem,
    editData: { commonStock: number; stockOptionPlan: number }
  ) => {
    if (!user?.companyId) {
      toast.error('No company selected');
      return false;
    }

    setIsUpdating(true);
    
    try {
      const payload: UpdateCapTableItemPayload = {
        officerId: stockholder.officerId || stockholder.id,
        serviceProviderId: stockholder.serviceProviderId,
        commonStock: editData.commonStock,
        sopShares: editData.stockOptionPlan,
      };

      await updateCapTableItem(user.companyId, payload);
      
      toast.success('Stockholder updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating stockholder:', error);
      toast.error('Failed to update stockholder. Please try again.');
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  const terminateStockholder = async (
    stockholder: EnhancedCapTableListItem,
    payload: {
      terminationDate: string;
      serviceProviderTerminationType: string;
    }
  ) => {
    if (!user?.companyId) {
      toast.error('No company selected');
      return false;
    }

    setIsTerminating(true);
    
    try {
      const terminatePayload: TerminateCapTableItemPayload = {
        officerId: stockholder.officerId || stockholder.id,
        serviceProviderId: stockholder.serviceProviderId,
        terminationDate: payload.terminationDate,
        serviceProviderTerminationType: payload.serviceProviderTerminationType,
      };

      await terminateCapTableItem(user.companyId, terminatePayload);
      
      toast.success('Stockholder terminated successfully');
      return true;
    } catch (error) {
      console.error('Error terminating stockholder:', error);
      toast.error('Failed to terminate stockholder. Please try again.');
      return false;
    } finally {
      setIsTerminating(false);
    }
  };

  return {
    updateStockholder,
    terminateStockholder,
    isUpdating,
    isTerminating,
  };
};
```

#### **4. Component Updates**
**Location**: `src/components/captable/CapTableGrid.tsx`
**Updates Required**:

```typescript
// Import new dependencies
import { useCapTableEdit } from '@/hooks/cap-table/useCapTableEdit';
import { toast } from 'sonner';
import TerminateStockholderDialog from './TerminateStockholderDialog';

// Add hook usage
const { updateStockholder, terminateStockholder, isUpdating, isTerminating } = useCapTableEdit();

// Add state for terminate dialog
const [terminateDialog, setTerminateDialog] = useState<{
  isOpen: boolean;
  stockholder: EnhancedCapTableListItem | null;
}>({ isOpen: false, stockholder: null });

// Update handleTerminate function
const handleTerminate = (stockholder: EnhancedCapTableListItem) => {
  setTerminateDialog({ isOpen: true, stockholder });
};

// Add dialog to JSX
return (
  <>
    {/* Existing table JSX */}
    
    {/* Add this at the end */}
    <TerminateStockholderDialog
      isOpen={terminateDialog.isOpen}
      stockholder={terminateDialog.stockholder}
      onClose={() => setTerminateDialog({ isOpen: false, stockholder: null })}
      onSuccess={() => {
        refetchList(); // Refetch data after termination
      }}
    />
  </>
);
```

#### **5. API Client Update**
**Location**: `src/integrations/legal-concierge/client.ts`
**Update Required**: Add POST method if not exists

```typescript
// Add to APIClient class if not exists
async post(endpoint: string, data: any): Promise<any> {
  const response = await fetch(`${this.baseURL}${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...this.getAuthHeaders(),
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response.json();
}
```

### Loading States & Toast Notifications

#### **Loading States**:
- **Save Button**: Shows spinner and "Saving..." text during API call
- **Button Disabled**: Prevents multiple submissions
- **Modal Disabled**: Prevents interaction during save operation
- **Terminate Button**: Shows spinner and "Terminating..." text during API call [NEW]

#### **Toast Notifications**:
- **Success**: "Stockholder updated successfully" (green)
- **Error**: "Failed to update stockholder. Please try again." (red)
- **Validation**: "No company selected" (red)
- **Termination Success**: "Stockholder terminated successfully" (green) [NEW]
- **Termination Error**: "Failed to terminate stockholder. Please try again." (red) [NEW]

#### **Error Handling**:
- **Network Errors**: Graceful fallback with user-friendly messages
- **Validation Errors**: Clear feedback for invalid data
- **API Errors**: Proper error logging and user notification
- **Termination Errors**: Proper error logging and user notification [NEW]

---

## Risk Assessment

### Technical Risks
- **Complex State Management**: Multiple edit modals and view modes may increase complexity
- **Performance Impact**: SOP calculations and view toggles may impact performance
- **Bundle Size**: Additional UI components may increase bundle size
- **API Integration**: Data transformation complexity and field mapping errors
- **Missing Data Handling**: Placeholder text may confuse users about data availability
- **Termination Complexity**: Additional API integration and state management [NEW]

### Mitigation Strategies
- **Efficient State Management**: Use React Context or state machines for complex state
- **Performance Optimization**: Memoize calculations and optimize re-renders
- **Code Splitting**: Lazy load edit modals and complex components
- **Data Transformation**: Comprehensive testing of API response mapping
- **Clear Documentation**: Document which fields are placeholders vs real data
- **Termination Testing**: Comprehensive testing of termination workflow [NEW]

### Business Risks
- **Data Accuracy**: Complex SOP calculations may lead to errors
- **User Confusion**: Multiple view modes may confuse users
- **Edit Errors**: Administrative edits may introduce data inconsistencies
- **API Changes**: Backend API structure changes may break frontend integration
- **Termination Errors**: Incorrect termination data may affect legal compliance [NEW]

### Mitigation Strategies
- **Data Validation**: Comprehensive validation of all edit operations
- **User Testing**: Extensive user testing of view modes and edit functionality
- **Audit Trail**: Log all edit operations for accountability
- **API Versioning**: Ensure API changes are backward compatible or versioned
- **Termination Validation**: Comprehensive validation of termination data [NEW]
- **Legal Review**: Ensure termination workflow meets legal requirements [NEW]

---

## Conclusion

The Enhanced Cap Table feature provides a comprehensive, professional-grade interface for cap table management with dual view modes, SOP integration, administrative edit capabilities, and comprehensive termination functionality. The implementation follows established patterns in the codebase while introducing sophisticated new functionality.

### Key Integration Points:
- **API Integration**: Uses `getCapTableList` endpoint with data transformation
- **Edit API Integration**: Uses POST `/api/p2/companies/{companyId}/captable` for stockholder updates
- **Termination API Integration**: Uses POST `/api/p2/companies/{companyId}/captable/terminate` for stockholder termination [NEW]
- **Field Mapping**: Maps API fields (`common` → `commonStock`, `stockOption` → `stockOptionPlan`)
- **Missing Data Handling**: Displays "N/A" for vesting data (`unvestedShares`, `vestedShares`)
- **SOP Pool Integration**: Transforms API SOP structure to UI expectations (read-only)
- **Fallback Strategy**: Uses static demo data when API is unavailable or returns invalid structure
- **Enhanced Actions**: Dropdown menu system for individual stockholders with Edit, Terminate options
- **Add Grant Functionality**: Empty modal for adding new grants
- **Comprehensive Termination**: Professional termination workflow with all required fields and API integration [NEW]

The feature enhances the cap table experience by providing flexible viewing options, comprehensive SOP tracking, administrative control, and professional termination capabilities while maintaining the application's high quality standards and accessibility requirements.

This implementation will provide significant value to users by offering both high-level summary views and detailed individual tracking, along with the ability to make necessary administrative adjustments for offline actions and proper termination workflows. The API integration ensures real-time data while the placeholder system clearly indicates which features are planned for future API enhancements.

The termination functionality represents a critical business process that requires careful implementation with proper validation, user confirmation, and comprehensive error handling to ensure legal compliance and data integrity.
