# Stock Option Plan Tab Implementation Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Draft

## Overview

The Stock Option Plan tab provides a comprehensive tracking system for equity grants, from promise to exercise. This component modifies the existing Stock Option Pool UI to add new functionality while preserving the original design and structure, now with full API integration for real-time data and complete grant management capabilities including exercise, edit, and delete operations.

## Goals

- **Preserve Original UI**: Maintain the existing 3-card layout and table structure
- **Add Required Columns**: Grant Status and Grant Type columns as per documentation
- **Remove Specified Columns**: Current Value and % of Pool columns as per documentation
- **Add Edit Functionality**: Administrative edit capabilities for grant management
- **Support Exercise Workflow**: Seamless process for tracking option exercises with dual entries
- **Maintain Professional Design**: Keep the clean, modern interface suitable for financial applications
- **API Integration**: Real-time data from backend with proper loading states and error handling
- **Exercise Functionality**: Complete option exercise workflow with validation and dual entries
- **Edit Functionality**: Comprehensive grant editing capabilities for all fields
- **Delete Functionality**: Grant deletion with confirmation and validation

## UX Overview

- **Pool Metrics Section**: Preserve existing 3-card layout (Total Plan Size, Allocated Shares, Remaining Plan)
- **Pool Allocation Bar**: Keep existing visual allocation representation
- **Grant Table**: Modify existing table structure with new columns and removed columns
- **Edit Functionality**: Add modal-based editing for grant details
- **Exercise Workflow**: Add option exercise process with dual entry tracking
- **Professional Design**: Maintain existing clean, modern interface
- **API Integration**: Real-time data from backend with proper loading states

---

## Requirements Summary

### 1. API Integration
- **Summary Cards API**: `GET /api/p2/companies/{companyId}/stockoptionsplansummary`
- **Table Data API**: `GET /api/p2/companies/{companyId}/captable/optionpool` (with global filter support)
- **Edit Grant API**: `POST /api/p2/companies/{companyId}/captable/optionpool/update`
- **Exercise Option API**: `PUT /api/p2/companies/{companyId}/captable/optionpool/exercisestockoption`
- **Delete Grant API**: `DELETE /api/p2/companies/{companyId}/captable/optionpool`
- **Service Layer**: Create dedicated services for API calls
- **Hook Layer**: Create React Query hooks for data fetching and mutations
- **Data Transformation**: Transform API responses to component format
- **Global Filter Integration**: Add filter parameters to table data API for real-time filtering

### 2. Preserve Original Pool Metrics Display
- **Location**: Keep existing 3-card layout above the main grant table
- **Metrics**: 
  - **Total Plan Size** (changed from "Total Pool Size")
  - **Allocated Shares** (changed from "Allocated Options") 
  - **Remaining Plan** (changed from "Remaining Pool")
- **Visual Element**: Keep existing pool allocation bar
- **Styling**: Maintain existing professional financial dashboard style
- **Negative Values**: Display negative remaining plan in parentheses (e.g., -10000 shows as "(10000)")
- **API Data**: Use `getStockOptionInformation` API for summary metrics and percentages

### 3. Modify Existing Grant Table
- **Base Structure**: Keep existing table layout and design
- **New Columns**: 
  - Grant Status (Promised/Granted)
  - Grant Type (Restricted Stock/Option)
  - Vested Shares (number of shares currently vested)
  - Unvested Shares (number of shares not yet vested)
  - Vesting Schedule (schedule type with custom indicator)
- **Removed Columns**: Remove Current Value and % of Pool as per documentation
- **Preserved Columns**: Keep Recipient, Role, Grant Date, Options, Vesting Progress, Actions
- **Edit Functionality**: Add edit buttons for each grant row
- **API Data**: Use `optionPools` API for table data with enhanced vesting information

### 4. Exercise Workflow
- **Dual Entries**: Create separate entries for remaining options and exercised shares
- **Exercise Details**: Track number of shares exercised and exercise date
- **Vesting Handling**: Hide vesting progress for exercised shares, maintain for active grants
- **Validation**: Only allow exercise of vested shares as of exercise date
- **Grant Type**: Categorize exercised grants as "Exercised Option"
- **Multiple Exercises**: Support multiple exercise entries per person
- **Pool Impact**: Exercised options tracked as "restricted option inside pool" (no effect on total/outstanding pool size)

### 5. Edit Functionality
- **Comprehensive Editing**: Edit all grant fields (Recipient, Role, Grant Status, Grant Type, Options Granted)
- **Status Changes**: Allow changing from "Promised" to "Granted" and vice versa
- **Grant Type Restrictions**: 
  - "Exercised Option" grants should show "Exercised Option" as read-only in edit modal
  - "Exercised Option" grants should send `"exercise-stock"` in API payload
  - Other grant types remain editable
- **Real-time Updates**: All changes update collective parameters immediately
- **Validation**: Client-side validation for all editable fields

### 6. Delete Functionality
- **Universal Delete**: Delete button available on all grant rows
- **Confirmation**: Confirmation dialog before deletion
- **Validation**: Prevent deletion of grants with dependencies
- **Real-time Updates**: Immediate UI updates after deletion

### 7. Global Filter Integration (New Requirements)
- **Filter System**: Integrate existing CapTableFilters component for real-time filtering
- **Search Functionality**: Real-time shareholder name search with debounced API calls
- **Role Filter Dropdown**: Filter by shareholder roles (All, Founders, Investors, Advisors, Contractors, Employees)
- **Share Class Filter Dropdown**: Filter by share classes (All, Common, SeriesAPreferred, StockOptionPool)
- **Global State Management**: Use existing GlobalFilterContext for filter state
- **API Integration**: Add filter parameters to table data API endpoint
- **Real-time Updates**: All table data updates based on global filter selections
- **Component Reuse**: Use existing CapTableFilters component without modification

---

## API Integration Architecture

### Summary Cards API Endpoint Details
```
GET /api/p2/companies/{companyId}/stockoptionsplansummary
```

**Parameters:**
- `companyId` (path): UUID of the company

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "totalAuthorizedSize": 14000007,
    "totalPoolSize": 1000000,
    "allocated": 13000,
    "remaining": 987000,
    "totalRemaining": 12500007,
    "promiseGrant": 13000,
    "pendingDetail": {
      "additionalShare": 0,
      "isBoardApproved": "no",
      "isStockHolderApproved": "no",
      "workflowId": null
    },
    "stockOptionPoolPercentage": {
      "totalPoolSizePercentage": 7.14,
      "poolAllocatedPercentage": 1.3,
      "remainingPoolPercentage": 98.7
    }
  },
  "error": null,
  "validationErrors": null
}
```

### Table Data API Endpoint Details
```
GET /api/p2/companies/{companyId}/captable/optionpool
```

**Parameters:**
- `companyId` (path): UUID of the company
- `proFormaRoleFilter` (query, optional): Filter by shareholder role
  - Available values: All, Founders, Investors, Advisors, Contractors, Employees
  - Default: "All"
- `proFormaShareClassFilter` (query, optional): Filter by share class
  - Available values: All, Common, SeriesAPreferred, StockOptionPool
  - Default: "All"
- `shareHolderName` (query, optional): Search by shareholder name
  - Type: string
  - Default: ""

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "latestUpdatedDate": "2025-07-19",
    "optionPools": [
      {
        "id": "b39c5e33-6c99-4cc6-99dc-bc00cc57e5f0",
        "recipient": "Employee 1",
        "role": "employee",
        "grantType": "option",
        "grantStatus": "promised",
        "grantDate": "2025-06-19",
        "options": 1000,
        "percentageOfPool": 0.1,
        "currentValue": 1000,
        "vestingProgress": 8.33,
        "vestedShare": 0,
        "unVestedShare": 1000,
        "vestingCommencementDate": "2025-06-19",
        "term": 48,
        "vestingSchedule": "standard-four-years-monthly-vesting",
        "compensation": 50000,
        "compensationPeriod": "annual",
        "vestingPeriod": null,
        "cliff": null
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

### Edit Grant API Endpoint Details
```
POST /api/p2/companies/{companyId}/captable/optionpool/update
```

**Parameters:**
- `companyId` (path): UUID of the company

**Request Body:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "role": "advisor",
  "grantStatus": "promised",
  "grantType": "option",
  "optionsGranted": 0
}
```

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "updatedAt": "2025-01-15T10:30:00Z"
  },
  "error": null,
  "validationErrors": null
}
```

### Exercise Option API Endpoint Details
```
PUT /api/p2/companies/{companyId}/captable/optionpool/exercisestockoption
```

**Parameters:**
- `companyId` (path): UUID of the company

**Request Body:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "exerciseDate": "2025-08-30",
  "sharesToExercise": 0
}
```

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "exerciseDate": "2025-08-30",
    "sharesExercised": 0,
    "updatedAt": "2025-01-15T10:30:00Z"
  },
  "error": null,
  "validationErrors": null
}
```

### Delete Grant API Endpoint Details
```
DELETE /api/p2/companies/{companyId}/captable/optionpool
```

**Parameters:**
- `companyId` (path): UUID of the company

**Request Body:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "grantType": "option"
}
```

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "deletedAt": "2025-01-15T10:30:00Z"
  },
  "error": null,
  "validationErrors": null
}
```

### Global Filter Implementation

#### **Filter Integration Architecture**
The Stock Option Plan table will integrate with the existing global filter system used in the Pro Forma tables, providing consistent filtering capabilities across the application.

#### **API Request with Filters**
```
GET /api/p2/companies/{companyId}/captable/optionpool?proFormaRoleFilter=All&proFormaShareClassFilter=All&shareHolderName=
```

#### **Filter Parameters Mapping**
- **proFormaRoleFilter**: Maps to role filter dropdown selection
  - UI: "All", "Founders", "Investors", "Advisors", "Contractors", "Employees"
  - API: Same values passed directly
- **proFormaShareClassFilter**: Maps to share class filter dropdown selection
  - UI: "All", "Common", "SeriesAPreferred", "StockOptionPool"
  - API: Same values passed directly
- **shareHolderName**: Maps to search input field
  - UI: Text input for shareholder name search
  - API: Search term passed as string

#### **Implementation Approach**
- **Reuse Existing Components**: Use existing `CapTableFilters` component without modification
- **Global State**: Use existing `GlobalFilterContext` for filter state management
- **API Integration**: Add filter parameters to existing `getStockOptionPlanTable` API call
- **Real-time Updates**: Filter changes trigger debounced API calls (500ms delay)
- **Data Flow**: Filter changes → Global state update → API call → Table refresh

#### **Component Integration**
```typescript
// In SOPSummary.tsx
import CapTableFilters from './CapTableFilters';
import { useGlobalFilters } from '@/contexts/GlobalFilterContext';

// Use existing global filter state
const { filters } = useGlobalFilters();

// Pass filter parameters to API
const apiParams = {
  proFormaRoleFilter: filters.roleFilter,
  proFormaShareClassFilter: filters.shareClassFilter,
  shareHolderName: filters.shareholderNameSearch
};
```

#### **API Client Update**
```typescript
// In client.ts - update getStockOptionPlanTable method
async getStockOptionPlanTable(companyId: string, filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) {
  const queryParams = new URLSearchParams();
  queryParams.append('companyId', companyId);
  
  if (filters?.proFormaRoleFilter) {
    queryParams.append('proFormaRoleFilter', filters.proFormaRoleFilter);
  }
  if (filters?.proFormaShareClassFilter) {
    queryParams.append('proFormaShareClassFilter', filters.proFormaShareClassFilter);
  }
  if (filters?.shareHolderName) {
    queryParams.append('shareHolderName', filters.shareHolderName);
  }
  
  return this.get(`/api/p2/companies/${companyId}/captable/optionpool?${queryParams.toString()}`);
}
```

### Service Layer Implementation

#### **File**: `src/services/cap-table/stockOptionPlan.service.ts`
```typescript
import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  StockOptionPlanSummaryApiResponse, 
  StockOptionPlanTableApiResponse,
  StockOptionPlanData,
  StockOptionPlanSummary,
  StockOptionGrant 
} from "@/types/capTable";

export const getStockOptionPlanSummary = async (companyId: string): Promise<StockOptionPlanSummary> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getStockOptionInformation(companyId);
    
    return transformSummaryApiResponse(response as StockOptionPlanSummaryApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch stock option plan summary: ${error}`);
  }
};

export const getStockOptionPlanTable = async (
  companyId: string, 
  filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }
): Promise<StockOptionGrant[]> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getStockOptionPlanTable(companyId, filters);
    
    return transformTableApiResponse(response as StockOptionPlanTableApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch stock option plan table: ${error}`);
  }
};

export const updateStockOptionGrant = async (
  companyId: string, 
  request: UpdateGrantRequest
): Promise<UpdateGrantResponse> => {
  try {
    const apiClient = new APIClient();
    // Transform role and grant type from display values to API values
    const apiRequest = {
      ...request,
      role: mapRoleToApi(request.role),
      grantType: mapGrantTypeToApi(request.grantType),
    };
    const response = await apiClient.post(`/api/p2/companies/${companyId}/captable/optionpool/update`, apiRequest);
    
    return response as UpdateGrantResponse;
  } catch (error) {
    throw new Error(`Failed to update stock option grant: ${error}`);
  }
};

export const exerciseStockOption = async (
  companyId: string, 
  request: ExerciseOptionRequest
): Promise<ExerciseOptionResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.put(`/api/p2/companies/${companyId}/captable/optionpool/exercisestockoption`, request);
    
    return response as ExerciseOptionResponse;
  } catch (error) {
    throw new Error(`Failed to exercise stock option: ${error}`);
  }
};

export const deleteStockOptionGrant = async (
  companyId: string, 
  request: DeleteGrantRequest
): Promise<DeleteGrantResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.delete(`/api/p2/companies/${companyId}/captable/optionpool`, request);
    
    return response as DeleteGrantResponse;
  } catch (error) {
    throw new Error(`Failed to delete stock option grant: ${error}`);
  }
};

const transformSummaryApiResponse = (apiData: StockOptionPlanSummaryApiResponse): StockOptionPlanSummary => {
  return {
    totalPool: apiData.totalPoolSize || 0,
    allocated: apiData.allocated || 0,
    remaining: apiData.remaining || 0,
    lastUpdated: new Date().toLocaleDateString(), // Will be updated from table API
    percentages: {
      totalPoolSizePercentage: apiData.stockOptionPoolPercentage?.totalPoolSizePercentage || 0,
      poolAllocatedPercentage: apiData.stockOptionPoolPercentage?.poolAllocatedPercentage || 0,
      remainingPoolPercentage: apiData.stockOptionPoolPercentage?.remainingPoolPercentage || 0,
    }
  };
};

const transformTableApiResponse = (apiData: StockOptionPlanTableApiResponse): StockOptionGrant[] => {
  return apiData.optionPools.map((pool) => ({
    id: pool.id, // Use the actual API ID
    recipient: pool.recipient || "-",
    role: mapRoleFromApi(pool.role),
    grantDate: new Date(pool.grantDate),
    optionsGranted: pool.options || 0,
    vestingStart: new Date(pool.grantDate), // Using grant date as vesting start
    vestingPeriod: 48, // Default 4 years, could be made configurable
    cliff: 12, // Default 1 year cliff, could be made configurable
    percentVested: pool.vestingProgress || 0,
    // NEW FIELDS
    grantStatus: pool.grantStatus === 'promised' ? 'Promised' : 'Granted',
    grantType: mapGrantTypeFromApi(pool.grantType),
    issueStatus: 'Active', // Default, could be enhanced
    stateOfResidency: 'CA', // Default, could be enhanced
    // ENHANCED VESTING FIELDS
    vestedShares: pool.vestedShare || 0,
    unvestedShares: pool.unVestedShare || 0,
    vestingSchedule: pool.vestingSchedule || "standard-four-years-monthly-vesting",
    vestingPeriod: pool.vestingPeriod || null,
    cliff: pool.cliff || null,
    vestingCommencementDate: pool.vestingCommencementDate || pool.grantDate,
    term: pool.term || 48,
    // Additional fields for backend processing
    percentageOfPool: pool.percentageOfPool || 0,
    currentValue: pool.currentValue || 0,
  }));
};

const mapRoleFromApi = (apiRole: string): string => {
  const roleMap: Record<string, string> = {
    'employee': 'Employee',
    'advisor': 'Advisor',
    'independent_contractor_consultant': 'Contractor',
  };
  return roleMap[apiRole] || apiRole;
};

const mapRoleToApi = (displayRole: string): string => {
  const reverseRoleMap: Record<string, string> = {
    'Employee': 'employee',
    'Advisor': 'advisor',
    'Contractor': 'independent_contractor_consultant',
  };
  return reverseRoleMap[displayRole] || displayRole.toLowerCase();
};

const mapGrantTypeFromApi = (apiGrantType: string): 'Restricted Stock' | 'Option' | 'Exercised Option' => {
  if (apiGrantType === 'restricted-stock-grant-inside-of-stock-option-plan') {
    return 'Restricted Stock';
  }
  if (apiGrantType === 'exercise-stock') {
    return 'Exercised Option';
  }
  return 'Option';
};

const mapGrantTypeToApi = (displayGrantType: string): string => {
  const grantTypeMap: Record<string, string> = {
    'Restricted Stock': 'restricted-stock-grant-inside-of-stock-option-plan',
    'Option': 'option',
    'Exercised Option': 'exercise-stock',
  };
  return grantTypeMap[displayGrantType] || displayGrantType.toLowerCase();
};

const getVestingScheduleDisplay = (schedule: string, vestingPeriod?: number, cliff?: number) => {
  if (schedule === 'custom') {
    return {
      display: 'Custom ⚠️',
      tooltip: `Custom Schedule: ${vestingPeriod} months vesting, ${cliff} month cliff`,
      isCustom: true
    };
  }
  
  // Standard schedules mapping
  const standardMap: Record<string, string> = {
    'standard-two-years-monthly-vesting': 'Standard 2-Year Monthly',
    'standard-four-years-monthly-vesting': 'Standard 4-Year Monthly',
    'standard-one-year-monthly-vesting': 'Standard 1-Year Monthly',
    'standard-three-years-monthly-vesting': 'Standard 3-Year Monthly',
  };
  
  return {
    display: standardMap[schedule] || schedule.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    tooltip: standardMap[schedule] || schedule,
    isCustom: false
  };
};
```

### Hook Layer Implementation

#### **File**: `src/hooks/cap-table/useStockOptionPlan.hooks.ts`
```typescript
import { useQuery } from "@tanstack/react-query";
import { getStockOptionPlanSummary, getStockOptionPlanTable } from "@/services/cap-table/stockOptionPlan.service";
import { useAuth } from "@/contexts/AuthContext";
import { StockOptionPlanSummary, StockOptionGrant } from "@/types/capTable";

export const useStockOptionPlan = (filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  const summaryQuery = useQuery<StockOptionPlanSummary>({
    queryKey: ["stockOptionPlanSummary", companyId],
    queryFn: () => getStockOptionPlanSummary(companyId!),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const tableQuery = useQuery<StockOptionGrant[]>({
    queryKey: ["stockOptionPlanTable", companyId, filters],
    queryFn: () => getStockOptionPlanTable(companyId!, filters),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Combine data and update lastUpdated from table API
  const combinedData = useMemo(() => {
    if (!summaryQuery.data || !tableQuery.data) return null;

    return {
      summary: {
        ...summaryQuery.data,
        lastUpdated: tableQuery.data.length > 0 ? 
          new Date(tableQuery.data[0]?.grantDate || new Date()).toLocaleDateString() : 
          summaryQuery.data.lastUpdated
      },
      grants: tableQuery.data
    };
  }, [summaryQuery.data, tableQuery.data]);

  return {
    data: combinedData,
    isLoading: summaryQuery.isLoading || tableQuery.isLoading,
    error: summaryQuery.error || tableQuery.error,
    refetch: () => {
      summaryQuery.refetch();
      tableQuery.refetch();
    }
  };
};

export const useUpdateStockOptionGrant = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<UpdateGrantResponse, Error, UpdateGrantRequest>({
    mutationFn: (request) => updateStockOptionGrant(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast({
        title: "Grant Updated",
        description: "Stock option grant has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update stock option grant.",
        variant: "destructive",
      });
    },
  });
};

export const useExerciseStockOption = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<ExerciseOptionResponse, Error, ExerciseOptionRequest>({
    mutationFn: (request) => exerciseStockOption(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast({
        title: "Option Exercised",
        description: "Stock option has been exercised successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Exercise Failed",
        description: error.message || "Failed to exercise stock option.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteStockOptionGrant = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<DeleteGrantResponse, Error, DeleteGrantRequest>({
    mutationFn: (request) => deleteStockOptionGrant(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast({
        title: "Grant Deleted",
        description: "Stock option grant has been deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete stock option grant.",
        variant: "destructive",
      });
    },
  });
};
```

---

## Enhanced Data Model

### API Response Types
Add to `src/types/capTable.ts`:

```ts
// API Response Types for Stock Option Plan
export interface StockOptionPlanSummaryApiResponse {
  totalAuthorizedSize: number;
  totalPoolSize: number;
  allocated: number;
  remaining: number;
  totalRemaining: number;
  promiseGrant: number;
  pendingDetail: {
    additionalShare: number;
    isBoardApproved: string;
    isStockHolderApproved: string;
    workflowId: string | null;
  };
  stockOptionPoolPercentage: {
    totalPoolSizePercentage: number;
    poolAllocatedPercentage: number;
    remainingPoolPercentage: number;
  };
}

export interface StockOptionPlanTableApiResponse {
  latestUpdatedDate: string;
  optionPools: StockOptionPoolApiItem[];
}

export interface StockOptionPoolApiItem {
  recipient: string;
  role: string;
  grantType: string;
  grantStatus: string;
  grantDate: string;
  options: number;
  percentageOfPool: number;
  currentValue: number;
  vestingProgress: number;
  // ENHANCED VESTING FIELDS
  vestedShare: number;
  unVestedShare: number;
  vestingCommencementDate: string;
  term: number;
  vestingSchedule: string;
  compensation: number;
  compensationPeriod: string;
  vestingPeriod: number | null;
  cliff: number | null;
}

// Enhanced Stock Option Plan Types
export interface StockOptionPlanSummary {
  totalPool: number;
  allocated: number;
  remaining: number;
  lastUpdated: string;
  percentages: {
    totalPoolSizePercentage: number;
    poolAllocatedPercentage: number;
    remainingPoolPercentage: number;
  };
}

export interface StockOptionGrant {
  id: string;
  recipient: string;
  role: string;
  grantDate: Date;
  optionsGranted: number;
  vestingStart: Date;
  vestingPeriod: number; // months
  cliff: number; // months
  percentVested: number;
  // NEW FIELDS
  grantStatus: 'Promised' | 'Granted';
  grantType: 'Restricted Stock' | 'Option';
  issueStatus: 'Active' | 'Terminated' | 'Exercised';
  stateOfResidency: string;
  exerciseDate?: Date;
  exercisedShares?: number;
  remainingShares?: number;
  isExercised?: boolean;
  // ENHANCED VESTING FIELDS
  vestedShares: number;
  unvestedShares: number;
  vestingSchedule: string;
  vestingPeriod: number | null;
  cliff: number | null;
  vestingCommencementDate: string;
  term: number;
  // Additional fields for backend processing
  percentageOfPool: number;
  currentValue: number;
}

export interface StockOptionPlanData {
  summary: StockOptionPlanSummary;
  grants: StockOptionGrant[];
}

// API Request/Response Types for Grant Management
export interface UpdateGrantRequest {
  id: string;
  role: string;
  grantStatus: string;
  grantType: string;
  optionsGranted: number;
}

export interface UpdateGrantResponse {
  id: string;
  updatedAt: string;
}

export interface ExerciseOptionRequest {
  id: string;
  exerciseDate: string;
  sharesToExercise: number;
}

export interface ExerciseOptionResponse {
  id: string;
  exerciseDate: string;
  sharesExercised: number;
  updatedAt: string;
}

export interface DeleteGrantRequest {
  id: string;
  grantType: string;
}

export interface DeleteGrantResponse {
  id: string;
  deletedAt: string;
}
```

---

## Component Architecture

### Entry Point
- `components/captable/SOPSummary.tsx` (modify existing component)
  - **Purpose**: Modified Stock Option Plan component with API integration
  - **Props**: None (self-contained with API data)
  - **State**: Extended grants list, edit modal states, exercise modal state, loading states
  - **Children**: Original pool metrics section, modified grant table, edit modals, loading states

### Modified Component Structure
```
SOPSummary.tsx (Modified with API Integration + Global Filters)
├── Card (container) - PRESERVE
├── CardHeader - PRESERVE
│   ├── CardTitle ("Stock Option Plan") - UPDATE TITLE
│   └── Last Updated Date - FROM API
├── CardContent - PRESERVE
│   ├── Loading State (skeleton) - ADD
│   ├── Error State (error message) - ADD
│   ├── Pool Metrics Section - PRESERVE (API DATA)
│   │   ├── Total Plan Size Display - PRESERVE
│   │   ├── Allocated Shares Display - PRESERVE
│   │   ├── Remaining Plan Display - PRESERVE
│   │   └── Pool Allocation Bar - PRESERVE
│   ├── Global Filters Section - ADD (REUSE EXISTING)
│   │   ├── CapTableFilters Component - REUSE
│   │   ├── Search Input - REUSE
│   │   ├── Role Filter Dropdown - REUSE
│   │   └── Share Class Filter Dropdown - REUSE
│   ├── Grant Table - MODIFY (API DATA + FILTERED)
│   │   ├── TableHeader - MODIFY
│   │   │   ├── Recipient Column - PRESERVE
│   │   │   ├── Role Column - PRESERVE
│   │   │   ├── Grant Date Column - PRESERVE
│   │   │   ├── Options Column - PRESERVE
│   │   │   ├── Grant Status Column (NEW)
│   │   │   ├── Grant Type Column (NEW)
│   │   │   ├── Vested Shares Column (NEW)
│   │   │   ├── Unvested Shares Column (NEW)
│   │   │   ├── Vesting Schedule Column (NEW)
│   │   │   ├── Vesting Progress Column - PRESERVE
│   │   │   └── Actions Column - MODIFY
│   │   └── TableBody - MODIFY
│   │       ├── Individual Grant Rows - MODIFY (FILTERED DATA)
│   │       └── Totals Row - PRESERVE
│   ├── Edit Grant Modal - ADD
│   ├── Exercise Option Modal - ADD
│   ├── Delete Confirmation Modal - ADD
│   └── Empty State - PRESERVE
```

### New Shared Components (Add)
- `Skeleton` for loading states
- `Alert`, `AlertDescription` for error states
- `Button` with retry functionality
- `Dialog`, `DialogContent`, `DialogHeader`, `DialogTitle` for modals
- `Input` for form fields
- `Label` for form labels
- `Select` for dropdown fields
- `DatePicker` for date selection
- `ConfirmationDialog` for delete confirmations

### Modal Specifications

#### **Edit Grant Modal**
- **Purpose**: Edit all grant fields
- **Fields**: Recipient, Role, Grant Status, Grant Type, Options Granted
- **Validation**: Client-side validation for all fields
- **Actions**: Cancel, Save Changes
- **API**: `POST /api/p2/companies/{companyId}/captable/optionpool/update`

#### **Exercise Option Modal**
- **Purpose**: Exercise stock options with validation
- **Fields**: Grantee (read-only), Total Options (read-only), Shares to Exercise, Exercise Date
- **Validation**: 
  - Shares to Exercise must be ≤ vested shares as of exercise date
  - Exercise Date must be valid date
  - Shares to Exercise must be > 0
- **Actions**: Cancel, Exercise Option
- **API**: `PUT /api/p2/companies/{companyId}/captable/optionpool/exercisestockoption`

#### **Delete Confirmation Modal**
- **Purpose**: Confirm grant deletion
- **Content**: Warning message and grant details
- **Validation**: Prevent deletion if dependencies exist
- **Actions**: Cancel, Delete
- **API**: `DELETE /api/p2/companies/{companyId}/captable/optionpool`

---

## Enhanced UI/UX Requirements

### Loading and Error States
- **Loading State**: Skeleton loader while fetching data from both APIs
- **Error State**: Clear error message with retry option for each API
- **Empty State**: Message when no stock option plan data found
- **Partial Data**: Handle cases where one API succeeds but the other fails

### Preserve Original Pool Metrics Section Design
- **Location**: Keep existing placement above the grant table
- **Layout**: Maintain existing 3-card horizontal layout
- **Styling**: Keep existing professional financial dashboard style
- **Allocation Bar**: Preserve existing visual progress bar
- **API Data**: Use summary API for metrics and percentages

### Original Pool Metrics Layout (Preserve with API Data)
```tsx
<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Total Plan Size</div>
      <div className="text-2xl font-bold">{formatNumber(summary.totalPool)}</div>
      <div className="text-sm text-muted-foreground">{summary.percentages.totalPoolSizePercentage}% of outstanding shares</div>
    </CardContent>
  </Card>
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Allocated Shares</div>
      <div className="text-2xl font-bold">{formatNumber(summary.allocated)}</div>
      <div className="text-sm text-muted-foreground">{summary.percentages.poolAllocatedPercentage}% of pool allocated</div>
    </CardContent>
  </Card>
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Remaining Plan</div>
      <div className={`text-2xl font-bold ${summary.remaining < 0 ? 'text-red-600' : ''}`}>
        {summary.remaining < 0 ? `(${formatNumber(Math.abs(summary.remaining))})` : formatNumber(summary.remaining)}
      </div>
      <div className="text-sm text-muted-foreground">{summary.percentages.remainingPoolPercentage}% of pool available</div>
    </CardContent>
  </Card>
</div>
```

### Pool Allocation Section (Preserve with API Data)
```tsx
<div className="mb-6">
  <h3 className="text-lg font-semibold mb-2">Pool Allocation</h3>
  <div className="flex items-center gap-4">
    <Progress value={summary.percentages.poolAllocatedPercentage} className="flex-1 h-2" />
    <span className="text-sm text-muted-foreground">
      {summary.percentages.poolAllocatedPercentage.toFixed(1)}% Used
    </span>
  </div>
</div>
```

### Modified Table Design
- **Layout**: Keep existing clean, modern table with subtle borders
- **Headers**: Preserve existing bold column headers, add tooltips for new columns
- **Rows**: Keep existing alternating row colors for better readability
- **Responsive**: Maintain existing horizontal scroll on mobile devices
- **API Data**: Use table API for all grant data

### Updated Column Specifications

#### **Recipient Column (PRESERVE):**
- **Left-aligned text**
- **Font weight**: Medium
- **Truncation**: For long names with tooltip
- **API Data**: `pool.recipient`

#### **Role Column (PRESERVE):**
- **Left-aligned text**
- **Badge Display**: Keep existing outline badges for roles
- **Standard Roles**: Employee, Advisor, Contractor
- **API Data**: `pool.role` (mapped to display names)

#### **Grant Date Column (PRESERVE):**
- **Left-aligned text**
- **Format**: MM/DD/YYYY
- **Tooltip**: "Date when the grant was issued"
- **API Data**: `pool.grantDate`

#### **Options Column (PRESERVE):**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Number of options granted"
- **API Data**: `pool.options`

#### **Grant Status Column (NEW):**
- **Left-aligned text**
- **Badge Display**: 
  - Promised (yellow/orange badge)
  - Granted (green badge)
- **Tooltip**: "Promised = planned grant, docs not executed. Granted = grant documents executed"
- **API Data**: `pool.grantStatus` (mapped to display names)

#### **Grant Type Column (NEW):**
- **Left-aligned text**
- **Badge Display**:
  - Restricted Stock (purple badge)
  - Option (blue badge)
- **Tooltip**: "Type of equity grant"
- **API Data**: `pool.grantType` (mapped to display names)

#### **Vested Shares Column (NEW):**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Number of shares currently vested"
- **API Data**: `pool.vestedShare`
- **Display**: Direct numeric value

#### **Unvested Shares Column (NEW):**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Number of shares not yet vested"
- **API Data**: `pool.unVestedShare`
- **Display**: Direct numeric value

#### **Vesting Schedule Column (NEW):**
- **Left-aligned text**
- **Display Logic**:
  - Standard schedules: "Standard 2-Year Monthly", "Standard 4-Year Monthly", etc.
  - Custom schedules: "Custom ⚠️" with warning icon
- **Tooltip**: 
  - Standard: Schedule description
  - Custom: "Custom Schedule: X months vesting, Y month cliff"
- **API Data**: `pool.vestingSchedule`, `pool.vestingPeriod`, `pool.cliff`
- **Special Features**: Warning icon (⚠️) for custom schedules with hover details

#### **Vesting Progress Column (PRESERVE):**
- **Center-aligned**
- **Progress Bar**: Keep existing visual progress bar
- **Percentage**: Keep existing percentage display
- **Color Coding**: Keep existing green for 100% vested, blue for others
- **API Data**: `pool.vestingProgress`

#### **Actions Column (MODIFY):**
- **Edit Button**: Available on ALL rows (pencil icon)
- **Exercise Button**: Available ONLY on rows where `grantType === "Option"` AND `grantStatus === "Granted"` (calendar icon)
- **Delete Button**: Available on ALL rows (trash icon)
- **Layout**: Three-button layout with proper spacing and tooltips

#### **Button Visibility Conditions:**
| Grant Type | Grant Status | Edit | Exercise | Delete |
|------------|--------------|------|----------|--------|
| Option | Promised | ✅ | ❌ | ✅ |
| Option | Granted | ✅ | ✅ | ✅ |
| Restricted Stock | Promised | ✅ | ❌ | ✅ |
| Restricted Stock | Granted | ✅ | ❌ | ✅ |
| Exercised Option | Any | ✅ | ❌ | ✅ |

### **Vesting Schedule Display Logic:**
```typescript
const getVestingScheduleDisplay = (schedule: string, vestingPeriod?: number, cliff?: number) => {
  if (schedule === 'custom') {
    return {
      display: 'Custom ⚠️',
      tooltip: `Custom Schedule: ${vestingPeriod} months vesting, ${cliff} month cliff`,
      isCustom: true
    };
  }
  
  // Standard schedules mapping
  const standardMap: Record<string, string> = {
    'standard-two-years-monthly-vesting': 'Standard 2-Year Monthly',
    'standard-four-years-monthly-vesting': 'Standard 4-Year Monthly',
    'standard-one-year-monthly-vesting': 'Standard 1-Year Monthly',
    'standard-three-years-monthly-vesting': 'Standard 3-Year Monthly',
  };
  
  return {
    display: standardMap[schedule] || schedule.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    tooltip: standardMap[schedule] || schedule,
    isCustom: false
  };
};
```

### **Vesting Schedule Examples:**
| API Value | Display | Tooltip | Custom |
|-----------|---------|---------|--------|
| `"standard-four-years-monthly-vesting"` | "Standard 4-Year Monthly" | "Standard 4-Year Monthly" | ❌ |
| `"standard-two-years-monthly-vesting"` | "Standard 2-Year Monthly" | "Standard 2-Year Monthly" | ❌ |
| `"custom"` | "Custom ⚠️" | "Custom Schedule: 24 months vesting, 1 month cliff" | ✅ |

---

## Implementation Plan

### Phase 1: API Integration Setup
1. **Add API endpoints** to `client.ts` for all operations (GET, POST, PUT, DELETE)
2. **Update table data API** to support global filter parameters
3. **Create service layer** (`stockOptionPlan.service.ts`) with all CRUD operations and filter support
4. **Create hook layer** (`useStockOptionPlan.hooks.ts`) with queries and mutations
5. **Add TypeScript interfaces** for all API requests and responses

### Phase 2: Data Transformation
1. **Implement data transformation** from APIs to component format
2. **Add error handling** for missing or invalid data
3. **Implement role and grant type mapping** logic
4. **Add loading and error states**

### Phase 3: Global Filter Integration
1. **Integrate CapTableFilters component** into SOPSummary.tsx
2. **Connect global filter state** using useGlobalFilters hook
3. **Update API calls** to include filter parameters
4. **Implement debounced filtering** with 500ms delay
5. **Add filter state to query keys** for proper caching
6. **Test filter functionality** with real API data

### Phase 4: Component Updates
1. **Update component** to use API data instead of static data
2. **Implement loading states** with skeleton loaders
3. **Add error handling** with retry functionality
4. **Update summary metrics** to use API values and percentages
5. **Add new vesting columns** (Vested Shares, Unvested Shares, Vesting Schedule)
6. **Implement vesting schedule display logic** with custom schedule handling

### Phase 5: Modal Implementation
1. **Create Edit Grant Modal** with all editable fields
2. **Create Exercise Option Modal** with validation logic
3. **Create Delete Confirmation Modal** with safety checks
4. **Implement form validation** for all modals

### Phase 6: Action Buttons and Logic
1. **Add action buttons** to table rows with proper visibility conditions
2. **Implement exercise validation** (vested shares check)
3. **Add delete confirmation** logic
4. **Implement real-time updates** after mutations

### Phase 7: Professional Polish
1. **Add comprehensive tooltips** for all columns and buttons
2. **Implement accessibility features** and keyboard navigation
3. **Add professional styling** and responsive design
4. **Test all API integrations** with real data

---

## Success Metrics

### User Experience
- **API Integration**: Seamless data loading from backend
- **Original UI Preservation**: Users familiar with the interface will recognize the layout
- **Enhanced Functionality**: New features seamlessly integrated into existing design
- **Professional Appearance**: Maintains suitability for board meetings and compliance
- **Error Handling**: Graceful handling of missing or invalid data

### Technical Metrics
- **Performance**: Fast API calls with proper caching
- **Accessibility**: Full WCAG compliance
- **Responsive Design**: Perfect functionality across all devices
- **Type Safety**: Complete TypeScript support
- **Error Resilience**: Robust error handling and recovery

---

## Dependencies

### External Dependencies
- `@tanstack/react-query`: Data fetching and caching
- `@radix-ui/react-progress`: Progress bar component (existing)
- `@radix-ui/react-select`: Select dropdown component (new)
- `lucide-react`: Icon library for consistent iconography (existing)

### Internal Dependencies
- `@/components/ui/card`: Card components for layout (existing)
- `@/components/ui/table`: Table components (existing)
- `@/components/ui/dialog`: Dialog components for modals (new)
- `@/components/ui/input`: Input components for forms (new)
- `@/components/ui/select`: Select components for dropdowns (new)
- `@/components/ui/progress`: Progress bar component (existing)
- `@/components/ui/badge`: Badge components for status display (new)
- `@/components/ui/button`: Button components for actions (existing)
- `@/components/ui/tooltip`: Tooltip components for explanations (new)
- `@/components/ui/skeleton`: Skeleton components for loading states (new)
- `@/components/ui/alert`: Alert components for error states (new)
- `@/integrations/legal-concierge/client`: API client
- `@/contexts/AuthContext`: Authentication context for company ID

---

## Risk Assessment

### Technical Risks
- **API Reliability**: Backend API availability and response times
- **Data Transformation**: Complex mapping between API and component formats
- **Error Handling**: Comprehensive error scenarios and edge cases
- **Performance**: Large datasets and caching strategies

### Mitigation Strategies
- **Robust Error Handling**: Comprehensive try-catch blocks and fallbacks
- **Data Validation**: Validate API responses before transformation
- **Caching Strategy**: Implement proper caching with React Query
- **Loading States**: Provide clear feedback during data fetching

### Business Risks
- **Data Accuracy**: Incorrect grant tracking may lead to compliance issues
- **User Experience**: Poor loading times or error states may frustrate users
- **Data Integrity**: API data must be accurately transformed and displayed

### Mitigation Strategies
- **Comprehensive Testing**: Test all API scenarios and edge cases
- **User Feedback**: Clear loading and error states
- **Data Validation**: Validate all API responses before display
- **Fallback Mechanisms**: Graceful degradation when API is unavailable

---

## Conclusion

The Stock Option Plan tab modification preserves the existing professional UI while adding the required functionality and full API integration. The implementation ensures compliance with legal and financial requirements while maintaining excellent user experience and accessibility standards.

### Key Features:
✅ **API Integration**: Real-time data from backend with proper error handling  
✅ **Original UI Preservation**: Maintains existing 3-card layout and table structure  
✅ **Enhanced Grant Table**: Adds Grant Status, Grant Type, Vested Shares, Unvested Shares, and Vesting Schedule columns  
✅ **Vesting Schedule Display**: Intelligent display of standard vs custom vesting schedules with warning icons  
✅ **Edit Functionality**: Professional editing capabilities for all grant details  
✅ **Exercise Workflow**: Seamless option exercise process with dual entries  
✅ **Global Filter Integration**: Real-time filtering by role, share class, and shareholder name  
✅ **Professional Design**: Preserves clean, modern UI suitable for financial applications  
✅ **Compliance Ready**: Proper tracking for legal and financial reporting  

The implementation provides a solid foundation for equity grant management with full API integration and room for future enhancements while preserving the familiar user interface.

---

## UI/UX Enhancements (Latest Updates)

### Grant Type Display Fix
- **Issue**: API returns `grantType: "exercise-stock"` but UI shows "Option" instead of "Exercised Option"
- **Solution**: Update grant type mapping to include `exercise-stock` → "Exercised Option"
- **Impact**: Proper display of exercised options in the table

### Action Icons Alignment
- **Issue**: Varying number of action icons (2-3) creates visual misalignment across rows
- **Solution**: Remove fixed width, show only present icons
- **Approach**: Use conditional rendering to show only the icons that are applicable for each row

### Exercise Icon Update
- **Issue**: Calendar icon is confusing for exercise action
- **Solution**: Replace calendar icon with a more appropriate exercise-related icon
- **New Icon**: Use `Zap` or `TrendingUp` icon from lucide-react for exercise action
- **Rationale**: Better represents the action of exercising stock options

### Edit Modal Recipient Field
- **Issue**: Recipient field should not be editable in edit modal
- **Solution**: Make recipient field read-only/disabled
- **Approach**: Use `disabled` attribute and `bg-muted` styling for visual indication

### API Payload Row ID
- **Requirement**: All API calls (edit, exercise, delete) should use the row ID in payload
- **Current Status**: ✅ Already implemented correctly
- **Verification**: `editModal.data.id`, `exerciseModal.data.id`, `deleteModal.data.id` are used

### Implementation Priority
1. Fix grant type mapping for "exercise-stock"
2. Update exercise action icon
3. Improve action icons alignment
4. Make recipient field non-editable in edit modal
5. Confirm API payload uses correct row IDs
6. Test with real API data

---

## Add Grant Functionality (New Requirements)

### Overview
Add a new "Add Grant" button to the Stock Option Plan interface that allows users to create new stock option grants directly from the SOP tab.

### UI Requirements

#### **Add Grant Button**
- **Location**: Left side of "Last updated: 9/3/2024" text in the header
- **Design**: Button with plus icon and "Add Grant" text
- **Action**: Opens Add Grant dialog when clicked

#### **Add Grant Dialog**
- **Title**: "Add New Grant" (form step) / "Confirm Grant Details" (confirmation step)
- **Type**: Two-step form (form → confirmation) matching AdvisorDialog UX
- **Size**: Similar to existing modals (sm:max-w-[600px])
- **UX Pattern**: Follows AdvisorDialog pattern for consistency

### Form Fields

#### **Required Fields (All from AdvisorDialog)**
1. **Name** (text input) - Recipient's full name
2. **Email** (email input) - Recipient's email address
3. **Address** (text input) - Recipient's address
4. **Services** (text input) - Services provided (for advisors/contractors)
5. **Grant Type** (radio buttons) - Type of equity grant:
   - Option (Default to Non-Statutory Option Grant) - Default selected
   - Restricted Stock Grant (These shares will be issued from the Stock Option Plan)
   - Restricted Stock Grant (These shares will be issued from outside the Stock Option Plan)
   - None
6. **Shares** (number input) - Number of shares/options granted (conditional on grant type)
7. **Start Date** (date picker) - Grant start date
8. **Vesting Schedule** (radio buttons) - Vesting schedule type:
   - Standard (2 years monthly)
   - Custom (shows additional fields when selected)
9. **Compensation** (number input) - Compensation amount
10. **Compensation Period** (dropdown) - hourly, monthly, annually

#### **Conditional Fields (Show when Custom vesting selected)**
- **Vesting Period** (number input) - Vesting period in months
- **Cliff** (number input) - Cliff period in months

#### **Type Field (Hidden)**
- **Type** (dropdown) - Maps to API `type` field:
  - Advisor → "advisor"
  - Employee → "employee" 
  - Contractor → "independent-contractor-consultant"

### API Integration

#### **API Endpoint**
```
POST /api/p2/companies/{companyId}/promisegrantswithoutapproval
```

#### **Request Payload Mapping**
```json
{
  "name": "string",                    // From Name field
  "email": "string",                   // From Email field
  "address": "string",                 // From Address field
  "services": "string",                // From Services field
  "grantType": "option",               // Mapped from Grant Type selection
  "shares": 0,                         // From Shares field
  "startDate": "2025-09-08",          // From Start Date field
  "vestingSchedule": "none",           // Mapped from Vesting Schedule
  "compensation": 0,                   // From Compensation field
  "compensationPeriod": "hourly",      // From Compensation Period dropdown
  "vestingPeriod": 0,                  // From Vesting Period (if Custom)
  "cliff": 0,                          // From Cliff (if Custom)
  "term": 0,                           // Ignore for now (not mandatory)
  "pricePerShare": 0,                  // Ignore for now (not mandatory)
  "type": "advisor"                    // Mapped from Type dropdown
}
```

#### **Grant Type Mapping**
- "Option" → "option"
- "Restricted Stock Grant (These shares will be issued from the Stock Option Plan)" → "restricted-stock-grant-inside-of-stock-option-plan"
- "Restricted Stock Grant (These shares will be issued from outside the Stock Option Plan)" → "restricted-stock-grant-outside-of-stock-option-plan"
- "None" → "none"

#### **Vesting Schedule Mapping**
- "Standard" → "standard-two-years-monthly-vesting"
- "Custom" → "custom"

#### **Compensation Period Mapping**
- "Hourly" → "hourly"
- "Monthly" → "monthly"
- "Annually" → "annually"

### Implementation Details

#### **Hook Integration**
- **File**: `src/hooks/cap-table/useStockOptionPlan.hooks.ts`
- **New Hook**: `useAddStockOptionGrant()`
- **Features**:
  - API call to promisegrantswithoutapproval endpoint
  - Success/error handling with toast notifications
  - Automatic data refresh after successful creation
  - Loading state management

#### **Component Integration**
- **File**: `src/components/captable/SOPSummary.tsx`
- **Changes**:
  - Add "Add Grant" button in header
  - Add AddGrantDialog component
  - Add state management for dialog
  - Integrate with new hook

#### **Form Validation**
- **Required Fields**: Name, Email, Address, Services, Start Date, Compensation, Compensation Period, Type
- **Conditional Validation**: Shares required when grant type is not "None"
- **Custom Vesting Validation**: Vesting Period and Cliff required when Custom vesting selected
- **Email Validation**: Valid email format
- **Date Validation**: Start date cannot be in the future
- **Number Validation**: Positive numbers for shares, compensation, vesting period, cliff

#### **User Experience**
- **Loading States**: Button shows loading spinner during API call
- **Success Feedback**: Toast notification on successful grant creation
- **Error Handling**: Toast notification with specific error message
- **Form Reset**: Form clears after successful submission
- **Dialog Close**: Dialog closes automatically after successful submission

### Success Criteria
- ✅ Add Grant button appears in correct location
- ✅ Dialog opens with all required fields
- ✅ Form validation works correctly
- ✅ API integration functions properly
- ✅ Success/error feedback provided
- ✅ Data refreshes automatically after creation
- ✅ Form resets and dialog closes on success
- ✅ Loading states work correctly
- ✅ Conditional fields show/hide appropriately

### Dependencies
- Existing UI components (Dialog, Button, Input, Select, etc.)
- Existing toast notification system
- Existing API client
- React Query for data management
- Form validation utilities
