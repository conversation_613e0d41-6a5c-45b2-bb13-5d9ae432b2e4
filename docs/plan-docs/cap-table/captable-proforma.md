# Pro Forma Model Tab Implementation Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Updated Requirements - December 2024

## Overview

The Pro Forma Model tab provides comprehensive financial modeling and analysis capabilities for investment rounds, combining the functionality of the existing "Pro Forma Summary" and "Pro Forma Cap" tabs into a single, integrated interface. This component enables users to perform in-depth analysis of cap table changes after an investment round, with dynamic calculations, series-specific pricing, and comprehensive output sections.

## Goals

- Provide comprehensive pro forma analysis for investment rounds
- Enable dynamic calculation of cap table changes based on user inputs
- Support series-specific pricing and voting rights analysis
- Generate professional reports suitable for board meetings and investor presentations
- Ensure compliance with financial modeling best practices
- Maintain professional UI/UX suitable for financial applications

## UX Overview

- **Vertical Layout**: Single page with multiple sections stacked vertically
- **Input Section**: Comprehensive input controls at the top
- **Summary Metrics**: Key financial calculations and notes
- **Pro Forma Cap Table**: Detailed ownership structure with series-specific columns
- **Ownership Distribution**: Interactive pie chart with filters
- **Voting Rights Analysis**: Series-specific voting power calculations
- **Stock Option Plan**: Updated option pool analysis
- **Professional Navigation**: Sticky navigation and smooth scrolling

---

## Requirements Summary

### 1. Tab Consolidation
- **Combine Tabs**: Merge "Pro Forma Summary" and "Pro Forma Cap" into single "Pro Forma Model" tab
- **Replace Existing**: Remove current tabs and implement new comprehensive interface
- **Maintain Functionality**: Preserve all existing features while adding enhanced capabilities

### 2. Enhanced Inputs (Editable Fields)
- **Designation Toggle**: Fixed Pre-Money vs Fixed Post-Money Valuation (editable)
- **Investment Amount**: Dollar amount input with validation (editable, default: 0, erasable)
- **Option Pool Available for Issuance Post-Money**: Percentage input with validation (editable, default: 0, erasable)
- **Preferred Stock Designation**: Series Seed or Series A dropdown (editable)
- **Common Stock Buffer**: Percentage input (editable, default: 0, erasable)
- **Pre-Money Valuation or Post-Money Valuation**: Dollar amount input based on designation (editable, default: 0, erasable)
- **Professional Disclaimer**: Legal/financial caveat about convertible securities
- **Real-time Calculations**: All calculations update immediately based on input changes
- **Input Behavior**: All number inputs default to 0 and allow complete erasure (empty state)

### 2.1. Global Cap Table Filters Integration (December 2024)
- **Global Filter System**: CapTableFilters.tsx functions as a global filter affecting all cap table data
- **Search Functionality**: Real-time shareholder name search with debounced API calls
- **Role Filter Dropdown**: Filter by shareholder roles (All, Founders, Investors, Advisors, Contractors, Employees)
- **Share Class Filter Dropdown**: Filter by share classes (All, Common, SeriesAPreferred, StockOptionPool)
- **Global State Management**: Filter state managed at application level, consumed by ProFormaModel
- **API Integration**: Filter parameters sent as additional query parameters to proforma API
- **Real-time Updates**: All calculations and cap table data update based on global filter selections
- **No UI Recreation**: Existing CapTableFilters.tsx component used as-is without modification
- **Cross-Tab Functionality**: Filters affect Pro Forma tab data from global state

### 2.2. Empty State UI/UX Implementation (December 2024)
- **Pro Forma Cap Table Empty State**: Professional empty state when no shareholder data is available
- **Pro Rata Rights Empty State**: Clear messaging when no pro rata rights data exists
- **Voting Rights Analysis Empty State**: Informative empty state for missing voting rights data
- **Stock Option Plan Empty State**: User-friendly empty state for missing stock option details
- **Consistent Design Language**: Unified empty state components across all tables
- **Actionable CTAs**: Clear call-to-action buttons where appropriate (e.g., "Add Investor", "Set Up Stock Options")
- **Contextual Messaging**: Specific messages explaining why data might be missing
- **Visual Indicators**: Icons and illustrations to enhance user understanding
- **Loading States**: Proper loading indicators during data fetching
- **Error States**: Graceful error handling with retry options

### 3. Dynamic Summary Metrics
- **Conditional Calculations**: Different metrics based on designation choice
- **Series-Specific Pricing**: Multiple price per share calculations
- **Authorized Stock Breakdown**: Detailed stock authorization tracking
- **Total Pool**: Total option pool size calculation
- **Pool Remaining for Issuance**: Available option pool for future grants
- **Professional Notes**: Explanatory notes for each calculation

### 4. Enhanced Pro Forma Cap Table (Updated per Client Requirements)
- **Add Investor Functionality**: "Add Investor" button with modal for manual investor entry and investment amount input
- **Add Investor API Integration**: Modal calls POST API to create new investor with modified payload structure
- **Simplified Column Structure**: Single unified table with streamlined columns:
  - Stockholder, Cash Investment, Common Stock, Stock Option Plan, Series A/Series Seed Shares, Series A-1 Shares, Total Shares, Fully Diluted Ownership, Ownership Change
- **Three-Section Table Layout**: Table divided into three distinct sections with horizontal dividers:
  - **Section 1 (Top)**: Regular investor rows (Acme Ventures, Convertible Note Holder, Founders, Employee Stock Pool, Advisor Shares)
- **Section 2 (Middle)**: SOP Summary rows with Stock Option Pool Increase
  - "Outstanding Stock Option Plan Shares" - Total outstanding SOP shares
  - "Promised Stock Option Plan Shares" - Total promised SOP shares
  - "Stock Option Plan Shares Available" - Available shares (calculated from Option Pool % input)
  - "Stock Option Pool Increase" - Increase amount for option pool
  - **Ownership Change Display**: Show actual ownership change percentages for SOP summary rows (calculated from API response data)
  - **Section 3 (Bottom)**: Additional investor and unallocated money rows
    - Additional investor row (placeholder for API population)
    - "Unallocated New Money" row (placeholder for API population)
- **Horizontal Dividers**: Two thick horizontal lines separating the three sections for clear visual separation
- **Inline Tooltip Integration**: Tooltips appear on hover over "(i)" icons for Ownership Change column
- **Professional Styling**: Clean borders, alternating row colors, modern design
- **Dynamic Calculations**: SOP available shares update based on Option Pool % input
- **Placeholder Data**: Static placeholder values for Section 3 rows (will be populated by API later)
- **Cash Investment Editability**: Conditional editability for Section 3 Cash Investment column based on ID presence
  - **Editable**: Rows with valid `id` display as input fields
  - **Read-only**: Rows with `id: null` display as plain text
  - **Visual Indicators**: Different styling for editable vs read-only states
  - **API Integration**: Real-time updates via POST mutation
- **Negative Value Formatting**: All negative values in the Pro Forma Cap Table should display with parentheses instead of minus signs
  - **Format Rule**: Negative values like `-123` should display as `(123)`
  - **Applicable Columns**: Cash Investment, Total Shares, Fully Diluted Ownership, Ownership Change
  - **Implementation**: Custom formatting function to handle negative value display consistently across all table columns
- **Percentage Formatting**: All percentage values throughout the entire Pro Forma Model should display with exactly 2 decimal places
  - **Format Rule**: Percentages should display as `X.XX%` (e.g., `12.23%`, `412.12%`)
  - **Applicable Areas**: Cap table percentages, summary metrics, voting rights, ownership distribution, tooltips
  - **Implementation**: Update all percentage formatting functions to use `.toFixed(2)` instead of `.toFixed(4)`

### 4.1. Cap Table Column Updates (December 2024)
- **Remove Catch Investment Column**: Completely remove the "Catch Investment" column from the table
- **Rename Investment Amount Column**: Change "Investment Amount" column header to "Cash Investment" (same functionality)
- **Add Series A/Series Seed Column**: New column positioned after "Stock Option Plan" column:
  - **Column Name**: "Series A Shares" when Preferred Stock Designation = "Series A"
  - **Column Name**: "Series Seed Shares" when Preferred Stock Designation = "Series Seed"
  - **Data**: Populated from `seiresADetails.resultingShares` for lower section
  - **Position**: Static column that's always present (not dynamic like Series A-1, A-2, etc.)
- **Updated Column Order**: 
  1. Stockholder
  2. Cash Investment (renamed from Investment Amount)
  3. Common Stock
  4. Stock Option Plan
  5. Series A/Series Seed Shares (NEW - static column)
  6. Series A-1 Shares (dynamic)
  7. Series A-2 Shares (dynamic)
  8. ... (additional dynamic series columns)
  9. Total Shares
  10. Fully Diluted Ownership
  11. Ownership Change
- **Dynamic Series Columns**: Keep existing Series A-1, Series A-2, etc. columns as dynamic columns based on API response
- **Tooltip Updates**: Remove Catch Investment tooltips, keep only Ownership Change tooltips

### 4.2. Cash Investment Editability Requirements (December 2024)

#### 4.2.1. Upper Section Editability
- **Condition**: Cash Investment column in upper section (classACommonStockholders) is editable ONLY if the row has a valid ID
- **Valid IDs**: Either `serviceProviderId` or `officerId` must be present
- **No Delete**: Upper section rows do not have delete functionality
- **Visual Indicators**: Same edit styling as lower section with debouncing visual feedback
- **Input Behavior**: Can clear 0 values (empty state) and shows debouncing status with color-coded borders
- **Debouncing**: 800ms debounce delay with visual feedback (yellow for updating, orange for pending)
- **Cash Investment Tooltip**: Add "(i)" tooltip icon next to Cash Investment values in upper section that displays "Actual wire amount: $X,XXX" format using `actualWireAmount` from API response
- **Tooltip Condition**: Tooltip only appears when `actualWireAmount` is not 0 (non-zero values only)

#### 4.2.2. Lower Section Cash Investment Tooltip
- **Location**: Cash Investment column in lower section (Section 3 rows)
- **Position**: Right of the delete icon (trash icon)
- **Icon**: Blue `(i)` icon with hover tooltip
- **Tooltip Content**: Shows actual wire amount with descriptive text
- **Styling**: Same blue styling as Ownership Change tooltip
- **Spacing**: Appropriate spacing between delete icon and tooltip icon

#### 4.2.3. Dynamic Payload Structure for Update API
The `updateInvestorInvestmentAmount` API now supports three different payload structures based on the available ID:

**Investor Type (when `id` is present)**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "shareholderName": "string",
  "investmentAmount": 0,
  "valuationModeType": "fixed_pre_money"
}
```

**Officer Type (when `officerId` is present)**
```json
{
  "officerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "shareholderName": "string",
  "investmentAmount": 0,
  "valuationModeType": "fixed_pre_money"
}
```

**Service Provider Type (when `serviceProviderId` is present)**
```json
{
  "serviceProviderId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "shareholderName": "string",
  "investmentAmount": 0,
  "valuationModeType": "fixed_pre_money"
}
```

#### 4.2.4. Payload Logic
- **ID Priority**: Only one ID type will be present per row
- **Conditional Inclusion**: Only include the ID field that is present (not null)
- **Always Include**: `shareholderName`, `investmentAmount`, `valuationModeType`

#### 4.2.5. Delete API Updates
- **Endpoint**: `/api/p2/companies/{companyId}/captable/proforma/delete`
- **Payload Structure**: Same dynamic structure as update API
- **Logic**: Send the appropriate ID field based on what's available
- **Implementation**: Complete three-layer architecture (Client, Service, Hook) with proper payload construction
- **Required Fields**: Always include `valuationModeType` based on dropdown selection
- **ID Fields**: Include `id`, `officerId`, or `serviceProviderId` based on what's present

### 5. Additional Output Sections
- **Pro Rata Rights Table**: New table beneath Pro Forma Cap Table with:
  - **Columns**: Investor, Pro Rata Amount, Number of Series A Shares
  - **Data Source**: Existing investors from current cap table
  - **Purpose**: Show pro rata investment entitlements for existing investors
  - **Statement**: Italicized text explaining pro rata rights and cap table inclusion
- **Ownership Distribution**: Updated pie chart with filters
- **Enhanced Voting Rights**: Streamlined voting analysis with consolidated voting power columns and info capsule
- **Updated Stock Option Plan**: Reflected changes in option pool (client signed off)
- **Export Functionality**: PDF and Excel export of entire Pro Forma Model UI

---

## Enhanced Data Model

### Updated Type Definitions (Enhanced December 2024)
Add to `src/types/capTable.ts`:

```ts
// Pro Forma Model Types
export type ValuationMode = 'Fixed Pre-Money' | 'Fixed Post-Money';
export type PreferredStockSeries = 'Series Seed' | 'Series A';

// New Filter Types (December 2024)
export type ProFormaRoleFilter = 'All' | 'Founders' | 'Investors' | 'Advisors' | 'Contractors' | 'Employees';
export type ProFormaShareClassFilter = 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';

export interface ProFormaInputs {
  designation: ValuationMode;
  fixedValuation: number;
  investmentAmount: number;
  optionPoolPercentage: number;
  preferredStockDesignation: PreferredStockSeries;
  commonStockBuffer: number;
}

// New Filter State Interface (December 2024)
export interface ProFormaFilters {
  roleFilter: ProFormaRoleFilter;
  shareClassFilter: ProFormaShareClassFilter;
  shareholderNameSearch: string;
}

export interface ProFormaSummaryMetrics {
  effectivePreMoneyValuation?: number;
  effectivePostMoneyValuation?: number;
  seriesPricing: SeriesPricing[];
  authorizedStock: AuthorizedStockBreakdown;
  totalAuthorizedStock: number;
  totalPool: number; // Total option pool size
  poolRemainingForIssuance: number; // Available option pool for future grants
  notes: string[];
}

export interface SeriesPricing {
  seriesName: string;
  pricePerShare: number;
  sharesIssued: number;
  totalValue: number;
}

export interface AuthorizedStockBreakdown {
  commonStock: number;
  preferredStock: number;
  seriesStock: { [series: string]: number };
}

export interface ProFormaCapTableItem {
  id: string;
  name: string;
  investmentAmount: number;
  commonStockShares: number;
  stockOptionPlanShares: number;
  seriesAShares: number;
  seriesA1Shares: number;
  totalShares: number;
  fullyDilutedOwnership: number;
  ownershipChange: number;
  catchInvestment: number;
  isStockOptionPoolRow?: boolean; // Flag for Stock Option Pool Increase row
  isSection3Row?: boolean; // Flag for Section 3 rows (Additional Investor, Unallocated New Money)
}

export interface ProFormaCapTableData {
  items: ProFormaCapTableItem[];
  unallocatedNewMoney: number;
  totalCurrentShares: number;
  totalNewShares: number;
  totalPostMoneyShares: number;
  series: string[];
}

export interface ProFormaVotingRightsItem {
  id: string;
  name: string;
  // Voting percentage columns
  overallVotingPercentage: number;
  commonStockVotingPercentage: number;
  preferredStockVotingPercentage: number;
  // Voting power columns
  overallVotingPower: 'High' | 'Medium' | 'Low';
  commonStockVotingPower: 'High' | 'Medium' | 'Low';
  preferredStockVotingPower: 'High' | 'Medium' | 'Low';
}

export interface ProFormaVotingRightsData {
  items: ProFormaVotingRightsItem[];
  totalVotingShares: number;
  overallMajorityThreshold: number;
  preferredStockMajorityThreshold: number;
  commonStockMajorityThreshold: number;
  infoMessage: string; // Static message for info capsule
}

export interface ProFormaStockOptionPlanData {
  totalPlanSize: number;
  allocatedShares: number;
  remainingPlan: number;
  postMoneyAllocation: number;
  grants: ProFormaSOPGrant[];
  lastUpdated: string;
}

export interface ProFormaSOPGrant {
  id: string;
  fullName: string;
  role: string;
  currentShares: number;
  newShares: number;
  totalShares: number;
  vestingSchedule: string;
  grantStatus: 'Promised' | 'Granted';
  grantType: 'Restricted Stock' | 'Option';
}

// Add Investor Modal Types
export interface AddInvestorModalData {
  investorName: string;
  investmentAmount: number;
}


// Pro Rata Rights Table Types
export interface ProRataRightsItem {
  id: string;
  investorName: string;
  currentOwnershipPercentage: number;
  proRataAmount: number; // Auto-calculated based on current ownership
  numberOfSeriesAShares: number; // Auto-calculated based on round and pro rata amount
  isExistingInvestor: boolean;
}

export interface ProRataRightsData {
  items: ProRataRightsItem[];
  totalProRataAmount: number;
  roundType: PreferredStockSeries;
  pricePerShare: number;
}

export interface ProFormaModelData {
  inputs: ProFormaInputs;
  summary: ProFormaSummaryMetrics;
  capTable: ProFormaCapTableData;
  votingRights: ProFormaVotingRightsData;
  stockOptionPlan: ProFormaStockOptionPlanData;
  proRataRights: ProRataRightsData;
  ownershipDistribution: PieChartItem[];
  lastUpdated: string;
  // Export functionality
  exportOptions: {
    pdfEnabled: boolean;
    excelEnabled: boolean;
  };
}
```

---

## API Integration Requirements

### 1. API Endpoint Structure
- **Endpoint**: `/api/p2/companies/captable/proforma`
- **Method**: `GET`
- **Parameters**: Query parameters (including CompanyId)
- **Purpose**: Single API call to populate all Pro Forma Model data

### 2. API Request Parameters (Updated December 2024)
```
GET /api/p2/companies/captable/proforma?CompanyId=4dc692cc-**************-6e7b9c0ce572&PrePostMoneyValuation=15000000&InvestmentAmount=5000000&OptionPoolAvailableForIssuance=10&CommonStockBuffer=5&ValuationModeType=fixed_pre_money&PreferredStockDesignation=SeriesA&ProFormaRoleFilter=All&ProFormaShareClassFilter=All&ShareHolderName=
```

#### Query Parameters
**Core Pro Forma Parameters:**
- **CompanyId**: Company UUID (string)
- **PrePostMoneyValuation**: Pre-Money/Post-Money Valuation value
- **InvestmentAmount**: Investment Amount value
- **OptionPoolAvailableForIssuance**: Option Pool Available for Issuance Post-Money value
- **CommonStockBuffer**: Common Stock Buffer value
- **ValuationModeType**: "fixed_pre_money" or "fixed_post_money"
- **PreferredStockDesignation**: "SeriesA" or "SeriesA1"

**New Filter Parameters (December 2024):**
- **ProFormaRoleFilter**: Role filter (string) - "All", "Founders", "Investors", "Advisors", "Contractors", "Employees"
- **ProFormaShareClassFilter**: Share class filter (string) - "All", "Common", "SeriesAPreferred", "StockOptionPool"
- **ShareHolderName**: Shareholder name search (string) - Text search term for filtering by shareholder name

#### Request Field Mappings
**Core Pro Forma Field Mappings:**
- **PrePostMoneyValuation** → Pre-Money/Post-Money Valuation input (UI shows as entered)
- **InvestmentAmount** → Investment Amount input (UI shows as entered)
- **OptionPoolAvailableForIssuance** → Option Pool Available for Issuance Post-Money input (UI shows as entered)
- **CommonStockBuffer** → Common Stock Buffer input (UI shows as entered)
- **ValuationModeType** → Valuation Mode toggle (UI shows as "Fixed Pre-Money" or "Fixed Post-Money")
- **PreferredStockDesignation** → Preferred Stock Designation dropdown (UI shows as "Series A" or "Series A-1")

**New Filter Field Mappings (December 2024):**
- **ProFormaRoleFilter** → Role filter dropdown (UI shows as "Role" placeholder)
- **ProFormaShareClassFilter** → Share Class filter dropdown (UI shows as "Share Class" placeholder)
- **ShareHolderName** → Search input field (UI shows as "Search shareholders..." placeholder)

### 3. API Response Structure (Updated December 2024)
```json
{
  "message": "Success",
  "data": {
    "proformaSummaryMetrics": {
      "effectivePrePostMoneyValuation": 0,
      "seriesPricePerShare": {
        "Series Seed1": {
          "pricePerShare": 0,
          "shareIssued": 0
        },
        "Series Seed": {
          "pricePerShare": 0,
          "shareIssued": 0
        }
      }
    },
    "authorizedStockBreakdown": {
      "commonStock": 14000000,
      "preferredStock": 0,
      "series": {
        "Series Seed1": 0,
        "Series Seed": 0
      },
      "totalPool": 15000000,
      "poolRemainingForIssuance": 6000000
    },
    "proFormaCapTable": {
      "classACommonStockholders": [
        {
          "id": "485f1a9f-a1db-47d6-9c45-017b7f1147e6",
          "serviceProviderId": null,
          "officerId": "485f1a9f-a1db-47d6-9c45-017b7f1147e6",
          "shareholderName": "Omed",
          "role": "Director",
          "commonStock": 4500000,
          "sopCommonStock": 0,
          "total": 4500000,
          "fullyDilutedOwnership": 44.12,
          "cashInvestment": 0,
          "newCommonStock": 4500000,
          "newSopCommonStock": 0,
          "newTotal": 4500000,
          "newFullyDilutedOwnership": 44.12,
          "actualWireAmount": 0,
          "series": 0
        },
        {
          "id": "0edaac67-a34a-491e-9899-5c89aba9b0d5",
          "serviceProviderId": null,
          "officerId": "0edaac67-a34a-491e-9899-5c89aba9b0d5",
          "shareholderName": "Abdul",
          "role": "CEO",
          "commonStock": 4500000,
          "sopCommonStock": 0,
          "total": 4500000,
          "fullyDilutedOwnership": 44.12,
          "cashInvestment": 0,
          "newCommonStock": 4500000,
          "newSopCommonStock": 0,
          "newTotal": 4500000,
          "newFullyDilutedOwnership": 44.12,
          "actualWireAmount": 0,
          "series": 0
        },
        {
          "id": "1837fd84-d05d-440e-8605-c8fcb6c32ec7",
          "serviceProviderId": "1837fd84-d05d-440e-8605-c8fcb6c32ec7",
          "officerId": null,
          "shareholderName": "Sam",
          "role": "advisor",
          "commonStock": 200000,
          "sopCommonStock": 0,
          "total": 200000,
          "fullyDilutedOwnership": 1.96,
          "cashInvestment": 0,
          "newCommonStock": 200000,
          "newSopCommonStock": 0,
          "newTotal": 200000,
          "newFullyDilutedOwnership": 1.96,
          "actualWireAmount": 0,
          "series": 0
        }
      ],
      "outstandingStockOptionPlanShares": {
        "totalShares": 350000,
        "fullyDilutedOwnership": 3.43,
        "newFullyDilutedOwnership": 3.43
      },
      "promisedStockOptionPlanShares": {
        "totalShares": 0,
        "fullyDilutedOwnership": 0,
        "newFullyDilutedOwnership": 0
      },
      "stockOptionPlanSharesAvailable": {
        "totalShares": 650000,
        "fullyDilutedOwnership": 6.37,
        "newFullyDilutedOwnership": 6.37
      },
      "sopPoolIncrease": {
        "totalShares": 0,
        "fullyDilutedOwnership": 0,
        "newFullyDilutedOwnership": 0
      },
      "preferredCommonStockholders": {
        "Series Seed1": [
          {
            "id": null,
            "cashInvestment": 0,
            "investor": "Note 1",
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": 0,
            "newFullyDilutedOwnerShip": 0
          },
          {
            "id": null,
            "cashInvestment": 0,
            "investor": "Note 2",
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": 0,
            "newFullyDilutedOwnerShip": 0
          },
          {
            "id": null,
            "cashInvestment": 0,
            "investor": "Safe 1",
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": 0,
            "newFullyDilutedOwnerShip": 0
          },
          {
            "id": null,
            "cashInvestment": 0,
            "investor": "Safe 2",
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": 0,
            "newFullyDilutedOwnerShip": 0
          },
          {
            "id": null,
            "cashInvestment": 0,
            "investor": "Safe 3",
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": 0,
            "newFullyDilutedOwnerShip": 0
          }
        ],
        "Unallocated New Money": [
          {
            "id": null,
            "cashInvestment": -1000000,
            "investor": null,
            "resultingShares": 0,
            "actualWireAmount": 0,
            "pricePerShare": null,
            "newFullyDilutedOwnerShip": 0
          }
        ]
      },
      "seiresADetails": [
        {
          "officerId": null,
          "serviceProviderId": null,
          "id": "a513915e-f3e8-4058-b0de-4611d8a2d5d8",
          "cashInvestment": 1000000,
          "investor": "Prashant",
          "resultingShares": 0,
          "actualWireAmount": 0,
          "pricePerShare": 0,
          "newFullyDilutedOwnerShip": 0
        }
      ]
    },
    "proRataRights": [
      {
        "investor": "Omed",
        "currentOwnershipPercentage": 44.12,
        "prorataAmount": 0,
        "seriesAShares": null
      },
      {
        "investor": "Abdul",
        "currentOwnershipPercentage": 44.12,
        "prorataAmount": 0,
        "seriesAShares": null
      },
      {
        "investor": "Sam",
        "currentOwnershipPercentage": 1.96,
        "prorataAmount": 0,
        "seriesAShares": null
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

### 4. API Response Changes and Data Mapping (December 2024)

#### 4.1. Key API Response Changes
- **Field Name Changes**:
  - `investmentAmount` → `cashInvestment` in `classACommonStockholders`
  - `investmentAmount` → `prorataAmount` in `proRataRights`
- **Data Structure Changes**:
  - `preferredCommonStockholders` series are now **arrays** instead of single objects
  - Added new `seiresADetails` array for Series A/Series Seed investors
  - Series naming changed from "SeriesA1", "SeriesA2" to "Series Seed1", "Series Seed", etc.
- **New Data Sources**:
  - `seiresADetails[]` - Array of Series A/Series Seed investors for static column
  - `preferredCommonStockholders[seriesName][]` - Arrays of investors per series for dynamic columns

#### 4.2. Cap Table Data Mapping Strategy

##### Upper Section (classACommonStockholders)
- **Cash Investment**: Use `cashInvestment` field
- **Series A/Series Seed Column**: Use `series` field from each stockholder object
- **Dynamic Series Columns**: All `0` (common stockholders don't have preferred shares)

##### Lower Section (preferredCommonStockholders + seiresADetails)
- **Cash Investment**: Use `cashInvestment` field from each investor in series arrays
- **Series A/Series Seed Column**: Use `resultingShares` from `seiresADetails[]` array
- **Dynamic Series Columns**: Use `resultingShares` from corresponding series array in `preferredCommonStockholders`

##### Pro Rata Rights
- **Pro Rata Amount**: Use `prorataAmount` field (renamed from `investmentAmount`)
- **Series A Shares**: Use `seriesAShares` field (unchanged)

#### 4.3. Pro Rata Rights Data Mapping
- **Data Source**: `proRataRights[]` array from API response
- **Field Mappings**:
  - `investor` → **Investor** column
  - `prorataAmount` → **Pro Rata Amount** column (renamed from `investmentAmount`)
  - `seriesAShares` → **Number of Series A Shares** column
- **Implementation**: Direct mapping from API response to table display

### 5. Data Mapping Requirements

#### Section 1: Inputs (API Payload)
- **UI Display**: Show user-entered values as-is
- **API Payload**: Send values in API-required format
- **ValuationModeType Mapping**:
  - UI: "Fixed Pre-Money" → API: "fixed_pre_money"
  - UI: "Fixed Post-Money" → API: "fixed_post_money"
- **PreferredStockDesignation Mapping**:
  - UI: "Series A" → API: "SeriesA"
  - UI: "Series A-1" → API: "SeriesA1"

#### Section 2: Summary Metrics (API Response)
- **Effective Post-Money Valuation**: `proformaSummaryMetrics.effectivePrePostMoneyValuation`
- **Series Pricing**: `proformaSummaryMetrics.seriesPricePerShare` (object with SeriesA1, SeriesA2 containing pricePerShare, shareIssued)
- **Authorized Stock Breakdown**: `authorizedStockBreakdown` object
- **Total Pool**: `authorizedStockBreakdown.totalPool`
- **Pool Remaining**: `authorizedStockBreakdown.poolRemainingForIssuance`
- **Calculation Notes**: Static data (not provided by API)
- **Display Logic**:
  - If value is `0`: Show `0`
  - If value is `null`: Show `-`
  - If value is `undefined`: Show `-`

#### Section 3: Cap Table Data (API Response)
- **Data Source**: `proFormaCapTable` object from API response
- **Three-Section Structure**: 
  - **Section 1 (Top)**: `proFormaCapTable.classACommonStockholders[]` - Dynamic rows based on API
  - **Section 2 (Middle)**: Hardcoded SOP summary rows with API data
  - **Section 3 (Bottom)**: `proFormaCapTable.preferredCommonStockholders` - Dynamic rows + "Unallocated New Money"

#### Section 1: Top Section (Individual Stockholders)
- **Data Source**: `proFormaCapTable.classACommonStockholders[]`
- **Dynamic Rows**: Number of rows depends on API response array length
- **Field Mappings**:
  - `shareholderName` → **Stockholder**
  - `cashInvestment` → **Cash Investment** (renamed from Investment Amount)
  - `commonStock` → **Common Stock**
  - `sopCommonStock` → **Stock Option Plan**
  - `series` → **Series A/Series Seed Column** (populated from `series` field in classACommonStockholders)
  - **Dynamic Series Columns**: Determined by `preferredCommonStockholders` keys (Series Seed1, Series Seed2, etc.)
  - `newTotal` → **Total Shares** (use newTotal instead of total for upper section)
  - `fullyDilutedOwnership` → **Fully Diluted Ownership**
  - **Ownership Change**: Calculated as `newFullyDilutedOwnership - fullyDilutedOwnership`
  - **Tooltip Calculation**: Use actual API values for tooltip display:
    - **From Value**: `fullyDilutedOwnership` (current ownership percentage)
    - **To Value**: `newFullyDilutedOwnership` (new ownership percentage after round)
    - **Display Format**: `[increased/decreased from X.XX% to Y.YY%]` (2 decimal places)
  - **Cash Investment Tooltip**: Add "(i)" icon next to Cash Investment values that shows "Actual wire amount: $X,XXX" using `actualWireAmount` (only when `actualWireAmount` is not 0)

#### Section 2: Middle Section (SOP Summary Rows)
- **Data Source**: Multiple objects from `proFormaCapTable`
- **Hardcoded Row Names**: 
  - "Outstanding Stock Option Plan Shares" → `proFormaCapTable.outstandingStockOptionPlanShares`
  - "Promised Stock Option Plan Shares" → `proFormaCapTable.promisedStockOptionPlanShares`
  - "Stock Option Plan Shares Available" → `proFormaCapTable.stockOptionPlanSharesAvailable`
  - "Stock Option Pool Increase" → `proFormaCapTable.sopPoolIncrease`
- **Field Mappings**:
  - `totalShares` → **Total Shares**
  - `fullyDilutedOwnership` → **Fully Diluted Ownership**
  - **Ownership Change**: Calculated as `newFullyDilutedOwnership - fullyDilutedOwnership` (show actual percentage change)
  - **Tooltip Calculation**: Use actual API values for SOP tooltip display:
    - **From Value**: `fullyDilutedOwnership` (current ownership percentage)
    - **To Value**: `newFullyDilutedOwnership` (new ownership percentage after round)
    - **Display Format**: `[increased/decreased from X.XX% to Y.YY%]` (2 decimal places)
  - Other columns show "-" (not applicable)

#### Section 3: Bottom Section (Additional Investors & Unallocated)
- **Data Source**: `proFormaCapTable.preferredCommonStockholders` (arrays) + `proFormaCapTable.seiresADetails` (array)
- **Dynamic Rows**: Based on API response arrays in `preferredCommonStockholders` (excluding "Unallocated New Money")
- **Hardcoded Row**: "Unallocated New Money" (always present)
- **Delete Functionality**: Trash icon with confirmation modal for editable investors only
- **Field Mappings**:
  - `investor` → **Stockholder**
  - `cashInvestment` → **Cash Investment** (conditional editability based on ID, renamed from Investment Amount)
  - **Series A/Series Seed Column**: Populated from `seiresADetails[].resultingShares` for Series A/Series Seed shares
  - `resultingShares` → **Dynamic Series Columns** (mapped to appropriate series from `preferredCommonStockholders`)
- **Delete Functionality**:
  - **API Endpoint**: `DELETE /api/p2/companies/{companyId}/captable/proforma/{id}`
  - **UI Element**: Trash icon next to editable investor rows only
  - **Confirmation Modal**: Simple confirmation with investor name and investment amount
  - **Error Handling**: Graceful error handling with user feedback
  - `newFullyDilutedOwnerShip` → **Fully Diluted Ownership**
  - **Ownership Change**: Calculated as `newFullyDilutedOwnerShip - fullyDilutedOwnership` (if available)
- **Cash Investment Editability Logic**:
  - **Editable**: If row has valid `id` (not null) - displays as input field
  - **Read-only**: If row has `id: null` - displays as plain text
  - **Visual Indicators**: Different styling for editable vs read-only states
  - **API Integration**: Uses POST `/api/p2/companies/{companyId}/captable/proforma` for updates

#### Dynamic Series Columns
- **Column Generation**: Based on keys in `proFormaCapTable.preferredCommonStockholders`
- **Naming Convention**: Series Seed1 → "Series Seed1 Shares", Series Seed2 → "Series Seed2 Shares", etc.
- **Data Mapping**:
  - **Section 1**: `0` for all series columns (common stockholders don't have preferred shares)
  - **Section 2**: `-` for all series columns (not applicable)
  - **Section 3**: `resultingShares` from corresponding series array in `preferredCommonStockholders`
- **Series A/Series Seed Column**: 
  - **Section 1**: Populated from `series` field in `classACommonStockholders`
  - **Section 2**: `-` (not applicable)
  - **Section 3**: Populated from `seiresADetails[].resultingShares` for Series A/Series Seed shares

### 5. Empty State UI/UX Requirements (December 2024)

#### 5.1. Empty State Design Principles
- **Consistent Visual Language**: All empty states follow the same design pattern
- **Contextual Messaging**: Clear, helpful messages explaining the empty state
- **Actionable Guidance**: Provide clear next steps for users
- **Visual Hierarchy**: Use appropriate icons, typography, and spacing
- **Accessibility**: Ensure empty states are accessible and screen-reader friendly

#### 5.2. Specific Empty State Requirements

**Pro Forma Cap Table Empty State:**
- **Trigger**: When `capTableItems` array is empty or undefined
- **Message**: "No shareholders found for the current filters"
- **Sub-message**: "Try adjusting your filters or add new investors to see data here"
- **Actions**: "Add Investor" button, "Clear Filters" button
- **Icon**: Users icon with subtle styling

**Pro Rata Rights Empty State:**
- **Trigger**: When `proRataRights.items` array is empty
- **Message**: "No pro rata rights configured"
- **Sub-message**: "Pro rata rights will appear here once investors are added with pro rata entitlements"
- **Actions**: "Learn More" link to documentation
- **Icon**: FileText icon with subtle styling

**Voting Rights Analysis Empty State:**
- **Trigger**: When voting rights data is not available
- **Message**: "Voting rights analysis not available"
- **Sub-message**: "This analysis requires shareholder data to calculate voting power distribution"
- **Actions**: "Refresh Data" button
- **Icon**: Vote icon with subtle styling

**Stock Option Plan Empty State:**
- **Trigger**: When stock option plan details are not available
- **Message**: "No stock option plan details available"
- **Sub-message**: "Set up your stock option plan to see detailed allocation information"
- **Actions**: "Set Up Stock Options" button, "Learn More" link
- **Icon**: Briefcase icon with subtle styling

#### 5.3. Loading States
- **Skeleton Loading**: Show skeleton placeholders during data fetching
- **Progressive Loading**: Load different sections independently
- **Timeout Handling**: Show empty state after reasonable timeout period

#### 5.4. Error States
- **API Errors**: Show retry button with error message
- **Network Errors**: Show offline indicator with retry option
- **Permission Errors**: Show appropriate access denied messaging

### 6. Implementation Architecture (Enhanced December 2024)

#### 5.1. Global Filter Integration Data Flow
**Data Flow for Global Filter Integration:**
1. **User Input** → CapTableFilters component (global filter UI)
2. **Global State Update** → Application-level filter state management
3. **ProFormaModel Consumption** → ProFormaModel reads global filter state
4. **API Conversion** → Combined inputs and global filters converted to API parameters
5. **Debounced Call** → Single API call with all parameters (500ms delay)
6. **API Response** → Filtered and recalculated pro forma data
7. **UI Update** → All Pro Forma sections update with filtered data

**Component Integration:**
- **CapTableFilters.tsx**: Global filter component (no changes needed)
- **Global State Management**: Context or state management for filter state
- **ProFormaModel.tsx**: Consumes global filter state, no direct CapTableFilters import
- **useProforma.hooks.ts**: Extended to handle global filter parameters in API calls
- **proforma.service.ts**: Updated to accept filter parameters
- **client.ts**: Updated API method to include filter query parameters

#### 5.2. Client Layer (`src/integrations/legal-concierge/client.ts`)
Enhanced API methods with filter support:

```tsx
// GET: Get proforma data (Enhanced with Filters - December 2024)
async getProformaData(companyId: string, params: {
  PrePostMoneyValuation?: number;
  InvestmentAmount?: number;
  OptionPoolAvailableForIssuance?: number;
  CommonStockBuffer?: number;
  ValuationModeType?: 'fixed_pre_money' | 'fixed_post_money';
  PreferredStockDesignation?: 'SeriesA' | 'SeriesA1';
  // New Filter Parameters (December 2024)
  ProFormaRoleFilter?: 'All' | 'Founders' | 'Investors' | 'Employees' | 'Advisors' | 'Contractors';
  ProFormaShareClassFilter?: 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';
  ShareHolderName?: string;
}) {
  try {
    const queryParams = new URLSearchParams();
    // Add CompanyId as first parameter
    queryParams.append('CompanyId', companyId);
    // Add all mandatory parameters (default to 0 if not provided)
    queryParams.append('PrePostMoneyValuation', (params.PrePostMoneyValuation ?? 0).toString());
    queryParams.append('InvestmentAmount', (params.InvestmentAmount ?? 0).toString());
    queryParams.append('OptionPoolAvailableForIssuance', (params.OptionPoolAvailableForIssuance ?? 0).toString());
    queryParams.append('CommonStockBuffer', (params.CommonStockBuffer ?? 0).toString());
    queryParams.append('ValuationModeType', params.ValuationModeType ?? 'fixed_pre_money');
    queryParams.append('PreferredStockDesignation', params.PreferredStockDesignation ?? 'SeriesA');
    
    // Add new filter parameters (December 2024)
    queryParams.append('ProFormaRoleFilter', params.ProFormaRoleFilter ?? 'All');
    queryParams.append('ProFormaShareClassFilter', params.ProFormaShareClassFilter ?? 'All');
    queryParams.append('ShareHolderName', params.ShareHolderName ?? '');
    
    const response = await this.get(
      `/api/p2/companies/captable/proforma?${queryParams.toString()}`
    );
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// POST: Update investor investment amount
async updateInvestorInvestmentAmount(companyId: string, data: {
  id: string;
  shareholderName: string;
  investmentAmount: number;
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}) {
  try {
    const response = await this.post(
      `/api/p2/companies/${companyId}/captable/proforma`,
      data
    );
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

```

#### 5.2. Service Layer (`src/services/cap-table/proforma.service.ts`)
Enhanced service layer with filter support:

```tsx
import { apiClient } from '@/integrations/legal-concierge/client';
import { ProFormaModelData } from '@/types/capTable';

export interface ProFormaApiParams {
  PrePostMoneyValuation?: number;
  InvestmentAmount?: number;
  OptionPoolAvailableForIssuance?: number;
  CommonStockBuffer?: number;
  ValuationModeType?: 'fixed_pre_money' | 'fixed_post_money';
  PreferredStockDesignation?: 'SeriesA' | 'SeriesA1';
  // New Filter Parameters (December 2024)
  ProFormaRoleFilter?: 'All' | 'Founders' | 'Investors' | 'Employees' | 'Advisors' | 'Contractors';
  ProFormaShareClassFilter?: 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';
  ShareHolderName?: string;
}

export interface UpdateInvestorParams {
  id: string;
  shareholderName: string;
  investmentAmount: number;
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}

export const proformaService = {
  async getProformaData(companyId: string, params: ProFormaApiParams): Promise<ProFormaModelData> {
    const response = await apiClient.getProformaData(companyId, params);
    return response;
  },

  async updateInvestorInvestmentAmount(companyId: string, data: UpdateInvestorParams) {
    const response = await apiClient.updateInvestorInvestmentAmount(companyId, data);
    return response;
  }
};
```

#### 5.3. Hook Layer (`src/hooks/cap-table/useProforma.hooks.ts`)
Create a new hook file:

```tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { proformaService, ProFormaApiParams, UpdateInvestorParams } from '@/services/cap-table/proforma.service';
import { useCompanySelection } from '@/contexts/CompanySelectionContext';

export const useProformaData = (params: ProFormaApiParams) => {
  const { selectedCompany } = useCompanySelection();
  
  return useQuery({
    queryKey: ['proforma', selectedCompany?.id, params],
    queryFn: () => proformaService.getProformaData(selectedCompany!.id, params),
    enabled: !!selectedCompany?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useUpdateInvestorInvestmentAmount = () => {
  const queryClient = useQueryClient();
  const { selectedCompany } = useCompanySelection();
  
  return useMutation({
    mutationFn: (data: UpdateInvestorParams) => 
      proformaService.updateInvestorInvestmentAmount(selectedCompany!.id, data),
    onSuccess: () => {
      // Invalidate and refetch proforma data
      queryClient.invalidateQueries({ queryKey: ['proforma'] });
    },
  });
};

```

#### 5.4. Input Handling
```tsx
// Convert UI values to API format including filters (Enhanced December 2024)
const convertToApiFormat = (inputs: ProFormaInputs, filters: ProFormaFilters): ProFormaApiParams => {
  return {
    // Core Pro Forma Parameters
    PrePostMoneyValuation: inputs.fixedValuation || undefined,
    InvestmentAmount: inputs.investmentAmount || undefined,
    OptionPoolAvailableForIssuance: inputs.optionPoolPercentage || undefined,
    CommonStockBuffer: inputs.commonStockBuffer || undefined,
    ValuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money',
    PreferredStockDesignation: inputs.preferredStockDesignation === 'Series A' ? 'SeriesA' : 'SeriesA1',
    // New Filter Parameters (December 2024)
    ProFormaRoleFilter: filters.roleFilter,
    ProFormaShareClassFilter: filters.shareClassFilter,
    ShareHolderName: filters.shareholderNameSearch
  };
};

// Default filter state
const DEFAULT_FILTERS: ProFormaFilters = {
  roleFilter: 'All',
  shareClassFilter: 'All',
  shareholderNameSearch: ''
};
```

#### Response Handling
```tsx
// Convert API response to UI format (Updated for new API structure)
const convertFromApiFormat = (apiResponse: ProFormaApiResponse) => {
  const { proFormaCapTable } = apiResponse.data;
  
  // Generate dynamic series columns from preferredCommonStockholders keys
  const seriesColumns = Object.keys(proFormaCapTable.preferredCommonStockholders)
    .filter(key => key !== 'Unallocated New Money')
    .map(key => `${key} Shares`); // Series Seed1 -> Series Seed1 Shares
  
  // Convert Section 1: classACommonStockholders
  const section1Rows = proFormaCapTable.classACommonStockholders.map(stockholder => ({
    id: stockholder.id,
    name: stockholder.shareholderName,
    investmentAmount: stockholder.cashInvestment, // Updated field name
    commonStockShares: stockholder.commonStock,
    stockOptionPlanShares: stockholder.sopCommonStock,
    seriesAShares: stockholder.series || 0, // New field for Series A/Series Seed column
    // Dynamic series columns - all 0 for common stockholders
    ...seriesColumns.reduce((acc, series) => {
      const seriesKey = series.replace(' Shares', '').toLowerCase().replace(' ', '') + 'Shares';
      acc[seriesKey] = 0;
      return acc;
    }, {}),
    totalShares: stockholder.total,
    fullyDilutedOwnership: stockholder.fullyDilutedOwnership,
    ownershipChange: stockholder.newFullyDilutedOwnership - stockholder.fullyDilutedOwnership,
    catchInvestment: stockholder.actualWireAmount,
  }));
  
  // Convert Section 2: SOP Summary rows (hardcoded names with API data)
  const section2Rows = [
    {
      id: 'outstanding-sop',
      name: 'Outstanding Stock Option Plan Shares',
      totalShares: proFormaCapTable.outstandingStockOptionPlanShares.totalShares,
      fullyDilutedOwnership: proFormaCapTable.outstandingStockOptionPlanShares.fullyDilutedOwnership,
      isStockOptionPoolRow: true,
    },
    {
      id: 'promised-sop',
      name: 'Promised Stock Option Plan Shares',
      totalShares: proFormaCapTable.promisedStockOptionPlanShares.totalShares,
      fullyDilutedOwnership: proFormaCapTable.promisedStockOptionPlanShares.fullyDilutedOwnership,
      isStockOptionPoolRow: true,
    },
    {
      id: 'available-sop',
      name: 'Stock Option Plan Shares Available',
      totalShares: proFormaCapTable.stockOptionPlanSharesAvailable.totalShares,
      fullyDilutedOwnership: proFormaCapTable.stockOptionPlanSharesAvailable.fullyDilutedOwnership,
      isStockOptionPoolRow: true,
    },
    {
      id: 'pool-increase',
      name: 'Stock Option Pool Increase',
      totalShares: proFormaCapTable.sopPoolIncrease.totalShares,
      fullyDilutedOwnership: proFormaCapTable.sopPoolIncrease.fullyDilutedOwnership,
      isStockOptionPoolRow: true,
    },
  ];
  
  // Convert Section 3: preferredCommonStockholders (arrays) + seiresADetails
  const section3Rows = [];
  
  // Process each series array in preferredCommonStockholders
  Object.entries(proFormaCapTable.preferredCommonStockholders).forEach(([seriesKey, investors]) => {
    if (Array.isArray(investors)) {
      investors.forEach(investor => {
        section3Rows.push({
          id: investor.id || `${seriesKey}-${investor.investor || 'unknown'}`,
          name: investor.investor || seriesKey,
          investmentAmount: investor.cashInvestment || 0, // Updated field name
          commonStockShares: 0,
          stockOptionPlanShares: 0,
          seriesAShares: 0, // Will be populated from seiresADetails
          // Dynamic series columns - map resultingShares to appropriate series
          ...seriesColumns.reduce((acc, series) => {
            const seriesKeyMatch = series.replace(' Shares', '');
            const seriesKeyForRow = series.toLowerCase().replace(' ', '') + 'Shares';
            acc[seriesKeyForRow] = seriesKey === seriesKeyMatch ? investor.resultingShares : 0;
            return acc;
          }, {}),
          totalShares: investor.resultingShares,
          fullyDilutedOwnership: investor.newFullyDilutedOwnerShip,
          ownershipChange: investor.newFullyDilutedOwnerShip - (investor.fullyDilutedOwnership || 0),
          catchInvestment: investor.actualWireAmount,
          isSection3Row: true,
          hasValidId: !!investor.id,
        });
      });
    }
  });
  
  // Add seiresADetails to Series A/Series Seed column for Section 3
  if (proFormaCapTable.seiresADetails && Array.isArray(proFormaCapTable.seiresADetails)) {
    proFormaCapTable.seiresADetails.forEach((seriesADetail, index) => {
      // Find corresponding row or create new one
      const existingRow = section3Rows.find(row => row.id === seriesADetail.id);
      if (existingRow) {
        existingRow.seriesAShares = seriesADetail.resultingShares;
      } else {
        section3Rows.push({
          id: seriesADetail.id || `series-a-${index}`,
          name: seriesADetail.investor || 'Series A Investor',
          investmentAmount: seriesADetail.cashInvestment || 0,
          commonStockShares: 0,
          stockOptionPlanShares: 0,
          seriesAShares: seriesADetail.resultingShares,
          ...seriesColumns.reduce((acc, series) => {
            const seriesKeyForRow = series.toLowerCase().replace(' ', '') + 'Shares';
            acc[seriesKeyForRow] = 0;
            return acc;
          }, {}),
          totalShares: seriesADetail.resultingShares,
          fullyDilutedOwnership: seriesADetail.newFullyDilutedOwnerShip,
          ownershipChange: seriesADetail.newFullyDilutedOwnerShip - (seriesADetail.fullyDilutedOwnership || 0),
          catchInvestment: seriesADetail.actualWireAmount,
          isSection3Row: true,
          hasValidId: !!seriesADetail.id,
        });
      }
    });
  }
  
  return {
    summary: {
      effectivePrePostMoneyValuation: apiResponse.data.proformaSummaryMetrics.effectivePrePostMoneyValuation,
      seriesPricePerShare: apiResponse.data.proformaSummaryMetrics.seriesPricePerShare,
      authorizedStock: apiResponse.data.authorizedStockBreakdown,
      totalPool: apiResponse.data.authorizedStockBreakdown.totalPool,
      poolRemainingForIssuance: apiResponse.data.authorizedStockBreakdown.poolRemainingForIssuance,
    },
    capTable: {
      items: [...section1Rows, ...section2Rows, ...section3Rows],
      seriesColumns,
    }
  };
};
```

#### Display Logic
```tsx
// Handle null/undefined values in UI
const formatValue = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  if (value === 0) return '0';
  return value.toString();
};

// Format negative values with parentheses for Pro Forma Cap Table
const formatNegativeValue = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  if (value === 0) return '0';
  if (value < 0) return `(${Math.abs(value)})`;
  return value.toString();
};

// Format currency with parentheses for negative values
const formatCurrencyWithParentheses = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  if (value === 0) return '$0';
  if (value < 0) return `($${Math.abs(value).toLocaleString()})`;
  return `$${value.toLocaleString()}`;
};

// Format percentage with parentheses for negative values (2 decimal places)
const formatPercentageWithParentheses = (value: number | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  if (value === 0) return '0.00%';
  if (value < 0) return `(${Math.abs(value).toFixed(2)}%)`;
  return `${value.toFixed(2)}%`;
};
```

### 6. API Integration Phases

#### Phase 1: Infrastructure Setup
- **Goal**: Set up the API integration infrastructure
- **Implementation**: 
  - Add `getProformaData` method to `APIClient` in `client.ts`
  - Add `updateInvestorInvestmentAmount` method to `APIClient` in `client.ts`
  - Create `proforma.service.ts` service layer
  - Create `useProforma.hooks.ts` hook layer with mutation support
  - Set up proper TypeScript interfaces
- **Status**: Ready for implementation

#### Phase 2: Input Section Integration
- **Goal**: Connect input fields to API query parameters
- **Implementation**: 
  - Convert UI inputs to API query parameters
  - Implement debounced API calls on input changes
  - Handle loading states and error handling
  - Use React Query for caching and state management
- **Status**: Ready for implementation

#### Phase 3: Summary Metrics Integration
- **Goal**: Populate summary metrics from API response
- **Implementation**:
  - Map API response to UI components
  - Handle null/undefined values (show `-` for null, `0` for zero)
  - Display loading states during API calls
  - Implement error fallback to static data
- **Status**: Ready for implementation

#### Phase 4: Cap Table Integration
- **Goal**: Populate cap table from API response
- **Implementation**:
  - **Section 1**: Map `proFormaCapTable.classACommonStockholders[]` to dynamic rows
  - **Section 2**: Map SOP summary objects to hardcoded rows with API data
  - **Section 3**: Map `proFormaCapTable.preferredCommonStockholders` to dynamic rows + "Unallocated New Money"
  - **Dynamic Series Columns**: Generate based on `preferredCommonStockholders` keys
  - **Ownership Change Calculation**: `newFullyDilutedOwnership - fullyDilutedOwnership`
  - **Series Column Mapping**: 
    - Section 1: `0` for all series (common stockholders)
    - Section 2: `-` for all series (not applicable)
    - Section 3: `resultingShares` from corresponding series object
- **Status**: Ready for implementation

#### Phase 5: Investment Amount Editability
- **Goal**: Implement conditional editability for Section 3 Investment Amount column
- **Implementation**:
  - **Conditional Rendering**: Show input field if `id` exists, plain text if `id` is null
  - **Visual Indicators**: Different styling for editable vs read-only states
  - **API Integration**: Use POST mutation to update investment amounts
  - **Real-time Updates**: Refresh data after successful updates
  - **Error Handling**: Handle API errors gracefully with user feedback
  - **Loading States**: Show loading indicators during updates
- **Status**: Ready for implementation

#### Phase 6: Add Investor Modal API Integration
- **Goal**: Integrate Add Investor Modal with POST API for creating new investors
- **Implementation**:
  - **Modal Structure**: Simplified modal with only Investor Name and Investment Amount fields
  - **Removed Fields**: Remove role dropdown from modal interface
  - **API Integration**: Call POST `/api/p2/companies/{companyId}/captable/proforma` endpoint
  - **Payload Structure**: Send modified payload without `id` key for new investors
  - **Real-time Updates**: Refresh cap table data after successful investor creation
  - **Error Handling**: Handle API errors with user-friendly messages
  - **Loading States**: Show loading indicators during API calls
  - **Success Feedback**: Display success toast notifications
- **Status**: Ready for implementation

### 7. Add Investor Modal Implementation

#### 7.1. Modal Structure Requirements
- **Simplified Interface**: Remove role dropdown, keep only essential fields
- **Required Fields**:
  - **Investor Name**: Text input for investor name
  - **Investment Amount**: Number input for investment amount
- **Validation**: Real-time validation for required fields
- **Professional Styling**: Consistent with existing modal design

#### 7.2. API Integration Requirements
- **Reuse Existing Infrastructure**: Use the same `updateInvestorInvestmentAmount` API, service, and hook
- **Endpoint**: Same POST endpoint as investment amount updates
- **URL**: `/api/p2/companies/{companyId}/captable/proforma`
- **Method**: `POST`
- **Payload Structure** (without `id` key for new investors):
```json
{
  "shareholderName": "New Investor Name",
  "investmentAmount": 1000000,
  "valuationModeType": "fixed_pre_money"
}
```
- **Implementation Strategy**: 
  - Reuse `useUpdateInvestorInvestmentAmount` hook
  - Pass `null` or empty string for `id` field to indicate new investor
  - API will handle creation vs update based on presence of `id`

#### 7.3. Modal Implementation
```tsx
// Add Investor Modal Component
const AddInvestorModal = ({ isOpen, onClose, onAdd }) => {
  const [investorName, setInvestorName] = useState('');
  const [investmentAmount, setInvestmentAmount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const updateInvestorMutation = useUpdateInvestorInvestmentAmount();
  const { inputs } = useProformaModel();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!investorName.trim()) {
      toast.error("Investor name is required");
      return;
    }

    if (investmentAmount <= 0) {
      toast.error("Investment amount must be greater than 0");
      return;
    }

    setIsLoading(true);
    
    try {
      await updateInvestorMutation.mutateAsync({
        id: '', // Empty ID indicates new investor
        shareholderName: investorName.trim(),
        investmentAmount: investmentAmount,
        valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
      });
      
      toast.success(`✅ Successfully added ${investorName} as new investor`);
      onClose();
      onAdd(); // Refresh cap table data
    } catch (error) {
      toast.error(`❌ Failed to add investor: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Investor</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="investorName">Investor Name</Label>
            <Input
              id="investorName"
              value={investorName}
              onChange={(e) => setInvestorName(e.target.value)}
              placeholder="Enter investor name"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="investmentAmount">Investment Amount</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
              <Input
                id="investmentAmount"
                type="number"
                value={investmentAmount}
                onChange={(e) => setInvestmentAmount(Number(e.target.value))}
                placeholder="0"
                min="0"
                step="1000"
                className="pl-7"
                required
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Adding...
                </>
              ) : (
                'Add Investor'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
```

#### 7.4. Integration with Pro Forma Model
- **Hook Integration**: Reuse `useUpdateInvestorInvestmentAmount` hook for API calls
- **State Management**: Handle modal open/close state in parent component
- **Data Refresh**: Automatically refresh cap table data after successful addition
- **Error Handling**: Display user-friendly error messages
- **Loading States**: Show loading indicators during API calls
- **SOLID Principles**: Single responsibility, reusing existing infrastructure

### 8. Error Handling
- **API Errors**: Display user-friendly error messages
- **Network Issues**: Show retry options
- **Validation Errors**: Highlight invalid inputs
- **Loading States**: Show loading indicators during API calls
- **Investment Amount Updates**: Handle validation errors for investment amount changes
- **Mutation Errors**: Display specific error messages for failed updates
- **Add Investor Errors**: Handle validation and API errors for new investor creation

### 9. Performance Considerations
- **Debouncing**: Debounce API calls to avoid excessive requests
- **Caching**: Cache API responses for better performance
- **Optimistic Updates**: Update UI immediately, sync with API
- **Error Recovery**: Graceful fallback to static data if API fails
- **Mutation Optimization**: Efficient handling of investment amount updates
- **Real-time Updates**: Automatic data refresh after successful mutations

---

## Component Architecture

### Entry Point
- `components/captable/ProFormaModel.tsx` (new component)
  - **Purpose**: Main Pro Forma Model component with comprehensive functionality
  - **Props**: None (self-contained with static data)
  - **State**: Inputs, calculations, section visibility
  - **Children**: Multiple sub-components for each section

### Enhanced Component Structure (Updated December 2024)
```
ProFormaModel.tsx
├── Section Navigation (sticky)
├── Input Section
│   ├── Designation Toggle
│   ├── Investment Amount Input
│   ├── Option Pool Input
│   ├── Preferred Stock Dropdown
│   ├── Common Stock Buffer Input
│   └── Professional Disclaimer
├── Filter Section (NEW - December 2024)
│   ├── CapTableFilters Component Integration
│   ├── Search Input (ShareHolderName)
│   ├── Role Filter Dropdown (ProFormaRoleFilter)
│   └── Share Class Filter Dropdown (ProFormaShareClassFilter)
├── Summary Metrics Section
│   ├── Conditional Valuation Display
│   ├── Series Pricing Cards
│   ├── Authorized Stock Breakdown
│   └── Calculation Notes
├── Pro Forma Cap Table Section
│   ├── Add Investor Button
│   ├── Simplified Single Table Layout
│   ├── New Column Structure (Stockholder, Cash Investment, Common Stock, Stock Option Plan, Series A/Series Seed, Series A-1, Total Shares, Fully Diluted Ownership, Ownership Change)
│   ├── Tooltip Integration for Key Metrics
│   ├── Stock Option Pool Increase Row
│   └── Export Functionality
├── Pro Rata Rights Table Section
│   ├── Pro Rata Rights Table
│   ├── Auto-Generated Investment Amounts
│   ├── Series Shares Calculation
│   └── Pro Rata Rights Statement
├── Ownership Distribution Section
│   ├── Interactive Pie Chart
│   ├── Filter Controls
│   └── Legend
├── Voting Rights Section
│   ├── Streamlined Voting Table
│   ├── Consolidated Voting Power Columns
│   └── Info Capsule with Hover Message
└── Stock Option Plan Section
    ├── Updated Pool Metrics
    ├── Grant Table
    └── Post-Money Calculations
```

### Component Integration Strategy (Updated December 2024)
**Existing Components (REUSED - No UI Recreated):**
- `CapTableFilters.tsx`: **EXISTING COMPONENT** - Reused as-is for filter functionality

**New Shared Components:**
- `ProFormaInputs.tsx`: Input controls and validation
- `ProFormaSummary.tsx`: Summary metrics and calculations
- `ProFormaCapTable.tsx`: Enhanced cap table with series columns and split layout
- `AddInvestorModal.tsx`: Modal for adding new investors with investment amount
- `ProRataRightsTable.tsx`: Pro rata rights table with auto-calculations
- `ProFormaOwnershipChart.tsx`: Interactive ownership distribution
- `ProFormaVotingRights.tsx`: Series-specific voting analysis (updated with "?" suffixes)
- `ProFormaStockOptionPlan.tsx`: Updated option plan display
- `SectionNavigation.tsx`: Sticky navigation component

**Filter Integration Approach:**
- **NO NEW UI CREATION**: Existing `CapTableFilters.tsx` component used directly
- **COMPONENT IMPORT**: Simply import and use CapTableFilters in ProFormaModel
- **PROP MAPPING**: Connect existing CapTableFilters props to ProFormaModel state
- **NO MODIFICATIONS**: CapTableFilters component remains unchanged

---

## Enhanced UI/UX Requirements

### 1. Section Navigation Design
- **Location**: Sticky top navigation bar
- **Layout**: Horizontal scrollable navigation with section names
- **Functionality**: Smooth scroll to sections with active state
- **Styling**: Professional design with hover effects

### 2. Input Section Design
- **Layout**: Card-based layout with clear grouping
- **Validation**: Real-time validation with error messages
- **Professional Styling**: Clean, modern interface suitable for financial applications

### 3. Input Section Layout (Editable Fields)
```tsx
<div className="bg-white rounded-lg border p-6 mb-6">
  <h3 className="text-lg font-semibold mb-4">Pro Forma Inputs</h3>
  
  {/* Designation Toggle */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">Valuation Mode</label>
    <ToggleGroup value={designation} onValueChange={setDesignation}>
      <ToggleGroupItem value="Fixed Pre-Money">Fixed Pre-Money</ToggleGroupItem>
      <ToggleGroupItem value="Fixed Post-Money">Fixed Post-Money</ToggleGroupItem>
    </ToggleGroup>
  </div>
  
  {/* Investment Amount */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">Investment Amount</label>
    <Input 
      type="number" 
      value={investmentAmount} 
      onChange={(e) => setInvestmentAmount(Number(e.target.value))}
      placeholder="$0"
    />
  </div>
  
  {/* Option Pool Available for Issuance Post-Money */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">Option Pool Available for Issuance Post-Money</label>
    <Input 
      type="number" 
      value={optionPoolPercentage} 
      onChange={(e) => setOptionPoolPercentage(Number(e.target.value))}
      placeholder="0"
    />
  </div>
  
  {/* Preferred Stock Designation */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">Preferred Stock Designation</label>
    <Select value={preferredStockDesignation} onValueChange={setPreferredStockDesignation}>
      <SelectItem value="Series Seed">Series Seed</SelectItem>
      <SelectItem value="Series A">Series A</SelectItem>
    </Select>
  </div>
  
  {/* Common Stock Buffer */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">Common Stock Buffer</label>
    <Input 
      type="number" 
      value={commonStockBuffer} 
      onChange={(e) => setCommonStockBuffer(Number(e.target.value))}
      placeholder="5"
    />
  </div>
  
  {/* Pre-Money or Post-Money Valuation */}
  <div className="mb-4">
    <label className="text-sm font-medium mb-2">
      {designation === 'Fixed Pre-Money' ? 'Pre-Money Valuation' : 'Post-Money Valuation'}
    </label>
    <Input 
      type="number" 
      value={fixedValuation} 
      onChange={(e) => setFixedValuation(Number(e.target.value))}
      placeholder="$0"
    />
  </div>
  
  {/* Professional Disclaimer */}
  <Alert className="mt-4">
    <AlertCircle className="h-4 w-4" />
    <AlertDescription>
      This pro forma calculator assumes that all Convertible Securities will convert 
      in the pre-money value of the Company as this is industry-standard. 
      For more information, please contact Founders Form.
    </AlertDescription>
  </Alert>
</div>
```

### 4. Summary Metrics Section Design
- **Layout**: Grid of metric cards with professional styling
- **Conditional Display**: Different metrics based on designation choice
- **Professional Formatting**: Currency, percentages, and numbers properly formatted

### 5. Summary Metrics Layout
```tsx
<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
  {/* Conditional Valuation Card */}
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground mb-2">
        {designation === 'Fixed Pre-Money' ? 'Effective Post-Money Valuation' : 'Effective Pre-Money Valuation'}
      </div>
      <div className="text-2xl font-bold">{formatCurrency(effectiveValuation)}</div>
      <div className="text-xs text-muted-foreground mt-1">
        {designation === 'Fixed Pre-Money' 
          ? 'Pre-money + Investment + Convertible conversions' 
          : 'Post-money - Investment - Convertible conversions'}
      </div>
    </CardContent>
  </Card>
  
  {/* Series Pricing Cards */}
  {seriesPricing.map((series) => (
    <Card key={series.seriesName}>
      <CardContent className="p-4">
        <div className="text-sm text-muted-foreground mb-2">
          {series.seriesName} Price Per Share
        </div>
        <div className="text-2xl font-bold">{formatCurrency(series.pricePerShare)}</div>
        <div className="text-xs text-muted-foreground mt-1">
          {formatNumber(series.sharesIssued)} shares issued
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### 6. Simplified Pro Forma Cap Table Design
- **Layout**: Single unified table without visual sections or separators
- **Column Structure**: Streamlined columns in logical order
- **Professional Styling**: Clean borders, alternating row colors, modern design
- **Dynamic Calculations**: Real-time updates based on input changes
- **Tooltip Integration**: Inline tooltips on hover over "(i)" icons for each row
- **SOP Summary Rows**: Include three SOP summary rows plus Stock Option Pool Increase row

### 7. Enhanced Cap Table Structure (Updated per Client Requirements)
```tsx
{/* Add Investor Button */}
<div className="mb-4">
  <Button onClick={() => setAddInvestorModalOpen(true)}>
    <Plus className="h-4 w-4 mr-2" />
    Add Investor
  </Button>
</div>

{/* Pro Forma Cap Table with Pre-Round/Post-Round Split */}
<div className="rounded-md border">
  <Table>
    <TableHeader>
      <TableRow>
        {/* Pre-Round Section */}
        <TableHead className="border-r-2 border-gray-300 bg-gray-50">
          <div className="text-center font-bold text-gray-700">Pre-Round</div>
        </TableHead>
        <TableHead>Role</TableHead>
        <TableHead className="text-right">Common Stock</TableHead>
        <TableHead className="text-right border-r-2 border-gray-300">Current Ownership %</TableHead>
        
        {/* Post-Round Section */}
        <TableHead className="bg-blue-50">
          <div className="text-center font-bold text-blue-700">Post-Round</div>
        </TableHead>
        <TableHead className="text-right">Investment Amount</TableHead>
        <TableHead className="text-right">Common Stock</TableHead>
        {/* Dynamic Series Columns */}
        {series.map((seriesName) => (
          <TableHead key={seriesName} className="text-right">
            {seriesName} Shares
          </TableHead>
        ))}
        <TableHead className="text-right">Total Shares</TableHead>
        <TableHead className="text-right">%</TableHead>
        <TableHead className="text-right">Ownership Change</TableHead>
        <TableHead className="text-right">Exact Investment Amount</TableHead>
      </TableRow>
    </TableHeader>
  <TableBody>
    {capTableItems.map((item) => (
      <TableRow key={item.id}>
        <TableCell className="font-medium">{item.name}</TableCell>
        <TableCell>{item.role}</TableCell>
        <TableCell className="text-right">{formatNumber(item.currentShares)}</TableCell>
        <TableCell className="text-right">{item.currentOwnership.toFixed(2)}%</TableCell>
        <TableCell className="text-right">{formatNumber(item.newShares)}</TableCell>
        <TableCell className="text-right">{item.newOwnership.toFixed(2)}%</TableCell>
        <TableCell className="text-right">
          <span className={item.ownershipChange >= 0 ? 'text-green-600' : 'text-red-600'}>
            {item.ownershipChange >= 0 ? '+' : ''}{item.ownershipChange.toFixed(2)}%
          </span>
        </TableCell>
        {/* Dynamic Series Cells */}
        {series.map((seriesName) => (
          <TableCell key={seriesName} className="text-right">
            {formatNumber(item.seriesAllocation[seriesName] || 0)}
          </TableCell>
        ))}
        <TableCell className="text-right">{formatNumber(item.totalShares)}</TableCell>
        <TableCell className="text-right">{item.fullyDilutedOwnership.toFixed(2)}%</TableCell>
      </TableRow>
    ))}
    {/* SOP Summary Rows */}
    <TableRow className="bg-muted/30">
      <TableCell className="font-medium">Outstanding Stock Option Plan Shares</TableCell>
      <TableCell>-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      {series.map((seriesName) => (
        <TableCell key={seriesName} className="text-right">-</TableCell>
      ))}
      <TableCell className="text-right">{formatNumber(outstandingSOPShares)}</TableCell>
      <TableCell className="text-right">{((outstandingSOPShares / totalAggregateShares) * 100).toFixed(2)}%</TableCell>
    </TableRow>
    <TableRow className="bg-muted/30">
      <TableCell className="font-medium">Promised Stock Option Plan Shares</TableCell>
      <TableCell>-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      {series.map((seriesName) => (
        <TableCell key={seriesName} className="text-right">-</TableCell>
      ))}
      <TableCell className="text-right">{formatNumber(promisedSOPShares)}</TableCell>
      <TableCell className="text-right">{((promisedSOPShares / totalAggregateShares) * 100).toFixed(2)}%</TableCell>
    </TableRow>
    <TableRow className="bg-muted/30">
      <TableCell className="font-medium">Stock Option Plan Shares Available</TableCell>
      <TableCell>-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      <TableCell className="text-right">-</TableCell>
      {series.map((seriesName) => (
        <TableCell key={seriesName} className="text-right">-</TableCell>
      ))}
      <TableCell className="text-right">{formatNumber(availableSOPShares)}</TableCell>
      <TableCell className="text-right">{((availableSOPShares / totalAggregateShares) * 100).toFixed(2)}%</TableCell>
    </TableRow>
  </TableBody>
</Table>
```

### 8. Pro Rata Rights Table Structure
```tsx
{/* Pro Rata Rights Table */}
<div className="mt-8">
  <h3 className="text-lg font-semibold mb-2">Pro Rata Rights</h3>
  <p className="text-sm italic text-muted-foreground mb-4">
    The Investors noted below are entitled to invest the amounts listed in this table for the round. 
    Please include the investment amount in the Pro Forma Cap Table for an illustration of the cap table with their pro rata.
  </p>
  
  <div className="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Investor</TableHead>
          <TableHead className="text-right">Pro Rata Amount</TableHead>
          <TableHead className="text-right">Number of Series A Shares</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {proRataRights.items.map((item) => (
          <TableRow key={item.id}>
            <TableCell className="font-medium">{item.investorName}</TableCell>
            <TableCell className="text-right">{formatCurrency(item.proRataAmount)}</TableCell>
            <TableCell className="text-right">{formatNumber(item.numberOfSeriesAShares)}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
</div>
```

### 9. Ownership Distribution Design
- **Chart Type**: Interactive pie chart with filters
- **Professional Styling**: Clean design with proper legends
- **Responsive**: Works on all device sizes

### 9. Voting Rights Table Design (Updated per Client Requirements - December 2024)
- **Layout**: Streamlined table with consolidated voting columns for cleaner presentation
- **Column Structure**: 
  - **Removed Columns**: Common Stock, Preferred Stock, Series A, Series A-1 (share columns)
  - **Removed Columns**: Can Block Overall Majority?, Can Block Preferred Stock Majority?, Can Block Common Stock Majority? (blocking rights)
- **New Column Order**: 
  1. Shareholder
  2. Overall Voting %
  3. Overall Voting Power
  4. Common Stock Voting %
  5. Common Stock Voting Power
  6. Preferred Stock Voting %
  7. Preferred Stock Voting Power
- **Voting Power**: Badge classifications per shareholder (High/Medium/Low) for Overall, Common Stock, and Preferred Stock
- **Info Capsule**: Hoverable information capsule below table displaying static message about voting power calculations
- **Simplified Structure**: Focus on essential voting metrics without complex series-specific breakdowns

### 9.1. Info Capsule Implementation Requirements
- **Location**: Positioned below the Voting Rights table
- **Trigger**: Hover interaction to display static message
- **Content**: Static informational text explaining voting power calculations
- **Styling**: Professional appearance consistent with overall design theme
- **Accessibility**: Keyboard accessible and screen reader friendly
- **Message Content**: "Voting power is calculated based on total shares outstanding. Overall voting percentage represents the shareholder's total voting influence across all stock classes."

### 10. Stock Option Plan Design
- **Layout**: Updated metrics and grant table
- **Post-Money Calculations**: Reflect option pool changes
- **Professional Styling**: Consistent with existing SOP design
- **Client Approval**: This section has been signed off by the client

---

## Static Data Implementation

### Comprehensive Demo Data
```tsx
const STATIC_PRO_FORMA_DATA: ProFormaModelData = {
  inputs: {
    designation: 'Fixed Pre-Money',
    fixedValuation: 0,
    investmentAmount: 0,
    optionPoolPercentage: 0,
    preferredStockDesignation: 'Series A',
    commonStockBuffer: 0,
  },
  summary: {
    effectivePostMoneyValuation: 25000000,
    seriesPricing: [
      {
        seriesName: 'Series A',
        pricePerShare: 5.00,
        sharesIssued: 1000000,
        totalValue: 5000000,
      },
      {
        seriesName: 'Series A-1',
        pricePerShare: 4.50,
        sharesIssued: 200000,
        totalValue: 900000,
      },
    ],
    authorizedStock: {
      commonStock: 10000000,
      preferredStock: 5000000,
      seriesStock: {
        'Series A': 2000000,
        'Series A-1': 500000,
      },
    },
    totalAuthorizedStock: 15500000,
    notes: [
      'Effective Post-Money Valuation includes convertible security conversions',
      'Series A-1 represents convertible notes converting at discount',
      'Option pool increased to 10% post-money',
    ],
  },
  capTable: {
    items: [
      {
        id: '1',
        name: 'Acme Ventures',
        investmentAmount: 5000000,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        seriesAShares: 1000000,
        seriesA1Shares: 0,
        totalShares: 1000000,
        fullyDilutedOwnership: 20.00,
        ownershipChange: 20.00,
        catchInvestment: 5000000,
      },
      {
        id: '2',
        name: 'Convertible Note Holder',
        investmentAmount: 900000,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        seriesAShares: 0,
        seriesA1Shares: 200000,
        totalShares: 200000,
        fullyDilutedOwnership: 4.00,
        ownershipChange: 4.00,
        catchInvestment: 900000,
      },
      {
        id: '3',
        name: 'Founders',
        investmentAmount: 0,
        commonStockShares: 3000000,
        stockOptionPlanShares: 0,
        seriesAShares: 0,
        seriesA1Shares: 0,
        totalShares: 3000000,
        fullyDilutedOwnership: 60.00,
        ownershipChange: 0,
        catchInvestment: 0,
      },
      {
        id: '4',
        name: 'Employee Stock Pool',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 200000,
        seriesAShares: 0,
        seriesA1Shares: 0,
        totalShares: 200000,
        fullyDilutedOwnership: 3.85,
        ownershipChange: 0,
        catchInvestment: 0,
      },
      {
        id: '5',
        name: 'Advisor Shares',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 50000,
        seriesAShares: 0,
        seriesA1Shares: 0,
        totalShares: 50000,
        fullyDilutedOwnership: 0.96,
        ownershipChange: 0,
        catchInvestment: 0,
      },
      // Section 3: Additional rows (placeholder for API population)
      {
        id: '6',
        name: 'Additional Investor',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        seriesAShares: 0,
        seriesA1Shares: 0,
        totalShares: 0,
        fullyDilutedOwnership: 0,
        ownershipChange: 0,
        catchInvestment: 0,
        isSection3Row: true, // Flag for Section 3 rows
      },
      {
        id: '7',
        name: 'Unallocated New Money',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        seriesAShares: 0,
        seriesA1Shares: 0,
        totalShares: 0,
        fullyDilutedOwnership: 0,
        ownershipChange: 0,
        catchInvestment: 0,
        isSection3Row: true, // Flag for Section 3 rows
      },
    ],
    stockOptionPoolIncrease: 520000,
    totalAggregateShares: 5250000,
  },
  votingRights: {
    items: [
      {
        id: '1',
        name: 'Acme Ventures',
        overallVotingPercentage: 20.00,
        commonStockVotingPercentage: 0,
        preferredStockVotingPercentage: 20.00,
        overallVotingPower: 'Medium',
        commonStockVotingPower: 'Low',
        preferredStockVotingPower: 'Medium',
      },
    ],
    totalVotingShares: 5200000,
    overallMajorityThreshold: 2600000,
    preferredStockMajorityThreshold: 1500000,
    commonStockMajorityThreshold: 2000000,
    infoMessage: 'Voting power is calculated based on total shares outstanding. Overall voting percentage represents the shareholder\'s total voting influence across all stock classes.',
  },
  stockOptionPlan: {
    totalPlanSize: 520000,
    allocatedShares: 200000,
    remainingPlan: 320000,
    postMoneyAllocation: 520000,
    grants: [
      {
        id: '1',
        fullName: 'John Doe',
        role: 'CEO',
        currentShares: 100000,
        newShares: 0,
        totalShares: 100000,
        vestingSchedule: '4 years, 1 year cliff',
        grantStatus: 'Granted',
        grantType: 'Option',
      },
    ],
    lastUpdated: 'December 15, 2024',
  },
  ownershipDistribution: [
    { name: 'Founders', percentage: 60 },
    { name: 'Series A Investors', percentage: 20 },
    { name: 'Convertible Securities', percentage: 4 },
    { name: 'Option Pool', percentage: 10 },
    { name: 'Other', percentage: 6 },
  ],
  lastUpdated: 'December 15, 2024',
};
```

---

## Enhanced State Management

### Component State
```tsx
const [inputs, setInputs] = useState<ProFormaInputs>(STATIC_PRO_FORMA_DATA.inputs);
const [activeSection, setActiveSection] = useState<string>('inputs');
const [isCalculating, setIsCalculating] = useState<boolean>(false);
```

### Business Logic Functions
```tsx
const calculateProFormaMetrics = useMemo(() => {
  // Complex calculation logic based on inputs
  const { designation, fixedValuation, investmentAmount, optionPoolPercentage } = inputs;
  
  if (designation === 'Fixed Pre-Money') {
    const effectivePostMoney = fixedValuation + investmentAmount + convertibleConversions;
    return {
      effectivePostMoneyValuation: effectivePostMoney,
      // Other calculations...
    };
  } else {
    const effectivePreMoney = fixedValuation - investmentAmount - convertibleConversions;
    return {
      effectivePreMoneyValuation: effectivePreMoney,
      // Other calculations...
    };
  }
}, [inputs]);

const generateSeriesPricing = useMemo(() => {
  // Generate series-specific pricing based on convertible securities
  const series = new Set<string>();
  const pricing: SeriesPricing[] = [];
  
  // Add primary series
  series.add(inputs.preferredStockDesignation);
  
  // Add convertible security series
  convertibleSecurities.forEach((security, index) => {
    const seriesName = `${inputs.preferredStockDesignation}-${index + 1}`;
    series.add(seriesName);
    pricing.push({
      seriesName,
      pricePerShare: security.conversionPrice,
      sharesIssued: security.estimatedShares,
      totalValue: security.totalValue,
    });
  });
  
  return pricing;
}, [inputs, convertibleSecurities]);
```

---

## Implementation Plan

### Phase 1: Editable Inputs and Basic Structure
1. **Convert static inputs to editable fields** with real-time validation
2. **Implement input section** with all required editable controls
3. **Add real-time calculation updates** based on input changes
4. **Add section navigation** with smooth scrolling
5. **Create basic summary metrics** display with new fields

### Phase 1.1: Filter Integration (December 2024)
1. **Import existing CapTableFilters component** into ProFormaModel.tsx (NO NEW UI)
2. **Add filter state** to ProFormaModel component alongside existing inputs
3. **Connect CapTableFilters props** to ProFormaModel filter state handlers
4. **Update useProformaModel hook** to handle filter parameters in API calls
5. **Implement combined API parameter conversion** from both inputs and filters
6. **Add debounced filtering** with same 500ms delay as existing inputs
7. **Ensure real-time updates** when filters change, combining with existing pro forma inputs

**Implementation Example (No UI Recreation):**
```tsx
// In ProFormaModel.tsx
import CapTableFilters from './CapTableFilters'; // EXISTING COMPONENT

const ProFormaModel = () => {
  // Existing state
  const [inputs, setInputs] = useState<ProFormaInputs>(DEFAULT_INPUTS);
  
  // NEW: Add filter state (no UI changes needed)
  const [filters, setFilters] = useState<ProFormaFilters>(DEFAULT_FILTERS);
  
  // Filter handlers (connect to existing CapTableFilters props)
  const handleSearch = (term: string) => {
    setFilters(prev => ({ ...prev, shareholderNameSearch: term }));
  };
  
  const handleRoleFilter = (role: ProFormaRoleFilter) => {
    setFilters(prev => ({ ...prev, roleFilter: role }));
  };
  
  const handleShareClassFilter = (shareClass: ProFormaShareClassFilter) => {
    setFilters(prev => ({ ...prev, shareClassFilter: shareClass }));
  };

  return (
    <div>
      {/* Existing Pro Forma Inputs section */}
      
      {/* REUSE EXISTING CapTableFilters - NO NEW UI */}
      <CapTableFilters
        onSearch={handleSearch}
        onFilterRole={handleRoleFilter}
        onFilterShareClass={handleShareClassFilter}
        onToggleView={() => {}} // Not used in pro forma
        onExport={() => {}} // Not used in pro forma  
        currentView="standard" // Not used in pro forma
      />
      
      {/* Rest of existing Pro Forma sections */}
    </div>
  );
};
```

### Phase 2: Enhanced Calculations and Three-Section Cap Table (Updated per Client Requirements - December 2024)
1. **Implement complex calculation logic** for different valuation modes
2. **Create three-section cap table** with horizontal dividers:
   - **Section 1**: Regular investor rows (Acme Ventures, Convertible Note Holder, Founders, Employee Stock Pool, Advisor Shares)
   - **Section 2**: SOP Summary rows (Outstanding, Promised, Available, Stock Option Pool Increase)
   - **Section 3**: Additional rows (Additional Investor, Unallocated New Money)
3. **Add "Add Investor" functionality** with modal for manual investor entry
4. **Implement new column structure**:
   - Stockholder, Cash Investment, Common Stock, Stock Option Plan, Series A/Series Seed Shares, Series A-1 Shares, Total Shares, Fully Diluted Ownership, Ownership Change
5. **Add horizontal dividers**: Two thick horizontal lines separating the three sections
6. **Add inline tooltip integration** with "(i)" icons for Ownership Change column (tooltips appear on hover over each row's icon)
7. **Add Section 3 rows** with placeholder data (Additional Investor, Unallocated New Money)
8. **Implement simplified data structure** with direct column mapping and section flags
9. **Remove separate tooltip information section** below the table
10. **Use placeholder data** for Section 3 rows (API will populate later)

### Phase 3: Advanced Outputs and Analysis (Updated per Client Requirements)
1. **Create Pro Rata Rights table** beneath Pro Forma Cap Table:
   - Auto-generate investment amounts based on current ownership percentages
   - Auto-calculate series shares based on round type and price per share
   - Include italicized statement about pro rata rights
   - Populate from existing investors in current cap table
2. **Create ownership distribution chart** with filters
3. **Streamline voting rights table** with simplified column structure:
   - Remove series-specific share columns (Common Stock, Preferred Stock, Series A, Series A-1)
   - Remove blocking rights columns (Can Block Overall Majority?, Can Block Preferred Stock Majority?, Can Block Common Stock Majority?)
   - New column order: Shareholder, Overall Voting %, Overall Voting Power, Common Stock Voting %, Common Stock Voting Power, Preferred Stock Voting %, Preferred Stock Voting Power
   - Add info capsule below table with hoverable static message
4. **Add updated stock option plan** with post-money calculations (client signed off)
5. **Add export functionality** for professional reports (PDF and Excel)

### Phase 4: Professional Polish and Export
1. **Add comprehensive tooltips** and help text
2. **Implement print-friendly layouts** for board presentations
3. **Add validation and error handling** for all inputs
4. **Implement export functionality**:
   - PDF export with visual capture of entire UI
   - Excel export with preserved layout
   - Professional file naming and formatting
5. **Optimize performance** for large datasets

---

## Success Metrics

### User Experience
- **Professional Interface**: Suitable for board meetings and investor presentations
- **Comprehensive Analysis**: All required calculations and outputs available
- **Intuitive Navigation**: Easy movement between sections
- **Real-time Updates**: Immediate calculation updates based on input changes

### Technical Metrics
- **Performance**: Fast calculation and rendering of complex metrics
- **Accuracy**: Precise financial calculations with proper rounding
- **Scalability**: Handle multiple series and large cap tables
- **Accessibility**: Full keyboard navigation and screen reader support

---

## Dependencies

### External Dependencies
- `recharts`: Chart library for ownership distribution
- `lucide-react`: Icon library for consistent iconography
- `react-to-pdf` or `html2canvas` + `jspdf`: PDF export functionality
- `xlsx`: Excel export functionality

### Internal Dependencies
- `@/components/ui/card`: Card components for layout
- `@/components/ui/table`: Table components
- `@/components/ui/badge`: Badge components for status display
- `@/components/ui/tooltip`: Tooltip components for explanations
- `@/components/ui/button`: Button components for actions
- `@/components/ui/toggle-group`: Toggle components for mode selection
- `@/components/ui/input`: Input components for data entry
- `@/components/ui/select`: Select components for dropdowns
- `@/components/ui/alert`: Alert components for disclaimers
- `@/components/ui/dialog`: Dialog components for export options
- `@/components/ui/label`: Label components for form fields

---

## Risk Assessment

### Technical Risks
- **Calculation Complexity**: Complex financial calculations may introduce errors
- **Performance**: Large datasets may impact rendering performance
- **State Management**: Complex state interactions may cause bugs
- **Series Management**: Dynamic series generation may cause UI issues

### Mitigation Strategies
- **Comprehensive Testing**: Extensive testing of all calculations
- **Performance Optimization**: Memoization and lazy loading
- **State Validation**: Comprehensive state validation and error handling
- **UI Constraints**: Limit maximum series count to prevent UI overflow

### Business Risks
- **Calculation Accuracy**: Incorrect calculations may lead to financial errors
- **User Confusion**: Complex interface may confuse users
- **Compliance Issues**: Financial calculations must meet regulatory standards
- **Data Integrity**: Ensure all calculations are properly documented

### Mitigation Strategies
- **Professional Review**: Have financial professionals review calculations
- **User Training**: Provide comprehensive help and documentation
- **Compliance Validation**: Ensure all calculations meet industry standards
- **Audit Trail**: Log all calculations for verification

---

## Conclusion

The Pro Forma Model tab provides a comprehensive, professional-grade interface for investment round analysis and cap table modeling. The implementation ensures accuracy in financial calculations while maintaining excellent user experience and accessibility standards.

### Key Features:
✅ **Tab Consolidation**: Single comprehensive interface replacing two separate tabs  
✅ **Editable Inputs**: All input fields are editable with real-time validation  
✅ **Dynamic Calculations**: Real-time calculation updates based on input changes  
✅ **Series-Specific Analysis**: Support for multiple preferred stock series  
✅ **Simplified Cap Table**: Single unified table with streamlined column structure  
✅ **Add Investor Functionality**: Manual investor entry with investment amount input  
✅ **Inline Tooltip Integration**: Tooltips appear on hover over "(i)" icons for each row
✅ **SOP Summary Rows**: Three SOP summary rows plus Stock Option Pool Increase row
✅ **Pro Rata Rights Table**: Auto-generated investment amounts and series shares for existing investors
✅ **Streamlined Voting Rights**: Consolidated voting power columns with info capsule for better clarity  
✅ **Export Functionality**: PDF and Excel export with visual capture of entire UI  
✅ **Professional Design**: Clean, modern interface suitable for financial applications  
✅ **Compliance Ready**: Proper financial calculations and documentation
✅ **Investment Amount Editability**: Conditional editability for Section 3 based on ID presence
✅ **Add Investor Modal API Integration**: Simplified modal with POST API integration for creating new investors
✅ **API Integration**: Complete three-layer architecture with real-time data updates
✅ **Input Debouncing**: Optimized API calls with intelligent debouncing
✅ **Negative Value Formatting**: Professional display of negative values with parentheses (e.g., -123 displays as (123))
✅ **SOP Ownership Change Display**: Show actual ownership change percentages for SOP summary rows in Section 2 (calculated from API response data)
✅ **Accurate Tooltip Calculations**: Use actual API values (fullyDilutedOwnership and newFullyDilutedOwnership) for tooltip display instead of derived calculations
✅ **Percentage Formatting Standardization**: All percentage values display with exactly 2 decimal places throughout the entire Pro Forma Model
✅ **Upper Section Cash Investment Tooltips**: Add "(i)" tooltips next to Cash Investment values in upper section showing "Actual wire amount: $X,XXX" using actualWireAmount (only when non-zero)
✅ **Total Shares Column Fix**: Upper section Total Shares column displays newTotal from API response instead of total
✅ **Voting Rights Analysis Integration**: Integrate votingRightsAnalysis data from API response to display dynamic voting power analysis with all required columns
✅ **Stock Option Plan Summary Integration**: Replace static Stock Option Plan cards with dynamic data from stockoptionsplansummary API
✅ **Post Money Allocation Removal**: Remove Post Money Allocation card from Stock Option Plan section
✅ **Ownership Distribution Integration**: Integrate proFormaOwnershipDistribution data from API response to display interactive pie chart with proper legends
✅ **Cap Table Column Updates**: Remove Catch Investment column, rename Investment Amount to Cash Investment, add Series A/Series Seed column
✅ **Investor Deletion Functionality**: Delete investors from proforma with confirmation modal and API integration
✅ **API Response Structure Updates**: Updated to handle new API response format with array-based series data and field name changes
✅ **Dynamic Series Column Generation**: Support for Series Seed1, Series Seed2, etc. based on API response keys
✅ **Series A/Series Seed Column Population**: Populated from `seiresADetails` array for lower section and `series` field for upper section
✅ **Pro Rata Rights Field Updates**: Updated to use `prorataAmount` field instead of `investmentAmount`
✅ **Filter Integration (December 2024)**: Comprehensive filter integration with existing CapTableFilters component
✅ **Combined API Parameters**: Filter parameters work in combination with existing Pro Forma inputs
✅ **Debounced Filter Calls**: Filter changes trigger same debounced API pattern (500ms delay)
✅ **Real-time Filtered Updates**: All pro forma sections update with filtered and recalculated data
✅ **State Management Enhancement**: Filter state managed alongside existing pro forma inputs
✅ **UI Component Reuse**: Existing CapTableFilters component integrated into ProFormaModel
✅ **Type Safety**: New filter types and interfaces for comprehensive type coverage

### Filter Integration Summary (December 2024)

**New Query Parameters Added:**
- `ProFormaRoleFilter`: "All" | "Founders" | "Investors" | "Employees" | "Advisors" | "Contractors"
- `ProFormaShareClassFilter`: "All" | "Common" | "SeriesAPreferred" | "StockOptionPool"  
- `ShareHolderName`: string (search term)

**Data Flow Enhancement:**
1. **Filter Input** → CapTableFilters component in ProFormaModel
2. **Combined State** → Filters + Pro Forma inputs managed together
3. **Unified API Call** → Single API request with all parameters
4. **Filtered Response** → Backend recalculates data based on filters
5. **Real-time Update** → All UI sections update with filtered results

**Component Integration:**
- **Reused Component**: CapTableFilters.tsx integrated into ProFormaModel.tsx
- **State Extension**: ProFormaModel extended to manage filter state
- **Hook Enhancement**: useProformaModel enhanced for filter parameter handling
- **Service Update**: proforma.service.ts updated with filter parameters
- **API Enhancement**: client.ts getProformaData method supports filter query parameters

The implementation provides a comprehensive, professional-grade interface for investment round analysis and cap table modeling with advanced filtering capabilities that work seamlessly with existing pro forma calculations.
