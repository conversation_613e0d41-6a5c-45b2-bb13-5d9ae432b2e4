# Convertible Securities Tab Implementation Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Draft

## Overview

The Convertible Securities tab provides comprehensive tracking of convertible securities including both Convertible Notes and SAFEs (Simple Agreement for Future Equity) for pre-seed financing analysis. This component displays detailed financial parameters, conversion terms, and investment tracking for corporate governance and compliance purposes.

## Goals

- Display comprehensive convertible securities data including both SAFEs and Convertible Notes
- Provide clear distinction between different security types
- Track conversion terms, valuation caps, and discount rates
- Support pre-seed financing analysis and reporting
- Ensure compliance with financial reporting requirements
- Maintain professional UI/UX suitable for board meetings and investor presentations
- Integrate with backend API for real-time data

## UX Overview

- **Convertible Securities Table**: Comprehensive table with all convertible securities
- **Security Type Classification**: Clear distinction between SAFEs and Convertible Notes
- **Financial Parameters**: Detailed tracking of principal, interest, conversion terms
- **Professional Design**: Clean, modern interface suitable for financial applications
- **Responsive Layout**: Works perfectly on all device sizes
- **API Integration**: Real-time data from backend with proper loading states

---

## Requirements Summary

### 1. API Integration
- **Endpoint**: `GET /api/p2/companies/{companyId}/captable/convertiblenotes`
- **Service Layer**: Create dedicated service for API calls
- **Hook Layer**: Create React Query hook for data fetching
- **Data Transformation**: Transform API response to component format

### 2. Security Type Integration
- **Include SAFEs**: Add SAFEs from Pre-Seed Financing tab
- **Add Security Type Column**: Indicate "SAFE" or "Convertible Note"
- **Visual Distinction**: Use badges or styling to differentiate security types
- **Type Mapping**: `issueRoundType` determines security type:
  - `"authorized-safe"` → **SAFE**
  - `"authorized-round"` → **Convertible Note**

### 3. SAFE-Specific Rules
- **Interest Rate**: Display 0% for all SAFEs
- **Interest Accrued**: Show $0 for all SAFEs
- **Maturity Date**: Display "N/A" or leave blank for SAFEs

### 4. Data Organization
- **Sort by Name**: Alphabetical sorting by investor name
- **Remove Round Indication**: No need to show financing rounds
- **Combined Display**: Show SAFEs and Convertible Notes in single table
- **Use API Summary**: Use summary metrics from API response

### 5. Enhanced Column Structure
- **New Column**: "Convertible Security" type
- **Updated Logic**: Interest rate and accrued interest based on security type
- **Professional Display**: Clear formatting and tooltips
- **Error Handling**: Show "-" for missing or invalid data

---

## API Integration Architecture

### API Endpoint Details
```
GET /api/p2/companies/{companyId}/captable/convertiblenotes
```

**Parameters:**
- `companyId` (path): UUID of the company

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "totalPrincipal": 300000,
    "safes": 3,
    "convertibleNotes": 2,
    "totalInvestors": 5,
    "latestUpdatedDate": "2025-09-03",
    "convertibles": [
      {
        "id": null,
        "officerId": null,
        "serviceProviderId": null,
        "name": "Note 1",
        "issueRoundType": "authorized-round",
        "principal": 100000,
        "interestRate": 10,
        "investment": null,
        "issueDate": "2025-09-03",
        "maturityDate": "2025-09-29",
        "valuationCap": 500000,
        "discount": 25,
        "interestAccrued": 0.0,
        "totalValue": 100000.0,
        "mfn": true,
        "qft": 1000000
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

### Service Layer Implementation

#### **File**: `src/services/cap-table/convertibleSecurities.service.ts`
```typescript
import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  ConvertibleSecuritiesApiResponse, 
  ConvertibleSecuritiesData,
  ConvertibleSecurity 
} from "@/types/capTable";

export const getConvertibleSecurities = async (companyId: string): Promise<ConvertibleSecuritiesData> => {
  try {
    const response = await APIClient.get(`/api/p2/companies/${companyId}/captable/convertiblenotes`);
    
    if (response.error) {
      throw new Error(response.error);
    }

    return transformApiResponse(response.data);
  } catch (error) {
    throw new Error(`Failed to fetch convertible securities: ${error}`);
  }
};

const transformApiResponse = (apiData: ConvertibleSecuritiesApiResponse): ConvertibleSecuritiesData => {
  return {
    securities: apiData.convertibles.map((convertible, index) => ({
      id: convertible.id || `convertible-${index + 1}`,
      investor: convertible.name || "-",
      securityType: convertible.issueRoundType === 'authorized-safe' ? 'SAFE' : 'Convertible Note',
      principal: convertible.principal || 0,
      interestRate: convertible.interestRate || 0,
      issueDate: convertible.issueDate || "-",
      maturityDate: convertible.maturityDate || undefined,
      valuationCap: convertible.valuationCap || 0,
      discount: convertible.discount || 0,
      interestAccrued: convertible.interestAccrued || 0,
      totalValue: convertible.totalValue || 0,
      conversionPrice: 0, // Not provided in new API response
      estimatedShares: 0, // Not provided in new API response
      // Additional fields for backend processing
      mfn: convertible.mfn || false,
      qft: convertible.qft || 0,
      issueRoundType: convertible.issueRoundType || "-",
    })),
    summary: {
      totalPrincipal: apiData.totalPrincipal || 0,
      safes: apiData.safes || 0,
      convertibleNotes: apiData.convertibleNotes || 0,
      totalInvestors: apiData.totalInvestors || 0,
      lastUpdated: apiData.latestUpdatedDate || "-",
    }
  };
};
```

### Hook Layer Implementation

#### **File**: `src/hooks/cap-table/useConvertibleSecurities.hooks.ts`
```typescript
import { useQuery } from "@tanstack/react-query";
import { getConvertibleSecurities } from "@/services/cap-table/convertibleSecurities.service";
import { useAuth } from "@/contexts/AuthContext";
import { ConvertibleSecuritiesData } from "@/types/capTable";

export const useConvertibleSecurities = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  return useQuery<ConvertibleSecuritiesData>({
    queryKey: ["convertibleSecurities", companyId],
    queryFn: () => getConvertibleSecurities(companyId!),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

---

## Enhanced Data Model

### Updated Type Definitions
Add to `src/types/capTable.ts`:

```ts
// API Response Types
export interface ConvertibleSecuritiesApiResponse {
  totalPrincipal: number;
  safes: number;
  convertibleNotes: number;
  totalInvestors: number;
  latestUpdatedDate: string;
  convertibles: ConvertibleSecurityApiItem[];
}

export interface ConvertibleSecurityApiItem {
  id: string | null;
  officerId: string | null;
  serviceProviderId: string | null;
  name: string;
  issueRoundType: "authorized-safe" | "authorized-round";
  principal: number;
  interestRate: number;
  investment: number | null;
  issueDate: string;
  maturityDate: string | null;
  valuationCap: number;
  discount: number;
  interestAccrued: number;
  totalValue: number;
  mfn: boolean;
  qft: number;
}

// Convertible Securities Types (Enhanced)
export interface ConvertibleSecurity {
  id: string;
  investor: string;
  securityType: 'SAFE' | 'Convertible Note';
  principal: number;
  interestRate: number;
  issueDate: string;
  maturityDate?: string; // Optional for SAFEs
  valuationCap: number;
  discount: number;
  interestAccrued: number;
  totalValue: number;
  conversionPrice: number;
  estimatedShares: number;
  // Additional fields for backend processing (not displayed in UI)
  mfn: boolean;
  qft: number;
  issueRoundType: string;
}

export interface ConvertibleSecuritiesSummary {
  totalPrincipal: number;
  safes: number;
  convertibleNotes: number;
  totalInvestors: number;
  lastUpdated: string;
}

export interface ConvertibleSecuritiesData {
  securities: ConvertibleSecurity[];
  summary: ConvertibleSecuritiesSummary;
}
```

---

## Component Architecture

### Entry Point
- `components/captable/ConvertibleTable.tsx` (major updates required)
  - **Purpose**: Main Convertible Securities component with API integration
  - **Props**: None (self-contained with API data)
  - **State**: Securities list, sorting, filtering, loading states
  - **Children**: Securities table, summary metrics, professional styling

### Enhanced Component Structure
```
ConvertibleTable.tsx
├── Card (container)
├── CardHeader
│   ├── CardTitle ("Convertible Securities")
│   └── Last Updated Date (from API)
├── CardContent
│   ├── Loading State (skeleton)
│   ├── Error State (error message)
│   ├── Summary Metrics Section (from API)
│   │   ├── Total Principal Display
│   │   ├── SAFEs Count
│   │   ├── Convertible Notes Count
│   │   └── Total Investors Display
│   ├── Securities Table
│   │   ├── TableHeader
│   │   │   ├── Investor Column
│   │   │   ├── Convertible Security Column
│   │   │   ├── Principal Column
│   │   │   ├── Interest Rate Column
│   │   │   ├── Issue Date Column
│   │   │   ├── Maturity Column
│   │   │   ├── Valuation Cap Column
│   │   │   ├── Discount Column
│   │   │   ├── Interest Accrued Column
│   │   │   ├── Total Value Column
│   │   │   ├── Conv. Price Column
│   │   │   └── Est. Shares Column
│   │   └── TableBody
│   │       ├── Individual Security Rows
│   │       └── Totals Row
│   └── Empty State (when no securities)
```

### New Shared Components
- `Badge` for security type display
- `Tooltip` for explanatory information
- `Alert` for important notices about security types
- `Skeleton` for loading states

---

## Enhanced UI/UX Requirements

### Loading and Error States
- **Loading State**: Skeleton loader while fetching data
- **Error State**: Clear error message with retry option
- **Empty State**: Message when no convertible securities found

### Summary Metrics Section Design
- **Location**: Above the main table, prominent placement
- **Layout**: Horizontal cards with key metrics from API
- **Styling**: Professional financial dashboard style

### Summary Metrics Layout
```tsx
<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Total Principal</div>
      <div className="text-2xl font-bold">{formatCurrency(summary.totalPrincipal)}</div>
    </CardContent>
  </Card>
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">SAFEs</div>
      <div className="text-2xl font-bold">{summary.safes}</div>
    </CardContent>
  </Card>
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Convertible Notes</div>
      <div className="text-2xl font-bold">{summary.convertibleNotes}</div>
    </CardContent>
  </Card>
  <Card>
    <CardContent className="p-4">
      <div className="text-sm text-muted-foreground">Total Investors</div>
      <div className="text-2xl font-bold">{summary.totalInvestors}</div>
    </CardContent>
  </Card>
</div>
```

### Enhanced Table Design
- **Layout**: Clean, modern table with subtle borders
- **Headers**: Bold column headers with tooltips for explanations
- **Rows**: Alternating row colors for better readability
- **Responsive**: Horizontal scroll on mobile devices

### Updated Column Specifications

#### **Investor Column:**
- **Left-aligned text**
- **Font weight**: Medium
- **Sorting**: Alphabetical sorting by investor name
- **Tooltip**: "Investor name"
- **Error Handling**: Show "-" if missing

#### **Convertible Security Column:**
- **Left-aligned badges**
- **Badge Display**:
  - SAFE (blue badge) - when `issueRoundType === "authorized-safe"`
  - Convertible Note (green badge) - when `issueRoundType === "authorized-round"`
- **Tooltip**: "Type of convertible security"

#### **Principal Column:**
- **Right-aligned currency**
- **Formatting**: Thousands separator, currency symbol
- **Tooltip**: "Principal investment amount"
- **Error Handling**: Show "-" if missing

#### **Interest Rate Column:**
- **Right-aligned percentages**
- **Logic**: 0% for SAFEs, actual rate for Convertible Notes
- **Tooltip**: "Interest rate (0% for SAFEs)"
- **Error Handling**: Show "-" if missing

#### **Issue Date Column:**
- **Center-aligned dates**
- **Format**: MM/DD/YYYY
- **Tooltip**: "Date of security issuance"
- **Error Handling**: Show "-" if missing

#### **Maturity Column:**
- **Center-aligned dates**
- **Logic**: "N/A" for SAFEs, actual date for Convertible Notes
- **Tooltip**: "Maturity date (N/A for SAFEs)"
- **Error Handling**: Show "-" if missing

#### **Valuation Cap Column:**
- **Right-aligned currency**
- **Formatting**: Thousands separator, currency symbol
- **Tooltip**: "Valuation cap for conversion"
- **Error Handling**: Show "-" if missing

#### **Discount Column:**
- **Right-aligned percentages**
- **Formatting**: Percentage with % symbol
- **Tooltip**: "Discount rate for conversion"
- **Error Handling**: Show "-" if missing

#### **Interest Accrued Column:**
- **Right-aligned currency**
- **Logic**: $0 for SAFEs, calculated amount for Convertible Notes
- **Tooltip**: "Accrued interest ($0 for SAFEs)"
- **Error Handling**: Show "-" if missing

#### **Total Value Column:**
- **Right-aligned currency**
- **Formatting**: Thousands separator, currency symbol
- **Tooltip**: "Total value including accrued interest"
- **Error Handling**: Show "-" if missing

#### **MFN Column:**
- **Center-aligned badges**
- **Display**: "Yes" (default badge) or "No" (secondary badge)
- **Tooltip**: "Most Favored Nation clause (Yes/No)"
- **Logic**: Based on `mfn` boolean field from API
- **Error Handling**: Show "No" as default if missing

### Security Type Badge Design

#### **SAFE Badge:**
```tsx
<Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
  SAFE
</Badge>
```

#### **Convertible Note Badge:**
```tsx
<Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
  Convertible Note
</Badge>
```

---

## Implementation Plan

### Phase 1: API Integration Setup
1. **Add API endpoint** to `client.ts`
2. **Create service layer** (`convertibleSecurities.service.ts`)
3. **Create hook layer** (`useConvertibleSecurities.hooks.ts`)
4. **Add TypeScript interfaces** for API response

### Phase 2: Data Transformation
1. **Implement data transformation** from API to component format
2. **Add error handling** for missing or invalid data
3. **Implement security type mapping** logic
4. **Add loading and error states**

### Phase 3: Component Updates
1. **Update component** to use API data instead of static data
2. **Implement loading states** with skeleton loaders
3. **Add error handling** with retry functionality
4. **Update summary metrics** to use API values

### Phase 4: Professional Polish
1. **Add comprehensive tooltips** for all columns
2. **Implement accessibility features** and keyboard navigation
3. **Add professional styling** and responsive design
4. **Test API integration** with real data

---

## Success Metrics

### User Experience
- **API Integration**: Seamless data loading from backend
- **Security Clarity**: Users can quickly distinguish between SAFEs and Convertible Notes
- **Data Organization**: Clear alphabetical sorting by investor name
- **Professional Appearance**: Suitable for board meetings and investor presentations
- **Error Handling**: Graceful handling of missing or invalid data

### Technical Metrics
- **Performance**: Fast API calls with proper caching
- **Accessibility**: Full WCAG compliance
- **Responsive Design**: Perfect functionality across all devices
- **Type Safety**: Complete TypeScript support
- **Error Resilience**: Robust error handling and recovery

---

## Dependencies

### External Dependencies
- `@tanstack/react-query`: Data fetching and caching
- `lucide-react`: Icon library for consistent iconography

### Internal Dependencies
- `@/components/ui/card`: Card components for layout
- `@/components/ui/table`: Table components
- `@/components/ui/badge`: Badge components for security type display
- `@/components/ui/tooltip`: Tooltip components for explanations
- `@/components/ui/button`: Button components for actions
- `@/components/ui/skeleton`: Skeleton components for loading states
- `@/integrations/legal-concierge/client`: API client
- `@/contexts/AuthContext`: Authentication context for company ID

---

## Risk Assessment

### Technical Risks
- **API Reliability**: Backend API availability and response times
- **Data Transformation**: Complex mapping between API and component formats
- **Error Handling**: Comprehensive error scenarios and edge cases
- **Performance**: Large datasets and caching strategies

### Mitigation Strategies
- **Robust Error Handling**: Comprehensive try-catch blocks and fallbacks
- **Data Validation**: Validate API responses before transformation
- **Caching Strategy**: Implement proper caching with React Query
- **Loading States**: Provide clear feedback during data fetching

### Business Risks
- **Data Accuracy**: Incorrect security type classification may lead to compliance issues
- **User Experience**: Poor loading times or error states may frustrate users
- **Data Integrity**: API data must be accurately transformed and displayed

### Mitigation Strategies
- **Comprehensive Testing**: Test all API scenarios and edge cases
- **User Feedback**: Clear loading and error states
- **Data Validation**: Validate all API responses before display
- **Fallback Mechanisms**: Graceful degradation when API is unavailable

---

## Conclusion

The enhanced Convertible Securities tab provides a comprehensive, professional-grade interface for convertible securities tracking and analysis with full API integration. The implementation ensures compliance with financial reporting requirements while maintaining excellent user experience and accessibility standards.

### Key Features:
✅ **API Integration**: Real-time data from backend with proper error handling  
✅ **Security Type Integration**: Clear distinction between SAFEs and Convertible Notes  
✅ **SAFE-Specific Rules**: 0% interest rate and $0 interest accrued for SAFEs  
✅ **Enhanced Organization**: Alphabetical sorting by investor name  
✅ **Professional Design**: Clean, modern UI suitable for financial applications  
✅ **Error Handling**: Graceful handling of missing data with "-" display  
✅ **Compliance Ready**: Proper tracking for financial reporting requirements  

The implementation provides a solid foundation for convertible securities management with full API integration and room for future enhancements.
