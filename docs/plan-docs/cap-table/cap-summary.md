# Cap Table Summary Feature Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Draft

## Overview

The Cap Table Summary feature provides a dashboard widget that displays key cap table metrics and a visual representation of shareholder distribution. This component serves as a quick overview for users to understand their company's ownership structure at a glance.

## Goals

- Display key cap table metrics in an easy-to-read format
- Provide visual representation of shareholder distribution via pie chart
- Enable quick navigation to the full cap table view
- Ensure responsive design for all device sizes
- Maintain consistency with existing dashboard components

## UX Overview

- **Dashboard Widget**: Compact card displaying summary statistics and pie chart
- **Summary Cards**: 2x2 grid showing shareholders, share classes, total shares, and fully diluted shares
- **Pie Chart**: Visual representation of top shareholders with "Others" category
- **Navigation**: "View Full Cap Table" button linking to detailed cap table page
- **Responsive**: Adapts to different screen sizes with proper mobile optimization

---

## API Requirements

### GET Cap Table Summary
**Endpoint**: `GET /api/p2/companies/{companyId}/captable/summary`
**Purpose**: Retrieve cap table summary data for dashboard display
**Authentication**: Required (company context)
**Content-Type**: `application/json`

#### Request Parameters
```ts
interface CapTableSummaryRequest {
  companyId: string;  // UUID, required, path parameter
}
```

#### Response Structure
**Success Response (200 OK)**
```ts
interface CapTableSummaryResponse {
  message: string;
  data: {
    shareholders: number;           // Total number of shareholders
    shareClasses: number;           // Total number of share classes
    totalShares: number;            // Total shares issued
    fullyDiluted: number;           // Fully diluted shares (including options, warrants, etc.)
    pieChartData: PieChartItem[];   // Shareholder distribution data for pie chart
  };
  error: null;
  validationErrors: null;
}

interface PieChartItem {
  name: string;        // Shareholder name or "Others"
  percentage: number;  // Ownership percentage (0-100)
}
```

#### Example Response
```json
{
  "message": "Success",
  "data": {
    "shareholders": 4,
    "shareClasses": 2,
    "totalShares": 4,
    "fullyDiluted": 0,
    "pieChartData": [
      {
        "name": "Omed Shariff",
        "percentage": 10
      },
      {
        "name": "Abdul Majumdeer",
        "percentage": 50
      },
      {
        "name": "Others",
        "percentage": 40
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

#### Error Response (400/404/500)
```ts
interface CapTableSummaryErrorResponse {
  message: string;
  data: null;
  error: string;
  validationErrors: {
    field: string;
    message: string;
  }[] | null;
}
```

#### Validation Rules
- `companyId`: Required, must be a valid UUID for an existing company
- Response validation: All numeric fields must be non-negative
- Pie chart percentages must sum to 100% (or close to it, accounting for rounding)

---

## Data Model

### Type Definitions
Add to `src/types/capTable.ts`:
```ts
interface CapTableSummaryData {
  shareholders: number;
  shareClasses: number;
  totalShares: number;
  fullyDiluted: number;
  pieChartData: PieChartItem[];
}

interface PieChartItem {
  name: string;
  percentage: number;
}

interface CapTableSummaryResponse {
  message: string;
  data: CapTableSummaryData;
  error: null;
  validationErrors: null;
}
```

---

## Component Architecture

### Entry Point
- `components/dashboard/captable/DashboardCapTable.tsx` (existing, needs updates)
  - **Purpose**: Main dashboard widget component
  - **Props**: None (uses company context from auth)
  - **State**: Loading, error, data states
  - **Children**: Summary cards, pie chart, navigation button

### Component Structure
```
DashboardCapTable.tsx
├── Card (container)
├── CardHeader
│   ├── CardTitle ("Cap Table Summary")
│   └── ViewFullCapTableButton
├── CardContent
│   ├── SummaryStatsGrid (2x2 grid)
│   │   ├── ShareholdersCard
│   │   ├── ShareClassesCard
│   │   ├── TotalSharesCard
│   │   └── FullyDilutedCard
│   └── PieChartSection
│       ├── ChartContainer
│       ├── PieChart (recharts)
│       ├── ChartTooltip
│       └── ChartLegend
```

### Shared Components Used
- `Card`, `CardContent`, `CardHeader`, `CardTitle` from `@/components/ui/card`
- `Button` from `@/components/ui/button`
- `ChartContainer`, `ChartTooltip`, `ChartLegend` from `@/components/ui/chart`
- `PieChart`, `Pie`, `Cell` from `recharts`
- Icons from `lucide-react`

---

## Service Layer

### Cap Table Summary Service
**File**: `src/services/cap-table/capTableSummary.service.ts`

```ts
import { APIClient } from '@/integrations/legal-concierge/client';
import { CapTableSummaryResponse } from '@/types/capTable';

export class CapTableSummaryService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getCapTableSummary(companyId: string): Promise<CapTableSummaryResponse> {
    try {
      const response = await this.apiClient.get(`/api/p2/companies/${companyId}/captable/summary`);
      return response;
    } catch (error) {
      return this.apiClient.handleError(error);
    }
  }
}

export const capTableSummaryService = new CapTableSummaryService();
```

### Integration with Existing Client
**File**: `src/integrations/legal-concierge/client.ts`

Add method to existing `APIClient` class:
```ts
async getCapTableSummary(companyId: string) {
  try {
    const response = await this.get(`/api/p2/companies/${companyId}/captable/summary`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

---

## Hook Layer

### Cap Table Summary Hook
**File**: `src/hooks/cap-table/useCapTableSummary.hooks.ts`

```ts
import { useQuery } from '@tanstack/react-query';
import { capTableSummaryService } from '@/services/cap-table/capTableSummary.service';
import { useCompanyDetails } from '@/components/questions/hooks/useCompanyDetails';
import { CapTableSummaryData } from '@/types/capTable';

export const useCapTableSummary = () => {
  const { companyDetails } = useCompanyDetails();
  const companyId = companyDetails?.companyId;

  const {
    data: capTableSummary,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['capTableSummary', companyId],
    queryFn: () => capTableSummaryService.getCapTableSummary(companyId!),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    capTableSummary: capTableSummary?.data || null,
    isLoading,
    error: error?.message || null,
    refetch,
  };
};
```

---

## UI/UX Requirements

### Dashboard Widget Design
- **Card Layout**: Clean, modern card with subtle shadow and rounded corners
- **Header**: Title with navigation button aligned to the right
- **Content**: Summary statistics grid followed by pie chart
- **Spacing**: Consistent padding and margins throughout

### Summary Statistics Cards (2x2 Grid)
- **Shareholders Card**:
  - Icon: `Users` from lucide-react
  - Color: Blue (#3B82F6)
  - Label: "Shareholders"
  - Value: Number of shareholders
  
- **Share Classes Card**:
  - Icon: `FileBarChart` from lucide-react
  - Color: Indigo (#6366F1)
  - Label: "Share Classes"
  - Value: Number of share classes
  
- **Total Shares Card**:
  - Icon: `ChartPie` from lucide-react
  - Color: Green (#10B981)
  - Label: "Total Shares"
  - Value: Formatted number with commas
  
- **Fully Diluted Card**:
  - Icon: `CircleDollarSign` from lucide-react
  - Color: Purple (#8B5CF6)
  - Label: "Fully Diluted"
  - Value: Formatted number with commas

### Pie Chart Requirements
- **Size**: 180px height container with responsive width
- **Colors**: Predefined color palette for consistency
- **Data**: Top 4 shareholders + "Others" category
- **Tooltips**: Show shareholder name and percentage on hover
- **Legend**: Display below chart with color indicators
- **Empty State**: Show message when no data available

### Navigation Button
- **Text**: "View Full Cap Table"
- **Icon**: `ArrowRight` from lucide-react
- **Link**: Routes to `/cap-table` page
- **Style**: Ghost variant with subtle hover effect

### Responsive Design
- **Mobile**: Single column layout for summary cards
- **Tablet**: 2x2 grid maintained with adjusted spacing
- **Desktop**: Full layout with optimal spacing
- **Chart**: Responsive sizing with minimum width constraints

---

## State Management

### Loading States
- **Initial Load**: Skeleton loader or spinner
- **Data Loading**: Disable interactions, show loading indicator
- **Error State**: Display error message with retry option
- **Empty State**: Show appropriate message when no data

### Error Handling
- **API Errors**: Display user-friendly error messages
- **Network Errors**: Retry mechanism with exponential backoff
- **Validation Errors**: Highlight specific field issues
- **Fallback UI**: Graceful degradation when chart fails to render

### Data Caching
- **React Query**: Automatic caching with 5-minute stale time
- **Refetch**: Manual refresh capability
- **Optimistic Updates**: Immediate UI feedback for better UX

---

## Accessibility Requirements

### Keyboard Navigation
- **Focus Management**: Proper tab order through all interactive elements
- **Chart Interaction**: Keyboard accessible tooltips and legend
- **Button Actions**: Clear focus indicators and keyboard activation

### Screen Reader Support
- **ARIA Labels**: Descriptive labels for all interactive elements
- **Chart Description**: Alt text describing pie chart data
- **Status Announcements**: Loading, error, and success states announced

### Visual Accessibility
- **Color Contrast**: WCAG AA compliant color ratios
- **Focus Indicators**: Clear visual focus states
- **Text Scaling**: Support for browser text scaling
- **High Contrast**: Compatible with high contrast mode

---

## Performance Requirements

### Loading Performance
- **Initial Load**: < 2 seconds for data retrieval
- **Chart Rendering**: < 500ms for pie chart display
- **Caching**: Efficient data caching to reduce API calls
- **Bundle Size**: Minimal impact on overall application bundle

### Optimization
- **Lazy Loading**: Chart components loaded on demand
- **Memoization**: React.memo for expensive chart calculations
- **Debouncing**: Prevent excessive API calls during rapid navigation
- **Image Optimization**: Optimized chart rendering

---

## Testing Requirements

### Unit Tests
- **Service Layer**: Test API calls and error handling
- **Hook Layer**: Test data fetching and state management
- **Component Logic**: Test chart data processing and rendering

### Integration Tests
- **API Integration**: End-to-end API call testing
- **Component Integration**: Test component with real data
- **Navigation**: Test routing to full cap table page

### Visual Tests
- **Chart Rendering**: Verify pie chart displays correctly
- **Responsive Design**: Test across different screen sizes
- **Accessibility**: Test with screen readers and keyboard navigation

---

## Implementation Plan

### Phase 1: API Integration
1. Create `CapTableSummaryService` with API client integration
2. Implement `useCapTableSummary` hook with React Query
3. Add type definitions to `capTable.ts`

### Phase 2: Component Updates
1. Update `DashboardCapTable.tsx` to use new hook
2. Implement proper loading and error states
3. Add responsive design improvements

### Phase 3: Chart Enhancement
1. Fix pie chart rendering issues
2. Implement proper tooltips and legend
3. Add accessibility features

### Phase 4: Testing & Polish
1. Add comprehensive unit and integration tests
2. Performance optimization and bundle analysis
3. Accessibility audit and improvements

---

## Success Metrics

### User Experience
- **Load Time**: Dashboard widget loads in < 2 seconds
- **Error Rate**: < 1% API error rate
- **User Engagement**: Increased navigation to full cap table page

### Technical Metrics
- **Bundle Impact**: < 50KB additional bundle size
- **API Performance**: < 500ms average response time
- **Cache Hit Rate**: > 80% cache utilization

### Accessibility Metrics
- **WCAG Compliance**: AA level compliance achieved
- **Screen Reader**: 100% functionality with screen readers
- **Keyboard Navigation**: Full keyboard accessibility

---

## Dependencies

### External Dependencies
- `recharts`: Chart library for pie chart rendering
- `lucide-react`: Icon library for consistent iconography
- `@tanstack/react-query`: Data fetching and caching

### Internal Dependencies
- `@/components/ui/card`: Card components for layout
- `@/components/ui/button`: Button components
- `@/components/ui/chart`: Chart wrapper components
- `@/integrations/legal-concierge/client`: API client
- `@/hooks/useCompanyDetails`: Company context

---

## Risk Assessment

### Technical Risks
- **Chart Rendering**: Pie chart may not render correctly in all browsers
- **API Performance**: Large datasets may impact loading times
- **Bundle Size**: Additional dependencies may increase bundle size

### Mitigation Strategies
- **Fallback UI**: Provide alternative display when chart fails
- **Data Pagination**: Limit data size for better performance
- **Code Splitting**: Lazy load chart components to reduce initial bundle

### Business Risks
- **Data Accuracy**: Incorrect cap table data may mislead users
- **User Adoption**: Users may not find the summary useful
- **Maintenance**: Additional complexity for future updates

### Mitigation Strategies
- **Data Validation**: Comprehensive validation of API responses
- **User Feedback**: Gather user feedback on summary usefulness
- **Documentation**: Clear documentation for future maintenance

---

## Future Enhancements

### Potential Features
- **Export Functionality**: Allow users to export summary as PDF/image
- **Historical Data**: Show cap table changes over time
- **Interactive Chart**: Allow users to click on chart segments for details
- **Customization**: Allow users to customize displayed metrics

### Technical Improvements
- **Real-time Updates**: WebSocket integration for live data updates
- **Advanced Charts**: Additional chart types (bar chart, line chart)
- **Data Analytics**: Advanced analytics and insights
- **Mobile App**: Native mobile app integration

---

## Conclusion

The Cap Table Summary feature provides a comprehensive dashboard widget that displays key cap table metrics and visualizes shareholder distribution. The implementation follows established patterns for API integration, state management, and component architecture while maintaining high standards for accessibility, performance, and user experience.

The feature enhances the dashboard experience by providing quick insights into company ownership structure and facilitating navigation to detailed cap table information. With proper implementation of loading states, error handling, and responsive design, this feature will provide significant value to users while maintaining the application's high quality standards.
