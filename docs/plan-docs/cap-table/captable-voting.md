# Voting Rights Tab Implementation Requirements

**Author**: Frontend | **Owners**: Product, Legal, Frontend, Backend | **Status**: Draft

## Overview

The Voting Rights tab provides comprehensive tracking of voting control and shareholder voting percentages for corporate governance and compliance purposes. This component displays voting power analysis, majority control tracking, and email management for shareholder communications, now with full API integration for real-time data and email editing capabilities.

## Goals

- Display accurate voting percentages for all eligible shareholders
- Track majority voting control for corporate governance compliance
- Provide email management functionality for shareholder communications
- Ensure compliance with Stockholder Approval requirements
- Maintain accurate vested share calculations for terminated individuals
- Support corporate governance and legal compliance needs
- API Integration: Real-time data from backend with proper loading states and error handling
- Email Management: Allow administrators to view and edit shareholder email addresses

## UX Overview

- **Voting Rights Analysis**: Clear display of voting percentages and power levels
- **Shareholder Table**: Comprehensive table with voting shares, percentages, and power indicators
- **Email Management**: Display and edit shareholder email addresses
- **Majority Control Tracking**: Clear indication of majority blocking rights
- **Professional Design**: Clean, modern interface suitable for board meetings and legal compliance
- **API Integration**: Real-time data from backend with proper loading states

---

## Requirements Summary

### 1. API Integration
- **Voting Rights API**: `GET /api/p2/companies/{companyId}/captable/votingrights`
- **Email Update API**: `PUT /api/p2/companies/{companyId}/captable/votingrights/updateemail`
- **Email Status Update API**: `PUT /api/p2/companies/{companyId}/captable/votingrights/updatesendemail`
- **Global Filter Support**: `proFormaRoleFilter`, `proFormaShareClassFilter`, `shareHolderName` query parameters
- **Service Layer**: Create dedicated service for API calls with filter support
- **Hook Layer**: Create React Query hook for data fetching and mutations with debounced filters
- **Data Transformation**: Transform API responses to component format
- **Email Management**: Display and edit email addresses with API integration
- **Email Status Management**: Toggle email inclusion/exclusion with real-time validation

### 2. Tab Positioning and Structure
- **Location**: Positioned next to "Stock Option Plan" tab (second position)
- **Purpose**: Track voting percentages and majority control
- **Email Management**: Allow editing of shareholder email addresses

### 3. Stakeholder Inclusion Criteria
- **Common Stock**: Persons issued common stock per incorporation questionnaire
- **Restricted Stock**: Individuals with restricted stock (inside/outside SOP)
- **Exercised Options**: Individuals who exercised options (tracked via SOP tab)

### 4. Voting Share Calculations
- **Initial State**: Full share allocation for all shareholders
- **Termination Impact**: Only vested shares count for terminated individuals
- **Vesting Example**: 4-year vesting, 100 shares → 75 shares after 3 years if terminated

### 5. Voting Power Classification
- **High Voting Power**: ≥33% of total voting shares
- **Medium Voting Power**: ≥10% but <33% of total voting shares
- **Low Voting Power**: <10% of total voting shares

### 6. Simplified Blocking Rights
- **Remove**: All supermajority references and calculations
- **Keep**: Majority blocking rights only
- **Display**: Clear indication if shareholder can block majority decisions

### 7. Email Management System
- **Email Column**: Display shareholder email addresses (2nd column)
- **Actions Column**: Provide edit functionality for email addresses
- **API Integration**: Update email addresses via dedicated API endpoint
- **Validation**: Email format validation and error handling

### 8. Email Status/Exclusion System
- **Email Status Column**: Checkbox column for including/excluding shareholders from email communications
- **Email Exclusion Summary**: Display excluded and remaining voting power percentages
- **Validation Logic**: Ensure at least 50% of voting power remains for email communications
- **API Integration**: Update email status via `PUT /api/p2/companies/{companyId}/captable/votingrights/updatesendemail`
- **Real-time Updates**: Immediate UI updates when checkboxes are toggled
- **Warning System**: Display warning when exclusion would violate 50% threshold

### 9. Global Filter Integration (New Requirements)
- **Filter System**: Integrate with global filter system for consistent filtering across all Cap Table tabs
- **Search Functionality**: Filter by shareholder name using `shareHolderName` parameter
- **Role Filter**: Filter by shareholder role using `proFormaRoleFilter` parameter
- **Share Class Filter**: Filter by share class using `proFormaShareClassFilter` parameter
- **Global State Management**: Use `GlobalFilterContext` for consistent filter state across tabs
- **API Integration**: Pass filter parameters to voting rights API endpoint
- **Debounce Implementation**: 500ms debounce delay to prevent excessive API calls during rapid typing

---

## API Integration Architecture

### Voting Rights API Endpoint Details
```
GET /api/p2/companies/{companyId}/captable/votingrights
```

**Parameters:**
- `companyId` (path): UUID of the company
- `proFormaRoleFilter` (query, optional): Filter by shareholder role (All, Founders, Investors, Advisors, Contractors, Employees)
- `proFormaShareClassFilter` (query, optional): Filter by share class (All, Common, SeriesAPreferred, StockOptionPool)
- `shareHolderName` (query, optional): Filter by shareholder name (search term)

**Current Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "latestUpdatedDate": "2025-07-31",
    "votingRights": [
      {
        "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
        "sendEmail": true,
        "shareHolder": "Omed Shariff",
        "shareType": "Common",
        "votingShares": 250000,
        "votingPercentage": 82.78,
        "votingPower": "High",
        "canBlockMajority": true,
        "companyServiceProviderId": null,
        "officerId": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb"
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

**Future API Response Structure (when email field is added):**
```json
{
  "message": "Success",
  "data": {
    "latestUpdatedDate": "2025-07-31",
    "votingRights": [
      {
        "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
        "email": "<EMAIL>",
        "shareHolder": "Omed Shariff",
        "shareType": "Common",
        "votingShares": 250000,
        "votingPercentage": 82.78,
        "votingPower": "High",
        "canBlockMajority": true,
        "companyServiceProviderId": null,
        "officerId": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb"
      }
    ]
  },
  "error": null,
  "validationErrors": null
}
```

### Email Update API Endpoint Details
```
PUT /api/p2/companies/{companyId}/captable/votingrights/updateemail
```

**Parameters:**
- `companyId` (path): UUID of the company

**Request Body:**
```json
{
  "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
  "email": "<EMAIL>"
}
```

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
    "email": "<EMAIL>",
    "updatedAt": "2025-01-15T10:30:00Z"
  },
  "error": null,
  "validationErrors": null
}
```

### Email Status Update API Endpoint Details
```
PUT /api/p2/companies/{companyId}/captable/votingrights/updatesendemail
```

**Parameters:**
- `companyId` (path): UUID of the company

**Request Body:**
```json
{
  "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
  "sendEmail": true
}
```

**Response Structure:**
```json
{
  "message": "Success",
  "data": {
    "id": "f7b254c6-09c0-488e-ae6c-d44e48be2ebb",
    "sendEmail": true,
    "updatedAt": "2025-01-15T10:30:00Z"
  },
  "error": null,
  "validationErrors": null
}
```

**Usage:**
- `sendEmail: true` - Include shareholder in email communications (checkbox checked)
- `sendEmail: false` - Exclude shareholder from email communications (checkbox unchecked)
- `id` - The unique identifier of the voting right row being updated

### Service Layer Implementation

#### **File**: `src/services/cap-table/votingRights.service.ts`
```typescript
import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  VotingRightsApiResponse, 
  VotingRightsData,
  VotingShareholder,
  UpdateEmailRequest,
  UpdateEmailResponse
} from "@/types/capTable";

export const getVotingRights = async (
  companyId: string, 
  filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }
): Promise<VotingRightsData> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getVotingRights(companyId, filters);
    
    return transformApiResponse(response as VotingRightsApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch voting rights: ${error}`);
  }
};

export const updateVotingRightEmail = async (
  companyId: string, 
  request: UpdateEmailRequest
): Promise<UpdateEmailResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.updateVotingRightEmail(companyId, request);
    
    return response as UpdateEmailResponse;
  } catch (error) {
    throw new Error(`Failed to update email: ${error}`);
  }
};

export const updateVotingRightEmailStatus = async (
  companyId: string, 
  request: UpdateEmailStatusRequest
): Promise<UpdateEmailStatusResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.updateVotingRightEmailStatus(companyId, request);
    
    return response as UpdateEmailStatusResponse;
  } catch (error) {
    throw new Error(`Failed to update email status: ${error}`);
  }
};

const transformApiResponse = (apiData: VotingRightsApiResponse): VotingRightsData => {
  const shareholders = apiData.votingRights.map((votingRight) => ({
    id: votingRight.id,
    name: votingRight.shareHolder,
    email: votingRight.email || "No email available", // Will be populated when API includes email field
    shareType: mapShareTypeFromApi(votingRight.shareType),
    votingShares: votingRight.votingShares || 0,
    totalShares: votingRight.votingShares || 0, // Using voting shares as total for now
    vestedShares: votingRight.votingShares || 0, // Using voting shares as vested for now
    isTerminated: false, // Could be enhanced with additional API data
    votingPercentage: votingRight.votingPercentage || 0,
    votingPower: votingRight.votingPower as 'High' | 'Medium' | 'Low',
    canBlockMajority: votingRight.canBlockMajority || false,
    sendEmail: votingRight.sendEmail || true, // Default to true if not specified
    role: getRoleFromShareType(votingRight.shareType),
    // Additional fields for backend processing
    companyServiceProviderId: votingRight.companyServiceProviderId,
    officerId: votingRight.officerId,
  }));

  const totalVotingShares = shareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  
  // Calculate email exclusion summary
  const includedShareholders = shareholders.filter(sh => sh.sendEmail);
  const excludedShareholders = shareholders.filter(sh => !sh.sendEmail);
  
  const includedVotingShares = includedShareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  const excludedVotingShares = excludedShareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  
  const includedVotingPercentage = totalVotingShares > 0 ? (includedVotingShares / totalVotingShares) * 100 : 0;
  const excludedVotingPercentage = totalVotingShares > 0 ? (excludedVotingShares / totalVotingShares) * 100 : 0;
  
  const isEmailExclusionValid = includedVotingPercentage >= 50;

  return {
    shareholders,
    totalVotingShares,
    majorityThreshold: totalVotingShares * 0.5,
    majorityThresholdPercentage: 50,
    lastUpdated: apiData.latestUpdatedDate,
    // Email exclusion summary
    emailExclusionSummary: {
      includedVotingShares,
      excludedVotingShares,
      includedVotingPercentage,
      excludedVotingPercentage,
      isEmailExclusionValid,
      includedShareholders,
      excludedShareholders,
    },
  };
};

const mapShareTypeFromApi = (apiShareType: string): 'Common Stock' | 'Restricted Stock' | 'Exercised Options' => {
  const shareTypeMap: Record<string, 'Common Stock' | 'Restricted Stock' | 'Exercised Options'> = {
    'Common': 'Common Stock',
    'RestrictedStock': 'Restricted Stock',
    'ExercisedOptions': 'Exercised Options',
  };
  return shareTypeMap[apiShareType] || 'Common Stock';
};

const getRoleFromShareType = (shareType: string): string => {
  const roleMap: Record<string, string> = {
    'Common': 'Founder/Shareholder',
    'RestrictedStock': 'Employee/Advisor',
    'ExercisedOptions': 'Employee',
  };
  return roleMap[shareType] || 'Shareholder';
};
```

### Hook Layer Implementation

#### **File**: `src/hooks/cap-table/useVotingRights.hooks.ts`
```typescript
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { getVotingRights, updateVotingRightEmail } from "@/services/cap-table/votingRights.service";
import { useAuth } from "@/contexts/AuthContext";
import { VotingRightsData, UpdateEmailRequest, UpdateEmailResponse } from "@/types/capTable";
import { useToast } from "@/hooks/use-toast";

// Custom debounce hook for API parameters
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

export const useVotingRights = (filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const debouncedFilters = useDebounce(filters, 500); // 500ms debounce delay

  return useQuery<VotingRightsData>({
    queryKey: ["votingRights", companyId, debouncedFilters],
    queryFn: () => getVotingRights(companyId!, debouncedFilters),
    enabled: !!companyId,
  });
};

export const useUpdateVotingRightEmail = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<UpdateEmailResponse, Error, UpdateEmailRequest>({
    mutationFn: (request) => updateVotingRightEmail(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch voting rights data
      queryClient.invalidateQueries({ queryKey: ["votingRights", companyId] });
      
      toast({
        title: "Email Updated",
        description: "Shareholder email address has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update email address.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateVotingRightEmailStatus = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<UpdateEmailStatusResponse, Error, UpdateEmailStatusRequest>({
    mutationFn: (request) => updateVotingRightEmailStatus(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch voting rights data
      queryClient.invalidateQueries({ queryKey: ["votingRights", companyId] });
      
      toast({
        title: "Email Status Updated",
        description: "Shareholder email inclusion status has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update email status.",
        variant: "destructive",
      });
    },
  });
};
```

---

## Enhanced Data Model

### API Response Types
Add to `src/types/capTable.ts`:

```ts
// API Response Types for Voting Rights
export interface VotingRightsApiResponse {
  latestUpdatedDate: string;
  votingRights: VotingRightApiItem[];
}

export interface VotingRightApiItem {
  id: string;
  email?: string; // Optional for now, will be required when API includes email field
  shareHolder: string;
  shareType: string;
  votingShares: number;
  votingPercentage: number;
  votingPower: string;
  canBlockMajority: boolean;
  companyServiceProviderId: string | null;
  officerId: string | null;
}

// Email Update Types
export interface UpdateEmailRequest {
  id: string;
  email: string;
}

export interface UpdateEmailResponse {
  id: string;
  email: string;
  updatedAt: string;
}

// Email Status Update Types
export interface UpdateEmailStatusRequest {
  id: string;
  sendEmail: boolean;
}

export interface UpdateEmailStatusResponse {
  id: string;
  sendEmail: boolean;
  updatedAt: string;
}

// Enhanced Voting Rights Types
export interface VotingShareholder {
  id: string;
  name: string;
  email: string;
  shareType: 'Common Stock' | 'Restricted Stock' | 'Exercised Options';
  votingShares: number;
  totalShares: number;
  vestedShares: number;
  terminationDate?: string;
  isTerminated: boolean;
  votingPercentage: number;
  votingPower: 'High' | 'Medium' | 'Low';
  canBlockMajority: boolean;
  sendEmail: boolean; // Email inclusion/exclusion status
  role: string;
  grantDate?: string;
  vestingSchedule?: string;
  // Additional fields for backend processing
  companyServiceProviderId?: string | null;
  officerId?: string | null;
}

export interface EmailExclusionSummary {
  includedVotingShares: number;
  excludedVotingShares: number;
  includedVotingPercentage: number;
  excludedVotingPercentage: number;
  isEmailExclusionValid: boolean;
  includedShareholders: VotingShareholder[];
  excludedShareholders: VotingShareholder[];
}

export interface VotingRightsData {
  shareholders: VotingShareholder[];
  totalVotingShares: number;
  majorityThreshold: number;
  majorityThresholdPercentage: number;
  lastUpdated: string;
  emailExclusionSummary: EmailExclusionSummary;
}

export interface VotingPowerAnalysis {
  highPowerShareholders: VotingShareholder[];
  mediumPowerShareholders: VotingShareholder[];
  lowPowerShareholders: VotingShareholder[];
  majorityBlockers: VotingShareholder[];
  totalHighPowerPercentage: number;
  totalMediumPowerPercentage: number;
  totalLowPowerPercentage: number;
}
```

---

## Component Architecture

### Entry Point
- `components/captable/VotingRights.tsx` (major updates required)
  - **Purpose**: Main Voting Rights component with API integration and email management
  - **Props**: None (self-contained with API data)
  - **State**: API data, loading states, error states, email editing state
  - **Children**: Voting analysis section, shareholder table, email management

### Enhanced Component Structure
```
VotingRights.tsx (API Integration + Email Management + Email Status + Global Filters)
├── Card (container)
├── CardHeader
│   ├── CardTitle ("Voting Rights Analysis")
│   └── Last Updated Date (FROM API)
├── CardContent
│   ├── Loading State (skeleton) - ADD
│   ├── Error State (error message) - ADD
│   ├── Voting Thresholds Section
│   │   ├── Majority Threshold Display
│   │   └── Information Icon with Tooltip
│   ├── Email Exclusion Summary Section - NEW
│   │   ├── Excluded Voting Power Display
│   │   ├── Remaining Voting Power Display
│   │   ├── Status Indicator (Valid/Invalid)
│   │   └── Warning Message (if exclusion invalid)
│   ├── Shareholder Table
│   │   ├── TableHeader
│   │   │   ├── Email Status Column (1st) - NEW
│   │   │   ├── Shareholder Name Column (2nd)
│   │   │   ├── Email Column (3rd)
│   │   │   ├── Share Type Column (4th)
│   │   │   ├── Voting Shares Column (5th)
│   │   │   ├── Voting % Column (6th)
│   │   │   ├── Voting Power Column (7th)
│   │   │   ├── Can Block Majority Column (8th)
│   │   │   └── Actions Column (9th)
│   │   └── TableBody
│   │       ├── Individual Shareholder Rows
│   │       │   ├── Email Status Checkbox - NEW
│   │       │   └── Email Edit Button
│   │       └── Email Edit Modal/Dialog
│   ├── Voting Analysis Section
│   │   ├── Majority Vote Threshold Definition
│   │   ├── Voting Power Definitions
│   │   └── Blocking Rights Alert
│   └── Empty State (when no shareholders)
```

### Global Filter Integration
```typescript
// VotingRights component integration
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";

const VotingRights: React.FC = () => {
  const { filters } = useGlobalFilters();
  
  const { data, isLoading, error } = useVotingRights({
    proFormaRoleFilter: filters.roleFilter,
    proFormaShareClassFilter: filters.shareClassFilter,
    shareHolderName: filters.shareholderNameSearch
  });

  // Component uses global filter state automatically
  // No need to render CapTableFilters component here
  // as it's already rendered globally in CapTableTabs.tsx
};
```

### New Shared Components
- `Skeleton` for loading states
- `Alert`, `AlertDescription` for error states
- `Button` with retry functionality
- `Dialog`, `DialogContent`, `DialogHeader`, `DialogTitle` for email editing
- `Input` for email input field
- `Label` for form labels

---

## Enhanced UI/UX Requirements

### Loading and Error States
- **Loading State**: Skeleton loader while fetching data from API
- **Error State**: Clear error message with retry option
- **Empty State**: Message when no voting rights data found
- **Partial Data**: Handle cases where API returns incomplete data

### Email Management Design
- **Email Column**: Display current email addresses (3rd column)
- **Actions Column**: Edit button for each shareholder
- **Edit Modal**: Clean dialog for email editing
- **Validation**: Email format validation with error messages
- **Success Feedback**: Toast notification on successful update

### Email Status/Exclusion Design
- **Email Status Column**: Checkbox column (1st column) for including/excluding shareholders
- **Email Exclusion Summary**: Display section showing excluded and remaining voting power
- **Real-time Validation**: Prevent exclusion if it would violate 50% voting power threshold
- **Warning System**: Display warning message when exclusion is invalid
- **API Integration**: Immediate API calls when checkboxes are toggled
- **Visual Feedback**: Clear indication of valid/invalid exclusion status

### Email Exclusion Summary Section
```tsx
<div className="space-y-4 p-4 bg-muted rounded-lg">
  <h3 className="text-lg font-semibold">Email Exclusion Summary</h3>
  <div className="grid grid-cols-2 gap-4">
    <div>
      <span className="text-sm text-muted-foreground">Excluded Voting Power:</span>
      <div className="text-2xl font-bold">{emailExclusionSummary.excludedVotingPercentage.toFixed(2)}%</div>
    </div>
    <div>
      <span className="text-sm text-muted-foreground">Remaining Voting Power:</span>
      <div className="text-2xl font-bold">{emailExclusionSummary.includedVotingPercentage.toFixed(2)}%</div>
    </div>
  </div>
  <div className="flex items-center gap-2">
    <span className="text-sm">Status:</span>
    <Badge variant={emailExclusionSummary.isEmailExclusionValid ? "default" : "destructive"}>
      {emailExclusionSummary.isEmailExclusionValid ? "Valid" : "Invalid"}
    </Badge>
  </div>
  {!emailExclusionSummary.isEmailExclusionValid && (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Cannot exclude shareholders: must maintain at least 50% of voting power for email communications.
      </AlertDescription>
    </Alert>
  )}
</div>
```

### Email Status Implementation
```tsx
// Email Status Column
<TableHead className="text-center">
  <Tooltip>
    <TooltipTrigger>Email</TooltipTrigger>
    <TooltipContent>Include/exclude shareholder from email communications</TooltipContent>
  </Tooltip>
</TableHead>

<TableCell className="text-center">
  <Checkbox
    checked={shareholder.sendEmail}
    onCheckedChange={(checked) => {
      const newSendEmail = checked as boolean;
      // Validate before making API call
      const wouldExclude = !newSendEmail;
      if (wouldExclude) {
        const currentIncludedPercentage = data.emailExclusionSummary.includedVotingPercentage;
        const shareholderPercentage = shareholder.votingPercentage;
        const newIncludedPercentage = currentIncludedPercentage - shareholderPercentage;
        
        if (newIncludedPercentage < 50) {
          toast.error("Cannot exclude shareholder: must maintain at least 50% of voting power for email communications.");
          return;
        }
      }
      
      // Make API call
      updateEmailStatusMutation.mutate({
        id: shareholder.id,
        sendEmail: newSendEmail,
      });
    }}
    disabled={updateEmailStatusMutation.isPending}
  />
</TableCell>

// Email Column
<TableHead>
  <Tooltip>
    <TooltipTrigger>Email</TooltipTrigger>
    <TooltipContent>Shareholder email address for communications</TooltipContent>
  </Tooltip>
</TableHead>

<TableCell>
  <span className="text-sm">{shareholder.email || "No email"}</span>
</TableCell>

// Actions Column
<TableHead className="text-center">
  <Tooltip>
    <TooltipTrigger>Actions</TooltipTrigger>
    <TooltipContent>Manage shareholder information</TooltipContent>
  </Tooltip>
</TableHead>

<TableCell className="text-center">
  <Button
    variant="ghost"
    size="sm"
    onClick={() => setEditEmailModal({ isOpen: true, data: shareholder })}
  >
    <Edit className="h-4 w-4" />
  </Button>
</TableCell>
```

### Email Edit Modal Design
```tsx
<Dialog open={editEmailModal.isOpen} onOpenChange={(open) => setEditEmailModal({ isOpen: open, data: null })}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Edit Email Address</DialogTitle>
    </DialogHeader>
    <div className="space-y-4">
      <div>
        <Label htmlFor="shareholder-name">Shareholder</Label>
        <div className="text-sm text-muted-foreground">{editEmailModal.data?.name}</div>
      </div>
      <div>
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          type="email"
          value={emailValue}
          onChange={(e) => setEmailValue(e.target.value)}
          placeholder="Enter email address"
        />
      </div>
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => setEditEmailModal({ isOpen: false, data: null })}>
          Cancel
        </Button>
        <Button 
          onClick={handleEmailUpdate}
          disabled={!emailValue || emailValue === editEmailModal.data?.email}
        >
          Update Email
        </Button>
      </div>
    </div>
  </DialogContent>
</Dialog>
```

### Enhanced Table Design
- **Layout**: Clean, modern table with subtle borders
- **Headers**: Bold column headers with tooltips for explanations
- **Rows**: Alternating row colors for better readability
- **Responsive**: Horizontal scroll on mobile devices
- **API Data**: Use API data for all shareholder information

### Updated Column Specifications

#### **Email Status Column (1st) - NEW:**
- **Center-aligned checkbox**
- **Functionality**: Toggle email inclusion/exclusion for shareholder
- **API Data**: `votingRight.sendEmail`
- **Behavior**: 
  - Checked = Include in email communications (`sendEmail: true`)
  - Unchecked = Exclude from email communications (`sendEmail: false`)
- **Real-time Updates**: Immediate API call when checkbox is toggled
- **Validation**: Prevents exclusion if it would violate 50% voting power threshold
- **Tooltip**: "Include/exclude shareholder from email communications"

#### **Shareholder Name Column (2nd):**
- **Left-aligned text**
- **Font weight**: Medium
- **Truncation**: For long names with tooltip
- **API Data**: `votingRight.shareHolder`

#### **Email Column (3rd):**
- **Left-aligned text**
- **Display**: Email address or "No email available" placeholder
- **Tooltip**: "Shareholder email address for communications"
- **API Data**: `votingRight.email` (will be populated when API includes email field)
- **Current State**: Shows placeholder text until API includes email field

#### **Share Type Column (4th):**
- **Left-aligned text**
- **Badge Display**: 
  - Common Stock (blue badge)
  - Restricted Stock (purple badge)
  - Exercised Options (green badge)
- **Tooltip**: "Type of equity holding"
- **API Data**: `votingRight.shareType` (mapped to display names)

#### **Voting Shares Column (5th):**
- **Right-aligned numbers**
- **Formatting**: Thousands separator (comma)
- **Tooltip**: "Number of voting shares"
- **API Data**: `votingRight.votingShares`

#### **Voting % Column (6th):**
- **Right-aligned percentages**
- **Formatting**: Two decimal places
- **Tooltip**: "Percentage of total voting power"
- **API Data**: `votingRight.votingPercentage`

#### **Voting Power Column (7th):**
- **Left-aligned badges**
- **Badge Display**:
  - High (≥33%): Green badge
  - Medium (≥10%): Yellow badge
  - Low (<10%): Gray badge
- **Tooltip**: "Voting power classification based on percentage"
- **API Data**: `votingRight.votingPower`

#### **Can Block Majority Column (8th):**
- **Center-aligned text**
- **Display**: "Yes" or "No"
- **Styling**: 
  - "Yes": Green text
  - "No": Gray text
- **Tooltip**: "Can block majority decisions (≥50%)"
- **API Data**: `votingRight.canBlockMajority`

#### **Actions Column (9th):**
- **Center-aligned buttons**
- **Edit Button**: Icon button for email editing
- **Tooltip**: "Manage shareholder information"
- **Functionality**: Opens email edit modal

---

## Implementation Plan

### Phase 1: API Integration Setup
1. **Add API endpoints** to `client.ts` for `votingRights`, `updateVotingRightEmail`, and `updateVotingRightEmailStatus`
2. **Create service layer** (`votingRights.service.ts`) with GET and PUT methods for all endpoints
3. **Create hook layer** (`useVotingRights.hooks.ts`) with query and mutation hooks for all operations
4. **Add TypeScript interfaces** for API responses and requests including email status

### Phase 2: Data Transformation
1. **Implement data transformation** from API to component format
2. **Add error handling** for missing or invalid data
3. **Implement share type mapping** logic
4. **Add email exclusion summary calculations** with validation logic
5. **Add loading and error states**

### Phase 3: Component Updates
1. **Update component** to use API data instead of static data
2. **Implement loading states** with skeleton loaders
3. **Add error handling** with retry functionality
4. **Add Email Status column** as 1st column with checkboxes
5. **Add Email Exclusion Summary section** with validation display
6. **Add Email column** as 3rd column (with placeholder text for now)
7. **Add Actions column** with edit functionality
8. **Implement real-time validation** for email exclusion

### Phase 4: Email Management & Status
1. **Implement email edit modal** with form validation
2. **Add email update mutation** with success/error handling
3. **Implement email status checkboxes** with real-time API calls
4. **Add email status update mutation** with validation
5. **Add toast notifications** for user feedback
6. **Implement optimistic updates** for better UX
7. **Handle placeholder email display** until API includes email field
8. **Add email exclusion validation** with warning messages

### Phase 5: Global Filter Integration
1. **Update API client** to support filter parameters for voting rights endpoint
2. **Update service layer** to accept and pass filter parameters
3. **Update hook layer** to use debounced filter parameters
4. **Update component** to integrate with global filter state
5. **Test filter functionality** with real data

### Phase 6: Professional Polish
1. **Add comprehensive tooltips** for all columns
2. **Implement accessibility features** and keyboard navigation
3. **Add professional styling** and responsive design
4. **Test API integration** with real data

---

## Success Metrics

### User Experience
- **API Integration**: Seamless data loading from backend
- **Email Management**: Smooth email editing workflow
- **Compliance Tracking**: Clear indication of majority control requirements
- **Professional Appearance**: Suitable for board meetings and legal compliance
- **Error Handling**: Graceful handling of missing or invalid data

### Technical Metrics
- **Performance**: Fast API calls with proper caching
- **Accessibility**: Full WCAG compliance
- **Responsive Design**: Perfect functionality across all devices
- **Type Safety**: Complete TypeScript support
- **Error Resilience**: Robust error handling and recovery

---

## Dependencies

### External Dependencies
- `@tanstack/react-query`: Data fetching and caching
- `lucide-react`: Icon library for consistent iconography

### Internal Dependencies
- `@/components/ui/card`: Card components for layout
- `@/components/ui/table`: Table components
- `@/components/ui/dialog`: Dialog components for email editing
- `@/components/ui/input`: Input components for email field
- `@/components/ui/label`: Label components for form fields
- `@/components/ui/button`: Button components for actions
- `@/components/ui/alert`: Alert components for error states
- `@/components/ui/badge`: Badge components for status display
- `@/components/ui/tooltip`: Tooltip components for explanations
- `@/components/ui/skeleton`: Skeleton components for loading states
- `@/integrations/legal-concierge/client`: API client
- `@/contexts/AuthContext`: Authentication context for company ID
- `@/hooks/use-toast`: Toast notification hook

---

## Risk Assessment

### Technical Risks
- **API Reliability**: Backend API availability and response times
- **Data Transformation**: Complex mapping between API and component formats
- **Error Handling**: Comprehensive error scenarios and edge cases
- **Performance**: Large datasets and caching strategies
- **Email Validation**: Email format validation and error handling

### Mitigation Strategies
- **Robust Error Handling**: Comprehensive try-catch blocks and fallbacks
- **Data Validation**: Validate API responses before transformation
- **Caching Strategy**: Implement proper caching with React Query
- **Loading States**: Provide clear feedback during data fetching
- **Email Validation**: Client-side email format validation

### Business Risks
- **Data Accuracy**: Incorrect voting calculations may lead to compliance issues
- **User Experience**: Poor loading times or error states may frustrate users
- **Data Integrity**: API data must be accurately transformed and displayed
- **Email Management**: Incorrect email updates may affect communications

### Mitigation Strategies
- **Comprehensive Testing**: Test all API scenarios and edge cases
- **User Feedback**: Clear loading and error states
- **Data Validation**: Validate all API responses before display
- **Fallback Mechanisms**: Graceful degradation when API is unavailable
- **Email Validation**: Proper email format validation and confirmation

---

## Conclusion

The enhanced Voting Rights tab provides a comprehensive, professional-grade interface for voting control tracking with full API integration and email management capabilities. The implementation ensures compliance with corporate governance requirements while maintaining excellent user experience and accessibility standards.

### Key Features:
✅ **API Integration**: Real-time data from backend with proper error handling  
✅ **Email Management**: Display and edit shareholder email addresses (ready for API email field)  
✅ **Email Status/Exclusion**: Checkbox system for including/excluding shareholders from email communications  
✅ **Email Exclusion Summary**: Real-time display of excluded and remaining voting power with validation  
✅ **Voting Power Analysis**: Clear visualization of voting power distribution  
✅ **Majority Control Tracking**: Simplified blocking rights display  
✅ **Share Type Classification**: Clear identification of equity types  
✅ **Professional Design**: Clean, modern UI suitable for legal compliance  
✅ **Compliance Ready**: Proper tracking for corporate governance requirements  
✅ **Real-time Validation**: Prevents exclusion that would violate 50% voting power threshold  
✅ **Future Ready**: UI structure ready for when API includes email field  
✅ **Global Filter Integration**: Consistent filtering across all Cap Table tabs with debounced search  

The implementation provides a solid foundation for voting rights management with full API integration, email management capabilities, and room for future enhancements. The email column will automatically populate when the backend API includes the email field.
