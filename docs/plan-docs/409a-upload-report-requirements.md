### 409A Valuation – Upload Report + Submit for Approval (Shared Across Two Modals)

#### Goal
Implement a single, shared upload-and-submit workflow used by both modals:
- Action menu → “Update 409A Valuation” in `Dialog409Valuation.tsx` (dark theme)
- Dashboard → `Maintenance409Valuation.tsx` (light theme)

Both modals call the same API sequence and use the same button text: “Send for Approval”.

---

### Endpoints
1) Get presigned URL (S3) for 409A valuation file
- Method: POST
- Path: `/api/p2/companies/{companyId}/documents/409avaluation/presigned-url`
- Body: `{ key: string; contentType: string }`
- Response:
  - `{ data: { uploadUrl: string; key: string } }`
  - `uploadUrl` is a temporary S3 URL; `key` is the S3 object key we will later persist.

2) Submit 409A valuation for approval
- Method: POST
- Path: `/api/p2/companies/{companyId}/409avaluation`
- Body:
  ```json
  {
    "appraiserName": "string",
    "appraisalDate": "YYYY-MM-DD",
    "fairMarketValue": 0,
    "confirmationStatus": "no|yes",
    "s3Key": "companies/.../409a-valuation/filename-..."
  }
  ```

---

### UX and Content (both modals)
- Single primary action label: “Send for Approval” (consistent across both).
- File validations
  - Accept `.pdf,.doc,.docx` only
  - Size ≤ 5MB; error toast otherwise
- Required fields (Update 409A mode)
  - File (doc/pdf)
  - Appraiser Name
  - Appraisal Date (ISO YYYY-MM-DD)
  - Fair Market Value (> 0)
- Loading/disabled states
  - Disable file input and button during upload/submit
  - Primary CTA shows “Sending…” while in-flight
- Toasts (sonner)
  - Success: “409A valuation submitted for approval”
  - Error: show parsed server message or fallback

---

### Data and Types
- `PresignedRequest { key: string; contentType: string }`
- `PresignedResponse { uploadUrl: string; key: string }`
- `Submit409ARequest { appraiserName: string; appraisalDate: string; fairMarketValue: number; confirmationStatus: 'yes' | 'no'; s3Key: string }`

---

### Implementation Plan (minimal, SOLID, reuse)

1) API client – `src/integrations/legal-concierge/client.ts`
- Add:
  - `get409aPresignedUrl(companyId: string, payload: { key: string; contentType: string })` → POST `/api/p2/companies/${companyId}/documents/409avaluation/presigned-url`
  - `submit409aValuation(companyId: string, payload: { appraiserName: string; appraisalDate: string; fairMarketValue: number; confirmationStatus: string; s3Key: string })` → POST `/api/p2/companies/${companyId}/409avaluation`

2) Service layer – `src/services/service-providers/promisedGrants.service.ts`
- Add wrappers:
  - `get409APresignedUrl(companyId, key, contentType)` → returns `{ uploadUrl, key }`
  - `submit409AValuation(companyId, dto)` → returns API response

3) Hook – `src/hooks/service-providers/usePromisedGrants.hooks.ts` (or a small dedicated `use409aValuation.hooks.ts`)
- `useSubmit409AValuation(companyId)` (react-query `useMutation`):
  - Input: `{ file: File; appraiserName: string; appraisalDate: string; fairMarketValue: number; confirmationStatus: 'yes' | 'no' }`
  - Steps:
    1. Build `key` using filename + timestamp (server may also return a canonical key)
    2. Call `get409APresignedUrl(companyId, { key, contentType: file.type })`
    3. Upload to S3 using `fetch(presigned.uploadUrl, { method: 'PUT', headers: { 'Content-Type': file.type }, body: file })`
    4. POST `submit409AValuation` with `{ appraiserName, appraisalDate, fairMarketValue, confirmationStatus, s3Key: presigned.key }`
  - On success:
    - `invalidateQueries(['active409AValuation', companyId])`
    - `toast.success('409A valuation submitted for approval')`
  - On error: `toast.error(message)`
  - Return `{ submit409A, isSubmitting409A }`

4) UI wiring – both modals
- `Dialog409Valuation.tsx` (mode: `update-409a`)
  - Use `useSubmit409AValuation(companyId)`
  - Validate inputs, call `submit409A(formData)`
  - Primary button text: “Send for Approval”
- `Maintenance409Valuation.tsx`
  - Same hook and submit flow; same button text

---

### Validation Rules
- File required and ≤ 5MB; allowed content types: `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- `fairMarketValue` required and `> 0`
- `appraisalDate` required; convert UI date to `YYYY-MM-DD`

---

### Query Keys / Invalidation
- `['active409AValuation', companyId]` – refetch after successful submission to refresh the Equity Overview / 409A readouts on the page(s)

---

### Error Handling
- Use existing client error parser where available
- Show first validation error if present; otherwise generic fallback

---

### Pseudocode
```
const { submit409A, isSubmitting409A } = useSubmit409AValuation(companyId);

async function onSendForApproval() {
  if (!file || !appraiserName || !appraisalDate || !fairMarketValue) {
    toast.error('Please fill in all required fields');
    return;
  }
  await submit409A({ file, appraiserName, appraisalDate, fairMarketValue: Number(fairMarketValue), confirmationStatus: 'yes' });
  onClose();
}
```

---

### QA Checklist / Acceptance Criteria
- Same CTA text in both modals: “Send for Approval”
- Button disabled and shows “Sending…” during submit
- Rejection for invalid file type/size
- Success toast then dialog closes; page shows updated active 409A on refetch
- Robust error toasts; dialog remains open on error
- No console errors; strict types; no hydration errors

---

### Notes
- If the server already returns a canonical `key` pattern, we should use that rather than building our own; the presigned-url response includes the final `key` to persist.
- The flow is intentionally minimal and consistent with our existing hook/service patterns.
