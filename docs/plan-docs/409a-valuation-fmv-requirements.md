### 409A Valuation – Set Fair Market Value (FMV) – Requirements

#### Goal
Implement the API call to set the FMV for a company from the “Set Fair Market Value” flow in `Dialog409Valuation.tsx` with clear validation, good loading/disabled states, and toast feedback using `sonner`.

---

### Endpoint
- Method: PUT
- Path: `/api/p2/companies/{companyId}/409avaluation/setfairmarketvalue`
- Body: `{ fairMarketValue: number }`

Notes:
- `companyId` is the active company selected in auth context.
- `fairMarketValue` must be a positive number; decimals allowed.

---

### Data and Types
- Request type: `SetFMVRequest { fairMarketValue: number }`
- Success response: `{ message: string; data?: unknown }`

---

### Implementation Plan (minimal, SOLID, existing patterns)

1) API client
- File: `src/integrations/legal-concierge/client.ts`
- Add method:
  - `set409aFairMarketValue(companyId: string, payload: { fairMarketValue: number })` → `PUT /api/p2/companies/${companyId}/409avaluation/setfairmarketvalue`

2) Service layer
- File: `src/services/service-providers/promisedGrants.service.ts`
- Add function:
  - `setFairMarketValue(companyId: string, value: number)` → uses API client method above.

3) Hook
- New file: `src/hooks/service-providers/use409aValuation.hooks.ts`
- Export `useSetFairMarketValue()` – `react-query` `useMutation` calling service; invalidate:
  - `['active409a', companyId]`
- Toasts using `sonner`:
  - `toast.success('Fair market value updated')`
  - `toast.error(message)` on failure
- Return `{ setFMV, isSetting }` where `setFMV(value: number): Promise<void>`

4) UI wiring in dialog (mode: 'set-fair-market-value')
- File: `src/components/service-providers/dialog/Dialog409Valuation.tsx`
- Behaviors:
  - Validate input: non-empty, numeric, `> 0`.
  - Call `setFMV(Number(formData.pricePerShare))`.
  - While submitting: disable inputs/buttons; CTA shows “Sending…”.
  - On success: close dialog, clear form, toast success.
  - On error: stay open, toast error.

---

### Loading/Disabled States
- Primary button: `disabled={isSetting || !isValid}`; shows `Sending…` when `isSetting`.
- Input disabled while `isSetting`.
- Prevent duplicate submissions with guard in handler.

---

### Validation Rules
- Required: `pricePerShare` (FMV)
- Must be a number and `> 0`

---

### Query Keys and Invalidation
- `['active409a', companyId]` – refetch after success to update Equity Overview / Active 409A display.

---

### Error Handling & Toast Messages
- Network/server errors: `toast.error(parsedMessage)`
- 401 auto-refresh handled by client.
- 400/422 validation: show server message.

---

### Pseudocode
```
// use409aValuation.hooks.ts
const mutation = useMutation({
  mutationFn: (value: number) => service.setFairMarketValue(companyId, value),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['active409a', companyId] });
    toast.success('Fair market value updated');
  },
  onError: (e) => toast.error(normalizeError(e)),
});

return { setFMV: (v) => mutation.mutateAsync(v), isSetting: mutation.isPending };
```

---

### QA Checklist / Acceptance Criteria
- Button disabled during request; spinner/text change visible.
- Success toast, dialog closes, overview shows updated FMV/date after refetch.
- Invalid input shows error toast; no request is sent.
- Server error shows error toast; dialog stays open.
- No console errors; strict types; no hydration errors.

---

### Testing
- Unit: mock service; assert invalidate + toasts.
- Integration: simulate input + submit in `Dialog409Valuation` (set mode), assert disabled state, success close, and query refetch call.
