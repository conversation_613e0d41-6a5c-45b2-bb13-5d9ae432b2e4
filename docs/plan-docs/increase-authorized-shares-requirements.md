 # Increase Authorized Shares - API Integration Requirements

## Overview
The "Increase Authorized Shares" feature needs to be updated to integrate with backend APIs for real-time data fetching and state management, replacing the current mock data approach.

## API Endpoints

### 1. GET `/api/p2/companies/{companyId}/authorizedshares/currentpending`
**Purpose**: Fetch current authorized shares status and pending increase details

**Response Structure**:
```typescript
interface AuthorizedSharesResponse {
  message: string;
  data: {
    authorizedShare: number;
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    isFileOfCertificateOfAmendmentUploaded: "yes" | "no";
  };
  error: null | string;
  validationErrors: null | string[];
}
```

### 2. POST `/api/p2/companies/{companyId}/authorizedshares`
**Purpose**: Submit additional shares for board approval

**Request Body**:
```typescript
{
  "additionalShares": number;
}
```

### 3. PUT `/api/p2/companies/{companyId}/authorizedshares/submitforstockholderapproval`
**Purpose**: Submit for stockholder approval

### 4. PUT `/api/p2/companies/{companyId}/authorizedshares/abandoned`
**Purpose**: Abandon current active increase authorize process

**Response**: Success/error message with toast notification

### 5. POST `/api/p2/companies/{companyId}/documents/authorized-share/amendment-presigned-url`
**Purpose**: Get presigned URL for file upload to S3

**Request Body**:
```typescript
{
  "key": string; // filename with extension (e.g., "certificate.pdf")
  "contentType": string; // MIME type (e.g., "application/pdf", "application/msword")
}
```

**Response Structure**:
```typescript
{
  "message": string;
  "data": {
    "uploadUrl": string; // AWS S3 presigned URL for direct upload
    "key": string; // The S3 key to be used in the second API
  };
  "error": null | string;
  "validationErrors": null | string[];
}
```

### 6. POST `/api/p2/companies/{companyId}/authorizedshares/submitfileamendment`
**Purpose**: Submit uploaded file for approval (notify backend of successful upload)

**Request Body**:
```typescript
{
  "s3Key": string; // The "key" value from the first API response
}
```

**Response**: Success/error message with toast notification

## Component Architecture

### 1. Service Layer (`src/services/authorized-shares/authorizedShares.service.ts`)

```typescript
interface AuthorizedSharesService {
  getCurrentPending(companyId: string): Promise<AuthorizedSharesResponse>;
  submitForBoardApproval(companyId: string, additionalShares: number): Promise<void>;
  submitForStockholderApproval(companyId: string): Promise<void>;
  abandonProcess(companyId: string): Promise<void>;
  getPresignedUrl(companyId: string, key: string, contentType: string): Promise<{ uploadUrl: string; key: string }>;
  submitFileAmendment(companyId: string, s3Key: string): Promise<void>;
}
```

### 2. Hook Layer (`src/hooks/authorized-shares/useAuthorizedShares.hooks.ts`)

```typescript
interface UseAuthorizedSharesReturn {
  data: AuthorizedSharesResponse['data'] | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  isFileUploadLoading: boolean;
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (additionalShares: number) => Promise<void>;
  submitForStockholderApproval: () => Promise<void>;
  abandonProcess: () => Promise<void>;
  uploadFileAmendment: (file: File) => Promise<void>;
}
```

### 3. API Client Integration (`src/integrations/legal-concierge/client.ts`)

**New Methods to Add**:
```typescript
// GET: Get current pending authorized shares
async getCurrentPendingAuthorizedShares(companyId: string) {
  try {
    const response = await this.get(`/api/p2/companies/${companyId}/authorizedshares/currentpending`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// POST: Submit for board approval
async submitForBoardApproval(companyId: string, additionalShares: number) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/authorizedshares`, {
      additionalShares
    });
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// PUT: Submit for stockholder approval
async submitForStockholderApproval(companyId: string) {
  try {
    const response = await this.put(`/api/p2/companies/${companyId}/authorizedshares/submitforstockholderapproval`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// PUT: Abandon process
async abandonAuthorizedSharesProcess(companyId: string) {
  try {
    const response = await this.put(`/api/p2/companies/${companyId}/authorizedshares/abandoned`);
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// POST: Get presigned URL for file upload
async getAmendmentPresignedUrl(companyId: string, key: string, contentType: string) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/documents/authorized-share/amendment-presigned-url`, {
      key,
      contentType
    });
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// POST: Submit file amendment
async submitFileAmendment(companyId: string, s3Key: string) {
  try {
    const response = await this.post(`/api/p2/companies/${companyId}/authorizedshares/submitfileamendment`, {
      s3Key
    });
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
```

### 4. Updated Dialog Component (`src/components/shares/IncreaseSharesDialog.tsx`)

**Key Changes**:
- Replace mock data with API calls
- Add loading states
- Implement real-time status updates
- Add abandon functionality

## Implementation Plan

### Phase 1: Service Layer Implementation

1. **Create `src/services/authorized-shares/authorizedShares.service.ts`**
   - Implement all API calls using existing API client pattern from `src/integrations/legal-concierge/client.ts`
   - Add proper error handling and type safety
   - Follow existing service structure from `capTableService.ts`

2. **Create `src/hooks/authorized-shares/useAuthorizedShares.hooks.ts`**
   - Use `react-query` for data fetching (following existing pattern)
   - Implement mutations for POST/PUT operations
   - Add proper loading and error states
   - Follow existing hook patterns from `useCapTableData.ts`

### Phase 2: Component Updates

3. **Update `IncreaseSharesDialog.tsx`**
   - Replace mock data with `useAuthorizedShares` hook from `src/hooks/authorized-shares/useAuthorizedShares.hooks.ts`
   - Add loading states and error handling
   - Implement real-time status updates
   - Add abandon button functionality

4. **Update `IncreaseSharesInitialState.tsx`**
   - Make fields read-only based on API response
   - Update button states based on approval status
   - Add abandon button to dialog footer

### Phase 3: State Management

5. **Dialog State Logic**:
   ```typescript
   // Button visibility logic
   const showBoardApprovalButton = data?.isBoardApproved === "no";
   const showStockholderApprovalButton = data?.isBoardApproved === "yes" && data?.isStockHolderApproved === "no";
   const showFileAmendmentButton = data?.isStockHolderApproved === "yes";
   ```

6. **Field Editability Logic**:
   ```typescript
   // Additional shares field
   const isAdditionalSharesEditable = data?.additionalShare === 0;
   ```

## Detailed Component Changes

### `IncreaseSharesDialog.tsx` Updates

```typescript
const IncreaseSharesDialog: React.FC<IncreaseSharesDialogProps> = ({
  isOpen,
  onClose,
  companyId, // New prop instead of currentAuthorizedShares
}) => {
  const {
    data,
    isLoading,
    error,
    refetch,
    submitForBoardApproval,
    submitForStockholderApproval,
    abandonProcess,
  } = useAuthorizedShares(companyId); // Import from src/hooks/authorized-shares/useAuthorizedShares.hooks.ts

  // Remove local state management - use API data
  // Remove step management - use API status

  const handleBoardApprovalClick = async () => {
    if (!data?.additionalShare) return;
    await submitForBoardApproval(data.additionalShare);
    await refetch(); // Refresh data after API call
  };

  const handleStockholderApprovalClick = async () => {
    await submitForStockholderApproval();
    await refetch(); // Refresh data after API call
  };

  const handleAbandon = async () => {
    await abandonProcess();
    onClose();
  };

  // Render based on API data instead of local state
};
```

### `IncreaseSharesInitialState.tsx` Updates

```typescript
interface IncreaseSharesInitialStateProps {
  data: AuthorizedSharesResponse['data'] | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  isFileUploadLoading: boolean;
  onBoardApprovalClick: () => void;
  onStockholderApprovalClick: () => void;
  onFileAmendmentClick: () => void;
  onAbandon: () => void;
  onClose: () => void;
}
```

## API Integration Flow

### Initial Load
1. User clicks "Start" button
2. Dialog opens and calls `GET /api/p2/companies/{companyId}/authorizedshares/currentpending` via `src/integrations/legal-concierge/client.ts`
3. Display data based on response:
   - If `additionalShare > 0`: Show existing pending increase (read-only)
   - If `additionalShare === 0`: Show editable form for new increase

### Board Approval Flow
1. User enters additional shares (only if `additionalShare === 0`)
2. User clicks "Submit for Board Approval" (button shows "Submitting..." during API call)
3. Call `POST /api/p2/companies/{companyId}/authorizedshares` via `src/integrations/legal-concierge/client.ts`
4. Show success toast: `toast.success("Board Approval Submitted Successfully")`
5. Call `GET` API again to refresh status
6. Update UI based on new `isBoardApproved` status

### Stockholder Approval Flow
1. User clicks "Submit for Stockholder Approval" (button shows "Submitting..." during API call)
2. Call `PUT /api/p2/companies/{companyId}/authorizedshares/submitforstockholderapproval` via `src/integrations/legal-concierge/client.ts`
3. Show success toast: `toast.success("Stockholder Approval Submitted Successfully")`
4. Call `GET` API again to refresh status
5. Update UI based on new `isStockHolderApproved` status

### Abandon Flow
1. User clicks "Abandon" button (button shows "Abandoning..." during API call)
2. Call `PUT /api/p2/companies/{companyId}/authorizedshares/abandoned` via `src/integrations/legal-concierge/client.ts`
3. Show success toast: `toast.success("Process Abandoned Successfully")`
4. Close dialog

### File Upload Flow
1. User clicks "File Certificate of Amendment" button (button shows "Uploading..." during process)
2. File picker opens (accepts only PDF and DOC files)
3. **Step 1**: Call `POST /api/p2/companies/{companyId}/documents/authorized-share/amendment-presigned-url` with file details
4. **Step 2**: Upload file directly to S3 using the presigned URL (bypasses backend)
5. **Step 3**: Call `POST /api/p2/companies/{companyId}/authorizedshares/submitfileamendment` with S3 key
6. Show success toast: `toast.success("Certificate Uploaded Successfully")`
7. Call `GET` API again to refresh status
8. Update UI based on new `isFileOfCertificateOfAmendmentUploaded` status

## Status Display Requirements

### Approval Status Display
- **Board Approved**: Show "Yes" if `isBoardApproved === "yes"`, "Pending" if `isBoardApproved === "pending"`, "No" otherwise
- **Stockholders Approved**: Show "Yes" if `isStockHolderApproved === "yes"`, "Pending" if `isStockHolderApproved === "pending"`, "No" otherwise

### Toast Notifications (Using Sonner)
- **Abandon Success**: `toast.success("Process Abandoned Successfully")`
- **Board Approval Success**: `toast.success("Board Approval Submitted Successfully")`
- **Stockholder Approval Success**: `toast.success("Stockholder Approval Submitted Successfully")`
- **File Upload Success**: `toast.success("Certificate Uploaded Successfully")`
- **Error Messages**: `toast.error(error.message)` for all failed operations

## Error Handling

1. **API Errors**: Display toast notifications for failed API calls
2. **Loading States**: Show specific loading states for each button during API calls
   - Board Approval: Shows "Submitting..." when `isBoardApprovalLoading` is true
   - Stockholder Approval: Shows "Submitting..." when `isStockholderApprovalLoading` is true
   - Abandon: Shows "Abandoning..." when `isAbandonLoading` is true
   - File Certificate: Shows "Uploading..." when `isFileUploadLoading` is true
3. **Network Issues**: Implement retry logic for failed requests
4. **Validation**: Handle API validation errors gracefully

## Testing Strategy

1. **Unit Tests**: Test service functions and hooks
2. **Integration Tests**: Test API integration flows
3. **Component Tests**: Test dialog behavior with different API responses
4. **Error Scenarios**: Test error handling and edge cases

## Migration Strategy

1. **Backward Compatibility**: Keep existing mock data as fallback
2. **Feature Flag**: Add feature flag to switch between mock and API data
3. **Gradual Rollout**: Deploy API integration incrementally
4. **Monitoring**: Add logging for API calls and user interactions

## File Upload Technical Implementation

### File Type Validation
- **Accepted Formats**: PDF (`application/pdf`) and DOC (`application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`)
- **File Size Limit**: Maximum 10MB per file
- **Validation**: Client-side validation before upload attempt

### S3 Upload Process
```typescript
// Step 1: Get presigned URL
const presignedResponse = await apiClient.post('/amendment-presigned-url', {
  key: fileName,
  contentType: file.type
});

// Step 2: Upload file directly to S3
const uploadResponse = await fetch(presignedResponse.data.uploadUrl, {
  method: 'PUT',
  body: file, // Binary file data
  headers: {
    'Content-Type': file.type
  }
});

// Step 3: Notify backend of successful upload
await apiClient.post('/submitfileamendment', {
  s3Key: presignedResponse.data.key
});
```

### Error Handling for File Upload
- **Invalid File Type**: Show error toast and allow retry
- **File Too Large**: Show error toast with size limit
- **Upload Failure**: Show error toast and allow retry
- **Network Issues**: Implement retry logic with exponential backoff

## Success Criteria

- [ ] API integration works seamlessly with existing UI
- [ ] Real-time status updates work correctly
- [ ] Error handling is robust and user-friendly
- [ ] Performance is maintained with API calls
- [ ] All existing functionality is preserved
- [ ] Code follows existing patterns and standards
- [ ] File upload works with S3 presigned URLs
- [ ] File type validation works correctly
- [ ] Upload progress and error states are handled properly