## SAFEs Revamp – Feature Requirements

Author: Frontend | Owners: Product, Legal, Frontend, Backend | Status: Draft

### Goals
- Streamlined four-action flow: Authorize Round, Issue Individual SAFEs, Increase Authorized Round, Record
- Add Qualified Financing Threshold (QFT) input
- Robust tables with clear actions and statuses
- Single Action button with dropdown (replaces four-button grid)
- Display Aggregate SAFE Rounds table

---

## Component Architecture

Entry point:
- `components/financing/safe/SafesFlow.tsx` - Action button with dropdown, dialogs, and `RoundsTable`

Deprecated:
- `EntryModeSelector.tsx` - replaced by Action button dropdown

- `dialogs/AuthorizeRoundDialog.tsx`
  - **Fields**: Total Authorized Amount*, Interest Rate*, Interest Type*, Maturity Date*, Valuation Cap*, Discount*, MFN*, QFT*
  - **Validation**: All required, real-time validation
  - **Actions**: Cancel, Submit for Board Approval

- `dialogs/IssueIndividualSafesDialog.tsx`
  - **Layout**: 600px dialog with round selection + SAFE form
  - **Table**: Active board-approved rounds only
  - **Form**: Principal amount, investor details, investment date, document uploads
  - **Actions**: Cancel, Issue SAFE

- `dialogs/IncreaseAuthorizedRoundDialog.tsx`
  - **Layout**: 900px dialog with rounds table + increase form
  - **Form**: USD input (>0), shows current + new total
  - **API**: POST `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedsafe`

- `dialogs/RecordAuthorizedRoundDialog.tsx`
  - **Disclaimer**: "Use this workflow to record any round authorized outside of the platform."
  - **Fields**: Total Authorized Amount, Valuation Cap, Discount, MFN, Board Approved Date, File Upload
  - **Mandatory Fields**: Total Authorized Amount*, Valuation Cap*, Discount*, MFN*, Board Approved Date*, File Upload*
  - **Validation**: All required, real-time validation, form blocked until valid, file upload required
  - **File Upload**: SAFE Agreement (PDF/DOCX), max 5MB, drag & drop interface
  - **States**: Loading (spinner), Success (auto-close + toast), Error (inline messages)
  - **Actions**: Cancel, Record SAFE → POST API → loading → success: close + toast + refresh
  - **Creates Round**: `source = external`, `boardApproved = true`

### File Upload for Record Authorized Round
- **File Types**: PDF/DOC, max 5MB
- **API**: GET `/api/p2/companies/{companyId}/documents/authorized-safe-round/presigned-url`
- **Fields**: All mandatory with real-time validation
- **Upload Flow**: S3 presigned URL → direct upload → S3 key in final payload

- `tables/RoundsTable.tsx`
  - Columns: Date Authorized, Maturity Date, Authorized Amount, Outstanding Principal, Interest Rate, Valuation Cap, Discount, MFN, QFT, Board Approved
  - Filtering: "Show All", "Approved Only", "Not Approved"

### Aggregate SAFE Rounds API
- **Endpoint**: GET `/api/p2/companies/{companyId}/preseedfinance/authorizedsafelist`
- **Response**: Array of SafeRoundResponse objects
- **Filtering**: Client-side by `isActive` status

## Service Contracts

### Core Interfaces
```ts
interface SafeRoundResponse {
  id: string;
  companyId: string;
  qualifiedFinancingThreshold: number;
  totalAuthorizedAmountOfRound: number;
  interestRate: number;
  interestType: 'simple-interest' | 'compound-interest';
  maturityDate: string;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null;
  isActive: boolean;
}
```

### Key Service Interfaces
```ts
interface AuthorizeSafeRequest {
  qualifiedFinancingThreshold: number;
  totalAuthorizedAmountOfRound: number;
  interestRate: number;
  interestType: "simple-interest" | "compound-interest";
  maturityDate: string;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

interface IncreaseAuthorizedSafeRequest {
  authorizedSafeId: string;
  increaseAmount: number;
}
```


### IssueIndividualSafesService
```ts
interface IssueIndividualSafeRequest {
  authorizedSafeId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  includeProRataSideLetter: boolean;
  prorataS3Key?: string;
  includeSideLetter?: boolean;
  optionalS3Key?: string;
  s3Key?: string;
}

interface DocumentUploadRequest {
  key: string;
  contentType: string;
}
```


---

## Hook Contracts

### Primary Hooks
```ts
interface UseAuthorizedSafeListReturn {
  safes: SafeRoundResponse[];
  isLoadingSafes: boolean;
  error: string | null;
  refetchSafes: () => void;
  filterType: 'all' | 'approved' | 'notApproved';
  setFilterType: (filter: 'all' | 'approved' | 'notApproved') => void;
  filteredSafes: SafeRoundResponse[];
}

interface UseAuthorizeSafeReturn {
  createAuthorizedSafe: (payload: AuthorizeSafeRequest) => Promise<void>;
  isCreatingSafe: boolean;
  error: string | null;
}

interface UseIssueIndividualSafesReturn {
  issueIndividualSafe: (payload: IssueIndividualSafeRequest) => Promise<IssueIndividualSafeResponse>;
  isIssuingSafe: boolean;
  error: string | null;
}
```


---

## Data Model Extensions

```ts
type RoundSource = "platform" | "external";
type SafeIssuanceStatus = "saved" | "pending_circulation" | "circulating" | "signed" | "void";

interface SafeRound {
  id: string;
  dateAuthorized: Date | null;
  maturityDate: Date | null;
  interestRate: number;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  authorizedAmount: number;
  qualifiedFinancingThreshold: number;
  boardApproved: boolean;
  source: RoundSource;
}
```

---

## Validation & Business Rules

- Currency inputs: numbers with $ format
- Interest Rate, Discount: 0–100
- QFT: required, ≥ 0
- Increase amount: > 0, no pending increases
- File uploads: PDF/DOC/DOCX, max 5MB
- Full keyboard accessibility and proper ARIA labels

---

## API Requirements

### Key Endpoints
- **GET Authorized SAFEs**: `/api/p2/companies/{companyId}/preseedfinance/authorizedsafelist`
- **POST Authorize SAFE**: `/api/p2/companies/{companyId}/preseedfinance/authorizedsafe`
- **POST Increase SAFE**: `/api/p2/companies/{companyId}/preseedfinance/increaseauthorizedsafe`
- **POST Issue Individual**: `/api/p2/companies/{companyId}/preseedfinance/issueindividualsafe`
- **POST Record Authorized**: `/api/p2/companies/{companyId}/preseedfinance/recordauthorizedsafe`

### Document Upload APIs
- **Prorata Upload**: POST `/documents/issue-individual-safe-note/prorata-presigned-url`
- **Side Letter Upload**: POST `/documents/issue-individual-safe-note/side-letter-presigned-url`
- **Optional Document Upload**: POST `/documents/issue-individual-safe-note/optional-presigned-url`
- **Constraints**: PDF/DOC/DOCX, max 5MB, S3 presigned URL flow

---

## File Plan

### Key Components
- `src/components/financing/safe/SafesFlow.tsx` - Main entry point with Action button
- `src/components/financing/safe/ActionButton.tsx` - Dropdown action button
- `src/components/financing/safe/dialogs/` - All dialog components
- `src/components/financing/safe/tables/RoundsTable.tsx` - SAFE rounds table

### Deprecated
- `src/components/financing/SAFETab.tsx` - Replaced by SafesFlow

### Hooks & Services
- `src/hooks/financing/useAuthorizedSafeList.hooks.ts` - Fetch/manage SAFE data with filtering
- `src/hooks/financing/useAuthorizeSafe.hooks.ts` - Create SAFE rounds
- `src/hooks/financing/useIssueIndividualSafes.hooks.ts` - Issue individual SAFEs
- `src/services/financing/authorizedSafeList.service.ts` - API service layer

### Types & Integration
- `src/types/financing.ts` - Core SAFE interfaces and types
- API Client methods for all SAFE operations
- Integration with existing `APIClient` from legal-concierge

### UI/UX Standards
- Real-time form validation with inline errors
- Loading states during API calls
- Auto-close on success with toast notifications
- Full keyboard accessibility
- Responsive design for all screen sizes

---

## Edge Cases
- External SAFEs can be used for individual issuance without board approval
- Disallow multiple pending increases per SAFE
- Show status badges: Approved, Pending, External, Closed
- Disable actions for closed SAFEs
- Validate principal amount against authorized total

---

## Acceptance Criteria

### General Flow
- Four-button toolbar is rendered; each button opens the correct dialog/panel.
- SAFEs table shows columns per mapping (no Round Name, no Pending Increase) and is powered by the provided API; actions work.

### Core Functionality Completed
- ✅ **Authorize SAFE Dialog**: Complete form with validation and API integration
- ✅ **API Integration**: All endpoints implemented with proper error handling
- ✅ **Form Validation**: Real-time validation with formatted inputs and loading states

### Increase Authorized SAFE Dialog
- **Layout**: 900px max width dialog with SAFE selection table and increase form
- **Table**: Shows approved SAFEs only with radio selection and horizontal scrolling
- **Form**: Increase amount input with current/new total display and validation
- **API**: POST endpoint with authorizedSafeId and increaseAmount payload

### Issue Individual SAFEs Dialog
- **Layout**: 600px max width with SAFE selection table and SAFE form
- **Table**: Approved SAFEs only with radio selection
- **Form**: Principal amount, investor details, investment date
- **NEW**: Prorata sideletter checkbox with document upload (PDF/DOC/DOCX, max 5MB)
- **NEW**: Side letter checkbox with document upload (PDF/DOC/DOCX, max 5MB)
- **NEW**: Optional document upload section
- **NEW**: Principal Amount validation to ensure amount does not exceed available authorized amount
- **API**: S3 upload flow with pre-signed URL integration
- **Document Upload**: Support for both prorata sideletter and side letter documents (PDF/DOC/DOCX, max 5MB)

### Record Authorized SAFE Dialog
- **Purpose**: Record SAFEs authorized outside the platform
- **Fields**: Same as Authorize SAFE plus document upload
- **Source**: Creates SAFE with `source = external` and `boardApproved = true`

## Aggregate SAFE Rounds Filtering

### **Implementation**
- **Filter Options**: "Approved Only" (default), "Not Approved", "Show All"
- **UI**: Radio buttons above table with visual status badges
- **Logic**: Client-side filtering of API response data

## Implementation Summary

### Required Components
- **API Layer**: Service classes with React Query hooks for data fetching and mutations
- **UI Components**: Increase and Issue Individual SAFEs dialogs with table selection
- **Form Validation**: Real-time validation with error handling and loading states
- **File Upload**: S3 integration for prorata sideletter documents
- **Table Filtering**: Client-side filtering for approved/unapproved SAFEs

### Key Features
- SAFE selection tables with radio buttons and horizontal scrolling
- Currency inputs with formatting and validation
- Document upload with drag & drop interface
- Responsive design maintaining accessibility standards

---

## **Feature: View Individual SAFEs**

### Overview
- Eye icon in Aggregate SAFE Rounds table
- Modal shows individual SAFEs for selected round
- API: `GET /api/p2/companies/{companyId}/preseedfinance/issueindividualsafelist/{authorizedSafeId}`

### Implementation
- `ViewIndividualSafesDialog` component
- `useIndividualSafesList` hook with React Query
- Responsive table with loading/error states

---


---

## **NEW FEATURE: Issue SAFE Form API Integration with Prorata and Side Letter Support**

### **Overview**
Implement API integration for the Issue SAFE form within the RecordPopupDialog, enabling users to issue individual SAFEs with document upload capabilities including prorata side letter and side letter support.

### **API Endpoints Required**

#### **1. Document Upload Presigned URL APIs**
- **Main Document**: `POST /api/p2/companies/{companyId}/documents/record-issue-individual-safe-note/optional-presigned-url`
- **Prorata Side Letter**: `POST /api/p2/companies/{companyId}/documents/issue-individual-safe-note/prorata-presigned-url`
- **Side Letter**: `POST /api/p2/companies/{companyId}/documents/issue-individual-safe-note/side-letter-presigned-url`
- **Purpose**: Get presigned S3 URLs for uploading SAFE note documents
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### **Request Payload**
```json
{
  "key": "string",           // File name with extension (e.g., "new.doc")
  "contentType": "string"    // MIME type of the file
}
```

#### **Response Structure**
```json
{
  "message": "Success",
  "data": {
    "uploadUrl": "https://form-documents-dev.s3.us-east-1.amazonaws.com/...",
    "key": "companies/{companyId}/others/issue-individual-safe-note/{filename}"
  },
  "error": null,
  "validationErrors": null
}
```

#### **2. Record Issue Individual SAFE Note API**
- **Endpoint**: `POST /api/p2/companies/{companyId}/preseedfinance/recordissueindividualsafenote`
- **Purpose**: Record the issuance of an individual SAFE note with document references
- **Authentication**: Required (company context)
- **Content-Type**: `application/json`

#### **Request Payload**
```json
{
  "authorizedRoundId": "string",      // UUID of the selected authorized SAFE round
  "principalAmount": 0,               // Principal amount of the SAFE
  "name": "string",                   // Investor name
  "email": "string",                  // Investor email
  "dateOfInvestment": "2025-08-17",  // Investment date (ISO format)
  "approvedDate": "2025-08-17",      // Approval date (ISO format)
  "prorataS3Key": "string",          // S3 key for prorata sideletter (optional)
  "s3Key": "string",                 // S3 key for main SAFE note document (optional)
  "optionalS3Key": "string"          // S3 key for side letter (optional)
}
```

### **Implementation Requirements**

#### **File Upload Flow**
1. **File Selection**: User selects PDF/DOC files (max 5MB)
2. **Presigned URL Request**: Call presigned URL APIs concurrently for all selected documents
3. **S3 Upload**: Upload all files directly to S3 using returned URLs concurrently
4. **S3 Key Storage**: Store returned S3 keys for final API submission
5. **SAFE Issuance**: Include all S3 keys in final SAFE issuance payload

#### **File Validation Rules**
- **File Types**: PDF (.pdf), DOC (.doc), and DOCX (.docx) files only
- **File Size**: Maximum 5MB
- **Content Types**: `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- **Real-time Validation**: Immediate feedback on file selection
- **Dual Validation**: MIME type + file extension validation for cross-browser compatibility

#### **NEW: Prorata and Side Letter Checkboxes**
- **Include Prorata Side Letter**: Checkbox in SAFE Details form
- **Include Side Letter**: Checkbox in SAFE Details form
- **Conditional Display**: File upload sections appear only when checkboxes are checked
- **Required Indicators**: Visual * indicators when file upload is required
- **Validation**: Ensure files are selected if checkboxes are checked

#### **Form Integration**
- **Dynamic File Upload**: File upload section appears in Issue SAFE form
- **Required Field**: Document upload becomes required when issuing SAFE
- **File Preview**: Show selected file name, size, and remove functionality
- **Upload Progress**: Display upload status and progress indication
- **Error Handling**: Clear error messages for upload failures

#### **API Service Layer**
- **New Service Method**: `getIssueSafeNoteUploadUrl()` in document service
- **Enhanced SAFE Service**: Update `recordIssueIndividualSafeNote()` method
- **Error Handling**: Proper error handling for both upload and issuance APIs
- **Response Validation**: Validate API responses and handle errors gracefully

### **Technical Implementation Details**

#### **Service Contracts**
```typescript
interface IssueSafeNoteUploadService {
  getUploadUrl(companyId: string, payload: UploadUrlRequest): Promise<UploadUrlResponse>;
}

interface RecordIssueIndividualSafeNoteService {
  recordSafeNote(companyId: string, payload: RecordSafeNoteRequest): Promise<RecordSafeNoteResponse>;
}

interface UploadUrlRequest {
  key: string;                    // filename with extension
  contentType: string;            // MIME type of file
}

interface RecordSafeNoteRequest {
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  approvedDate: string;
  prorataS3Key?: string;         // optional
  s3Key?: string;                // optional
  optionalS3Key?: string;        // optional
}
```

#### **Hook Implementation**
```typescript
interface UseIssueSafeNoteUploadReturn {
  getUploadUrl: (payload: UploadUrlRequest) => Promise<UploadUrlResponse>;
  isGettingUrl: boolean;
  error: string | null;
}

interface UseRecordIssueIndividualSafeNoteReturn {
  recordSafeNote: (payload: RecordSafeNoteRequest) => Promise<RecordSafeNoteResponse>;
  isRecording: boolean;
  error: string | null;
}
```

#### **Component Updates Required**
- **RecordPopupDialog.tsx**: Integrate new API calls for Issue SAFE form
- **File Upload Section**: Implement S3 upload flow with progress indication
- **Form Submission**: Update form submission to include document upload
- **Error Handling**: Add proper error handling for upload and issuance failures

### **Business Logic**
- **Document Storage**: Documents stored in S3 for internal record keeping
- **SAFE Issuance**: Individual SAFE recorded with document references
- **Validation**: All form fields required, document upload mandatory
- **Status Tracking**: SAFE issuance status tracked and displayed
- **Audit Trail**: Complete audit trail of issued SAFEs with documents

---

## **UPDATED UI DESIGN REQUIREMENTS**

### **Action Button Functionality**
- **Dropdown Menu**: Clicking "Action" button opens dropdown with 4 options:
  1. **Authorize Round** → Opens existing AuthorizeSafeForm dialog
  2. **Issue Individual SAFEs** → Opens existing IssueIndividualSafesDialog
  3. **Increase Authorized Round** → Opens existing IncreaseAuthorizedSafeDialog
  4. **Record** → Opens new RecordPopupDialog with two radio options

### **Record Popup Dialog Design**
- **Two Radio Options**:
  - **Issued SAFE**: Shows description and Continue button
  - **Authorized Round**: Shows description and embedded form immediately below
- **Embedded Form**: When "Authorized Round" is selected, RecordAuthorizedSafeForm appears directly below radio buttons
- **No Continue Button**: Form appears immediately without requiring additional clicks
- **Continue Button**: Only shown for "Issued SAFE" option

### **Issued SAFE with Upload Dialog**
- **Enhanced Functionality**: Same as existing Issue Individual SAFEs but with document upload
- **Document Upload**: PDF/DOCX files, max 5MB, drag & drop interface
- **Internal Use Only**: Documents are for internal record keeping, not sent to investors
- **Complete Form**: Principal Amount, Investor Name, Email, Date of Investment with validation

### **Visual Design Requirements**
- **Action Button**: Default button style with ChevronDown icon, positioned top-right
- **Dropdown Styling**: 56px width, right-aligned, proper hover states
- **Icons**: Plus, FileText, TrendingUp, Upload for respective actions
- **Responsive Layout**: Maintains proper spacing and alignment on all screen sizes

### **User Experience Flow**
1. User sees single "Action" button in top-right corner
2. Clicking "Action" reveals dropdown with 4 action options
3. **Record Action**: Opens popup with radio selection
4. **Authorized Round Selection**: Form appears immediately below radio buttons
5. **Issued SAFE Selection**: Shows Continue button to open enhanced dialog
6. **Enhanced Dialog**: Includes document upload capability for internal records

### **Component Architecture Updates**
- **New Components**:
  - `ActionButton.tsx` - Single action button with dropdown
  - `RecordPopupDialog.tsx` - Record popup with radio options and embedded form
  - `IssuedSafeWithUploadDialog.tsx` - Enhanced SAFE issuance with document upload
- **Modified Components**:
  - `SafesFlow.tsx` - Updated layout with Action button in header, removed button grid
- **Unchanged Components**:
  - All existing dialogs, forms, and services maintain current functionality
  - All API calls and business logic preserved

### **Implementation Benefits**
- **Cleaner Interface**: Single Action button instead of cluttered button grid
- **Better Organization**: Logical grouping of actions in dropdown menu
- **Faster Workflow**: Form appears immediately for Authorized Round (no extra clicks)
- **Enhanced Features**: Document upload capability for Issued SAFEs
- **Consistent Layout**: Action button always visible in top right corner
- **Maintained Functionality**: All existing business logic and API integration preserved

### **File Changes Summary**
- **Created**: `ActionButton.tsx`, `RecordPopupDialog.tsx`, `IssuedSafeWithUploadDialog.tsx`
- **Modified**: `SafesFlow.tsx` (updated layout and imports)
- **Unchanged**: All existing forms, services, hooks, and API integration

### **Testing Requirements**
- Action button positioning and styling
- Dropdown menu functionality and all 4 action options
- Record popup radio selection and embedded form display
- Issued SAFE with upload functionality
- Responsive design and accessibility features
- Form appears immediately without Continue button for Authorized Round

---

## Implementation Status

### Completed Components

#### **1. New Action Button Component** ✅
- **File**: `src/components/financing/safe/ActionButton.tsx`
- **Features**: Single "Action" button with dropdown menu containing 4 action options
- **Icons**: Plus (Authorize), FileText (Issue), TrendingUp (Increase), Upload (Record)
- **Positioning**: Top-right corner of SAFEs card with proper styling

#### **2. Updated SafesFlow Layout** ✅
- **File**: `src/components/financing/safe/SafesFlow.tsx`
- **Changes**: 
  - Removed four-button grid layout
  - Added Action button to CardHeader with `flex flex-row items-center justify-between`
  - Moved SAFEs table to bottom of card content (no left section)
  - Updated imports to use new components
- **Result**: Clean, organized interface with Action button prominently displayed

#### **3. New Record Popup Dialog** ✅
- **File**: `src/components/financing/safe/dialogs/RecordPopupDialog.tsx`
- **Features**:
  - Radio button selection between "Issued SAFE" and "Authorized Round"
  - Descriptive text explaining each option
  - **Embedded Form**: RecordAuthorizedSafeForm appears immediately when "Authorized Round" is selected
  - **No Continue Button**: Form shows directly below radio buttons
  - Continue button only shown for "Issued SAFE" option

#### **4. New Issued SAFE with Upload Dialog** ✅
- **File**: `src/components/financing/safe/dialogs/IssuedSafeWithUploadDialog.tsx`
- **Features**:
  - Round selection table (approved SAFEs only)
  - Complete form with validation (Principal Amount, Name, Email, Date)
  - **Document Upload**: PDF/DOCX files, max 5MB, drag & drop interface
  - **Internal Use**: Documents for internal record keeping only
  - Professional layout with proper responsive design

### **UI Layout Changes**

#### **Before (Old Design)**
```
┌─────────────────────────────────────────┐
│ SAFEs                                   │
│                                         │
│ [Authorize] [Issue] [Increase] [Record] │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │     Aggregate SAFE Rounds Table    │ │
│ │                                     │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### **After (New Design)**
```
┌─────────────────────────────────────────┐
│ SAFEs                               [Action] │
│                                         │
│                                         │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │     Aggregate SAFE Rounds Table    │ │
│ │         (No Changes)               │ │
│ │                                     │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### **Action Button Dropdown Options**
1. **Authorize Round** → Opens existing AuthorizeSafeForm dialog
2. **Issue Individual SAFEs** → Opens existing IssueIndividualSafesDialog
3. **Increase Authorized Round** → Opens existing IncreaseAuthorizedSafeDialog
4. **Record** → Opens new RecordPopupDialog with two options:
   - **Issued SAFE**: Opens IssuedSafeWithUploadDialog (with document upload)
   - **Authorized Round**: Shows embedded RecordAuthorizedSafeForm immediately

### **New User Experience Flow**

#### **Record Action Flow**
1. User clicks "Record" from Action button dropdown
2. Record popup appears with two radio options:
   - **Issued SAFE** (with Continue button)
   - **Authorized Round** (form appears immediately)
3. **If "Issued SAFE" selected**:
   - Shows description and Cancel/Continue buttons
   - Clicking Continue opens IssuedSafeWithUploadDialog
4. **If "Authorized Round" selected**:
   - Shows description
   - **Form appears immediately below** (no Continue button needed)
   - User can fill out and submit form directly in same dialog

#### **Form Embedding Behavior**
- **Dynamic Form Display**: Form appears/disappears based on radio selection
- **No Extra Clicks**: Form shows immediately when "Authorized Round" is selected
- **Same Form Functionality**: All existing validation, fields, and API integration preserved
- **Seamless Experience**: Everything happens in one dialog

### **Technical Implementation Details**

#### **Component Dependencies**
- Uses existing `DropdownMenu` from shadcn/ui
- Integrates with existing dialog state management
- Maintains all existing API calls and business logic
- No changes to data flow or service layer

#### **Responsive Design**
- Action button positioned correctly on all screen sizes
- Dropdown menu aligns properly with button
- Table maintains existing responsive behavior
- All dialogs maintain existing responsive design
- RecordPopupDialog expanded to accommodate embedded form

#### **Accessibility**
- Proper ARIA labels for dropdown items
- Keyboard navigation support
- Screen reader compatibility
- Focus management maintained

### **Files Modified/Created (Latest Changes)**

#### **New Files Created**
- `src/components/financing/safe/ActionButton.tsx`
- `src/components/financing/safe/dialogs/RecordPopupDialog.tsx`
- `src/components/financing/safe/dialogs/IssuedSafeWithUploadDialog.tsx`

#### **Files Modified**
- `src/components/financing/safe/SafesFlow.tsx`

#### **Files Unchanged (All Logic Preserved)**
- All existing dialog components
- All existing forms and services
- All existing hooks and API calls
- All existing table functionality

### **Key Benefits of New Design**
#### **Improved User Experience**
- **Cleaner Interface**: Single Action button instead of cluttered button grid
- **Better Organization**: Logical grouping of actions in dropdown
- **Faster Workflow**: Form appears immediately for Authorized Round (no extra clicks)
- **Consistent Layout**: Action button always visible in top right

#### **Maintained Functionality**
- **Zero Logic Changes**: All existing API calls, validation, and business logic preserved
- **Same Forms**: All existing forms work exactly as before
- **Same Validation**: All existing validation rules and error handling maintained
- **Same API Integration**: All existing endpoints and data flow unchanged

#### **Enhanced Features**
- **Document Upload**: New capability for Issued SAFEs (internal record keeping)
- **Embedded Forms**: Better user experience with immediate form display
- **Professional Look**: More polished and organized interface

### Implementation Status
- Action button with dropdown implemented
- Record popup with dual options completed
- Document upload functionality integrated
- All existing business logic preserved

---

## Prorata Sideletter Feature
- "Include prorata sideletter" checkbox in Issue Individual SAFEs dialog
- Document upload section with file validation (PDF/DOC, max 5MB)
- S3 upload integration with presigned URLs
- Enhanced `IssueIndividualSafeRequest` with prorata fields

### Technical Implementation
- S3 presigned URL flow for secure document uploads
- Enhanced request interfaces with document support
- Real-time validation and comprehensive error handling

---

## **Feature: Document Upload Support**

### Overview
- Multiple document types: prorata sideletters, side letters, optional documents
- Checkbox-driven UI with conditional file uploads
- PDF/DOC/DOCX files, max 5MB each
- Independent upload flows with S3 integration

### Implementation
- Enhanced `IssueIndividualSafeRequest` interface
- S3 presigned URL upload flow
- Real-time validation and error handling
- Concurrent uploads for better performance

---

## **UI Enhancements Summary**

### Action Button Pattern
- Single "Action" button with dropdown menu
- Cleaner interface with better space utilization
- All actions accessible from one location

### Record Dialog Improvements  
- Dual radio options: "Issued SAFE" and "Authorized Round"
- Immediate form display for Authorized Round
- Document upload capability for all record types

### Multi-Document Support
- Enhanced service methods for concurrent uploads
- Updated components with multi-document capability
- Improved error handling and user feedback

---

## Key Features Summary

### Core Functionality
- **Action Button Pattern**: Single dropdown button replacing four-button grid
- **Four Main Actions**: Authorize Round, Issue Individual SAFEs, Increase Authorized Round, Record
- **Document Upload**: Support for prorata sideletters, side letters, and optional documents
- **File Validation**: PDF/DOC/DOCX files, max 5MB, real-time validation
- **S3 Integration**: Secure upload flow with presigned URLs

### Data Management
- **Filtering System**: Client-side filtering by approval status
- **Real-time Validation**: All forms with comprehensive error handling
- **Loading States**: Professional UI feedback during API operations
- **Toast Notifications**: Success and error feedback throughout workflows

### Accessibility & Design
- **Responsive Design**: Works across all screen sizes
- **Keyboard Navigation**: Full accessibility support with ARIA labels
- **Modern Interface**: Clean, professional design following platform standards
- **Error Handling**: Comprehensive validation with user-friendly messages

---

## Implementation Priorities

### Phase 1 - Core Features ✅
- Action button with dropdown functionality
- All four main dialog implementations
- Basic file upload and validation
- API integration with error handling

### Phase 2 - Enhanced Features ✅  
- Multi-document upload support
- Embedded form display for better UX
- Professional drag & drop interfaces
- Concurrent upload processing

### Phase 3 - Production Polish ✅
- Comprehensive testing and validation
- Performance optimization
- Accessibility compliance
- Documentation completion

---

## Final Requirements Summary

The SAFEs feature provides a complete, modern interface for managing SAFE rounds with:

- **Streamlined Workflow**: Single Action button with intuitive dropdown options
- **Complete API Integration**: All endpoints implemented with proper error handling
- **Enhanced Document Management**: Support for multiple document types with secure S3 upload
- **Professional User Experience**: Modern UI with loading states, validation, and accessibility
- **Consistent Architecture**: Follows established patterns from convertible notes implementation

All requirements have been successfully implemented and are production-ready.

---

## **NEW FEATURE: Principal Amount Validation for SAFEs**

### **Feature Overview**
Add real-time validation to the Principal Amount input field in the Issue Individual SAFEs dialog to ensure users cannot enter amounts that exceed the available authorized amount for the selected SAFE round.

### **Business Value**
- **Prevents Invalid Submissions**: Users cannot submit amounts that exceed available authorized capacity
- **Clear User Guidance**: Available amount display helps users understand their limits
- **Improved Data Integrity**: Ensures all SAFE issuances are within authorized bounds
- **Better User Experience**: Real-time feedback prevents form submission errors

### **Technical Requirements**

#### **Validation Logic**
- **Formula**: Principal Amount ≤ (Authorized Amount - Outstanding Principal)
- **Real-time Validation**: Validation occurs as user types in the input field
- **Available Amount Display**: Show calculated available amount below input field
- **Error Prevention**: Block form submission if validation fails

#### **UI/UX Requirements**
- **Available Amount Display**: "Available: $X,XXX of $Y,YYY authorized"
- **Error Messages**: "Amount cannot exceed available amount of $X,XXX"
- **Visual Feedback**: Red border on input field when validation fails
- **Helper Text**: Clear indication of available vs. total authorized amounts

#### **Form Integration**
- **Real-time Validation**: Validate on every input change
- **Form Submission Blocking**: Prevent submission until validation passes
- **Error State Management**: Clear errors when user corrects input
- **SAFE Selection Reset**: Clear validation when switching SAFE rounds

### **API Response Updates**

#### **Individual SAFEs API Response**
```json
{
  "message": "Success",
  "data": [
    {
      "id": "6e4d8338-a475-4dc4-9535-52a0fc434b67",
      "companyId": "4dc692cc-5086-4433-8339-6e7b9c0ce572",
      "authorizedRoundId": "16ac6fae-47b5-43f2-b48b-7bb90e063077",
      "interestType": null,
      "principalAmount": 25000,
      "accruedInterest": 0,           // NEW: Displayed in Accrued Interest column
      "interestRate": null,
      "name": "Sujan Safe",
      "email": "<EMAIL>",
      "dateOfInvestment": "2025-09-20",
      "approvedDate": null,
      "isActive": false
    }
  ],
  "error": null,
  "validationErrors": null
}
```

### **Implementation Details**

#### **State Management**
```typescript
const [principalAmountError, setPrincipalAmountError] = useState<string>("");

const getAvailableAmount = (safe: SafeRoundResponse | undefined): number => {
  if (!safe) return 0;
  return safe.totalAuthorizedAmountOfRound - safe.outstandingPrincipal;
};

const validatePrincipalAmount = (amount: number, safe: SafeRoundResponse | undefined): string => {
  if (!safe) return "";
  
  const availableAmount = getAvailableAmount(safe);
  if (amount > availableAmount) {
    return `Amount cannot exceed available amount of $${availableAmount.toLocaleString()}`;
  }
  return "";
};
```

#### **UI Implementation**
```typescript
<div className="space-y-3">
  <Label htmlFor="principalAmount" className="text-sm font-medium">
    Principal Amount ($) <span className="text-red-500">*</span>
  </Label>
  <Input
    id="principalAmount"
    type="number"
    placeholder="0.00"
    value={formData.principalAmount || ""}
    onChange={(e) => handleInputChange("principalAmount", parseFloat(e.target.value) || 0)}
    min="0"
    step="0.01"
    className={`h-11 ${principalAmountError ? "border-red-500" : ""}`}
  />
  {selectedSafe && (
    <p className="text-sm text-gray-600">
      Available: ${getAvailableAmount(selectedSafe).toLocaleString()} of ${selectedSafe.totalAuthorizedAmountOfRound.toLocaleString()} authorized
    </p>
  )}
  {principalAmountError && (
    <p className="text-sm text-red-600">{principalAmountError}</p>
  )}
</div>
```

### **User Experience Flow**
1. **User selects SAFE**: Available amount is calculated and displayed
2. **User enters amount**: Real-time validation occurs on every keystroke
3. **Amount exceeds available**: Red border appears, error message shows
4. **User corrects amount**: Error clears, validation passes
5. **Form submission**: Only allowed when validation passes

### **Error Handling**
- **Validation Errors**: Clear, specific error messages
- **Edge Cases**: Handle null/undefined SAFE data gracefully
- **Form Reset**: Clear validation errors when switching SAFE rounds
- **User Feedback**: Immediate visual and textual feedback

### **Testing Requirements**
- ✅ Available amount calculation is accurate for all SAFE rounds
- ✅ Real-time validation works on every input change
- ✅ Error messages are clear and helpful
- ✅ Visual feedback (red border) appears on validation errors
- ✅ Form submission is blocked when validation fails
- ✅ Validation errors clear when user corrects input
- ✅ Available amount updates when switching SAFE rounds
- ✅ Edge cases handled (null/undefined data)

### **Implementation Status**
The Principal Amount validation feature for SAFEs is **complete and production-ready**. The implementation provides:
- **Real-time Validation**: Immediate feedback as users type
- **Clear User Guidance**: Available amount display helps users understand limits
- **Error Prevention**: Form submission blocked until validation passes
- **Professional UX**: Visual feedback and clear error messages
- **Robust Logic**: Handles all edge cases and data scenarios

### **Production Readiness**
Ready for production use with comprehensive validation, clear user feedback, and robust error handling.
