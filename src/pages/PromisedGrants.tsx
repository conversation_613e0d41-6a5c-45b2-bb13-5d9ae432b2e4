import React, { useState } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  InfoIcon,
  ClipboardCheckIcon,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import Dialog409Valuation from "@/components/service-providers/dialog/Dialog409Valuation";
import EquityIssueDialog from "@/components/service-providers/EquityIssueDialog";
import ModifyGrantsDialog from "@/components/service-providers/dialog/ModifyGrantsDialog";
import IssueGrants409AModal from "@/components/service-providers/dialog/IssueGrants409AModal";
import PromisedGrantsTable from "@/components/service-providers/PromisedGrantsTable";
import CustomVestingDialog from "@/components/service-providers/dialog/CustomVestingDialog";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { PromisedGrant } from "@/services/service-providers/promisedGrants.service";
import {
  useStockOptionInformation,
  usePromisedGrantsManager,
  useActive409AValuation,
  useRowSelection,
  useGrantEditing,
  useGrantDeletion,
  use409ADialog,
  useBoardApproval,
  useEquityIssueDialog,
  useModifyGrantsDialog,
  useCustomVestingDialog,
  useGrantIssuance,
  useIssuePromisedGrants,
} from "@/hooks/service-providers/usePromisedGrants.hooks";
import { useQueryClient } from "@tanstack/react-query";

const grantTypeOptions = [
  {
    value: "option",
    label: "Option (Default to Non-Statutory Option Grant)",
  },
  {
    value: "restricted-stock-grant-inside-of-stock-option-plan",
    label:
      "Restricted Stock Grant (These shares will be issued from the Stock Option Plan)",
  },
  {
    value: "restricted-stock-grant-outside-of-stock-option-plan",
    label:
      "Restricted Stock Grant (These shares will be issued from outside the Stock Option Plan)",
  },
  { value: "none", label: "None" },
];

const vestingScheduleOptions = [
  {
    value: "standard-two-years-monthly-vesting",
    label: "Standard (2 years, monthly vesting)",
  },
  { value: "custom", label: "Custom" },
];

const PromisedGrants: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  // Utility functions for 409A validation
  const getAnniversaryDate = (appraisalDate: string | null) => {
    if (!appraisalDate) return "N/A";
    const date = new Date(appraisalDate);
    if (isNaN(date.getTime())) return "N/A";
    date.setFullYear(date.getFullYear() + 1);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isValuationExpired = (appraisalDate: string | null) => {
    if (!appraisalDate) return false;
    const anniversaryDate = new Date(appraisalDate);
    if (isNaN(anniversaryDate.getTime())) return false;
    anniversaryDate.setFullYear(anniversaryDate.getFullYear() + 1);
    return new Date() > anniversaryDate;
  };

  // Data hooks
  const { data: stockOptionInfo, isLoading: isLoadingStockInfo } =
    useStockOptionInformation(companyId || "");
  const { data: active409AValuation, isLoading: isLoading409A } =
    useActive409AValuation(companyId || "");
  const { promisedGrants, isLoadingGrants, pendingGrantsCount } =
    usePromisedGrantsManager(companyId || "");

  // Business logic hooks
  const {
    selectedGrants,
    selectedCount,
    handleSelectAll,
    handleSelectGrant,
    clearSelection,
  } = useRowSelection();

  const {
    editingGrantId,
    editedGrantData,
    localEdits,
    setLocalEdits,
    isUpdating,
    handleEditClick,
    handleCancelClick,
    handleSaveClick,
    handleFieldChange,
    prepareGrantPayload,
  } = useGrantEditing();

  const {
    deletingGrantId,
    isDeleting,
    handleDeleteClick,
    handleConfirmDelete,
    handleCancelDelete,
  } = useGrantDeletion();

  const {
    is409ADialogOpen,
    dialogMode,
    open409ADialog,
    close409ADialog,
  } = use409ADialog();

  const {
    isBoardApprovalDialogOpen,
    isSubmittingApproval,
    handleBoardApprovalClick,
    handleSubmitForBoardApproval,
    closeBoardApproval,
  } = useBoardApproval();

  const {
    selectedGrant,
    isIssueDialogOpen,
    handleIssueClick,
    handleIssueDialogClose,
  } = useEquityIssueDialog();

  const {
    isModifyDialogOpen,
    openModifyDialog,
    closeModifyDialog,
  } = useModifyGrantsDialog();

  // State for the new Issue Grants 409A Modal
  const [isIssue409AModalOpen, setIsIssue409AModalOpen] = useState(false);

  const {
    isCustomVestingDialogOpen,
    currentGrantId,
    currentGrantName,
    openCustomVestingDialog,
    closeCustomVestingDialog,
    saveCustomVestingData,
    getCustomVestingData,
  } = useCustomVestingDialog();

  const {
    isIssuing: isSaving,
    handleSaveAndClose,
  } = useGrantIssuance();

  const {
    isIssuing: isIssuingGrants,
    handleIssueGrants,
  } = useIssuePromisedGrants();

  // State for tracking save results
  const [saveResults, setSaveResults] = useState<Array<{ grantId: string; success: boolean; error?: any }>>([]);

  // State for tracking issue results
  const [issueResults, setIssueResults] = useState<Array<{ grantId: string; success: boolean; error?: any }>>([]);

  // State for review mode
  const [isReviewModeActive, setIsReviewModeActive] = useState(false);

  const handleIssueSelected = () => {
    setIsIssue409AModalOpen(true);
  };

  const handle409AOptionSelect = (option: 'use-current' | 'set-fmv' | 'update-409a') => {
    switch (option) {
      case 'use-current':
        open409ADialog('use-current-409a');
        break;
      case 'set-fmv':
        open409ADialog('set-fair-market-value');
        break;
      case 'update-409a':
        open409ADialog('update-409a');
        break;
    }
  };

  const handle409ANext = () => {
    close409ADialog();
    // Refetch latest grants data
    queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
    // Open ModifyGrantsDialog in view mode
    setIsReviewModeActive(true);
    openModifyDialog();
  };

  const handleSetFMVNext = () => {
    close409ADialog();
    // Refetch latest grants data
    queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
    // Open ModifyGrantsDialog in view mode
    setIsReviewModeActive(true);
    openModifyDialog();
  };

  const handleUpdate409ANext = () => {
    close409ADialog();
    // Refetch latest grants data
    queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
    // Open ModifyGrantsDialog in view mode
    setIsReviewModeActive(true);
    openModifyDialog();
  };

  const handleIssueGrantsInDialog = async (grants: PromisedGrant[]) => {
    if (!companyId) return { success: false, results: [] };
    
    const result = await handleIssueGrants(grants, companyId);
    setIssueResults(result.results || []);
    
    if (result.success) {
      // Clear review mode and close modal
      setIsReviewModeActive(false);
      clearSelection();
      closeModifyDialog();
      setIssueResults([]);
    }
    
    return result;
  };

  const handleIssueWithCurrent409A = () => {
    open409ADialog('use-current-409a');
  };

  const handleIssueWithSetValue = () => {
    open409ADialog('set-fair-market-value');
  };

  const handleIssueWithUpdate409A = () => {
    open409ADialog('update-409a');
  };

  const handleModifySelected = () => {
    setIsReviewModeActive(false); // Reset to edit mode
    openModifyDialog();
  };

  const handleSaveAndCloseInDialog = async (grants: PromisedGrant[]) => {
    const result = await handleSaveAndClose(grants, localEdits);
    
    // Store the results for display in the table
    setSaveResults(result.results || []);
    
    if (result.success) {
      // Only close modal if ALL grants succeeded
      handleCancelClick();
      clearSelection();
      closeModifyDialog();
      setSaveResults([]); // Clear results after closing
    }
    // If not all succeeded, keep modal open to show individual statuses
    return result;
  };

  const handleBulkFieldChange = (grantId: string, field: keyof PromisedGrant, value: string | number) => {
    setLocalEdits(prev => ({
      ...prev,
      [grantId]: {
        ...prev[grantId],
        [field]: value
      }
    }));
  };

  const getStatusIcon = (grantId: string) => {
    const result = saveResults.find(r => r.grantId === grantId);
    if (!result) return null;
    
    if (result.success) {
      return <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">✓</div>;
    } else {
      return <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold">✗</div>;
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4 md:px-8">
        <div className="max-w-8xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800">
            Promised Grants
            </h1>
            <p className="text-gray-600 mt-1">
            View and manage promised equity grants for your team.
          </p>
          </div>

          <div className="p-8 rounded-lg bg-[#1B1D2A] border border-[#2E2F3A] text-[#F2F2F5] mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#9B51E0] to-[#E1467C] flex items-center justify-center">
                  <InfoIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-[#F2F2F5]">
                    Equity Overview
                  </h2>
                  <p className="text-sm text-[#F2F2F5] opacity-60">
                    Stock option plan and 409A valuation summary
                  </p>
                </div>
              </div>
             
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Stock Option Plan Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-6 h-6 rounded-md bg-[#9B51E0]/20 flex items-center justify-center">
                    <div className="w-3 h-3 bg-[#9B51E0] rounded-sm"></div>
                  </div>
                  <h3 className="text-lg font-medium text-[#F2F2F5]">
                    Stock Option Plan
                  </h3>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-[#2E2F3A] rounded-lg p-4 border border-[#2E2F3A] hover:border-[#9B51E0]/30 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-[#F2F2F5] opacity-60 uppercase tracking-wide">
                        Total Pool
                      </span>
                      <div className="w-2 h-2 bg-[#9B51E0] rounded-full"></div>
                    </div>
                    <p className="text-2xl font-bold text-[#F2F2F5]">
                      {stockOptionInfo?.totalPoolSize?.toLocaleString() || 0}
                    </p>
                    <p className="text-xs text-[#F2F2F5] opacity-60">shares</p>
                  </div>
                  
                  <div className="bg-[#2E2F3A] rounded-lg p-4 border border-[#2E2F3A] hover:border-[#9B51E0]/30 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-[#F2F2F5] opacity-60 uppercase tracking-wide">
                        Allocated
                      </span>
                      <div className="w-2 h-2 bg-[#E1467C] rounded-full"></div>
                    </div>
                    <p className="text-2xl font-bold text-[#F2F2F5]">
                      {stockOptionInfo?.allocated?.toLocaleString() || 0}
                    </p>
                    <p className="text-xs text-[#F2F2F5] opacity-60">shares</p>
                  </div>
                  
                  <div className="bg-[#2E2F3A] rounded-lg p-4 border border-[#2E2F3A] hover:border-[#9B51E0]/30 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-[#F2F2F5] opacity-60 uppercase tracking-wide">
                        Remaining
                      </span>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                    <p className="text-2xl font-bold text-[#F2F2F5]">
                      {stockOptionInfo?.remaining?.toLocaleString() || 0}
                    </p>
                    <p className="text-xs text-[#F2F2F5] opacity-60">shares</p>
                  </div>
                </div>
              </div>

              {/* 409A Valuation Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-md bg-[#E1467C]/20 flex items-center justify-center">
                      <div className="w-3 h-3 bg-[#E1467C] rounded-sm"></div>
                    </div>
                    <h3 className="text-lg font-medium text-[#F2F2F5]">
                      409A Valuation
                    </h3>
                  </div>
                </div>

                {isLoading409A ? (
                  <div className="bg-[#2E2F3A] rounded-lg p-6 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#E1467C]"></div>
                  </div>
                ) : active409AValuation ? (
                  <div className={`bg-[#2E2F3A] rounded-lg p-6 border transition-colors ${
                    !active409AValuation.appraisalDate
                      ? 'border-red-500/50 hover:border-red-500/70'
                      : isValuationExpired(active409AValuation.appraisalDate)
                      ? 'border-red-500/50 hover:border-red-500/70' 
                      : 'border-[#2E2F3A] hover:border-[#E1467C]/30'
                  }`}>
                    {!active409AValuation.appraisalDate ? (
                      <div className="bg-red-500/20 border border-red-500/30 rounded-md p-3 mb-4">
                        <p className="text-red-400 text-sm font-medium">⚠️ 409A Valuation Not Found</p>
                        <p className="text-red-300 text-xs mt-1">409A valuation not found. Please upload in the maintenance section.</p>
                      </div>
                    ) : isValuationExpired(active409AValuation.appraisalDate) && (
                      <div className="bg-red-500/20 border border-red-500/30 rounded-md p-3 mb-4">
                        <p className="text-red-400 text-sm font-medium">⚠️ 409A Valuation Expired</p>
                        <p className="text-red-300 text-xs mt-1">This valuation is older than 12 months and needs to be updated.</p>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-2 gap-6">
                      {/* Left Column - Fair Market Value and Valuation Date */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 rounded-md bg-[#E1467C]/20 flex items-center justify-center">
                              <span className="text-[#E1467C] font-bold text-sm">$</span>
                            </div>
                            <div>
                              <p className="text-sm text-[#F2F2F5] opacity-80">Fair Market Value</p>
                              <p className="text-xl font-bold text-[#F2F2F5]">
                                ${active409AValuation.fairMarketValue}
                              </p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between pt-4 border-t border-[#2E2F3A]">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 rounded-md bg-[#9B51E0]/20 flex items-center justify-center">
                              <span className="text-[#9B51E0] font-bold text-sm">📅</span>
                            </div>
                            <div>
                              <p className="text-sm text-[#F2F2F5] opacity-80">Valuation Date</p>
                              <p className="text-sm font-medium text-[#F2F2F5]">
                                {active409AValuation.appraisalDate 
                                  ? new Date(active409AValuation.appraisalDate).toLocaleDateString("en-US", {
                                      year: "numeric",
                                      month: "long",
                                      day: "numeric",
                                    })
                                  : "Not Available"
                                }
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right Column - Expired Since (only when expired) */}
                      {isValuationExpired(active409AValuation.appraisalDate) && (
          <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 rounded-md bg-red-500/20 flex items-center justify-center">
                                <span className="text-red-400 font-bold text-sm">⏰</span>
                              </div>
                              <div>
                                <p className="text-sm text-[#F2F2F5] opacity-80">Expired Since</p>
                                <p className="text-sm font-medium text-red-400">
                                  {getAnniversaryDate(active409AValuation.appraisalDate)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="bg-[#2E2F3A] rounded-lg p-6 border-2 border-dashed border-[#2E2F3A] hover:border-[#E1467C]/30 transition-colors">
                    <div className="text-center">
                      <div className="w-12 h-12 rounded-full bg-[#E1467C]/20 flex items-center justify-center mx-auto mb-3">
                        <span className="text-[#E1467C] font-bold text-lg">$</span>
                      </div>
                      <p className="text-[#F2F2F5] opacity-60 mb-3">No 409A valuation found</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {/* TODO: Open upload dialog */}}
                        className="border-[#E1467C] text-[#E1467C] hover:bg-[#E1467C] hover:text-[#F2F2F5]"
                      >
                        Upload Valuation Report
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="p-8 rounded-lg bg-[#1B1D2A] border border-[#2E2F3A] text-[#F2F2F5]">

            {/* {pendingGrantsCount > 0 && (
              <div className="bg-[#2E2F3A] border border-[#E1467C] rounded-md p-4 mb-4 flex items-center justify-between">
                <div className="flex items-center">
                  <ClipboardCheckIcon className="h-5 w-5 text-[#E1467C] mr-3" />
                  <div>
                    <h3 className="font-medium text-[#F2F2F5]">
                      Board Approval Required
                    </h3>
                    <p className="text-sm text-[#F2F2F5] opacity-80">
                      {pendingGrantsCount} promised grant
                      {pendingGrantsCount > 1 ? "s" : ""}{" "}
                      {pendingGrantsCount > 1 ? "require" : "requires"} board
                      approval.
                    </p>
                  </div>
                </div>
                <Button
                  variant="default"
                  onClick={handleBoardApprovalClick}
                  className="bg-[#E1467C] hover:bg-[#E1467C]/90 text-[#F2F2F5]"
                >
                  Submit for Board Approval
                </Button>
              </div>
            )} */}

            {isLoadingStockInfo || isLoadingGrants ? (
              <div className="text-center py-10">
                <p className="text-[#F2F2F5] opacity-60">
                  Loading promised grants...
                </p>
              </div>
            ) : promisedGrants && promisedGrants.length > 0 ? (
              <>
                <div className="mb-4 p-4 bg-[#2E2F3A] rounded-lg border border-[#9B51E0]/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-[#9B51E0]/20 flex items-center justify-center">
                        <span className="text-[#9B51E0] font-bold text-sm">📋</span>
                      </div>
                      <div>
                        <p className="text-[#F2F2F5] font-medium">
                          {selectedGrants.size > 0 
                            ? `${selectedGrants.size} grant${selectedGrants.size > 1 ? 's' : ''} selected`
                            : 'No grants selected'
                          }
                        </p>
                        <p className="text-sm text-[#F2F2F5] opacity-60">
                          {selectedGrants.size > 0 
                            ? 'Ready to issue equity grants'
                            : 'Select grants to issue'
                          }
                        </p>
                      </div>
                    </div>
                                         <DropdownMenu>
                       <DropdownMenuTrigger asChild>
                         <Button
                           disabled={selectedGrants.size === 0}
                           className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
                         >
                           Action
                           <ChevronDown className="ml-2 h-4 w-4" />
                         </Button>
                       </DropdownMenuTrigger>
                       <DropdownMenuContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
                         <DropdownMenuItem 
                           onClick={() => handleIssueSelected()}
                           className="text-[#F2F2F5] hover:bg-[#2E2F3A]"
                         >
                           Issue
                         </DropdownMenuItem>
                         <DropdownMenuSeparator className="bg-[#2E2F3A]" />
                         <DropdownMenuItem 
                           onClick={() => handleModifySelected()}
                           className="text-[#F2F2F5] hover:bg-[#2E2F3A]"
                         >
                           Modify
                         </DropdownMenuItem>
                       </DropdownMenuContent>
                     </DropdownMenu>
                  </div>
                </div>
                <div className="border border-[#2E2F3A] rounded-md max-h-[500px] overflow-y-auto">
                  <PromisedGrantsTable
                    grants={promisedGrants}
                    selectedGrants={selectedGrants}
                    editingGrantId={editingGrantId}
                    editedGrantData={editedGrantData}
                    isUpdating={isUpdating}
                    showCheckboxes={true}
                    actionType="delete"
                    onSelectAll={handleSelectAll}
                    onSelectGrant={handleSelectGrant}
                    onDeleteClick={handleDeleteClick}
                    onEditClick={handleEditClick}
                    onCancelClick={handleCancelClick}
                    onSaveClick={handleSaveClick}
                    onFieldChange={handleFieldChange}
                    grantTypeOptions={grantTypeOptions}
                    vestingScheduleOptions={vestingScheduleOptions}
                  />
                </div>
            </>
            ) : (
              <div className="text-center py-10">
                <p className="text-[#F2F2F5] opacity-60">
                  No promised grants found.
                </p>
                <p className="text-sm text-[#F2F2F5] opacity-40 mt-1">
                  Add service providers with equity grants to see them here.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <AlertDialog
        open={isBoardApprovalDialogOpen}
        onOpenChange={closeBoardApproval}
      >
        <AlertDialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-[#F2F2F5]">
              Submit for Board Approval
            </AlertDialogTitle>
            <AlertDialogDescription className="text-[#F2F2F5] opacity-80">
              This will submit all pending promised grants for board approval.
              Your board members will be notified and asked to review and
              approve these equity grants.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isSubmittingApproval}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSubmitForBoardApproval}
              disabled={isSubmittingApproval}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              {isSubmittingApproval ? "Submitting..." : "Submit for Approval"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog409Valuation
        isOpen={is409ADialogOpen}
        onClose={close409ADialog}
        selectedGrantsCount={selectedGrants.size}
        mode={dialogMode}
        currentFairMarketValue={active409AValuation?.fairMarketValue}
        currentAppraisalDate={active409AValuation?.appraisalDate}
        onNext={
          dialogMode === 'use-current-409a' ? handle409ANext :
          dialogMode === 'set-fair-market-value' ? handleSetFMVNext :
          dialogMode === 'update-409a' ? handleUpdate409ANext :
          undefined
        }
      />

      <IssueGrants409AModal
        isOpen={isIssue409AModalOpen}
        onClose={() => setIsIssue409AModalOpen(false)}
        selectedGrants={promisedGrants.filter(grant => selectedGrants.has(grant.id))}
        onOptionSelect={handle409AOptionSelect}
      />

      <ModifyGrantsDialog
        isOpen={isModifyDialogOpen}
        onClose={closeModifyDialog}
        selectedGrants={promisedGrants.filter(grant => selectedGrants.has(grant.id))}
        localEdits={localEdits}
        onFieldChange={handleBulkFieldChange}
        onCustomVestingClick={openCustomVestingDialog}
        onCustomVestingSave={saveCustomVestingData}
        getCustomVestingData={getCustomVestingData}
        grantTypeOptions={grantTypeOptions}
        vestingScheduleOptions={vestingScheduleOptions}
        onSaveAndClose={handleSaveAndCloseInDialog}
        isSaving={isSaving}
        saveResults={saveResults}
        getStatusIcon={getStatusIcon}
        isViewMode={isReviewModeActive}
        onIssueGrants={handleIssueGrantsInDialog}
        isIssuing={isIssuingGrants}
        issueResults={issueResults}
      />

      <CustomVestingDialog
        isOpen={isCustomVestingDialogOpen}
        onClose={closeCustomVestingDialog}
        onSave={(data) => saveCustomVestingData(currentGrantId, data)}
        grantId={currentGrantId}
        grantName={currentGrantName}
        initialData={getCustomVestingData(currentGrantId)}
      />

      {selectedGrant && (
        <EquityIssueDialog
          isOpen={isIssueDialogOpen}
          onClose={handleIssueDialogClose}
          grant={selectedGrant as any}
        />
      )}

      <AlertDialog
        open={!!deletingGrantId}
        onOpenChange={(open) => !open && handleCancelDelete()}
      >
        <AlertDialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-[#F2F2F5]">
              Delete Promised Grant
            </AlertDialogTitle>
            <AlertDialogDescription className="text-[#F2F2F5] opacity-80">
              Are you sure you want to delete this promised grant? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
              onClick={handleCancelDelete}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              {isDeleting ? "Deleting..." : "Delete Grant"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default PromisedGrants;
