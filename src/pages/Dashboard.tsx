import React, { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import DashboardCards from "@/components/dashboard/DashboardCards";
import DashboardSeriesSeedFinancing from "@/components/dashboard/DashboardSeriesSeedFinancing";
import DashboardCommercialAgreements from "@/components/dashboard/DashboardCommercialAgreements";
import GettingStartedCard from "@/components/dashboard/GettingStartedCard";
import MaintenanceCard from "@/components/dashboard/MaintenanceCard";
import PreSeedFinancingCard from "@/components/dashboard/PreSeedFinancingCard";
// import DashboardCapTable from "@/components/captable/DashboardCapTable";
import CollaborationSection from "@/components/collaboration/CollaborationSection";
import SectionToggle from "@/components/dashboard/SectionToggle";
import { useAuth } from "@/contexts/AuthContext";
import { useUserProfile } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { Button } from "@/components/ui/button";
import { UserPlus } from "lucide-react";
import InviteCollaboratorDialog from "@/components/collaboration/InviteCollaboratorDialog";
import { useSendInvite } from "@/integrations/legal-concierge/hooks/useCollaborators";
import { CollaboratorRole } from "@/components/collaboration/types";
import { toast } from "sonner";
import { Spinner } from "@/components/ui/spinner";

const Dashboard: React.FC = () => {
  const [documentsSignedStatus, setDocumentsSignedStatus] = useState(false);
  const [activeSection, setActiveSection] = useState("getting-started");
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const { user } = useAuth();
  const sendInvite = useSendInvite();

  const { data: userData, isLoading: isUserLoading } = useUserProfile();

  const isAdmin = userData?.roles?.includes("ADMIN") ?? false;

  const handleInviteCollaborator = async (
    email: string,
    role: CollaboratorRole
  ) => {
    try {
      await sendInvite.mutateAsync({
        email,
        role,
      });

      toast.success(`Invitation sent to ${email}`);
      setShowInviteDialog(false);
    } catch (error) {
      toast.error(
        `Failed to invite collaborator: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  useEffect(() => {
    setDocumentsSignedStatus(Math.random() > 0.5);
  }, []);

  if (!user) {
    return null;
  }

  const username = user?.fullName || user?.email?.split("@")[0] || "User";

  const renderActiveSection = () => {
    switch (activeSection) {
      case "getting-started":
        return <GettingStartedCard />;
      case "maintenance":
        return <MaintenanceCard />;
      case "series-seed-financing":
        return <DashboardSeriesSeedFinancing />;
      case "commercial-agreements":
        return <DashboardCommercialAgreements />;
      case "pre-seed-financing":
        return <PreSeedFinancingCard />;
      case "cap-table":
        // return <DashboardCapTable />;
      default:
        return <GettingStartedCard />;
    }
  };

  const renderAdminDashboard = () => (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)]">
      <div className="text-center space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome, {username}
        </h1>
        <p className="text-gray-500 max-w-md">
          As an administrator, you can invite team members to collaborate on
          this platform.
        </p>
        <Button
          size="lg"
          className="gap-2"
          onClick={() => setShowInviteDialog(true)}
        >
          <UserPlus className="h-5 w-5" />
          {userData?.roles?.includes("ADMIN")
            ? "Invite Users"
            : "Invite Collaborators"}
        </Button>
      </div>
      <InviteCollaboratorDialog
        open={showInviteDialog}
        onOpenChange={setShowInviteDialog}
        onInvite={handleInviteCollaborator}
        userData={userData}
      />
    </div>
  );

  const renderDefaultDashboard = () => (
    <div className="px-6 py-8">
      <DashboardHeader username={username} />

      <DashboardCards documentsSignedStatus={documentsSignedStatus} />
      <CollaborationSection />

      <SectionToggle
        activeSection={activeSection}
        setActiveSection={setActiveSection}
      />

      {renderActiveSection()}
    </div>
  );
  if (isUserLoading)
    return (
      <div className="flex items-center justify-center h-screen">
        <Spinner size="lg" />
      </div>
    );
  return (
    <Layout>
      {isAdmin ? renderAdminDashboard() : renderDefaultDashboard()}
    </Layout>
  );
};

export default Dashboard;
