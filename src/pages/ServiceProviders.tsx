import React from "react";
import Layout from "@/components/Layout";
import { getServiceProviderItems } from "@/components/dashboard/maintenance/ServiceProviderItems";
import MaintenanceItem from "@/components/dashboard/MaintenanceItem";
import { useMaintenanceDialogs } from "@/components/dashboard/maintenance/useMaintenanceDialogs";
import MaintenanceDialogs from "@/components/dashboard/maintenance/MaintenanceDialogs";
import { useServiceProviderDialogs } from "@/hooks/service-providers/useServiceProvider.hooks";
import ServiceProviderStatusDialog from "@/components/dashboard/maintenance/dialog/ServiceProviderStatusDialog";
import { useAuth } from "@/contexts/AuthContext";

const ServiceProviders: React.FC = () => {
  const {
    isAdvisorDialogOpen,
    setIsAdvisorDialogOpen,
    isContractorDialogOpen,
    setIsContractorDialogOpen,
    isEmployeeDialogOpen,
    setIsEmployeeDialogOpen,
    isTerminateDialogOpen,
    setIsTerminateDialogOpen,
    isPromisedGrantsDialogOpen,
    setIsPromisedGrantsDialogOpen,
  } = useMaintenanceDialogs();

  const { 
    advisorCounts,
    contractorCounts,
    employeeCounts,
    terminateCounts,
    handleStatusDialogOpen,
    handleStatusDialogClose,
    getDialogConfig,
  } = useServiceProviderDialogs();

  const { user } = useAuth();
  const companyId = user?.companyId || "";

  const serviceProviderItems = getServiceProviderItems({
    onAdvisorClick: () => setIsAdvisorDialogOpen(true),
    onContractorClick: () => setIsContractorDialogOpen(true),
    onEmployeeClick: () => setIsEmployeeDialogOpen(true),
    onTerminateClick: () => setIsTerminateDialogOpen(true),
    onPromisedGrantsClick: () => {}, // No longer needed
    advisorCounts,
    contractorCounts,
    employeeCounts,
    terminateCounts,
    onAdvisorActiveClick: () => handleStatusDialogOpen('advisor', 'active'),
    onAdvisorPendingClick: () => handleStatusDialogOpen('advisor', 'pending'),
    onAdvisorTerminatedClick: () => handleStatusDialogOpen('advisor', 'terminated'),
    onEmployeeActiveClick: () => handleStatusDialogOpen('employee', 'active'),
    onEmployeePendingClick: () => handleStatusDialogOpen('employee', 'pending'),
    onEmployeeTerminatedClick: () => handleStatusDialogOpen('employee', 'terminated'),
    onContractorActiveClick: () => handleStatusDialogOpen('contractor', 'active'),
    onContractorPendingClick: () => handleStatusDialogOpen('contractor', 'pending'),
    onContractorTerminatedClick: () => handleStatusDialogOpen('contractor', 'terminated'),
    onTerminatePendingClick: () => handleStatusDialogOpen('terminate', 'pending'),
    onTerminateTerminatedClick: () => handleStatusDialogOpen('terminate', 'terminated'),
  });

  const dialogConfig = getDialogConfig();

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
          <h2 className="text-xl font-semibold mb-4">
            Service Provider Management
          </h2>
          <p className="text-gray-600 mb-6">
            Add, update, or terminate service providers for your company.
          </p>

          <div className="space-y-4">
            {serviceProviderItems.map((item, index) => (
              <MaintenanceItem key={index} {...item} />
            ))}
          </div>
        </div>

        <MaintenanceDialogs
          isAdvisorDialogOpen={isAdvisorDialogOpen}
          setIsAdvisorDialogOpen={setIsAdvisorDialogOpen}
          isContractorDialogOpen={isContractorDialogOpen}
          setIsContractorDialogOpen={setIsContractorDialogOpen}
          isEmployeeDialogOpen={isEmployeeDialogOpen}
          setIsEmployeeDialogOpen={setIsEmployeeDialogOpen}
          isTerminateDialogOpen={isTerminateDialogOpen}
          setIsTerminateDialogOpen={setIsTerminateDialogOpen}
          isPromisedGrantsDialogOpen={isPromisedGrantsDialogOpen}
          setIsPromisedGrantsDialogOpen={setIsPromisedGrantsDialogOpen}
          // These dialogs aren't used in this page but are required by the component
          isSharesDialogOpen={false}
          setIsSharesDialogOpen={() => {}}
          isOptionPlanDialogOpen={false}
          setIsOptionPlanDialogOpen={() => {}}
          isBoardMeetingDialogOpen={false}
          setIsBoardMeetingDialogOpen={() => {}}
          companyId={companyId}
        />
        <ServiceProviderStatusDialog
          isOpen={!!dialogConfig.title}
          onClose={handleStatusDialogClose}
          title={dialogConfig.title}
          data={dialogConfig.data}
          columns={dialogConfig.columns}
          loading={dialogConfig.loading}
          className="max-h-96 overflow-y-auto"
        />
      </div>
    </Layout>
  );
};

export default ServiceProviders;
