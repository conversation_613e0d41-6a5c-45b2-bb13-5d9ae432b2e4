import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/common/Card";
import ConvertibleNotesFlow from "@/components/financing/convertiblenote/ConvertibleNotesFlow";
import Layout from "@/components/Layout";

const ConvertibleNotes: React.FC = () => {
  return (
    <Layout>
    <div className="container mx-auto py-6 px-4">
        <CardContent>
          <ConvertibleNotesFlow />
        </CardContent>
    </div>
    </Layout>
  );
};

export default ConvertibleNotes;
