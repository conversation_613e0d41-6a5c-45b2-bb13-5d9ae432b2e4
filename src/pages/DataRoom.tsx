import React, { useState, useEffect, useCallback } from "react";
import Layout from "@/components/Layout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import DataRoomFolders from "@/components/documents/DataRoomFolders";
import { FolderStructure, DocumentFile } from "@/types/capTable";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { APIClient } from "@/integrations/legal-concierge/client";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";

const api = new APIClient();

interface DocumentResponse {
  fileName: string;
  url: string;
  thumbnailUrl: string;
}

interface APISuccessResponse {
  message: string;
  data: Record<string, DocumentResponse[]>;
  error: string | null;
  validationErrors: unknown | null;
}

interface APIErrorResponse {
  error: string;
}

type APIResponse = { data: APISuccessResponse } | APIErrorResponse;

// Type guard to check if response has an error
function hasError(response: APIResponse): response is APIErrorResponse {
  return "error" in response && !("data" in response);
}

const INITIAL_FOLDER_STRUCTURE: FolderStructure = {
  "Incorporation Documents": {
    documents: [],
    subfolders: {
      "Certificate of Incorporation": { documents: [] },
      "Action by Sole Incorporator": { documents: [] },
      "Initial Board Consent": { documents: [] },
      Bylaws: { documents: [] },
      "IP Assignment Agreement": { documents: [] },
      "Restricted Stock Purchase Agreement": { documents: [] },
      "Stock Option Plan": { documents: [] },
      "83(b) Election Form": { documents: [] },
    },
  },
  Others: {
    documents: [],
    subfolders: {},
  },
};

const DataRoom: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { user } = useAuth();
  const { companyDetails } = useCompanyDetails();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);
  const [folderStructure, setFolderStructure] = useState<FolderStructure>(
    INITIAL_FOLDER_STRUCTURE
  );
  const [expandedFolders, setExpandedFolders] = useState<string[]>([
    "Incorporation Documents",
    "Others",
  ]);

  // Determine register mode and upload permissions
  const isRegisterExisting =
    companyDetails?.registerMode === "register-existing";

  // Role-based upload permissions: only owners can upload, and only in register-existing mode
  const allowUploads = permissions.canUploadDocuments && isRegisterExisting;

  // For collaborators and signers, only allow access after signature process is completed
  const isSignatureComplete = companyDetails?.isSignatureComplete;
  const canAccessDataRoom =
    permissions.isOwner ||
    (permissions.isCollaborator && isSignatureComplete) ||
    (permissions.isSigner && isSignatureComplete);

  const handleToggleFolder = useCallback((folderId: string) => {
    setExpandedFolders((prev) =>
      prev.includes(folderId)
        ? prev.filter((id) => id !== folderId)
        : [...prev, folderId]
    );
  }, []);

  const fetchDocuments = useCallback(
    async (isRefresh = false) => {
      if (!user?.companyId) return;

      try {
        if (!isRefresh) {
          setLoading(true);
        } else {
          setRefreshing(true);
        }

        const response = await api.getIncorporationDocuments(user.companyId);
        if (hasError(response)) {
          throw new Error(response.error);
        }

        const res = response.data as Record<string, DocumentFile[]>;
        if (res) {
          // Transform the API response into our folder structure
          const updatedStructure = { ...INITIAL_FOLDER_STRUCTURE };
          const incorpDocs = updatedStructure["Incorporation Documents"];
          if (incorpDocs && incorpDocs.subfolders) {
            // Update each subfolder with its documents
            Object.entries(res).forEach(([folderName, documents]) => {
              if (incorpDocs.subfolders?.[folderName]) {
                incorpDocs.subfolders[folderName] = {
                  documents: documents.map((doc) => ({
                    fileName: doc.fileName,
                    id: doc.id,
                    url: doc.url,
                    thumbnailUrl: doc.thumbnailUrl,
                  })),
                  isCompleted: documents.length > 0,
                };
              }
            });
            // Set parent folder completion status
            incorpDocs.isCompleted = Object.values(incorpDocs.subfolders).some(
              (folder) => folder.isCompleted
            );
          }

          // Handle Others folder separately
          if (res["Others"]) {
            const othersDocs = updatedStructure["Others"];
            if (othersDocs) {
              othersDocs.documents = res["Others"].map((doc) => ({
                fileName: doc.fileName,
                id: doc.id,
                url: doc.url,
                thumbnailUrl: doc.thumbnailUrl,
              }));
              othersDocs.isCompleted = res["Others"].length > 0;
            }
          }

          setFolderStructure(updatedStructure);
        }
      } catch (error) {
        console.error("Error fetching documents:", error);
        toast.error("Failed to load documents");
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [user?.companyId]
  );

  useEffect(() => {
    fetchDocuments(false);
  }, [fetchDocuments]);

  const handleRefresh = useCallback(() => {
    fetchDocuments(true);
  }, [fetchDocuments]);

  // Show access denied message for collaborators and signers before signature completion
  if (!canAccessDataRoom) {
    const userRoleMessage = permissions.isSigner
      ? "As a document signer, you'll have access to the data room once all signatures are completed."
      : "You'll be able to access the data room once all documents have been signed and the signature process is complete.";

    return (
      <Layout>
        <div className="container py-8 px-4 md:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Data Room</h1>
            <p className="text-gray-600 mt-1">
              Access to the data room will be available after the document
              signature process is completed.
            </p>
          </div>
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-blue-900 mb-2">
                  Data Room Access Pending
                </h3>
                <p className="text-blue-700">{userRoleMessage}</p>
                {permissions.isSigner && (
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <p className="text-sm text-blue-600">
                      You can return to your dashboard to check the signature
                      status.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8 px-4 md:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Data Room</h1>
          <p className="text-gray-600 mt-1">
            {isRegisterExisting
              ? "View and manage all your company's documents in one secure location."
              : "View your company's legal documents after the signature process is completed."}
          </p>
        </div>

        <Tabs defaultValue="folders" className="mb-8">
          <TabsList>
            <TabsTrigger value="folders">Folder View</TabsTrigger>
            {/* <TabsTrigger value="list" onClick={() => setActiveView("list")}>
              List View
            </TabsTrigger> */}
          </TabsList>

          <TabsContent value="folders" className="mt-6">
            {loading ? (
              <div className="text-center py-12">
                <p className="text-gray-600">Loading documents...</p>
              </div>
            ) : (
              <DataRoomFolders
                folderStructure={folderStructure}
                onUploadComplete={handleRefresh}
                expandedFolders={expandedFolders}
                onToggleFolder={handleToggleFolder}
                isRefreshing={refreshing}
                allowUploads={allowUploads}
              />
            )}
          </TabsContent>

          <TabsContent value="list" className="mt-6">
            <div className="text-center py-12">
              <p className="text-gray-600">List view is coming soon.</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default DataRoom;
