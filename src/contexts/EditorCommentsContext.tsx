import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from "react";
import { Comment } from "@/components/collaboration/types";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import {
  useDocumentComments,
  useCreateDocumentComment,
  useUpdateDocumentComment,
} from "@/integrations/legal-concierge/hooks/useDocumentComments";
import { useEditorStore } from "@/store/use-editor-store";
import { useUpdateDocument } from "@/integrations/legal-concierge/hooks/useDocuments";
import { generateDefaultCommitMessage } from "@/components/tiptap/extensions/change-tracker/utils";

interface EditorCommentsContextType {
  comments: Comment[];
  isLoading: boolean;
  error: string | null;
  documentId: string | null;

  // Comment management
  addComment: (commentData: {
    content: string;
    position?: { x: number; y: number };
  }) => Promise<Comment>;
  resolveComment: (commentId: string) => Promise<void>;
  refreshComments: () => Promise<void>;
  setDocumentId: (documentId: string | null) => void;
  clearError: () => void;

  // Editor integration
  highlightComment: (commentId: string) => void;
  clearActiveComment: () => void;
  activeCommentId: string | null;
  setActiveCommentId: (commentId: string | null) => void;
}

const EditorCommentsContext = createContext<EditorCommentsContextType | null>(
  null
);

export const useEditorComments = () => {
  const context = useContext(EditorCommentsContext);
  if (!context) {
    throw new Error(
      "useEditorComments must be used within EditorCommentsProvider"
    );
  }
  return context;
};

interface EditorCommentsProviderProps {
  children: React.ReactNode;
  initialDocumentId?: string;
}

export const EditorCommentsProvider: React.FC<EditorCommentsProviderProps> = ({
  children,
  initialDocumentId,
}) => {
  const [documentId, setDocumentId] = useState<string | null>(
    initialDocumentId || null
  );
  const [activeCommentId, setActiveCommentId] = useState<string | null>(null);
  const { user } = useAuth();
  const { editor } = useEditorStore();
  const { mutateAsync: updateDocument } = useUpdateDocument();

  // Use React Query hooks for data fetching
  const {
    data: commentsData,
    isLoading,
    error: queryError,
    refetch: refreshComments,
  } = useDocumentComments(
    user?.companyId || "",
    documentId || "",
    !!user?.companyId && !!documentId
  );

  const createCommentMutation = useCreateDocumentComment();
  const updateCommentMutation = useUpdateDocumentComment();

  // Function to trigger document save after comment is added
  const triggerDocumentSave = useCallback(async () => {
    if (!editor || !documentId) return;

    try {
      // First commit any pending changes in the editor
      editor.commands.saveDocument(generateDefaultCommitMessage());

      // Get the document payload from the extension
      const payload = editor.commands.saveToBackend();

      if (!payload) {
        console.warn("No document payload available for save");
        return;
      }

      // Call the backend API to save the document
      await updateDocument(payload);
      console.log("Document saved successfully after comment addition");
    } catch (error) {
      console.error("Failed to save document after comment addition:", error);
      // Don't show error toast as this is a background operation
    }
  }, [editor, documentId, updateDocument]);

  const error = queryError?.message || null;

  const clearError = useCallback(() => {
    // Error will be cleared automatically when query succeeds
  }, []);

  // Transform the API response to match our Comment type
  const comments: Comment[] = useMemo(() => {
    return commentsData
      ? commentsData.map((comment) => {
          const timestamp = (() => {
            try {
              const date = new Date(comment.createdAt);
              return isNaN(date.getTime()) ? new Date() : date;
            } catch {
              return new Date();
            }
          })();

          return {
            id: comment.id,
            userId: comment.userId,
            userName: comment.userName || comment.userId || "Anonymous",
            documentId: comment.documentIdentifier || documentId || "",
            content: comment.comment,
            timestamp,
            position: comment.position,
            resolved: comment.resolved,
            statusText: comment.statusText,
          };
        })
      : [];
  }, [commentsData, documentId]);

  const addComment = useCallback(
    async (commentData: {
      content: string;
      position?: { x: number; y: number };
    }): Promise<Comment> => {
      if (!documentId || !user?.companyId) {
        throw new Error("Document ID or company ID not available");
      }

      const response = await createCommentMutation.mutateAsync({
        companyId: user.companyId,
        documentIdentifier: documentId,
        comment: {
          comment: commentData.content,
          position: commentData.position,
          resolved: false,
        },
      });

      const timestamp = (() => {
        try {
          const date = new Date(response.createdAt);
          return isNaN(date.getTime()) ? new Date() : date;
        } catch {
          return new Date();
        }
      })();

      const newComment: Comment = {
        id: response.id,
        userId: response.userId,
        userName:
          response.userName ||
          user.fullName ||
          user.email?.split("@")[0] ||
          "Anonymous",
        documentId: response.documentIdentifier,
        content: response.comment,
        timestamp,
        position: response.position,
        resolved: response.resolved,
        statusText: response.statusText,
      };

      // Trigger document save after comment is successfully added
      await triggerDocumentSave();

      return newComment;
    },
    [documentId, user, createCommentMutation, triggerDocumentSave]
  );

  const resolveComment = useCallback(
    async (commentId: string) => {
      try {
        const comment = comments.find((c) => c.id === commentId);
        if (!comment) return;

        await updateCommentMutation.mutateAsync({
          commentId,
          comment: {
            comment: comment.content,
            resolved: true,
          },
          companyId: user?.companyId,
          documentIdentifier: documentId || undefined,
        });
        toast.success("Comment resolved successfully");
      } catch (error) {
        console.error("Error resolving comment:", error);
        toast.error("Failed to resolve comment");
      }
    },
    [comments, updateCommentMutation, user?.companyId, documentId]
  );

  const highlightComment = useCallback((commentId: string) => {
    // Try different possible selectors for comment elements
    const possibleSelectors = [
      `[data-comment-id="${commentId}"]`,
      `[data-comment="${commentId}"]`,
      `[comment-id="${commentId}"]`,
      `.comment-highlight[data-id="${commentId}"]`,
      `.comment-highlight[id="${commentId}"]`,
    ];

    let commentElements: NodeListOf<Element> | null = null;
    let usedSelector = "";

    for (const selector of possibleSelectors) {
      commentElements = document.querySelectorAll(selector);
      if (commentElements.length > 0) {
        usedSelector = selector;
        break;
      }
    }

    console.log(`Looking for comment elements with ID: ${commentId}`, {
      usedSelector,
      commentElements,
    });

    // Also log all elements with comment-highlight class to see what attributes they have
    const allCommentElements = document.querySelectorAll(".comment-highlight");
    console.log("All comment highlight elements:", allCommentElements);
    allCommentElements.forEach((el, index) => {
      console.log(
        `Comment element ${index}:`,
        el,
        "Attributes:",
        el.attributes
      );
    });

    // Remove active class from all comment highlights first
    const allHighlights = document.querySelectorAll(".comment-highlight");
    allHighlights.forEach((el) => el.classList.remove("active"));

    if (commentElements && commentElements.length > 0) {
      const firstElement = commentElements[0] as HTMLElement;
      console.log("Found comment element, scrolling to it:", firstElement);

      firstElement.scrollIntoView({ behavior: "smooth", block: "center" });

      // Add active class for persistent highlighting
      firstElement.classList.add("active");
    } else {
      console.log("No comment elements found for ID:", commentId);
    }
    setActiveCommentId(commentId);
  }, []);

  const clearActiveComment = useCallback(() => {
    // Remove active class from all comment highlights
    const allHighlights = document.querySelectorAll(".comment-highlight");
    allHighlights.forEach((el) => el.classList.remove("active"));
    setActiveCommentId(null);
  }, []);

  // Create a wrapper for refreshComments to match the expected type
  const refreshCommentsWrapper = useCallback(async () => {
    await refreshComments();
  }, [refreshComments]);

  const value: EditorCommentsContextType = {
    comments,
    isLoading,
    error,
    documentId,
    addComment,
    resolveComment,
    refreshComments: refreshCommentsWrapper,
    setDocumentId,
    clearError,
    highlightComment,
    clearActiveComment,
    activeCommentId,
    setActiveCommentId,
  };

  return (
    <EditorCommentsContext.Provider value={value}>
      {children}
    </EditorCommentsContext.Provider>
  );
};
