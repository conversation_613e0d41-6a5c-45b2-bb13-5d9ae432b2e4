import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ProFormaRoleFilter, ProFormaShareClassFilter } from '@/types/capTable';

interface GlobalFilterState {
  roleFilter: ProFormaRoleFilter;
  shareClassFilter: ProFormaShareClassFilter;
  shareholderNameSearch: string;
  isFiltering: boolean;
}

interface GlobalFilterContextType {
  filters: GlobalFilterState;
  setRoleFilter: (role: ProFormaRoleFilter) => void;
  setShareClassFilter: (shareClass: ProFormaShareClassFilter) => void;
  setShareholderNameSearch: (search: string) => void;
  resetFilters: () => void;
}

const GlobalFilterContext = createContext<GlobalFilterContextType | undefined>(undefined);

const DEFAULT_FILTERS: GlobalFilterState = {
  roleFilter: 'All',
  shareClassFilter: 'All',
  shareholderNameSearch: '',
  isFiltering: false
};

interface GlobalFilterProviderProps {
  children: ReactNode;
}

export const GlobalFilterProvider: React.FC<GlobalFilterProviderProps> = ({ children }) => {
  const [filters, setFilters] = useState<GlobalFilterState>(DEFAULT_FILTERS);

  const setRoleFilter = useCallback((role: ProFormaRoleFilter) => {
    setFilters(prev => ({ ...prev, roleFilter: role, isFiltering: true }));
    // Simulate filtering delay
    setTimeout(() => {
      setFilters(prev => ({ ...prev, isFiltering: false }));
    }, 500);
  }, []);

  const setShareClassFilter = useCallback((shareClass: ProFormaShareClassFilter) => {
    setFilters(prev => ({ ...prev, shareClassFilter: shareClass, isFiltering: true }));
    // Simulate filtering delay
    setTimeout(() => {
      setFilters(prev => ({ ...prev, isFiltering: false }));
    }, 500);
  }, []);

  const setShareholderNameSearch = useCallback((search: string) => {
    setFilters(prev => ({ ...prev, shareholderNameSearch: search, isFiltering: true }));
    // Simulate filtering delay
    setTimeout(() => {
      setFilters(prev => ({ ...prev, isFiltering: false }));
    }, 500);
  }, []);

  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, []);

  const value: GlobalFilterContextType = {
    filters,
    setRoleFilter,
    setShareClassFilter,
    setShareholderNameSearch,
    resetFilters
  };

  return (
    <GlobalFilterContext.Provider value={value}>
      {children}
    </GlobalFilterContext.Provider>
  );
};

export const useGlobalFilters = (): GlobalFilterContextType => {
  const context = useContext(GlobalFilterContext);
  if (context === undefined) {
    throw new Error('useGlobalFilters must be used within a GlobalFilterProvider');
  }
  return context;
};
