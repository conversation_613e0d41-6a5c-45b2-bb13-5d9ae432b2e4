import { APIClient } from '@/integrations/legal-concierge/client';
import { ConvertibleNoteRoundResponse, ApiResponse } from '@/types/financing';

export class AuthorizedRoundListService {
  constructor(private apiClient = new APIClient()) {}

  async getAuthorizedRounds(companyId: string): Promise<ConvertibleNoteRoundResponse[]> {
    try {
      const response = await this.apiClient.getAuthorizedRounds(companyId);

      if (response && typeof response === 'object' && 'error' in response) {
        throw new Error((response as { error: string }).error);
      }

      return response as ConvertibleNoteRoundResponse[];
    } catch (error) {
      throw new Error(`Failed to fetch authorized rounds: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
