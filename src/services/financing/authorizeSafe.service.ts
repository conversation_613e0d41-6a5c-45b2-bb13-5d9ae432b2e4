import { APIClient } from '@/integrations/legal-concierge/client';
import { AuthorizeSafeRequest, AuthorizeSafeResponse, AuthorizeSafeApiResponse } from '@/types/financing';

export class AuthorizeSafeService {
  constructor(private apiClient = new APIClient()) {}

  async createAuthorizeSafe(companyId: string, payload: AuthorizeSafeRequest): Promise<AuthorizeSafeResponse> {
    try {
      const response = await this.apiClient.createAuthorizeSafe(companyId, payload) as AuthorizeSafeApiResponse;

      // Check if there's an actual error message, not just the presence of the 'error' key
      if (response && response.error !== null) {
        throw new Error(response.error);
      }

      // Extract data from nested response structure
      if (response && response.data) {
        return response.data;
      }

      // Fallback if data is not present or response is unexpected
      throw new Error("Failed to create authorized SAFE: Unexpected response format.");
    } catch (error) {
      throw new Error(`Failed to create authorized SAFE: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
