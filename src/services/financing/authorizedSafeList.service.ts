import { APIClient } from '@/integrations/legal-concierge/client';
import { SafeRoundResponse, ApiResponse } from '@/types/financing';

export class AuthorizedSafeListService {
  constructor(private apiClient = new APIClient()) {}

  async getAuthorizedSafes(companyId: string): Promise<SafeRoundResponse[]> {
    try {
      const response = await this.apiClient.getAuthorizedSafes(companyId);

      if (response && typeof response === 'object' && 'error' in response) {
        throw new Error((response as { error: string }).error);
      }

      return response as SafeRoundResponse[];
    } catch (error) {
      throw new Error(`Failed to fetch authorized SAFEs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
