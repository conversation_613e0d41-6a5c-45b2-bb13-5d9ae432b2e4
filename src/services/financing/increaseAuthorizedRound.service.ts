import { APIClient } from '@/integrations/legal-concierge/client';
import { IncreaseAuthorizedRoundRequest, IncreaseAuthorizedRoundResponse } from '@/types/financing';

export class IncreaseAuthorizedRoundService {
  constructor(private apiClient = new APIClient()) {}

  async requestRoundIncrease(companyId: string, payload: IncreaseAuthorizedRoundRequest): Promise<IncreaseAuthorizedRoundResponse> {
    try {
      const response = await this.apiClient.increaseAuthorizedRound(companyId, payload);

      // Handle nested response structure
      if (response && typeof response === 'object' && 'data' in response) {
        const apiResponse = response as { 
          message: string; 
          data: any; 
          error: string | null; 
          validationErrors: any; 
        };
        
        if (apiResponse.error) {
          throw new Error(apiResponse.error);
        }

        if (apiResponse.validationErrors) {
          throw new Error('Validation errors occurred');
        }

        return apiResponse.data as IncreaseAuthorizedRoundResponse;
      }

      return response as IncreaseAuthorizedRoundResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit round increase request';
      throw new Error(errorMessage);
    }
  }
}
