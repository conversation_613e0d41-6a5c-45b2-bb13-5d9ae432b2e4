import { APIClient } from '@/integrations/legal-concierge/client';
import { SafeRepurchase } from '@/types/financing';

const apiClient = new APIClient();

export class SafeRepurchaseService {
  static async getSafeRepurchaseList(companyId: string): Promise<SafeRepurchase[]> {
    const response = await apiClient.getSafeRepurchaseList(companyId);
    // The APIClient.get method already unwraps response.data, so response is directly the array
    if (Array.isArray(response)) {
      return this.transformApiResponse(response);
    }
    return [];
  }

  static async submitSafeRepurchase(
    companyId: string, 
    safeId: string, 
    repurchaseAmount: number
  ): Promise<void> {
    await apiClient.submitSafeRepurchase(companyId, {
      id: safeId,
      repurchaseAmount
    });
  }

  private static transformApiResponse(apiData: any[]): SafeRepurchase[] {
    return apiData.map(item => ({
      id: item.id,
      authorizedRoundId: item.authorizedRoundId,
      selected: false, // Default state
      repurchaseAmount: 0, // Default state
      boardApproved: item.boardApproved || 'no', // Keep as string: 'yes', 'no', 'pending'
      investorName: item.investorName,
      dateIssued: item.dateIssued ? new Date(item.dateIssued) : null,
      purchaseAmount: item.purchaseAmount,
      valuationCap: item.valuationCap,
      discount: item.discount,
      mfn: true // Static value for now
    }));
  }
}
