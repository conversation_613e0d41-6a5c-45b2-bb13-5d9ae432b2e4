import { APIClient } from '@/integrations/legal-concierge/client';
import { AuthorizeRoundRequest, AuthorizeRoundResponse } from '@/types/financing';
import { toast } from 'sonner';

export class AuthorizeRoundService {
  constructor(private apiClient = new APIClient()) {}

  async createAuthorizeRound(companyId: string, payload: AuthorizeRoundRequest): Promise<AuthorizeRoundResponse> {
    try {
      const response = await this.apiClient.createAuthorizeRound(companyId, payload);

      if (response && typeof response === 'object' && 'error' in response && response.error) {
        throw new Error(response.error as string);
      }

      // Success toast notification
      toast.success('Convertible note round created successfully', {
        description: 'Board approval process has been initiated.'
      });

      return response.data as AuthorizeRoundResponse;
    } catch (error) {
      // Error toast notification
      toast.error('Failed to create convertible note round', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      
      throw new Error(`Failed to create authorize round: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}