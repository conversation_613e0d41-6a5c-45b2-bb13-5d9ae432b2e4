import { APIClient } from '@/integrations/legal-concierge/client';
import { IssueIndividualSafeRequest, IssueIndividualSafeResponse, IssueIndividualSafeApiResponse, ProrataSideletterUploadRequest, ProrataSideletterUploadResponse, SideLetterUploadRequest, SideLetterUploadResponse } from '@/types/financing';

export class IssueIndividualSafeService {
  constructor(private apiClient = new APIClient()) {}

  async issueIndividualSafe(companyId: string, payload: IssueIndividualSafeRequest): Promise<IssueIndividualSafeResponse> {
    try {
      const response = await this.apiClient.issueIndividualSafe(companyId, payload) as IssueIndividualSafeApiResponse;

      if (response && response.error !== null) {
        throw new Error(response.error);
      }

      // Check if the response indicates an error in the data field
      if (response && response.data && typeof response.data === 'string') {
        if (response.data.includes('Not enough authorized amount remaining for issue')) {
          throw new Error('Not enough authorized amount remaining for issue');
        }
        // Handle other potential error messages in data field
        throw new Error(response.data);
      }

      // If data is not a string, it should be the success response
      if (response && response.data && typeof response.data === 'object') {
        return response.data as IssueIndividualSafeResponse;
      }

      throw new Error('Unexpected response format from API');
    } catch (error) {
      // If it's already an Error object with a message, re-throw it as-is
      if (error instanceof Error) {
        throw error;
      }
      // Only wrap unknown errors
      throw new Error(`Failed to issue individual SAFE: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Method to get pre-signed URL for prorata sideletter upload
  async getProrataSideletterUploadUrl(companyId: string, payload: ProrataSideletterUploadRequest): Promise<ProrataSideletterUploadResponse> {
    try {
      const response = await this.apiClient.getSafeProrataSideletterUploadUrl(companyId, payload);

      if (response && typeof response === 'object' && 'error' in response && response.error) {
        throw new Error(response.error as string);
      }

      return response as ProrataSideletterUploadResponse;
    } catch (error) {
      throw new Error(`Failed to get upload URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // NEW: Method to get pre-signed URL for side letter upload
  async getSideLetterUploadUrl(companyId: string, payload: SideLetterUploadRequest): Promise<SideLetterUploadResponse> {
    try {
      const response = await this.apiClient.getSafeSideLetterUploadUrl(companyId, payload);

      if (response && typeof response === 'object' && 'error' in response && response.error) {
        throw new Error(response.error as string);
      }

      return response as SideLetterUploadResponse;
    } catch (error) {
      throw new Error(`Failed to get side letter upload URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
