import { APIClient } from '@/integrations/legal-concierge/client';
import { IncreaseAuthorizedSafeRequest, IncreaseAuthorizedSafeResponse } from '@/types/financing';

interface IncreaseAuthorizedSafeApiResponse {
  message: string;
  data: IncreaseAuthorizedSafeResponse;
  error: string | null;
  validationErrors: any;
}

export class IncreaseAuthorizedSafeService {
  constructor(private apiClient = new APIClient()) {}

  async increaseAuthorizedSafe(companyId: string, payload: IncreaseAuthorizedSafeRequest): Promise<IncreaseAuthorizedSafeResponse> {
    try {
      const response = await this.apiClient.increaseAuthorizedSafe(companyId, payload) as IncreaseAuthorizedSafeApiResponse;

      if (response && response.error !== null) {
        throw new Error(response.error);
      }

      return response.data;
    } catch (error) {
      // Re-throw the error to be handled by the hook
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to increase authorized SAFE');
    }
  }
}
