import { APIClient } from "@/integrations/legal-concierge/client";

export interface IssueSafeNoteUploadRequest {
  key: string;
  contentType: string;
}

export interface IssueSafeNoteUploadResponse {
  message: string;
  data: {
    uploadUrl: string;
    key: string;
  };
  error: string | null;
  validationErrors: unknown | null;
}

export interface RecordIssueIndividualSafeNoteRequest {
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  approvedDate: string;
  prorataS3Key?: string;
  s3Key?: string; // Changed to optional
  optionalS3Key?: string; // Added
}

export interface RecordIssueIndividualSafeNoteResponse {
  message: string;
  data: RecordIssueIndividualSafeNoteRequest & {
    id: string;
    companyId: string;
    status: string;
    createdAt: string;
  };
  error: string | null;
  validationErrors: unknown | null;
}

export class IssueSafeNoteService {
  private static client = new APIClient();

  static async getUploadUrl(
    companyId: string,
    payload: IssueSafeNoteUploadRequest
  ): Promise<IssueSafeNoteUploadResponse> {
    try {
      const response = await this.client.getIssueSafeNoteUploadUrl(companyId, payload);
      return response as IssueSafeNoteUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get upload URL");
    }
  }

  // NEW: Get presigned URL for prorata sideletter
  static async getProrataUploadUrl(
    companyId: string,
    payload: IssueSafeNoteUploadRequest
  ): Promise<IssueSafeNoteUploadResponse> {
    try {
      const response = await this.client.getSafeProrataSideletterUploadUrl(companyId, payload);
      return response as IssueSafeNoteUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get prorata upload URL");
    }
  }

  // NEW: Get presigned URL for side letter
  static async getSideLetterUploadUrl(
    companyId: string,
    payload: IssueSafeNoteUploadRequest
  ): Promise<IssueSafeNoteUploadResponse> {
    try {
      const response = await this.client.getSafeSideLetterUploadUrl(companyId, payload);
      return response as IssueSafeNoteUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get side letter upload URL");
    }
  }

  // NEW: Get presigned URL for optional document
  static async getOptionalDocumentUploadUrl(
    companyId: string,
    payload: IssueSafeNoteUploadRequest
  ): Promise<IssueSafeNoteUploadResponse> {
    try {
      const response = await this.client.getSafeOptionalDocumentUploadUrl(companyId, payload);
      return response as IssueSafeNoteUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get optional document upload URL");
    }
  }

  static async recordSafeNote(
    companyId: string,
    payload: RecordIssueIndividualSafeNoteRequest
  ): Promise<RecordIssueIndividualSafeNoteResponse> {
    try {
      const response = await this.client.recordIssueIndividualSafeNote(companyId, payload);
      // Check if the response contains an error message in the data field
      if (response && typeof response === 'object' && 'data' in response && typeof response.data === 'string') {
        throw new Error(response.data);
      }
      return response as RecordIssueIndividualSafeNoteResponse;
    } catch (error) {
      // If it's already an error with a message, use that
      if (error instanceof Error) {
        throw error;
      }
      // Otherwise, throw a generic error
      throw new Error("Failed to record SAFE note");
    }
  }
}
