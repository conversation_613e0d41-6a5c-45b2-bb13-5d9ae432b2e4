import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  IssueIndividualNoteRequest, 
  IssueIndividualNoteResponse,
  RecordIssueIndividualNoteRequest,
  RecordIssueIndividualNoteResponse,
  SideLetterUploadRequest,
  SideLetterUploadResponse
} from "@/types/financing";

export interface IssueIndividualNoteUploadRequest {
  key: string;
  contentType: string;
}

export interface IssueIndividualNoteUploadResponse {
  message: string;
  data: {
    uploadUrl: string;
    key: string;
  };
  error: string | null;
  validationErrors: unknown | null;
}

export class IssueIndividualNoteService {
  private static client = new APIClient();

  static async getUploadUrl(
    companyId: string,
    payload: IssueIndividualNoteUploadRequest
  ): Promise<IssueIndividualNoteUploadResponse> {
    try {
      const response = await this.client.getIssueIndividualNoteUploadUrl(companyId, payload);
      return response as IssueIndividualNoteUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get upload URL");
    }
  }

  // NEW: Get presigned URL for prorata sideletter
  static async getProrataUploadUrl(
    companyId: string,
    payload: SideLetterUploadRequest
  ): Promise<SideLetterUploadResponse> {
    try {
      const response = await this.client.getProrataSideletterUploadUrl(companyId, payload);
      return response as SideLetterUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get prorata upload URL");
    }
  }

  // NEW: Get presigned URL for side letter
  static async getSideLetterUploadUrl(
    companyId: string,
    payload: SideLetterUploadRequest
  ): Promise<SideLetterUploadResponse> {
    try {
      const response = await this.client.getSideLetterUploadUrl(companyId, payload);
      return response as SideLetterUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get side letter upload URL");
    }
  }

  // NEW: Get presigned URL for optional document
  static async getOptionalDocumentUploadUrl(
    companyId: string,
    payload: SideLetterUploadRequest
  ): Promise<SideLetterUploadResponse> {
    try {
      const response = await this.client.getOptionalDocumentUploadUrl(companyId, payload);
      return response as SideLetterUploadResponse;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : "Failed to get optional document upload URL");
    }
  }

  // NEW: Issue individual convertible note (final POST)
  static async issueIndividualNote(
    companyId: string,
    payload: IssueIndividualNoteRequest
  ): Promise<IssueIndividualNoteResponse> {
    try {
      const response = await this.client.issueIndividualConvertibleNote(companyId, payload);
      return response as IssueIndividualNoteResponse;
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : "Failed to issue individual convertible note"
      );
    }
  }

  // NEW: Record issue individual note (for RecordPopupDialog)
  static async recordIssueIndividualNote(
    companyId: string,
    payload: RecordIssueIndividualNoteRequest
  ): Promise<RecordIssueIndividualNoteResponse> {
    try {
      const response = await this.client.recordIssueIndividualNote(companyId, payload);
      return response as RecordIssueIndividualNoteResponse;
    } catch (error) {
      throw new Error(
        error instanceof Error ? error.message : "Failed to record issue individual note"
      );
    }
  }
}
