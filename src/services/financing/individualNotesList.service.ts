import { APIClient } from '@/integrations/legal-concierge/client';
import { IndividualNote, IndividualNotesResponse } from '@/types/financing';

export class IndividualNotesListService {
  constructor(private apiClient = new APIClient()) {}

  async getIndividualNotesList(companyId: string, authorizedRoundId: string): Promise<IndividualNote[]> {
    try {
      const response = await this.apiClient.getIndividualNotesList(companyId, authorizedRoundId);
      
      // Handle nested response structure
      if (response && typeof response === 'object' && 'data' in response) {
        const apiResponse = response as IndividualNotesResponse;
        
        if (apiResponse.error) {
          throw new Error(apiResponse.error);
        }
        
        if (apiResponse.validationErrors) {
          throw new Error('Validation errors occurred');
        }
        
        return apiResponse.data;
      }
      
      return response as IndividualNote[];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch individual notes';
      throw new Error(errorMessage);
    }
  }
}
