import { APIClient } from '@/integrations/legal-concierge/client';
import { RecordAuthorizedSafeRequest, RecordAuthorizedSafeResponse } from '@/types/financing';

interface RecordAuthorizedSafeApiResponse {
  message: string;
  data: RecordAuthorizedSafeResponse;
  error: string | null;
  validationErrors: unknown | null;
}

export class RecordAuthorizedSafeService {
  constructor(private apiClient = new APIClient()) {}

  async recordAuthorizedSafe(companyId: string, payload: RecordAuthorizedSafeRequest): Promise<RecordAuthorizedSafeResponse> {
    try {
      const response = await this.apiClient.recordAuthorizedSafe(companyId, payload) as RecordAuthorizedSafeApiResponse;

      if (response && response.error !== null) {
        throw new Error(response.error);
      }

      return response.data;
    } catch (error) {
      // Re-throw the error to be handled by the hook
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to record authorized SAFE');
    }
  }
}
