import { APIClient } from '@/integrations/legal-concierge/client';
import { IndividualSafe } from '@/types/financing';

export class IndividualSafesListService {
  constructor(private apiClient = new APIClient()) {}

  async getIndividualSafes(companyId: string, authorizedRoundId: string): Promise<IndividualSafe[]> {
    try {
      const response = await this.apiClient.getIndividualSafesList(companyId, authorizedRoundId);
      if (response && typeof response === 'object' && 'error' in response) {
        throw new Error((response as { error: string }).error);
      }
      return response as IndividualSafe[];
    } catch (error) {
      throw new Error(`Failed to fetch individual SAFEs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
