import { APIClient } from "@/integrations/legal-concierge/client";

const apiClient = new APIClient();

export interface DirectorSummary {
  authorizeSize: number;
  currentSize: number;
  vacantSize: number;
}

export interface Director {
  id: string;
  companyId: string;
  name: string;
  emailAddress: string | null;
  contactAddress: string | null;
  startDate: string;
  removalDate: string | null;
  resignationDate: string | null;
  resignationStatus: "yes" | "no" | "pending";
  removalStatus: "yes" | "no" | "pending";
  isActive: boolean;
}

export interface AddDirectorRequest {
  name: string;
  startDate: string;
  emailAddress?: string | null;
  contactAddress?: string | null;
}

export interface RemoveDirectorRequest {
  officerId: string;
  removalDate: string;
}

export interface ResignationDirectorRequest {
  officerId: string;
  resignationDate: string;
}

export const manageDirectorsService = {
  async getDirectorSummary(companyId: string): Promise<DirectorSummary> {
    const response = await apiClient.getDirectorSummary(companyId);
    return response as Director<PERSON><PERSON><PERSON><PERSON>;
  },

  async getDirectors(companyId: string): Promise<Director[]> {
    const response = await apiClient.getDirectors(companyId);
    return response as Director[];
  },

  async addDirector(companyId: string, payload: AddDirectorRequest): Promise<any> {
    const response = await apiClient.addDirector(companyId, payload);
    return response;
  },

  async removeDirector(companyId: string, payload: RemoveDirectorRequest): Promise<any> {
    const response = await apiClient.removeDirector(companyId, payload);
    return response;
  },

  async resignationDirector(companyId: string, payload: ResignationDirectorRequest): Promise<any> {
    const response = await apiClient.resignationDirector(companyId, payload);
    return response;
  },
};
