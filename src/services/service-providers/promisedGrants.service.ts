import { APIClient } from "@/integrations/legal-concierge/client";

const api = new APIClient();

export interface PromisedGrant {
  id: string;
  name: string;
  type: string;
  shares: number;
  grantType: string;
  vestingSchedule: string | null;
  vestingCommencementDate: string;
  pricePerShare: number;
  address: string;
  tenPercentHolder: boolean;
  boardApproved: string;
  editable: boolean;
}

export interface Active409AValuation {
  appraiserName: string | null;
  appraisalDate: string | null;
  fairMarketValue: number;
  confirmationStatus: string;
  isExpired: boolean;
}

export async function getPromisedGrants(companyId: string): Promise<PromisedGrant[]> {
  const response = await api.getPromisedGrants(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  
  return response as PromisedGrant[];
}

export async function updatePromisedGrant(
  id: string,
  payload: {
    name: string;
    grantType: string;
    shares: number;
    vestingSchedule: string | null;
    pricePerShare: number;
    compensation?: number;
    vestingPeriod?: number;
    cliff?: number;
    term?: number;
  }
): Promise<any> {
  const response = await api.updatePromisedGrant(id, payload);
  return response;
}

export async function deletePromisedGrant(id: string): Promise<any> {
  const response = await api.deletePromisedGrant(id);
  return response;
}

export async function getActive409AValuation(companyId: string): Promise<Active409AValuation | null> {
  const response = await api.getActive409AValuation(companyId);
  if ((response as any)?.error) {
    return null;
  }
  
  return response as Active409AValuation;
}

export async function setFairMarketValue(
  companyId: string,
  value: number
): Promise<any> {
  const response = await api.set409aFairMarketValue(companyId, {
    fairMarketValue: value,
  });
  return response;
}

export async function get409aUploadPresignedUrl(
  companyId: string,
  key: string,
  contentType: string
): Promise<{ uploadUrl: string; key: string }> {
  const response = await api.get409aPresignedUrl(companyId, { key, contentType });
  if ((response as any)?.error) throw new Error((response as any).error);
  return (response as any).data;
}

export async function submit409aValuation(
  companyId: string,
  payload: {
    appraiserName: string;
    appraisalDate: string;
    fairMarketValue: number;
    confirmationStatus: 'yes' | 'no';
    s3Key: string;
  }
): Promise<any> {
  const response = await api.submit409aValuation(companyId, payload);
  return response;
}

export async function issuePromisedGrant(
  companyId: string,
  promiseGrantId: string
): Promise<any> {
  const response = await api.issuePromisedGrant(companyId, promiseGrantId);
  return response;
}
