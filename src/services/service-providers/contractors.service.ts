import { APIClient } from "@/integrations/legal-concierge/client";
import { AddContractorPayload } from "@/types/serviceProvider";

const api = new APIClient();

export async function addCompanyContractor(companyId: string, payload: AddContractorPayload) {
  const response = await api.addCompanyContractor(companyId, payload);
  
  if (response.error) {
    throw new Error(response.error);
  }
  
  return response.data;
}

export async function getActiveContractors(companyId: string) {
  const response = await api.getActiveContractors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}

export async function getPendingContractors(companyId: string) {
  const response = await api.getPendingContractors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}

export async function getTerminatedContractors(companyId: string) {
  const response = await api.getTerminatedContractors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
} 