import { APIClient } from "@/integrations/legal-concierge/client";
import { AddAdvisorPayload } from "@/types/serviceProvider";

const api = new APIClient();

export async function addCompanyAdvisor(companyId: string, payload: AddAdvisorPayload) {
  const response = await api.addCompanyAdvisor(companyId, payload);
  
  if (response.error) {
    throw new Error(response.error);
  }
  
  return response.data;
}

export async function getActiveAdvisors(companyId: string) {
  const response = await api.getActiveAdvisors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  // response is already the array!
  return response ?? [];
}

export async function getPendingAdvisors(companyId: string) {
  const response = await api.getPendingAdvisors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}

export async function getTerminatedAdvisors(companyId: string) {
  const response = await api.getTerminatedAdvisors(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}
