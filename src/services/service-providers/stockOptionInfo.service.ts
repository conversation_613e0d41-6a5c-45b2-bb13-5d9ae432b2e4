import { APIClient } from "@/integrations/legal-concierge/client";

const api = new APIClient();

interface StockOptionInformation {
  totalPoolSize: number;
  allocated: number;
  remaining: number;
}

export async function getStockOptionInformation(companyId: string): Promise<StockOptionInformation> {
  const response = await api.getStockOptionInformation(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as StockOptionInformation;
}
