import { APIClient } from "@/integrations/legal-concierge/client";

const api = new APIClient();

interface PendingServiceProvider {
  id: string;
  companyId: string;
  type: "employee" | "advisor" | "independent-contractor-consultant";
  name: string;
  email: string;
  address: string;
  services: string | null;
  grantType: "option" | "restricted-stock-grant" | "none";
  shares: number;
  startDate: string;
  vestingSchedule: string | null;
  compensation: number;
  compensationPeriod: "hourly" | "monthly" | "annually";
  vestingPeriod: number | null;
  cliff: number | null;
  terminated: boolean;
  terminatedOn: string | null;
  isShareIssued: boolean;
}

export async function getPendingServiceProviders(companyId: string): Promise<PendingServiceProvider[]> {
  const response = await api.getPendingServiceProviders(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as PendingServiceProvider[];
}

export async function terminateServiceProvider(payload: {
  id: string;
  terminationDate: string;
  serviceProviderTerminationType: string;
  severanceAmount: number;
  reasonForTermination: string;
}): Promise<boolean> {
  const response = await api.terminateServiceProvider(payload.id, payload);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return true;
}

export async function getTerminatedServiceProviders(companyId: string): Promise<TerminatedServiceProvider[]> {
  const response = await api.getTerminatedServiceProviders(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as TerminatedServiceProvider[];
}

export async function getPendingTerminatedServiceProviders(companyId: string): Promise<TerminatedServiceProvider[]> {
  const response = await api.getPendingTerminatedServiceProviders(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response as TerminatedServiceProvider[];
}

interface TerminatedServiceProvider {
  id: string;
  name: string;
  serviceProviderStatus: "advisor" | "employee" | "independent-contractor-consultant";
  shares: number;
  typeOfGrant: string;
  vestedAmount: number;
  unvestedAmount: number;
  vestingProgress: number;
  severanceAmount: number | null;
}
