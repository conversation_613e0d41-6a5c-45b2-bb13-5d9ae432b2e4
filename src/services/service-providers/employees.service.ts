import { APIClient } from "@/integrations/legal-concierge/client";
import { AddEmployeePayload } from "@/types/serviceProvider";

const api = new APIClient();

export async function addCompanyEmployee(companyId: string, payload: AddEmployeePayload) {
  const response = await api.addCompanyEmployee(companyId, payload);
  
  if (response.error) {
    throw new Error(response.error);
  }
  
  return response.data;
} 

export async function getActiveEmployees(companyId: string) {
  const response = await api.getActiveEmployees(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}

export async function getPendingEmployees(companyId: string) {
  const response = await api.getPendingEmployees(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
}

export async function getTerminatedEmployees(companyId: string) {
  const response = await api.getTerminatedEmployees(companyId);
  if ((response as any)?.error) {
    throw new Error((response as any).error);
  }
  return response ?? [];
} 