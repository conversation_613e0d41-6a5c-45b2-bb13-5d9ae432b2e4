import { APIClient } from "@/integrations/legal-concierge/client";

export interface CreateSopState {
  additionalShare: number;
  isBoardApproved: "yes" | "no" | "pending";
  isStockHolderApproved: "yes" | "no" | "pending";
  workflowId: string | null;
}

export interface CreateSopService {
  getCurrentPending(companyId: string): Promise<CreateSopState | null>;
  submitForBoardApproval(companyId: string, shareAmount: number): Promise<void>;
  submitForStockholderApproval(companyId: string): Promise<void>;
  abandonProcess(companyId: string): Promise<void>;
}

class CreateSopServiceImpl implements CreateSopService {
  constructor(private apiClient = new APIClient()) {}

  async getCurrentPending(companyId: string): Promise<CreateSopState | null> {
    const response = await this.apiClient.getCreateSopCurrentPending(companyId);
    // client.get returns jsonResponse.data by default; response is data or null
    return (response as CreateSopState) ?? null;
  }

  async submitForBoardApproval(companyId: string, shareAmount: number): Promise<void> {
    await this.apiClient.createStockOptionPlan(companyId, shareAmount);
  }

  async submitForStockholderApproval(companyId: string): Promise<void> {
    await this.apiClient.submitCreateSopForStockholderApproval(companyId);
  }

  async abandonProcess(companyId: string): Promise<void> {
    await this.apiClient.abandonCreateStockOptionPlan(companyId);
  }
}

export const createSopService: CreateSopService = new CreateSopServiceImpl();
