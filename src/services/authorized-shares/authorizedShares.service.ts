import { APIClient } from "@/integrations/legal-concierge/client";

export interface AuthorizedSharesResponse {
  message: string;
  data: {
    authorizedShare: number;
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    isFileOfCertificateOfAmendmentUploaded: "yes" | "no";
  };
  error: null | string;
  validationErrors: null | string[];
}

export interface PresignedUrlResponse {
  message: string;
  data: {
    uploadUrl: string;
    key: string;
  };
  error: null | string;
  validationErrors: null | string[];
}

export interface AuthorizedSharesService {
  getCurrentPending(companyId: string): Promise<AuthorizedSharesResponse['data']>;
  submitForBoardApproval(companyId: string, additionalShares: number): Promise<void>;
  submitForStockholderApproval(companyId: string): Promise<void>;
  abandonProcess(companyId: string): Promise<void>;
  getPresignedUrl(companyId: string, key: string, contentType: string): Promise<{ uploadUrl: string; key: string }>;
  submitFileAmendment(companyId: string, s3Key: string): Promise<void>;
}

class AuthorizedSharesServiceImpl implements AuthorizedSharesService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getCurrentPending(companyId: string): Promise<AuthorizedSharesResponse['data']> {
    try {
      const response = await this.apiClient.getCurrentPendingAuthorizedShares(companyId);
      return response as AuthorizedSharesResponse['data'];
    } catch (error) {
      throw new Error(`Failed to get current pending authorized shares: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async submitForBoardApproval(companyId: string, additionalShares: number): Promise<void> {
    try {
      await this.apiClient.submitForBoardApproval(companyId, additionalShares);
    } catch (error) {
      throw new Error(`Failed to submit for board approval: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async submitForStockholderApproval(companyId: string): Promise<void> {
    try {
      await this.apiClient.submitForStockholderApproval(companyId);
    } catch (error) {
      throw new Error(`Failed to submit for stockholder approval: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async abandonProcess(companyId: string): Promise<void> {
    try {
      await this.apiClient.abandonAuthorizedSharesProcess(companyId);
    } catch (error) {
      throw new Error(`Failed to abandon process: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getPresignedUrl(companyId: string, key: string, contentType: string): Promise<{ uploadUrl: string; key: string }> {
    try {
      const response = await this.apiClient.getAmendmentPresignedUrl(companyId, key, contentType) as PresignedUrlResponse;
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get presigned URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async submitFileAmendment(companyId: string, s3Key: string): Promise<void> {
    try {
      await this.apiClient.submitFileAmendment(companyId, s3Key);
    } catch (error) {
      throw new Error(`Failed to submit file amendment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

export const authorizedSharesService = new AuthorizedSharesServiceImpl(); 