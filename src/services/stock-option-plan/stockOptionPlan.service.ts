import { APIClient } from "@/integrations/legal-concierge/client";

export interface StockOptionPlanSummary {
  totalAuthorizedSize: number;
  totalPoolSize: number;
  allocated: number;
  remaining: number;
  totalRemaining: number;
  promiseGrant: number;
  pendingDetail: {
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    workflowId: string | null;
  };
}

export interface StockOptionPlanService {
  getSummary(companyId: string): Promise<StockOptionPlanSummary>;
  submitForBoardApproval(companyId: string, additionalShares: number): Promise<void>;
  submitForStockholderApproval(companyId: string): Promise<void>;
  abandonProcess(companyId: string): Promise<void>;
}

class StockOptionPlanServiceImpl implements StockOptionPlanService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getSummary(companyId: string): Promise<StockOptionPlanSummary> {
    try {
      const response = await this.apiClient.getStockOptionPlanSummary(companyId);
      return response as StockOptionPlanSummary;
    } catch (error) {
      throw new Error(`Failed to get stock option plan summary: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  async submitForBoardApproval(companyId: string, additionalShares: number): Promise<void> {
    try {
      await this.apiClient.increaseStockOptionPlan(companyId, additionalShares);
    } catch (error) {
      throw new Error(`Failed to submit board approval: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  async submitForStockholderApproval(companyId: string): Promise<void> {
    try {
      await this.apiClient.submitSopForStockholderApproval(companyId);
    } catch (error) {
      throw new Error(`Failed to submit stockholder approval: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  async abandonProcess(companyId: string): Promise<void> {
    try {
      await this.apiClient.abandonIncreaseStockOptionPlan(companyId);
    } catch (error) {
      throw new Error(`Failed to abandon SOP increase: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}

export const stockOptionPlanService = new StockOptionPlanServiceImpl();
