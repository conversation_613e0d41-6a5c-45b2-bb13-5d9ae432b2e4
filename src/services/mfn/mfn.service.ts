import { APIClient } from "@/integrations/legal-concierge/client";
import {
  MfnSummaryResponse,
  MfnNotifyResponse,
  MfnConvertibleNotesResponse,
  MfnSafesResponse,
} from "@/types/mfn";

class MfnService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  /**
   * Get MFN summary including current issues and triggering note
   * @param companyId - The company ID
   * @returns Promise with MFN summary data
   */
  async getMfnSummary(companyId: string): Promise<MfnSummaryResponse> {
    try {
      const response = await this.apiClient.getMfnSummary(companyId);
      console.log("response", response);
      
      // The API returns the data directly, not nested under a 'data' property
      // Transform it to match our expected structure
      const rawResponse = response as { currentIssues: any[]; newTerm: any };
      const transformedResponse: MfnSummaryResponse = {
        message: "Success",
        data: {
          currentIssues: rawResponse.currentIssues || [],
          newTerm: rawResponse.newTerm || null,
        },
        error: null,
        validationErrors: null,
      };
      
      return transformedResponse;
    } catch (error) {
      console.error("Error fetching MFN summary:", error);
      throw error;
    }
  }

  /**
   * Notify all MFN holders
   * @param companyId - The company ID
   * @returns Promise with notification response
   */
  async notifyHolders(companyId: string): Promise<MfnNotifyResponse> {
    try {
      const response = await this.apiClient.notifyMfnHolders(companyId);
      return response as MfnNotifyResponse;
    } catch (error) {
      console.error("Error notifying MFN holders:", error);
      throw error;
    }
  }

  // Legacy methods for backward compatibility (can be removed after migration)
  /**
   * Get MFN details for convertible notes
   * @param companyId - The company ID
   * @returns Promise with convertible notes MFN data
   */
  async getMfnConvertibleNotes(
    companyId: string
  ): Promise<MfnConvertibleNotesResponse> {
    try {
      const response = await this.apiClient.getMfnConvertibleNotes(companyId);
      return response as MfnConvertibleNotesResponse;
    } catch (error) {
      console.error("Error fetching MFN convertible notes:", error);
      throw error;
    }
  }

  /**
   * Get MFN details for SAFEs
   * @param companyId - The company ID
   * @returns Promise with SAFEs MFN data
   */
  async getMfnSafes(companyId: string): Promise<MfnSafesResponse> {
    try {
      const response = await this.apiClient.getMfnSafes(companyId);
      return response as MfnSafesResponse;
    } catch (error) {
      console.error("Error fetching MFN SAFEs:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const mfnService = new MfnService();
export default mfnService;
