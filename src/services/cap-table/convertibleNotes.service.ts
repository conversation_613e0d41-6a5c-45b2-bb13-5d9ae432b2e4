import { APIClient } from '@/integrations/legal-concierge/client';
import { ConvertibleNotesResponse, ConvertibleNotesErrorResponse, ConvertibleNoteItem } from '@/types/capTable';

export class ConvertibleNotesService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getConvertibleNotes(companyId: string): Promise<ConvertibleNotesResponse | ConvertibleNotesErrorResponse> {
    try {
      const response = await this.apiClient.getConvertibleNotes(companyId) as ConvertibleNoteItem[] | ConvertibleNotesResponse;
      
      // If the response is the data directly (array), wrap it in the expected format
      if (Array.isArray(response)) {
        return {
          message: "Success",
          data: response,
          error: null,
          validationErrors: null,
        };
      }
      
      return response as ConvertibleNotesResponse;
    } catch (error) {
      // Return error response in the expected format
      return {
        message: "Error loading convertible notes",
        data: null,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
        validationErrors: null,
      };
    }
  }
}

export const convertibleNotesService = new ConvertibleNotesService();
