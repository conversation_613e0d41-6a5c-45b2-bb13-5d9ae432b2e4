import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  StockOptionPlanSummaryApiResponse, 
  StockOptionPlanTableApiResponse,
  StockOptionPlanSummary,
  StockOptionGrant,
  UpdateGrantRequest,
  UpdateGrantResponse,
  ExerciseOptionRequest,
  ExerciseOptionResponse,
  DeleteGrantRequest,
  DeleteGrantResponse,
  AddGrantRequest,
  AddGrantResponse
} from "@/types/capTable";

export const getStockOptionPlanSummary = async (companyId: string): Promise<StockOptionPlanSummary> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getStockOptionInformation(companyId);
    
    return transformSummaryApiResponse(response as StockOptionPlanSummaryApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch stock option plan summary: ${error}`);
  }
};

export const getStockOptionPlanTable = async (
  companyId: string, 
  filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }
): Promise<StockOptionGrant[]> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getStockOptionPlanTable(companyId, filters);
    
    return transformTableApiResponse(response as StockOptionPlanTableApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch stock option plan table: ${error}`);
  }
};

export const updateStockOptionGrant = async (
  companyId: string, 
  request: UpdateGrantRequest
): Promise<UpdateGrantResponse> => {
  try {
    const apiClient = new APIClient();
    // Transform role and grant type from display values to API values
    const apiRequest = {
      ...request,
      role: mapRoleToApi(request.role),
      grantType: mapGrantTypeToApi(request.grantType),
    };
    const response = await apiClient.updateStockOptionGrant(companyId, apiRequest);
    
    return response as UpdateGrantResponse;
  } catch (error) {
    throw new Error(`Failed to update stock option grant: ${error}`);
  }
};

export const exerciseStockOption = async (
  companyId: string, 
  request: ExerciseOptionRequest
): Promise<ExerciseOptionResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.exerciseStockOption(companyId, request);
    
    return response as ExerciseOptionResponse;
  } catch (error) {
    throw new Error(`Failed to exercise stock option: ${error}`);
  }
};

export const deleteStockOptionGrant = async (
  companyId: string, 
  request: DeleteGrantRequest
): Promise<DeleteGrantResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.deleteStockOptionGrant(companyId, request);
    
    return response as DeleteGrantResponse;
  } catch (error) {
    throw new Error(`Failed to delete stock option grant: ${error}`);
  }
};

export const addStockOptionGrant = async (
  companyId: string, 
  request: AddGrantRequest
): Promise<AddGrantResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.promiseGrantWithoutApproval(companyId, request);
    
    return response as AddGrantResponse;
  } catch (error) {
    throw new Error(`Failed to add stock option grant: ${error}`);
  }
};

const transformSummaryApiResponse = (apiData: StockOptionPlanSummaryApiResponse): StockOptionPlanSummary => {
  return {
    totalPool: apiData.totalPoolSize || 0,
    allocated: apiData.allocated || 0,
    remaining: apiData.remaining || 0,
    lastUpdated: new Date().toLocaleDateString(), // Will be updated from table API
    percentages: {
      totalPoolSizePercentage: apiData.stockOptionPoolPercentage?.totalPoolSizePercentage || 0,
      poolAllocatedPercentage: apiData.stockOptionPoolPercentage?.poolAllocatedPercentage || 0,
      remainingPoolPercentage: apiData.stockOptionPoolPercentage?.remainingPoolPercentage || 0,
    }
  };
};

const transformTableApiResponse = (apiData: StockOptionPlanTableApiResponse): StockOptionGrant[] => {
  return apiData.optionPools.map((pool) => ({
    id: pool.id, // Use the actual API ID instead of generating fake ones
    recipient: pool.recipient || "-",
    role: mapRoleFromApi(pool.role),
    grantDate: new Date(pool.grantDate),
    optionsGranted: pool.options || 0,
    vestingStart: new Date(pool.grantDate), // Using grant date as vesting start
    percentVested: pool.vestingProgress || 0,
    // NEW FIELDS
    grantStatus: pool.grantStatus === 'promised' ? 'Promised' : 'Granted',
    grantType: mapGrantTypeFromApi(pool.grantType),
    issueStatus: 'Active', // Default, could be enhanced
    stateOfResidency: 'CA', // Default, could be enhanced
    // ENHANCED VESTING FIELDS
    vestedShares: pool.vestedShare || 0,
    unvestedShares: pool.unVestedShare || 0,
    vestingSchedule: pool.vestingSchedule || "standard-four-years-monthly-vesting",
    vestingPeriod: pool.vestingPeriod || null,
    cliff: pool.cliff || null,
    vestingCommencementDate: pool.vestingCommencementDate || pool.grantDate,
    term: pool.term || 48,
    // Additional fields for backend processing
    percentageOfPool: pool.percentageOfPool || 0,
    currentValue: pool.currentValue || 0,
  }));
};

const mapRoleFromApi = (apiRole: string): string => {
  const roleMap: Record<string, string> = {
    'employee': 'Employee',
    'advisor': 'Advisor',
    'independent_contractor_consultant': 'Contractor',
  };
  return roleMap[apiRole] || apiRole;
};

const mapRoleToApi = (displayRole: string): string => {
  const reverseRoleMap: Record<string, string> = {
    'Employee': 'employee',
    'Advisor': 'advisor',
    'Contractor': 'independent_contractor_consultant',
  };
  return reverseRoleMap[displayRole] || displayRole.toLowerCase();
};

const mapGrantTypeFromApi = (apiGrantType: string): 'Restricted Stock' | 'Option' | 'Exercised Option' => {
  if (apiGrantType === 'restricted-stock-grant-inside-of-stock-option-plan') {
    return 'Restricted Stock';
  }
  if (apiGrantType === 'exercise-stock') {
    return 'Exercised Option';
  }
  return 'Option';
};

const mapGrantTypeToApi = (displayGrantType: string): string => {
  const grantTypeMap: Record<string, string> = {
    'Restricted Stock': 'restricted-stock-grant-inside-of-stock-option-plan',
    'Option': 'option',
    'Exercised Option': 'exercise-stock',
  };
  return grantTypeMap[displayGrantType] || displayGrantType.toLowerCase();
};

// Vesting Schedule Display Logic
export const getVestingScheduleDisplay = (schedule: string, vestingPeriod?: number, cliff?: number) => {
  if (schedule === 'custom') {
    return {
      display: 'Custom ⚠️',
      tooltip: `Custom Schedule: ${vestingPeriod} months vesting, ${cliff} month cliff`,
      isCustom: true
    };
  }
  
  // Standard schedules mapping
  const standardMap: Record<string, string> = {
    'standard-two-years-monthly-vesting': 'Standard 2-Year Monthly',
    'standard-four-years-monthly-vesting': 'Standard 4-Year Monthly',
    'standard-one-year-monthly-vesting': 'Standard 1-Year Monthly',
    'standard-three-years-monthly-vesting': 'Standard 3-Year Monthly',
  };
  
  return {
    display: standardMap[schedule] || schedule.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    tooltip: standardMap[schedule] || schedule,
    isCustom: false
  };
};
