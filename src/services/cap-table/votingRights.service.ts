import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  VotingRightsApiResponse, 
  VotingRightsData,
  VotingShareholder,
  UpdateEmailRequest,
  UpdateEmailResponse,
  UpdateEmailStatusRequest,
  UpdateEmailStatusResponse,
  EmailExclusionSummary
} from "@/types/capTable";

export const getVotingRights = async (
  companyId: string, 
  filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }
): Promise<VotingRightsData> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getVotingRights(companyId, filters);
    
    return transformApiResponse(response as VotingRightsApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch voting rights: ${error}`);
  }
};

export const updateVotingRightEmail = async (
  companyId: string, 
  request: UpdateEmailRequest
): Promise<UpdateEmailResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.updateVotingRightEmail(companyId, request);
    
    return response as UpdateEmailResponse;
  } catch (error) {
    throw new Error(`Failed to update email: ${error}`);
  }
};

export const updateVotingRightEmailStatus = async (
  companyId: string, 
  request: UpdateEmailStatusRequest
): Promise<UpdateEmailStatusResponse> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.updateVotingRightEmailStatus(companyId, request);
    
    return response as UpdateEmailStatusResponse;
  } catch (error) {
    throw new Error(`Failed to update email status: ${error}`);
  }
};

const transformApiResponse = (apiData: VotingRightsApiResponse): VotingRightsData => {
  const shareholders = apiData.votingRights.map((votingRight) => ({
    id: votingRight.id,
    name: votingRight.shareHolder,
    email: votingRight.email || "No email available",
    shareType: mapShareTypeFromApi(votingRight.shareType),
    votingShares: votingRight.votingShares || 0,
    totalShares: votingRight.votingShares || 0,
    vestedShares: votingRight.votingShares || 0,
    isTerminated: false,
    votingPercentage: votingRight.votingPercentage || 0,
    votingPower: votingRight.votingPower as 'High' | 'Medium' | 'Low',
    canBlockMajority: votingRight.canBlockMajority || false,
    sendEmail: votingRight.sendEmail !== undefined ? votingRight.sendEmail : true,
    role: getRoleFromShareType(votingRight.shareType),
    companyServiceProviderId: votingRight.companyServiceProviderId,
    officerId: votingRight.officerId,
  }));

  const totalVotingShares = shareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  
  // Calculate email exclusion summary
  const includedShareholders = shareholders.filter(sh => sh.sendEmail);
  const excludedShareholders = shareholders.filter(sh => !sh.sendEmail);
  
  const includedVotingShares = includedShareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  const excludedVotingShares = excludedShareholders.reduce((sum, sh) => sum + sh.votingShares, 0);
  
  const includedVotingPercentage = totalVotingShares > 0 ? (includedVotingShares / totalVotingShares) * 100 : 0;
  const excludedVotingPercentage = totalVotingShares > 0 ? (excludedVotingShares / totalVotingShares) * 100 : 0;
  
  const isEmailExclusionValid = includedVotingPercentage >= 50;

  return {
    shareholders,
    totalVotingShares,
    majorityThreshold: totalVotingShares * 0.5,
    majorityThresholdPercentage: 50,
    lastUpdated: apiData.latestUpdatedDate,
    emailExclusionSummary: {
      includedVotingShares,
      excludedVotingShares,
      includedVotingPercentage,
      excludedVotingPercentage,
      isEmailExclusionValid,
      includedShareholders,
      excludedShareholders,
    },
  };
};

const mapShareTypeFromApi = (apiShareType: string): 'Common Stock' | 'Restricted Stock' | 'Exercised Options' => {
  const shareTypeMap: Record<string, 'Common Stock' | 'Restricted Stock' | 'Exercised Options'> = {
    'Common': 'Common Stock',
    'RestrictedStock': 'Restricted Stock',
    'ExercisedOptions': 'Exercised Options',
  };
  return shareTypeMap[apiShareType] || 'Common Stock';
};

const getRoleFromShareType = (shareType: string): string => {
  const roleMap: Record<string, string> = {
    'Common': 'Founder/Shareholder',
    'RestrictedStock': 'Employee/Advisor',
    'ExercisedOptions': 'Employee',
  };
  return roleMap[shareType] || 'Shareholder';
};
