import { APIClient } from '@/integrations/legal-concierge/client';
import { CapTableSummaryResponse, CapTableSummaryErrorResponse, CapTableSummaryData } from '@/types/capTable';

export class CapTableSummaryService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getCapTableSummary(companyId: string): Promise<CapTableSummaryResponse | CapTableSummaryErrorResponse> {
    try {
      const response = await this.apiClient.getCapTableSummary(companyId) as CapTableSummaryData | CapTableSummaryResponse;
      
      // If the response is the data directly, wrap it in the expected format
      if (response && typeof response === 'object' && 'shareholders' in response && !('message' in response)) {
        return {
          message: "Success",
          data: response as CapTableSummaryData,
          error: null,
          validationErrors: null,
        };
      }
      
      return response as CapTableSummaryResponse;
    } catch (error) {
      // Return error response in the expected format
      return {
        message: "Error loading cap table summary",
        data: null,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
        validationErrors: null,
      };
    }
  }
}

export const capTableSummaryService = new CapTableSummaryService();
