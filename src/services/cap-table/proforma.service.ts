import { apiClient } from '@/integrations/legal-concierge/client';
import { ProFormaModelData } from '@/types/capTable';

export interface ProFormaApiParams {
  PrePostMoneyValuation?: number;
  InvestmentAmount?: number;
  OptionPoolAvailableForIssuance?: number;
  CommonStockBuffer?: number;
  ValuationModeType?: 'fixed_pre_money' | 'fixed_post_money';
  PreferredStockDesignation?: 'SeriesA' | 'SeriesSeed';
  // Filter Parameters
  ProFormaRoleFilter?: 'All' | 'Founders' | 'Investors' | 'Advisors' | 'Contractors' | 'Employees';
  ProFormaShareClassFilter?: 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';
  ShareHolderName?: string;
}

export interface UpdateInvestorParams {
  id?: string; // Optional for new investors
  shareholderName: string;
  investmentAmount: number;
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}

export interface ProFormaApiResponse {
  message: string;
  data: {
    proformaSummaryMetrics: {
      effectivePrePostMoneyValuation: number;
      seriesPricePerShare: {
        [seriesKey: string]: {
          pricePerShare: number;
          shareIssued: number;
        };
      };
    };
    authorizedStockBreakdown: {
      commonStock: number;
      preferredStock: number;
      series: {
        [seriesKey: string]: number;
      };
      totalPool: number;
      poolRemainingForIssuance: number;
    };
    proFormaCapTable: {
      classACommonStockholders: Array<{
        id: string;
        serviceProviderId: string | null;
        officerId: string | null;
        shareholderName: string;
        role: string;
        commonStock: number;
        sopCommonStock: number;
        total: number;
        fullyDilutedOwnership: number;
        investmentAmount: number;
        newCommonStock: number;
        newSopCommonStock: number;
        newTotal: number;
        newFullyDilutedOwnership: number;
        actualWireAmount: number;
      }>;
      outstandingStockOptionPlanShares: {
        totalShares: number;
        fullyDilutedOwnership: number;
        newFullyDilutedOwnership: number;
      };
      promisedStockOptionPlanShares: {
        totalShares: number;
        fullyDilutedOwnership: number;
        newFullyDilutedOwnership: number;
      };
      stockOptionPlanSharesAvailable: {
        totalShares: number;
        fullyDilutedOwnership: number;
        newFullyDilutedOwnership: number;
      };
      sopPoolIncrease: {
        totalShares: number;
        fullyDilutedOwnership: number;
        newFullyDilutedOwnership: number;
      };
      preferredCommonStockholders: {
        [seriesKey: string]: {
          id: string | null;
          investmentAmount: number | null;
          investor: string | null;
          resultingShares: number;
          actualWireAmount: number;
          pricePerShare: number;
          newFullyDilutedOwnerShip: number;
        };
      };
    };
  };
  error: string | null;
  validationErrors: string | null;
}

export const proformaService = {
  async getProformaData(companyId: string, params: ProFormaApiParams): Promise<ProFormaApiResponse> {
    const response = await apiClient.getProformaData(companyId, params);
    return response as ProFormaApiResponse;
  },

  async updateInvestorInvestmentAmount(companyId: string, data: UpdateInvestorParams) {
    const response = await apiClient.updateInvestorInvestmentAmount(companyId, data);
    return response;
  },

  async deleteInvestorFromProforma(companyId: string, data: {
    id?: string;
    officerId?: string;
    serviceProviderId?: string;
    valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
  }) {
    const response = await apiClient.deleteInvestorFromProforma(companyId, data);
    return response;
  }
};
