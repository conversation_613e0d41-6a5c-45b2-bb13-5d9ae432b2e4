import { apiClient } from '@/integrations/legal-concierge/client';

export interface UpdateCapTableItemPayload {
  officerId: string;
  serviceProviderId: string | null;
  commonStock: number;
  sopShares: number;
}

export const updateCapTableItem = async (
  companyId: string, 
  payload: UpdateCapTableItemPayload
): Promise<any> => {
  const response = await apiClient.updateCapTableItem(companyId, payload);
  return response;
};
