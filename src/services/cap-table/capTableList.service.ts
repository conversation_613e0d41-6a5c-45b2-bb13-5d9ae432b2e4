import { APIClient } from '@/integrations/legal-concierge/client';
import { CapTableListResponse, CapTableListErrorResponse, CapTableListItem, CapTableListApiResponse } from '@/types/capTable';

export class CapTableListService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  async getCapTableList(companyId: string, filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }): Promise<CapTableListResponse | CapTableListErrorResponse> {
    try {
      const response = await this.apiClient.getCapTableList(companyId, filters) as any;
      
      if (response && response.data && Array.isArray(response.data)) {
        // Transform the data structure to the expected format
        const transformedData = response.data.map((item: any) => ({
          ...item,
          // Add SOP pool data to each item for easy access
          outstandingStockOptionPlanShares: response.outstandingStockOptionPlanShares,
          promisedStockOptionPlanShares: response.promisedStockOptionPlanShares,
          stockOptionPlanSharesAvailable: response.stockOptionPlanSharesAvailable,
          latestUpdatedDate: response.latestUpdatedDate,
        }));
        

        
        return {
          message: "Success",
          data: transformedData,
          error: null,
          validationErrors: null,
        };
      }
      
      // Fallback for old format
      if (Array.isArray(response)) {
        return {
          message: "Success",
          data: response,
          error: null,
          validationErrors: null,
        };
      }
      

      return {
        message: "Success",
        data: [],
        error: null,
        validationErrors: null,
      };
    } catch (error) {

      // Return error response in the expected format
      return {
        message: "Error loading cap table list",
        data: null,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
        validationErrors: null,
      };
    }
  }
}

export const capTableListService = new CapTableListService();
