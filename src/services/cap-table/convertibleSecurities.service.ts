import { APIClient } from "@/integrations/legal-concierge/client";
import { 
  ConvertibleSecuritiesApiResponse, 
  ConvertibleSecuritiesData,
  ConvertibleSecurity 
} from "@/types/capTable";

export const getConvertibleSecurities = async (companyId: string): Promise<ConvertibleSecuritiesData> => {
  try {
    const apiClient = new APIClient();
    const response = await apiClient.getConvertibleSecurities(companyId);
    
    // The API client returns the data directly, not wrapped in error/data structure
    return transformApiResponse(response as ConvertibleSecuritiesApiResponse);
  } catch (error) {
    throw new Error(`Failed to fetch convertible securities: ${error}`);
  }
};

const transformApiResponse = (apiData: ConvertibleSecuritiesApiResponse): ConvertibleSecuritiesData => {
  return {
    securities: apiData.convertibles.map((convertible, index) => ({
      id: convertible.id || `convertible-${index + 1}`,
      investor: convertible.name || "-",
      securityType: convertible.issueRoundType === 'authorized-safe' ? 'SAFE' : 'Convertible Note',
      principal: convertible.principal || 0,
      interestRate: convertible.interestRate || 0,
      issueDate: convertible.issueDate || "-",
      maturityDate: convertible.maturityDate || undefined,
      valuationCap: convertible.valuationCap || 0,
      discount: convertible.discount || 0,
      interestAccrued: convertible.interestAccrued || 0,
      totalValue: convertible.totalValue || 0,
      conversionPrice: 0, // Not provided in new API response
      estimatedShares: 0, // Not provided in new API response
      // Additional fields for backend processing
      mfn: convertible.mfn || false,
      qft: convertible.qft || 0,
      issueRoundType: convertible.issueRoundType || "-",
    })),
    summary: {
      totalPrincipal: apiData.totalPrincipal || 0,
      safes: apiData.safes || 0,
      convertibleNotes: apiData.convertibleNotes || 0,
      totalInvestors: apiData.totalInvestors || 0,
      lastUpdated: apiData.latestUpdatedDate || "-",
    }
  };
};
