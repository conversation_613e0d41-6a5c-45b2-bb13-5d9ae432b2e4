export interface S3UploadResult {
  success: boolean;
  error?: string;
}

export const uploadFileToS3 = async (
  file: File,
  uploadUrl: string
): Promise<S3UploadResult> => {
  try {
    const response = await fetch(uploadUrl, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": file.type,
      },
    });

    if (!response.ok) {
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    };
  }
};

export const generateFileName = (originalName: string): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, -5);
  const extension = originalName.substring(originalName.lastIndexOf("."));
  const baseName = originalName.substring(0, originalName.lastIndexOf("."));
  
  return `${baseName}-${timestamp}${extension}`;
};
