import { ProFormaInvestment, ProRataRightsData } from "@/types/capTable";

// Mock Pro Forma investment
export const mockProFormaInvestment: ProFormaInvestment = {
  id: "pf1",
  name: "Series B Round",
  amount: 5000000,
  pricePerShare: 1.25,
  shareClass: "Series A Preferred",
  date: new Date("2023-10-30"),
  preMoneyValuation: 20000000,
  postMoneyValuation: 25000000,
  dilutedPoolPercentage: 10,
  isUserEdited: false,
  isAmountEdited: false,
};

// Mock Pro Rata Rights data with updated column names
export const mockProRataRights: ProRataRightsData = {
  items: [
    {
      id: "1",
      investorName: "Series A Investors",
      currentOwnershipPercentage: 20.00,
      proRataAmount: 2000000,
      numberOfSeriesAShares: 400000,
      isExistingInvestor: true,
    },
    {
      id: "2",
      investorName: "Series B Investors",
      currentOwnershipPercentage: 15.00,
      proRataAmount: 1500000,
      numberOfSeriesAShares: 300000,
      isExistingInvestor: true,
    },
    {
      id: "3",
      investorName: "Founder 1",
      currentOwnershipPercentage: 25.50,
      proRataAmount: 2550000,
      numberOfSeriesAShares: 510000,
      isExistingInvestor: true,
    },
  ],
  totalProRataAmount: 6050000,
  roundType: "Series A",
  pricePerShare: 5.00,
};
