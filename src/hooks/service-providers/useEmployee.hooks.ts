import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { addCompanyEmployee, getActiveEmployees, getPendingEmployees, getTerminatedEmployees } from "@/services/service-providers/employees.service";
import { AddEmployeePayload } from "@/types/serviceProvider";
import { toast } from "sonner";
import { ColumnDef } from "@tanstack/react-table";

export function useAddCompanyEmployee() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ companyId, payload }: { companyId: string; payload: AddEmployeePayload }) =>
      addCompanyEmployee(companyId, payload),
    onSuccess: (data, variables) => {
      toast.success("Employee added successfully");
      queryClient.invalidateQueries({ queryKey: ["activeEmployees"] });
      // Refetch the status API
      queryClient.invalidateQueries({ queryKey: ["serviceProviderStatus", variables.companyId] });
    },
    onError: (error: any) => {
      console.error("Employee API Error:", error);
      if (typeof error === "string") {
        toast.error(error);
        return;
      }
      if (error?.validationErrors) {
        const messages = Object.values(error.validationErrors)
          .flat()
          .join("\n");
        toast.error(messages);
        return;
      }
      if (error?.message) {
        toast.error(error.message);
        return;
      }
      toast.error("Failed to add employee. Please try again.");
    },
  });
}

export function useActiveEmployees(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["activeEmployees", companyId],
    queryFn: () => getActiveEmployees(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function usePendingEmployees(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["pendingEmployees", companyId],
    queryFn: () => getPendingEmployees(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function useTerminatedEmployees(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["terminatedEmployees", companyId],
    queryFn: () => getTerminatedEmployees(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function getEmployeeTableColumns(): ColumnDef<any>[] {
  return [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "email", header: "Email" },
    { accessorKey: "grantType", header: "Grant Type" },
    { accessorKey: "services", header: "Services" },
    { accessorKey: "shares", header: "Shares" },
  ];
} 