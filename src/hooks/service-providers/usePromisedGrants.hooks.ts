import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { getStockOptionInformation } from "@/services/service-providers/stockOptionInfo.service";
import { 
  getPromisedGrants,
  PromisedGrant,
  updatePromisedGrant,
  deletePromisedGrant,
  getActive409AValuation,
  Active409AValuation,
  setFairMarketValue,
  get409aUploadPresignedUrl,
  submit409aValuation,
  issuePromisedGrant,
} from "@/services/service-providers/promisedGrants.service";
import { toast } from "sonner";

interface StockOptionInformation {
  totalPoolSize: number;
  allocated: number;
  remaining: number;
}

export function useStockOptionInformation(companyId: string) {
  return useQuery<StockOptionInformation>({
    queryKey: ["stockOptionInformation", companyId],
    queryFn: () => getStockOptionInformation(companyId),
    enabled: !!companyId,
  });
}

export function usePromisedGrants(companyId: string) {
  return useQuery<PromisedGrant[]>({
    queryKey: ["promisedGrants", companyId],
    queryFn: () => getPromisedGrants(companyId),
    enabled: !!companyId,
  });
}

export function useUpdatePromisedGrant() {
  return useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: {
        name: string;
        grantType: string;
        shares: number;
        vestingSchedule: string | null;
        pricePerShare: number;
        compensation?: number;
        vestingPeriod?: number;
        cliff?: number;
        term?: number;
      };
    }) => updatePromisedGrant(id, payload),
  });
}

export function useDeletePromisedGrant() {
  return useMutation({
    mutationFn: (id: string) => deletePromisedGrant(id),
  });
}

export function useActive409AValuation(companyId: string) {
  return useQuery<Active409AValuation | null>({
    queryKey: ["active409AValuation", companyId],
    queryFn: () => getActive409AValuation(companyId),
    enabled: !!companyId,
  });
}

// Set FMV Hook
export function useSetFairMarketValue(companyId: string) {
  const queryClient = useQueryClient();
  const mutation = useMutation({
    mutationFn: (value: number) => setFairMarketValue(companyId, value),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["active409AValuation", companyId] });
      toast.success("Fair market value updated");
    },
    onError: (err: any) => {
      const message = err?.response?.data?.data?.[0]?.description || err?.message || "Failed to update FMV";
      toast.error(message);
    },
  });

  return {
    setFMV: (value: number) => mutation.mutateAsync(value),
    isSettingFMV: mutation.isPending,
  };
}

// 409A upload helpers
export function use409aUpload(companyId: string) {
  const getPresignedUrl = async (file: File) => {
    if (!file) throw new Error("No file selected");
    if (file.size > 5 * 1024 * 1024) throw new Error("File size must be less than 5MB");
    if (!/(pdf|doc|docx)$/i.test(file.name)) throw new Error("Invalid file type");
    const data = await get409aUploadPresignedUrl(companyId, file.name, file.type || 'application/octet-stream');
    return data; // { uploadUrl, key }
  };
  const uploadToS3 = async (uploadUrl: string, file: File, contentType: string) => {
    const res = await fetch(uploadUrl, {
      method: 'PUT',
      headers: { 'Content-Type': contentType },
      body: file,
    });
    if (!res.ok) throw new Error('Upload failed');
  };
  return { getPresignedUrl, uploadToS3 };
}

export function useSubmit409a(companyId: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: {
      appraiserName: string;
      appraisalDate: string;
      fairMarketValue: number;
      confirmationStatus: 'yes' | 'no';
      s3Key: string;
    }) => submit409aValuation(companyId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["active409AValuation", companyId] });
      toast.success('409A valuation sent for approval');
    },
    onError: (err: any) => {
      const message = err?.response?.data?.data?.[0]?.description || err?.message || 'Failed to submit 409A valuation';
      toast.error(message);
    },
  });
}

// Row Selection Hook
export function useRowSelection() {
  const [selectedGrants, setSelectedGrants] = useState<Set<string>>(new Set());

  const handleSelectAll = (grantIds: string[], checked: boolean) => {
    if (checked) {
      setSelectedGrants(new Set(grantIds));
    } else {
      setSelectedGrants(new Set());
    }
  };

  const handleSelectGrant = (grantId: string, checked: boolean) => {
    const newSelected = new Set(selectedGrants);
    if (checked) {
      newSelected.add(grantId);
    } else {
      newSelected.delete(grantId);
    }
    setSelectedGrants(newSelected);
  };

  const clearSelection = () => {
    setSelectedGrants(new Set());
  };

  return {
    selectedGrants,
    selectedCount: selectedGrants.size,
    handleSelectAll,
    handleSelectGrant,
    clearSelection,
  };
}

// Grant Editing Hook
export function useGrantEditing() {
  const [editingGrantId, setEditingGrantId] = useState<string | null>(null);
  const [editedGrantData, setEditedGrantData] = useState<Partial<PromisedGrant> | null>(null);
  // Persist locally saved edits by grant id so issuance can use them
  const [localEdits, setLocalEdits] = useState<Record<string, Partial<PromisedGrant>>>({});
  const updateGrantMutation = useUpdatePromisedGrant();

  const handleEditClick = (grant: PromisedGrant) => {
    // Prefill with any locally saved edits for a consistent experience
    const existing = localEdits[grant.id] || {};
    setEditingGrantId(grant.id);
    setEditedGrantData({ ...grant, ...existing });
  };

  const handleCancelClick = () => {
    setEditingGrantId(null);
    setEditedGrantData(null);
  };

  const handleSaveClick = () => {
    if (!editingGrantId || !editedGrantData) return;

    // Save the edited data locally so later issuance uses the latest values
    setLocalEdits((prev) => ({ ...prev, [editingGrantId]: editedGrantData }));

    // Exit edit mode without API call
    setEditingGrantId(null);
    setEditedGrantData(null);
  };

  const prepareGrantPayload = (
    grant: PromisedGrant, 
    editedData?: Partial<PromisedGrant>,
    customVestingData?: { vestingPeriod: string; cliff: string; compensation: string }
  ) => {
    // Use edited data if available, otherwise use original grant data
    const dataToUse = editedData ? { ...grant, ...editedData } : grant;
    
    const basePayload = {
      name: dataToUse.name,
      grantType: dataToUse.grantType || "",
      shares: dataToUse.shares || 0,
      vestingSchedule: dataToUse.vestingSchedule || null,
      pricePerShare: dataToUse.pricePerShare || 0,
    };

    // Add custom vesting data if available
    if (customVestingData && dataToUse.vestingSchedule === "custom") {
      return {
        ...basePayload,
        compensation: parseFloat(customVestingData.compensation) || 0,
        vestingPeriod: parseFloat(customVestingData.vestingPeriod) || 0,
        cliff: parseFloat(customVestingData.cliff) || 0,
        term: 0, // Default value
      };
    }

    return basePayload;
  };

  const handleFieldChange = (
    field: keyof PromisedGrant,
    value: string | number
  ) => {
    if (editedGrantData) {
      setEditedGrantData({ ...editedGrantData, [field]: value });
    }
  };

  return {
    editingGrantId,
    editedGrantData,
    localEdits,
    setLocalEdits,
    isUpdating: updateGrantMutation.isPending,
    handleEditClick,
    handleCancelClick,
    handleSaveClick,
    handleFieldChange,
    prepareGrantPayload,
  };
}

// Grant Deletion Hook
export function useGrantDeletion() {
  const [deletingGrantId, setDeletingGrantId] = useState<string | null>(null);
  const deleteGrantMutation = useDeletePromisedGrant();

  const handleDeleteClick = (grant: PromisedGrant) => {
    setDeletingGrantId(grant.id);
  };

  const handleConfirmDelete = () => {
    if (!deletingGrantId) return;

    deleteGrantMutation.mutate(deletingGrantId, {
      onSuccess: () => {
        setDeletingGrantId(null);
        toast.success("Grant deleted successfully!");
      },
      onError: (error) => {
        console.error("Error deleting grant:", error);
        toast.error("Failed to delete grant. Please try again.");
      },
    });
  };

  const handleCancelDelete = () => {
    setDeletingGrantId(null);
  };

  return {
    deletingGrantId,
    isDeleting: deleteGrantMutation.isPending,
    handleDeleteClick,
    handleConfirmDelete,
    handleCancelDelete,
  };
}

// 409A Dialog Hook
export function use409ADialog() {
  const [is409ADialogOpen, setIs409ADialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'set-fair-market-value' | 'update-409a' | 'use-current-409a' | 'issue-workflow'>('issue-workflow');

  const open409ADialog = (mode: 'set-fair-market-value' | 'update-409a' | 'use-current-409a' | 'issue-workflow' = 'issue-workflow') => {
    setDialogMode(mode);
    setIs409ADialogOpen(true);
  };

  const close409ADialog = () => {
    setIs409ADialogOpen(false);
  };

  return {
    is409ADialogOpen,
    dialogMode,
    open409ADialog,
    close409ADialog,
  };
}

// Board Approval Hook
export function useBoardApproval() {
  const [isBoardApprovalDialogOpen, setIsBoardApprovalDialogOpen] = useState(false);
  const [isSubmittingApproval, setIsSubmittingApproval] = useState(false);

  const handleBoardApprovalClick = () => {
    setIsBoardApprovalDialogOpen(true);
  };

  const handleSubmitForBoardApproval = async () => {
    setIsSubmittingApproval(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success(
        "Successfully submitted for board approval. Directors will be notified."
      );
      setIsBoardApprovalDialogOpen(false);
    } catch (error) {
      console.error("Error submitting for board approval:", error);
      toast.error("Failed to submit for board approval. Please try again.");
    } finally {
      setIsSubmittingApproval(false);
    }
  };

  return {
    isBoardApprovalDialogOpen,
    isSubmittingApproval,
    handleBoardApprovalClick,
    handleSubmitForBoardApproval,
    closeBoardApproval: () => setIsBoardApprovalDialogOpen(false),
  };
}

// Equity Issue Dialog Hook
export function useEquityIssueDialog() {
  const [selectedGrant, setSelectedGrant] = useState<PromisedGrant | null>(null);
  const [isIssueDialogOpen, setIsIssueDialogOpen] = useState(false);

  const handleIssueClick = (grant: PromisedGrant) => {
    setSelectedGrant(grant);
    setIsIssueDialogOpen(true);
  };

  const handleIssueDialogClose = () => {
    setIsIssueDialogOpen(false);
    setSelectedGrant(null);
  };

  return {
    selectedGrant,
    isIssueDialogOpen,
    handleIssueClick,
    handleIssueDialogClose,
  };
}

// Modify Grants Dialog Hook
export function useModifyGrantsDialog() {
  const [isModifyDialogOpen, setIsModifyDialogOpen] = useState(false);

  const openModifyDialog = () => {
    setIsModifyDialogOpen(true);
  };

  const closeModifyDialog = () => {
    setIsModifyDialogOpen(false);
  };

  return {
    isModifyDialogOpen,
    openModifyDialog,
    closeModifyDialog,
  };
}

// Custom Vesting Dialog Hook
export function useCustomVestingDialog() {
  const [isCustomVestingDialogOpen, setIsCustomVestingDialogOpen] = useState(false);
  const [customVestingData, setCustomVestingData] = useState<Record<string, { vestingPeriod: string; cliff: string; compensation: string }>>({});
  const [currentGrantId, setCurrentGrantId] = useState<string>("");
  const [currentGrantName, setCurrentGrantName] = useState<string>("");

  const openCustomVestingDialog = (grantId: string, grantName: string) => {
    setCurrentGrantId(grantId);
    setCurrentGrantName(grantName);
    setIsCustomVestingDialogOpen(true);
  };

  const closeCustomVestingDialog = () => {
    setIsCustomVestingDialogOpen(false);
  };

  const saveCustomVestingData = (grantId: string, data: { vestingPeriod: string; cliff: string; compensation: string }) => {
    setCustomVestingData(prev => ({ ...prev, [grantId]: data }));
  };

  const getCustomVestingData = (grantId: string) => {
    return customVestingData[grantId];
  };

  return {
    isCustomVestingDialogOpen,
    currentGrantId,
    currentGrantName,
    openCustomVestingDialog,
    closeCustomVestingDialog,
    saveCustomVestingData,
    getCustomVestingData,
  };
}

// Grant Issuance Hook
export function useGrantIssuance() {
  const [isIssuing, setIsIssuing] = useState(false);
  const updateGrantMutation = useUpdatePromisedGrant();
  const queryClient = useQueryClient();

  const handleSaveAndClose = async (
    grants: PromisedGrant[],
    localEdits: Record<string, Partial<PromisedGrant>> = {}
  ) => {
    setIsIssuing(true);
    const results: Array<{ grantId: string; success: boolean; error?: any }> = [];
    
    try {
      // Process grants sequentially for better error handling and status tracking
      for (const grant of grants) {
        try {
          const editedData = localEdits[grant.id];
          const payload = editedData ? { ...grant, ...editedData } : grant;
          
          await updateGrantMutation.mutateAsync({
            id: grant.id,
            payload: {
              name: payload.name,
              grantType: payload.grantType,
              shares: payload.shares,
              vestingSchedule: payload.vestingSchedule,
              pricePerShare: payload.pricePerShare,
            },
          });
          
          results.push({ grantId: grant.id, success: true });
        } catch (error) {
          results.push({ grantId: grant.id, success: false, error });
          // Continue with next grant instead of stopping
        }
      }
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      // Only refetch after ALL grants have been processed
      if (successCount > 0) {
        queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
        queryClient.invalidateQueries({ queryKey: ["stockOptionInformation"] });
      }
      
      if (failureCount === 0) {
        toast.success(`All ${successCount} grants saved successfully!`);
        return { success: true, results };
      } else {
        toast.error(`${successCount} grants saved, ${failureCount} failed. Check details.`);
        return { success: false, results };
      }
    } catch (error) {
      console.error("Error saving grants:", error);
      toast.error("Failed to save grants. Please try again.");
      return { success: false, results };
    } finally {
      setIsIssuing(false);
    }
  };

  return {
    isIssuing,
    handleSaveAndClose,
  };
}

// Main Promised Grants Hook
export function usePromisedGrantsManager(companyId: string) {
  const { data: initialPromisedGrants, isLoading: isLoadingGrants } = usePromisedGrants(companyId);
  const [promisedGrants, setPromisedGrants] = useState<PromisedGrant[]>([]);

  useEffect(() => {
    if (initialPromisedGrants) {
      setPromisedGrants(initialPromisedGrants);
    }
  }, [initialPromisedGrants]);

  const pendingGrantsCount = promisedGrants?.filter((grant) => grant.boardApproved === "pending").length || 0;

  return {
    promisedGrants,
    isLoadingGrants,
    pendingGrantsCount,
  };
}

// Issue Promised Grants Hook
export function useIssuePromisedGrants() {
  const [isIssuing, setIsIssuing] = useState(false);
  const queryClient = useQueryClient();

  const handleIssueGrants = async (grants: PromisedGrant[], companyId: string) => {
    setIsIssuing(true);
    const results: Array<{ grantId: string; success: boolean; error?: any }> = [];
    
    try {
      // Process grants sequentially for better error handling and status tracking
      for (const grant of grants) {
        try {
          await issuePromisedGrant(companyId, grant.id);
          results.push({ grantId: grant.id, success: true });
        } catch (error) {
          results.push({ grantId: grant.id, success: false, error });
          // Continue with next grant instead of stopping
        }
      }
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      // Only refetch after ALL grants have been processed
      if (successCount > 0) {
        queryClient.invalidateQueries({ queryKey: ["promisedGrants"] });
        queryClient.invalidateQueries({ queryKey: ["stockOptionInformation"] });
      }
      
      if (failureCount === 0) {
        toast.success(`All ${successCount} grants issued successfully!`);
        return { success: true, results };
      } else {
        toast.error(`${successCount} grants issued, ${failureCount} failed. Check details.`);
        return { success: false, results };
      }
    } catch (error) {
      console.error("Error issuing grants:", error);
      toast.error("Failed to issue grants. Please try again.");
      return { success: false, results };
    } finally {
      setIsIssuing(false);
    }
  };

  return {
    isIssuing,
    handleIssueGrants,
  };
}
