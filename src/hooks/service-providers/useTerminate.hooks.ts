import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  getPendingServiceProviders, 
  terminateServiceProvider,
  getTerminatedServiceProviders,
  getPendingTerminatedServiceProviders
} from "@/services/service-providers/terminateServiceProvider.service";

export const usePendingServiceProviders = (companyId: string, options = {}) => {
  return useQuery({
    queryKey: ["pendingServiceProviders", companyId],
    queryFn: () => getPendingServiceProviders(companyId),
    enabled: !!companyId,
    ...options,
  });
};

export const useTerminateServiceProvider = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (payload: {
      id: string;
      terminationDate: string;
      serviceProviderTerminationType: string;
      severanceAmount: number;
      reasonForTermination: string;
    }) => terminateServiceProvider(payload),
    onSuccess: () => {
      // Invalidate and refetch pending service providers
      queryClient.invalidateQueries({ queryKey: ["pendingServiceProviders"] });
      // Invalidate and refetch service provider status to update counts
      queryClient.invalidateQueries({ queryKey: ["serviceProviderStatus"] });
      // Invalidate terminated lists
      queryClient.invalidateQueries({ queryKey: ["terminatedServiceProviders"] });
      queryClient.invalidateQueries({ queryKey: ["pendingTerminatedServiceProviders"] });
    },
  });
};

export const useTerminatedServiceProviders = (companyId: string, options = {}) => {
  return useQuery({
    queryKey: ["terminatedServiceProviders", companyId],
    queryFn: () => getTerminatedServiceProviders(companyId),
    enabled: !!companyId,
    ...options,
  });
};

export const usePendingTerminatedServiceProviders = (companyId: string, options = {}) => {
  return useQuery({
    queryKey: ["pendingTerminatedServiceProviders", companyId],
    queryFn: () => getPendingTerminatedServiceProviders(companyId),
    enabled: !!companyId,
    ...options,
  });
};
