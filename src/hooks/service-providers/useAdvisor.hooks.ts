import { useMutation, useQuery,useQueryClient } from "@tanstack/react-query";
import { addCompanyAdvisor } from "@/services/service-providers/advisors.service";
import { AddAdvisorPayload } from "@/types/serviceProvider";
import { toast } from "sonner";
import { getActiveAdvisors, getPendingAdvisors, getTerminatedAdvisors } from "@/services/service-providers/advisors.service";
import { ColumnDef } from "@tanstack/react-table";

export function useAddCompanyAdvisor() {
  const queryClient = useQueryClient();


  return useMutation({
    mutationFn: ({ companyId, payload }: { companyId: string; payload: AddAdvisorPayload }) =>
      addCompanyAdvisor(companyId, payload),
    onSuccess: (data, variables) => {
      toast.success("Advisor added successfully");
      queryClient.invalidateQueries({ queryKey: ["activeAdvisors"] });
      // Refetch the status API
      queryClient.invalidateQueries({ queryKey: ["serviceProviderStatus", variables.companyId] });
    },
    onError: (error: any) => {
      
      // If error is a string, show it directly
      if (typeof error === "string") {
        toast.error(error);
        return;
      }
      
      // If error is an object with validationErrors
      if (error?.validationErrors) {
        const messages = Object.values(error.validationErrors)
          .flat()
          .join("\n");
        toast.error(messages);
        return;
      }
      
      // If error has a message
      if (error?.message) {
        toast.error(error.message);
        return;
      }
      
      // Fallback error message
      toast.error("Failed to add advisor. Please try again.");
    },
  });
}

export function useActiveAdvisors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["activeAdvisors", companyId],
    queryFn: () => getActiveAdvisors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function usePendingAdvisors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["pendingAdvisors", companyId],
    queryFn: () => getPendingAdvisors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function useTerminatedAdvisors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["terminatedAdvisors", companyId],
    queryFn: () => getTerminatedAdvisors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function getAdvisorTableColumns(): ColumnDef<any>[] {
  return [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "email", header: "Email" },
    { accessorKey: "grantType", header: "Grant Type" },
    { accessorKey: "services", header: "Services" },
    { accessorKey: "shares", header: "Shares" },
  ];
}
