import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { addCompanyContractor, getActiveContractors, getPendingContractors, getTerminatedContractors } from "@/services/service-providers/contractors.service";
import { AddContractorPayload } from "@/types/serviceProvider";
import { toast } from "sonner";
import { ColumnDef } from "@tanstack/react-table";

export function useAddCompanyContractor() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ companyId, payload }: { companyId: string; payload: AddContractorPayload }) =>
      addCompanyContractor(companyId, payload),
    onSuccess: (data, variables) => {
      toast.success("Contractor added successfully");
      queryClient.invalidateQueries({ queryKey: ["activeContractors"] });
      // Refetch the status API
      queryClient.invalidateQueries({ queryKey: ["serviceProviderStatus", variables.companyId] });
    },
    onError: (error: any) => {
      console.error("Contractor API Error:", error);
      if (typeof error === "string") {
        toast.error(error);
        return;
      }
      if (error?.validationErrors) {
        const messages = Object.values(error.validationErrors)
          .flat()
          .join("\n");
        toast.error(messages);
        return;
      }
      if (error?.message) {
        toast.error(error.message);
        return;
      }
      toast.error("Failed to add contractor. Please try again.");
    },
  });
}

export function useActiveContractors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["activeContractors", companyId],
    queryFn: () => getActiveContractors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function usePendingContractors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["pendingContractors", companyId],
    queryFn: () => getPendingContractors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function useTerminatedContractors(companyId: string, options = {}) {
  return useQuery({
    queryKey: ["terminatedContractors", companyId],
    queryFn: () => getTerminatedContractors(companyId),
    enabled: !!companyId,
    ...options,
  });
}

export function getContractorTableColumns(): ColumnDef<any>[] {
  return [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "email", header: "Email" },
    { accessorKey: "grantType", header: "Grant Type" },
    { accessorKey: "services", header: "Services" },
    { accessorKey: "shares", header: "Shares" },
  ];
} 