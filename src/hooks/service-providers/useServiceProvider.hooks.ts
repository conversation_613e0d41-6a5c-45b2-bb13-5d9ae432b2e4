import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useServiceProviderStatus } from "./useServiceProviderStatus.hooks";
import { useActiveAdvisors, usePendingAdvisors, useTerminatedAdvisors, getAdvisorTableColumns } from "./useAdvisor.hooks";
import { useActiveEmployees, usePendingEmployees, useTerminatedEmployees, getEmployeeTableColumns } from "./useEmployee.hooks";
import { useActiveContractors, usePendingContractors, useTerminatedContractors, getContractorTableColumns } from "./useContractor.hooks";
import { useTerminatedServiceProviders, usePendingTerminatedServiceProviders } from "./useTerminate.hooks";
import { terminatedServiceProviderColumns } from "@/components/service-providers/terminateColumns";

type ServiceProviderType = 'advisor' | 'employee' | 'contractor' | 'terminate';
type ServiceProviderStatus = 'active' | 'pending' | 'terminated';

type StatusDialogState = {
  type: ServiceProviderType;
  status: ServiceProviderStatus;
} | null;

interface DialogConfig {
  title: string;
  data: any[];
  columns: any[];
  loading: boolean;
}

export function useServiceProviderDialogs() {
  const { user } = useAuth();
  const companyId = user?.companyId || "";
  
  // Status data
  const { data: statusData, isLoading } = useServiceProviderStatus(companyId);
  
  // Dialog state
  const [statusDialog, setStatusDialog] = React.useState<StatusDialogState>(null);
  
  // Data fetching hooks
  const advisorData = useAdvisorData(companyId, statusDialog);
  const employeeData = useEmployeeData(companyId, statusDialog);
  const contractorData = useContractorData(companyId, statusDialog);
  const terminateData = useTerminateData(companyId, statusDialog);
  
  // Counts
  const advisorCounts = (statusData as any)?.advisors || { active: 0, pending: 0, terminated: 0 };
  const contractorCounts = (statusData as any)?.contractors || { active: 0, pending: 0, terminated: 0 };
  const employeeCounts = (statusData as any)?.employees || { active: 0, pending: 0, terminated: 0 };
  const terminateCounts = (statusData as any)?.terminated || { pending: 0, terminated: 0 };
  
  // Dialog handlers
  const handleStatusDialogOpen = React.useCallback((type: ServiceProviderType, status: ServiceProviderStatus) => {
    setStatusDialog({ type, status });
  }, []);
  
  const handleStatusDialogClose = React.useCallback(() => {
    setStatusDialog(null);
  }, []);
  
  // Get current dialog configuration
  const getDialogConfig = (): DialogConfig => {
    if (!statusDialog) {
      return { title: '', data: [], columns: [], loading: false };
    }
    
    const { type, status } = statusDialog;
    
    if (type === 'advisor') {
      return {
        title: `${status.charAt(0).toUpperCase() + status.slice(1)} Advisors`,
        data: advisorData.data,
        columns: getAdvisorTableColumns(),
        loading: advisorData.loading
      };
    }
    
    if (type === 'employee') {
      return {
        title: `${status.charAt(0).toUpperCase() + status.slice(1)} Employees`,
        data: employeeData.data,
        columns: getEmployeeTableColumns(),
        loading: employeeData.loading
      };
    }
    
    if (type === 'contractor') {
      return {
        title: `${status.charAt(0).toUpperCase() + status.slice(1)} Contractors`,
        data: contractorData.data,
        columns: getContractorTableColumns(),
        loading: contractorData.loading
      };
    }
    
    if (type === 'terminate') {
      return {
        title: `${status.charAt(0).toUpperCase() + status.slice(1)} Terminated Service Providers`,
        data: terminateData.data,
        columns: terminatedServiceProviderColumns,
        loading: terminateData.loading
      };
    }
    
    return { title: '', data: [], columns: [], loading: false };
  };
  
  return {
    // State
    statusDialog,
    isLoading,
    
    // Data
    advisorCounts,
    contractorCounts,
    employeeCounts,
    terminateCounts,
    
    // Handlers
    handleStatusDialogOpen,
    handleStatusDialogClose,
    
    // Dialog config
    getDialogConfig,
  };
}

// Helper hooks for data fetching
function useAdvisorData(companyId: string, statusDialog: StatusDialogState) {
  const { data: rawActiveAdvisors, isLoading: loadingActiveAdvisors } = useActiveAdvisors(companyId, {
    enabled: statusDialog?.type === 'advisor' && statusDialog?.status === 'active' && !!companyId,
  });
  const { data: rawPendingAdvisors, isLoading: loadingPendingAdvisors } = usePendingAdvisors(companyId, {
    enabled: statusDialog?.type === 'advisor' && statusDialog?.status === 'pending' && !!companyId,
  });
  const { data: rawTerminatedAdvisors, isLoading: loadingTerminatedAdvisors } = useTerminatedAdvisors(companyId, {
    enabled: statusDialog?.type === 'advisor' && statusDialog?.status === 'terminated' && !!companyId,
  });
  
  const getDataAndLoading = () => {
    if (statusDialog?.type !== 'advisor') return { data: [], loading: false };
    
    switch (statusDialog.status) {
      case 'active':
        return { data: Array.isArray(rawActiveAdvisors) ? rawActiveAdvisors : [], loading: loadingActiveAdvisors };
      case 'pending':
        return { data: Array.isArray(rawPendingAdvisors) ? rawPendingAdvisors : [], loading: loadingPendingAdvisors };
      case 'terminated':
        return { data: Array.isArray(rawTerminatedAdvisors) ? rawTerminatedAdvisors : [], loading: loadingTerminatedAdvisors };
      default:
        return { data: [], loading: false };
    }
  };
  
  return getDataAndLoading();
}

function useEmployeeData(companyId: string, statusDialog: StatusDialogState) {
  const { data: rawActiveEmployees, isLoading: loadingActiveEmployees } = useActiveEmployees(companyId, {
    enabled: statusDialog?.type === 'employee' && statusDialog?.status === 'active' && !!companyId,
  });
  const { data: rawPendingEmployees, isLoading: loadingPendingEmployees } = usePendingEmployees(companyId, {
    enabled: statusDialog?.type === 'employee' && statusDialog?.status === 'pending' && !!companyId,
  });
  const { data: rawTerminatedEmployees, isLoading: loadingTerminatedEmployees } = useTerminatedEmployees(companyId, {
    enabled: statusDialog?.type === 'employee' && statusDialog?.status === 'terminated' && !!companyId,
  });
  
  const getDataAndLoading = () => {
    if (statusDialog?.type !== 'employee') return { data: [], loading: false };
    
    switch (statusDialog.status) {
      case 'active':
        return { data: Array.isArray(rawActiveEmployees) ? rawActiveEmployees : [], loading: loadingActiveEmployees };
      case 'pending':
        return { data: Array.isArray(rawPendingEmployees) ? rawPendingEmployees : [], loading: loadingPendingEmployees };
      case 'terminated':
        return { data: Array.isArray(rawTerminatedEmployees) ? rawTerminatedEmployees : [], loading: loadingTerminatedEmployees };
      default:
        return { data: [], loading: false };
    }
  };
  
  return getDataAndLoading();
}

function useContractorData(companyId: string, statusDialog: StatusDialogState) {
  const { data: rawActiveContractors, isLoading: loadingActiveContractors } = useActiveContractors(companyId, {
    enabled: statusDialog?.type === 'contractor' && statusDialog?.status === 'active' && !!companyId,
  });
  const { data: rawPendingContractors, isLoading: loadingPendingContractors } = usePendingContractors(companyId, {
    enabled: statusDialog?.type === 'contractor' && statusDialog?.status === 'pending' && !!companyId,
  });
  const { data: rawTerminatedContractors, isLoading: loadingTerminatedContractors } = useTerminatedContractors(companyId, {
    enabled: statusDialog?.type === 'contractor' && statusDialog?.status === 'terminated' && !!companyId,
  });
  
  const getDataAndLoading = () => {
    if (statusDialog?.type !== 'contractor') return { data: [], loading: false };
    
    switch (statusDialog.status) {
      case 'active':
        return { data: Array.isArray(rawActiveContractors) ? rawActiveContractors : [], loading: loadingActiveContractors };
      case 'pending':
        return { data: Array.isArray(rawPendingContractors) ? rawPendingContractors : [], loading: loadingPendingContractors };
      case 'terminated':
        return { data: Array.isArray(rawTerminatedContractors) ? rawTerminatedContractors : [], loading: loadingTerminatedContractors };
      default:
        return { data: [], loading: false };
    }
  };
  
  return getDataAndLoading();
}

function useTerminateData(companyId: string, statusDialog: StatusDialogState) {
  const { data: rawTerminatedProviders, isLoading: loadingTerminated } = useTerminatedServiceProviders(companyId, {
    enabled: statusDialog?.type === 'terminate' && statusDialog?.status === 'terminated' && !!companyId,
  });
  const { data: rawPendingTerminatedProviders, isLoading: loadingPendingTerminated } = usePendingTerminatedServiceProviders(companyId, {
    enabled: statusDialog?.type === 'terminate' && statusDialog?.status === 'pending' && !!companyId,
  });
  
  const getDataAndLoading = () => {
    if (statusDialog?.type !== 'terminate') return { data: [], loading: false };
    
    switch (statusDialog.status) {
      case 'terminated':
        return { data: Array.isArray(rawTerminatedProviders) ? rawTerminatedProviders : [], loading: loadingTerminated };
      case 'pending':
        return { data: Array.isArray(rawPendingTerminatedProviders) ? rawPendingTerminatedProviders : [], loading: loadingPendingTerminated };
      default:
        return { data: [], loading: false };
    }
  };
  
  return getDataAndLoading();
}