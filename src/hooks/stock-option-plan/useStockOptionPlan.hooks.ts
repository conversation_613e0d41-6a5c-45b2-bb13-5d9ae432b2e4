import { useQuery, useMutation, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import { stockOptionPlanService, StockOptionPlanSummary } from "@/services/stock-option-plan/stockOptionPlan.service";
import { toast } from "sonner";

export interface UseStockOptionPlanReturn {
  data: StockOptionPlanSummary | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (additionalShares: number) => Promise<void>;
  submitForStockholderApproval: () => Promise<void>;
  abandonProcess: () => Promise<void>;
}

export const useStockOptionPlan = (companyId: string, enabled: boolean = true): UseStockOptionPlanReturn => {
  const queryClient = useQueryClient();

  const { data: response, isLoading, error, refetch } = useQuery({
    queryKey: ["stockOptionPlan", companyId],
    queryFn: () => stockOptionPlanService.getSummary(companyId),
    enabled: !!companyId && enabled,
    staleTime: 30000,
    retry: 2,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    placeholderData: keepPreviousData,
  });

  const boardApprovalMutation = useMutation({
    mutationFn: (additionalShares: number) => stockOptionPlanService.submitForBoardApproval(companyId, additionalShares),
    onSuccess: () => {
      toast.success("Board Approval Submitted Successfully");
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlan", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  const stockholderApprovalMutation = useMutation({
    mutationFn: () => stockOptionPlanService.submitForStockholderApproval(companyId),
    onSuccess: () => {
      toast.success("Stockholder Approval Submitted Successfully");
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlan", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  const abandonMutation = useMutation({
    mutationFn: () => stockOptionPlanService.abandonProcess(companyId),
    onSuccess: () => {
      toast.success("Process Abandoned Successfully");
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlan", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  return {
    data: response || null,
    // exclude board approval pending from the global loading to avoid full dialog spinner
    isLoading: isLoading || stockholderApprovalMutation.isPending,
    isBoardApprovalLoading: boardApprovalMutation.isPending,
    isStockholderApprovalLoading: stockholderApprovalMutation.isPending,
    isAbandonLoading: abandonMutation.isPending,
    error: error?.message || boardApprovalMutation.error?.message || stockholderApprovalMutation.error?.message || abandonMutation.error?.message || null,
    refetch,
    submitForBoardApproval: (n) => boardApprovalMutation.mutateAsync(n),
    submitForStockholderApproval: () => stockholderApprovalMutation.mutateAsync(),
    abandonProcess: () => abandonMutation.mutateAsync(),
  };
};
