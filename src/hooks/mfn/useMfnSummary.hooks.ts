import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { mfnService } from "@/services/mfn/mfn.service";
import { MfnSummaryResponse } from "@/types/mfn";

interface UseMfnSummaryReturn {
  data: MfnSummaryResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useMfnSummary = (): UseMfnSummaryReturn => {
  const { user } = useAuth();
  const [data, setData] = useState<MfnSummaryResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!user?.companyId) {
      setError("No company selected");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await mfnService.getMfnSummary(user.companyId);
      console.log("Hook response:", response);
      
      // Check if the response indicates no data available
      if (response && response.data === null) {
        setError("No MFN data available for this company");
        setData(null);
      } else {
        setData(response);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch MFN summary";
      setError(errorMessage);
      console.error("Error in useMfnSummary:", err);
    } finally {
      setLoading(false);
    }
  }, [user?.companyId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
};
