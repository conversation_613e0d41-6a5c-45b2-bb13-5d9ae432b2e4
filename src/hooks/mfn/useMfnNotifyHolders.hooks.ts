import { useState, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { mfnService } from "@/services/mfn/mfn.service";
import { MfnNotifyResponse } from "@/types/mfn";

interface UseMfnNotifyHoldersReturn {
  notify: () => Promise<MfnNotifyResponse | null>;
  loading: boolean;
  error: string | null;
}

export const useMfnNotifyHolders = (): UseMfnNotifyHoldersReturn => {
  const { user } = useAuth();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const notify = useCallback(async (): Promise<MfnNotifyResponse | null> => {
    if (!user?.companyId) {
      setError("No company selected");
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await mfnService.notifyHolders(user.companyId);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to notify holders";
      setError(errorMessage);
      console.error("Error in useMfnNotifyHolders:", err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.companyId]);

  return { notify, loading, error };
};
