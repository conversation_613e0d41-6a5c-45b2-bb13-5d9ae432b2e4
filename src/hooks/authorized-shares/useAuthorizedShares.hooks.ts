import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { authorizedSharesService, AuthorizedSharesResponse } from "@/services/authorized-shares/authorizedShares.service";
import { toast } from "sonner";

export interface UseAuthorizedSharesReturn {
  data: AuthorizedSharesResponse['data'] | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  isFileUploadLoading: boolean;
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (additionalShares: number) => Promise<void>;
  submitForStockholderApproval: () => Promise<void>;
  abandonProcess: () => Promise<void>;
  uploadFileAmendment: (file: File) => Promise<void>;
}

export const useAuthorizedShares = (companyId: string): UseAuthorizedSharesReturn => {
  const queryClient = useQueryClient();

  // Query for fetching current pending authorized shares
  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["authorizedShares", companyId],
    queryFn: () => authorizedSharesService.getCurrentPending(companyId),
    enabled: !!companyId,
    staleTime: 30000, // 30 seconds
    retry: 2,
  });

  // Mutation for submitting board approval
  const boardApprovalMutation = useMutation({
    mutationFn: (additionalShares: number) =>
      authorizedSharesService.submitForBoardApproval(companyId, additionalShares),
    onSuccess: () => {
      toast.success("Board Approval Submitted Successfully");
      // Refetch data to get updated status
      queryClient.invalidateQueries({ queryKey: ["authorizedShares", companyId] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Mutation for submitting stockholder approval
  const stockholderApprovalMutation = useMutation({
    mutationFn: () => authorizedSharesService.submitForStockholderApproval(companyId),
    onSuccess: () => {
      toast.success("Stockholder Approval Submitted Successfully");
      // Refetch data to get updated status
      queryClient.invalidateQueries({ queryKey: ["authorizedShares", companyId] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Mutation for abandoning process
  const abandonMutation = useMutation({
    mutationFn: () => authorizedSharesService.abandonProcess(companyId),
    onSuccess: () => {
      toast.success("Process Abandoned Successfully");
      // Refetch data to get updated status
      queryClient.invalidateQueries({ queryKey: ["authorizedShares", companyId] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  // Mutation for file upload
  const fileUploadMutation = useMutation({
    mutationFn: async (file: File) => {
      // Step 1: Get presigned URL
      const presignedResponse = await authorizedSharesService.getPresignedUrl(
        companyId,
        file.name,
        file.type
      );

      // Step 2: Upload file directly to S3
      const uploadResponse = await fetch(presignedResponse.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file to S3');
      }

      // Step 3: Notify backend of successful upload
      await authorizedSharesService.submitFileAmendment(companyId, presignedResponse.key);
    },
    onSuccess: () => {
      toast.success("Certificate Uploaded Successfully");
      // Refetch data to get updated status
      queryClient.invalidateQueries({ queryKey: ["authorizedShares", companyId] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const submitForBoardApproval = async (additionalShares: number) => {
    await boardApprovalMutation.mutateAsync(additionalShares);
  };

  const submitForStockholderApproval = async () => {
    await stockholderApprovalMutation.mutateAsync();
  };

  const abandonProcess = async () => {
    await abandonMutation.mutateAsync();
  };

  const uploadFileAmendment = async (file: File) => {
    await fileUploadMutation.mutateAsync(file);
  };

  return {
    data: response || null,
    // exclude board approval pending from global loading to avoid full dialog spinner
    isLoading: isLoading || stockholderApprovalMutation.isPending || abandonMutation.isPending || fileUploadMutation.isPending,
    isBoardApprovalLoading: boardApprovalMutation.isPending,
    isStockholderApprovalLoading: stockholderApprovalMutation.isPending,
    isAbandonLoading: abandonMutation.isPending,
    isFileUploadLoading: fileUploadMutation.isPending,
    error: error?.message || boardApprovalMutation.error?.message || stockholderApprovalMutation.error?.message || abandonMutation.error?.message || fileUploadMutation.error?.message || null,
    refetch,
    submitForBoardApproval,
    submitForStockholderApproval,
    abandonProcess,
    uploadFileAmendment,
  };
}; 