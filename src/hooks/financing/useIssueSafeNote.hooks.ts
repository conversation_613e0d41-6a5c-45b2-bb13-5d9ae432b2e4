import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  IssueSafeNoteService,
  IssueSafeNoteUploadRequest,
  RecordIssueIndividualSafeNoteRequest,
} from "@/services/financing/issueSafeNote.service";

export const useIssueSafeNoteUpload = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: IssueSafeNoteUploadRequest) => {
      return await IssueSafeNoteService.getUploadUrl(companyId, payload);
    },
    onError: (error: Error) => {
      toast.error("Failed to get upload URL", {
        description: error.message,
      });
    },
  });
};

export const useRecordIssueIndividualSafeNote = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: RecordIssueIndividualSafeNoteRequest) => {
      return await IssueSafeNoteService.recordSafeNote(companyId, payload);
    },
    onSuccess: (data) => {
      toast.success("SAFE issued successfully", {
        description: `SAFE for ${data.data.name} has been created and recorded.`,
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to issue SAFE", {
        description: error.message,
      });
    },
  });
};

// NEW: Hook for SAFE prorata upload URL
export const useSafeProrataUploadUrl = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: IssueSafeNoteUploadRequest) => {
      return await IssueSafeNoteService.getProrataUploadUrl(companyId, payload);
    },
    onError: (error: Error) => {
      toast.error("Failed to get prorata upload URL", {
        description: error.message,
      });
    },
  });
};

// NEW: Hook for SAFE side letter upload URL
export const useSafeSideLetterUploadUrl = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: IssueSafeNoteUploadRequest) => {
      return await IssueSafeNoteService.getSideLetterUploadUrl(companyId, payload);
    },
    onError: (error: Error) => {
      toast.error("Failed to get side letter upload URL", {
        description: error.message,
      });
    },
  });
};

// NEW: Hook for SAFE optional document upload URL
export const useSafeOptionalDocumentUploadUrl = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: IssueSafeNoteUploadRequest) => {
      return await IssueSafeNoteService.getOptionalDocumentUploadUrl(companyId, payload);
    },
    onError: (error: Error) => {
      toast.error("Failed to get optional document upload URL", {
        description: error.message,
      });
    },
  });
};
