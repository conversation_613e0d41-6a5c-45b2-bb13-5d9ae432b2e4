import { useQuery } from '@tanstack/react-query';
import { IndividualNotesListService } from '@/services/financing/individualNotesList.service';
import { IndividualNote } from '@/types/financing';

export interface UseIndividualNotesListReturn {
  // Data
  individualNotes: IndividualNote[] | undefined;
  // Loading States
  isLoading: boolean;
  isError: boolean;
  // Error States
  error: Error | null;
  // Actions
  refetch: () => Promise<any>;
}

export const useIndividualNotesList = (companyId: string, authorizedRoundId: string): UseIndividualNotesListReturn => {
  const service = new IndividualNotesListService();

  const {
    data: individualNotes,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['individualNotesList', companyId, authorizedRoundId],
    queryFn: () => service.getIndividualNotesList(companyId, authorizedRoundId),
    enabled: !!companyId && !!authorizedRoundId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });

  return {
    individualNotes,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
  };
};
