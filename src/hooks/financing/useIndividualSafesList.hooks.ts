import { useQuery } from "@tanstack/react-query";
import { IndividualSafesListService } from "@/services/financing/individualSafesList.service";
import { IndividualSafe } from "@/types/financing";

export interface UseIndividualSafesListReturn {
  individualSafes: IndividualSafe[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export const useIndividualSafesList = (
  companyId: string,
  authorizedRoundId: string
): UseIndividualSafesListReturn => {
  const safeService = new IndividualSafesListService();

  const {
    data: individualSafes = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["individual-safes", companyId, authorizedRoundId],
    queryFn: async (): Promise<IndividualSafe[]> => {
      return safeService.getIndividualSafes(companyId, authorizedRoundId);
    },
    enabled: !!companyId && !!authorizedRoundId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    individualSafes,
    isLoading,
    isError: !!error,
    error: error as Error | null,
    refetch,
  };
};
