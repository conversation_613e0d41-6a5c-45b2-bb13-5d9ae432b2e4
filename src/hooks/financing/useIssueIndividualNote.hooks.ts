import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  IssueIndividualNoteService,
  IssueIndividualNoteUploadRequest,
} from "@/services/financing/issueIndividualNote.service";
import { 
  IssueIndividualNoteRequest, 
  IssueIndividualNoteResponse,
  RecordIssueIndividualNoteRequest,
  RecordIssueIndividualNoteResponse
} from "@/types/financing";

export const useIssueIndividualNoteUpload = (companyId: string) => {
  return useMutation({
    mutationFn: async (payload: IssueIndividualNoteUploadRequest) => {
      return await IssueIndividualNoteService.getUploadUrl(companyId, payload);
    },
    onError: (error: Error) => {
      toast.error("Failed to get upload URL", {
        description: error.message,
      });
    },
  });
};

export const useIssueIndividualNote = (companyId: string) => {
  return useMutation<IssueIndividualNoteResponse, Error, IssueIndividualNoteRequest>({
    mutationFn: async (payload: IssueIndividualNoteRequest) => {
      return await IssueIndividualNoteService.issueIndividualNote(companyId, payload);
    },
    onSuccess: () => {
      console.log("Convertible note issued successfully");
      toast.success("Convertible note issued successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to issue convertible note", {
        description: error.message,
      });
    },
  });
};

// NEW: Hook for recording issue individual notes (for RecordPopupDialog)
export const useRecordIssueIndividualNote = (companyId: string) => {
  return useMutation<RecordIssueIndividualNoteResponse, Error, RecordIssueIndividualNoteRequest>({
    mutationFn: async (payload: RecordIssueIndividualNoteRequest) => {
      return await IssueIndividualNoteService.recordIssueIndividualNote(companyId, payload);
    },
    onSuccess: () => {
      toast.success("Note recorded successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to record note", {
        description: error.message,
      });
    },
  });
};
