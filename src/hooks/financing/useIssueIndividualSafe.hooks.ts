import { useMutation, useQueryClient } from "@tanstack/react-query";
import { IssueIndividualSafeRequest, IssueIndividualSafeResponse } from "@/types/financing";
import { IssueIndividualSafeService } from "@/services/financing/issueIndividualSafe.service";
import { toast } from "sonner";

export const useIssueIndividualSafe = (companyId: string) => {
  const queryClient = useQueryClient();
  const safeService = new IssueIndividualSafeService();

  return useMutation({
    mutationFn: async (payload: IssueIndividualSafeRequest): Promise<IssueIndividualSafeResponse> => {
      return safeService.issueIndividualSafe(companyId, payload);
    },
    onSuccess: () => {
      // Invalidate and refetch the authorized SAFEs list
      queryClient.invalidateQueries({ queryKey: ["authorized-safes", companyId] });
      toast.success("Individual SAFE issued successfully", {
        description: "The SAFE has been issued to the investor.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to issue individual SAFE", {
        description: error.message,
      });
    },
  });
};
