import React from "react";
import { useQuery } from "@tanstack/react-query";
import { SafeRoundResponse } from "@/types/financing";
import { AuthorizedSafeListService } from "@/services/financing/authorizedSafeList.service";

interface UseAuthorizedSafeListReturn {
  safes: SafeRoundResponse[];
  filteredSafes: SafeRoundResponse[];
  isLoadingSafes: boolean;
  error: string | null;
  refetchSafes: () => void;
  filterType: 'all' | 'approved' | 'pending';
  setFilterType: (filter: 'all' | 'approved' | 'pending') => void;
}

export const useAuthorizedSafeList = (companyId: string): UseAuthorizedSafeListReturn => {
  const [filterType, setFilterType] = React.useState<'all' | 'approved' | 'pending'>('approved');
  const safeService = React.useMemo(() => new AuthorizedSafeListService(), []);

  const {
    data: safes = [],
    isLoading: isLoadingSafes,
    error,
    refetch: refetchSafes,
  } = useQuery({
    queryKey: ["authorized-safes", companyId],
    queryFn: async (): Promise<SafeRoundResponse[]> => {
      return safeService.getAuthorizedSafes(companyId);
    },
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Filter SAFEs based on selection
  const filteredSafes = React.useMemo(() => {
    switch (filterType) {
      case 'approved':
        return safes.filter(safe => safe.isActive);
      case 'pending':
        return safes.filter(safe => !safe.isActive);
      case 'all':
        return safes;
      default:
        return safes.filter(safe => safe.isActive);
    }
  }, [safes, filterType]);

  return {
    safes,
    filteredSafes,
    isLoadingSafes,
    error: error ? (error as Error).message : null,
    refetchSafes,
    filterType,
    setFilterType,
  };
};
