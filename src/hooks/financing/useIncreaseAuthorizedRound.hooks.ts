import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IncreaseAuthorizedRoundService } from '@/services/financing/increaseAuthorizedRound.service';
import { IncreaseAuthorizedRoundRequest } from '@/types/financing';
import { toast } from 'sonner';

export interface UseIncreaseAuthorizedRoundReturn {
  // Actions
  requestRoundIncrease: (payload: IncreaseAuthorizedRoundRequest) => Promise<void>;

  // Loading States
  isRequestingIncrease: boolean;

  // Error States
  error: string | null;
}

export const useIncreaseAuthorizedRound = (companyId: string): UseIncreaseAuthorizedRoundReturn => {
  const queryClient = useQueryClient();
  const service = new IncreaseAuthorizedRoundService();

  const {
    mutateAsync: requestRoundIncrease,
    isPending: isRequestingIncrease,
    error,
  } = useMutation({
    mutationFn: (payload: IncreaseAuthorizedRoundRequest) => service.requestRoundIncrease(companyId, payload),
    onSuccess: () => {
      // Show success message
      toast.success('Round increase request submitted successfully');

      // Invalidate and refetch the authorized rounds list
      queryClient.invalidateQueries({
        queryKey: ['authorizedRoundList', companyId],
      });
    },
    onError: (error) => {
      // Show error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit round increase request';
      toast.error(errorMessage);
    },
  });

  const handleRequestRoundIncrease = async (payload: IncreaseAuthorizedRoundRequest): Promise<void> => {
    await requestRoundIncrease(payload);
  };

  return {
    requestRoundIncrease: handleRequestRoundIncrease,
    isRequestingIncrease,
    error: error?.message || null,
  };
};
