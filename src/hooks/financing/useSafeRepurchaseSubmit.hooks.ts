import { useMutation, useQueryClient } from '@tanstack/react-query';
import { SafeRepurchaseService } from '@/services/financing/safeRepurchase.service';
import { toast } from 'sonner';

export const useSafeRepurchaseSubmit = (companyId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ safeId, repurchaseAmount }: { safeId: string; repurchaseAmount: number }) =>
      SafeRepurchaseService.submitSafeRepurchase(companyId, safeId, repurchaseAmount),
    onSuccess: () => {
      toast.success('SAFE repurchase submitted successfully');
      queryClient.invalidateQueries({ queryKey: ['safeRepurchaseList', companyId] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to submit repurchase: ${error.message}`);
    },
  });
};
