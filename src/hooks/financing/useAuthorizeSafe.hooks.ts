import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthorizeSafeRequest, AuthorizeSafeResponse } from "@/types/financing";
import { AuthorizeSafeService } from "@/services/financing/authorizeSafe.service";
import { toast } from "sonner";

export const useAuthorizeSafe = (companyId: string) => {
  const queryClient = useQueryClient();
  const safeService = new AuthorizeSafeService();

  return useMutation({
    mutationFn: async (payload: AuthorizeSafeRequest): Promise<AuthorizeSafeResponse> => {
      return safeService.createAuthorizeSafe(companyId, payload);
    },
    onSuccess: () => {
      // Invalidate and refetch the authorized SAFEs list
      queryClient.invalidateQueries({ queryKey: ["authorized-safes", companyId] });
      toast.success("SAFE round created successfully", {
        description: "Board approval process has been initiated.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to create SAFE round", {
        description: error.message,
      });
    },
  });
};
