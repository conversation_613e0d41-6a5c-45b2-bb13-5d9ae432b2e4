import { useQuery } from '@tanstack/react-query';
import { useState, useMemo } from 'react';
import { AuthorizedRoundListService } from '@/services/financing/authorizedRoundList.service';
import { ConvertibleNoteRoundResponse } from '@/types/financing';

export interface UseAuthorizedRoundListReturn {
  // Data
  rounds: ConvertibleNoteRoundResponse[];

  // Loading States
  isLoadingRounds: boolean;

  // Error States
  error: string | null;

  // Actions
  refetchRounds: () => void;
}

// **NEW**: Filter state management
export interface RoundFilterState {
  filterType: 'all' | 'approved' | 'pending';
  setFilterType: (filter: 'all' | 'approved' | 'pending') => void;
  filteredRounds: ConvertibleNoteRoundResponse[];
}

export const useAuthorizedRoundList = (companyId: string): UseAuthorizedRoundListReturn & RoundFilterState => {
  const service = new AuthorizedRoundListService();
  const [filterType, setFilterType] = useState<'all' | 'approved' | 'pending'>('approved');
  
  const {
    data: rounds = [],
    isLoading: isLoadingRounds,
    error,
    refetch: refetchRounds,
  } = useQuery({
    queryKey: ['authorizedRoundList', companyId],
    queryFn: () => service.getAuthorizedRounds(companyId),
    enabled: !!companyId,
  });

  // **NEW**: Filter rounds based on selection
  const filteredRounds = useMemo(() => {
    switch (filterType) {
      case 'approved':
        return rounds.filter(round => round.isActive === true);
      case 'pending':
        return rounds.filter(round => round.isActive === false);
      case 'all':
        return rounds;
      default:
        return rounds.filter(round => round.isActive === true);
    }
  }, [rounds, filterType]);

  return {
    rounds,
    filteredRounds,
    isLoadingRounds,
    error: error?.message || null,
    refetchRounds,
    filterType,
    setFilterType,
  };
};
