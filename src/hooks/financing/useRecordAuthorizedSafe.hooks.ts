import { useMutation, useQueryClient } from "@tanstack/react-query";
import { RecordAuthorizedSafeRequest, RecordAuthorizedSafeResponse, FileUploadResponse } from "@/types/financing";
import { RecordAuthorizedSafeService } from "@/services/financing/recordAuthorizedSafe.service";
import { APIClient } from "@/integrations/legal-concierge/client";
import { toast } from "sonner";

export interface UseRecordAuthorizedSafeReturn {
  // State
  isRecording: boolean;
  
  // Actions
  recordRound: (file: File, formData: Omit<RecordAuthorizedSafeRequest, 's3Key'>) => Promise<RecordAuthorizedSafeResponse | null>;
  reset: () => void;
  
  // Mutation object for error handling
  mutation: any;
}

export const useRecordAuthorizedSafe = (companyId: string): UseRecordAuthorizedSafeReturn => {
  const queryClient = useQueryClient();
  const safeService = new RecordAuthorizedSafeService();
  const apiClient = new APIClient();

  const mutation = useMutation({
    mutationFn: async ({ file, formData }: { file: File; formData: Omit<RecordAuthorizedSafeRequest, 's3Key'> }) => {
      // Step 1: Upload file and get S3 key
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const key = `companies/${companyId}/others/authorized-safe-round/${file.name.replace(/\.[^/.]+$/, '')}-${timestamp}.${file.name.split('.').pop()}`;
      
      const presignedResponse = await apiClient.getSafeDocumentUploadUrl(companyId, { key, contentType: file.type }) as FileUploadResponse;
      if (!presignedResponse?.data?.uploadUrl) {
        throw new Error('Failed to get upload URL');
      }

      // Upload to S3
      await fetch(presignedResponse.data.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: { 'Content-Type': file.type },
      });

      // Step 2: Record the round with S3 key
      const payload: RecordAuthorizedSafeRequest = {
        ...formData,
        s3Key: presignedResponse.data.key
      };

      return await safeService.recordAuthorizedSafe(companyId, payload);
    },
    onSuccess: () => {
      // Invalidate and refetch the authorized SAFEs list
      queryClient.invalidateQueries({ queryKey: ["authorized-safes", companyId] });
      toast.success("SAFE round recorded successfully", {
        description: "The external SAFE round has been recorded.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to record SAFE round", {
        description: error.message,
      });
    },
  });

  const recordRound = async (file: File, formData: Omit<RecordAuthorizedSafeRequest, 's3Key'>): Promise<RecordAuthorizedSafeResponse | null> => {
    try {
      return await mutation.mutateAsync({ file, formData });
    } catch (error) {
      return null;
    }
  };

  const reset = () => {
    mutation.reset();
  };

  return {
    isRecording: mutation.isPending,
    recordRound,
    reset,
    mutation
  };
};
