import { useState, useEffect, useCallback } from 'react';
import { useSafeRepurchaseList } from './useSafeRepurchaseList.hooks';
import { useSafeRepurchaseSubmit } from './useSafeRepurchaseSubmit.hooks';
import { SafeRepurchase, isSafeSelectable, isSafeDisabled } from '@/types/financing';
import { toast } from 'sonner';

export const useSafeRepurchaseManagement = (companyId: string) => {
  const { data: apiSafes = [], isLoading, error, refetch } = useSafeRepurchaseList(companyId);
  const submitRepurchase = useSafeRepurchaseSubmit(companyId);

  const [safes, setSafes] = useState<SafeRepurchase[]>([]);
  const [selectedSafes, setSelectedSafes] = useState<Set<string>>(new Set());
  const [repurchaseAmounts, setRepurchaseAmounts] = useState<Record<string, number>>({});
  
  // New loading state management for individual SAFE processing
  const [processingSafes, setProcessingSafes] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (apiSafes.length > 0) {
      setSafes(apiSafes);
    }
  }, [apiSafes]);

  const handleCheckboxChange = useCallback((id: string, checked: boolean) => {
    const safe = safes.find(s => s.id === id);
    if (!safe) return;

    // Prevent selection of non-selectable SAFEs
    if (!isSafeSelectable(safe)) {
      toast.error(`Cannot select SAFE for ${safe.investorName} - Board approval required`);
      return;
    }

    setSelectedSafes(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(id);
      } else {
        newSet.delete(id);
      }
      return newSet;
    });
  }, [safes]);

  const handleRepurchaseAmountChange = useCallback((id: string, amount: number) => {
    const safe = safes.find(s => s.id === id);
    if (!safe || !isSafeSelectable(safe)) return;

    setRepurchaseAmounts(prev => ({
      ...prev,
      [id]: amount
    }));
  }, [safes]);

  const handleRepurchaseClick = useCallback(() => {
    const selectedSafeIds = Array.from(selectedSafes);
    if (selectedSafeIds.length === 0) {
      toast.error("Please select at least one SAFE to repurchase.");
      return false;
    }
    
    // Validate that all selected SAFEs are board approved
    const nonApprovedSafes = selectedSafeIds.filter(safeId => {
      const safe = safes.find(s => s.id === safeId);
      return safe && !isSafeSelectable(safe);
    });

    if (nonApprovedSafes.length > 0) {
      const nonApprovedNames = nonApprovedSafes
        .map(safeId => safes.find(s => s.id === safeId)?.investorName)
        .filter(Boolean)
        .join(', ');
      toast.error(`Cannot repurchase SAFEs for: ${nonApprovedNames} - Board approval required`);
      return false;
    }
    
    const invalidSafes = selectedSafeIds.filter(
      (safeId) => (repurchaseAmounts[safeId] || 0) <= 0
    );
    if (invalidSafes.length > 0) {
      toast.error("Please enter a valid repurchase amount for all selected SAFEs.");
      return false;
    }
    return true;
  }, [selectedSafes, repurchaseAmounts, safes]);

  const handleConfirmRepurchase = useCallback(async () => {
    const selectedSafeIds = Array.from(selectedSafes);
    const totalCount = selectedSafeIds.length;
    
    // Final validation before submission
    const nonApprovedSafes = selectedSafeIds.filter(safeId => {
      const safe = safes.find(s => s.id === safeId);
      return safe && !isSafeSelectable(safe);
    });

    if (nonApprovedSafes.length > 0) {
      toast.error("Cannot proceed - some selected SAFEs require board approval");
      return false;
    }
    
    // Initialize processing state for all selected SAFEs
    const initialProcessingState = selectedSafeIds.reduce((acc, safeId) => {
      acc[safeId] = 'pending';
      return acc;
    }, {} as Record<string, 'pending' | 'success' | 'error'>);
    
    setProcessingSafes(initialProcessingState);
    setIsProcessing(true);
    
    try {
      // Fire all requests concurrently for optimal performance
      const results = await Promise.allSettled(
        selectedSafeIds.map(async (safeId, index) => {
          const amount = repurchaseAmounts[safeId] || 0;
          
          try {
            // Update individual SAFE status to success when completed
            const result = await submitRepurchase.mutateAsync({ safeId, repurchaseAmount: amount });
            setProcessingSafes(prev => ({ ...prev, [safeId]: 'success' }));
            return { safeId, result, success: true };
          } catch (error) {
            // Update individual SAFE status to error when failed
            setProcessingSafes(prev => ({ ...prev, [safeId]: 'error' }));
            throw { safeId, error, success: false };
          }
        })
      );
      
      // Process results to determine success/failure
      const successful = results.filter(result => result.status === 'fulfilled' && result.value.success);
      const failed = results.filter(result => result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.success));
      
      // Clear selections and amounts after all requests complete
      setSelectedSafes(new Set());
      setRepurchaseAmounts({});
      setProcessingSafes({});
      setIsProcessing(false);
      
      // Show comprehensive results to user (only final toast)
      if (successful.length > 0 && failed.length === 0) {
        toast.success(`Successfully submitted ${successful.length} repurchase request(s)`);
      } else if (successful.length > 0 && failed.length > 0) {
        // Partial success - show both success and failure counts
        toast.success(`Completed with partial success: ${successful.length} succeeded, ${failed.length} failed`);
      } else if (failed.length > 0) {
        // All failed
        toast.error(`Failed to submit ${failed.length} repurchase request(s)`);
      }
      
      return successful.length > 0;
      
    } catch (error) {
      console.error('Failed to submit repurchases:', error);
      setProcessingSafes({});
      setIsProcessing(false);
      toast.error('An unexpected error occurred during submission');
      return false;
    }
  }, [selectedSafes, repurchaseAmounts, submitRepurchase, safes]);

  const clearSelections = useCallback(() => {
    setSelectedSafes(new Set());
    setRepurchaseAmounts({});
    setProcessingSafes({});
  }, []);

  const anySelected = selectedSafes.size > 0;
  const isSubmitting = submitRepurchase.isPending || isProcessing;

  return {
    safes,
    isLoading,
    error,
    refetch,
    selectedSafes,
    repurchaseAmounts,
    anySelected,
    handleCheckboxChange,
    handleRepurchaseAmountChange,
    handleRepurchaseClick,
    handleConfirmRepurchase,
    clearSelections,
    isSubmitting,
    // New loading state properties
    processingSafes,
    isProcessing
  };
};
