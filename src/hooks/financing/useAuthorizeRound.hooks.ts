import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AuthorizeRoundService } from '@/services/financing/authorizeRound.service';
import { AuthorizeRoundRequest, AuthorizeRoundResponse } from '@/types/financing';

export interface UseAuthorizeRoundReturn {
  // Actions
  createAuthorizeRound: (payload: AuthorizeRoundRequest) => Promise<AuthorizeRoundResponse>;
  
  // Loading States
  isCreatingRound: boolean;
  
  // Error States
  error: string | null;
}

export const useAuthorizeRound = (companyId: string): UseAuthorizeRoundReturn => {
  const queryClient = useQueryClient();
  const service = new AuthorizeRoundService();

  const {
    mutateAsync: createAuthorizeRound,
    isPending: isCreatingRound,
    error,
  } = useMutation({
    mutationFn: (payload: AuthorizeRoundRequest) => service.createAuthorizeRound(companyId, payload),
    onSuccess: () => {
      // Invalidate and refetch the authorized rounds list
      queryClient.invalidateQueries({
        queryKey: ['authorizedRoundList', companyId],
      });
    },
  });

  return {
    createAuthorizeRound,
    isCreatingRound,
    error: error?.message || null,
  };
};