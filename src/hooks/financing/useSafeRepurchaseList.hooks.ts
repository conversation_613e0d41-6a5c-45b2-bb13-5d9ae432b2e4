import { useQuery } from '@tanstack/react-query';
import { SafeRepurchaseService } from '@/services/financing/safeRepurchase.service';

export const useSafeRepurchaseList = (companyId: string) => {
  return useQuery({
    queryKey: ['safeRepurchaseList', companyId],
    queryFn: () => SafeRepurchaseService.getSafeRepurchaseList(companyId),
    enabled: !!companyId,
    staleTime: 30000, // 30 seconds
    retry: 2,
  });
};
