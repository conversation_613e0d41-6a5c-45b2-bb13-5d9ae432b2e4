import { useMutation } from '@tanstack/react-query';
import { APIClient } from '@/integrations/legal-concierge/client';
import { RecordAuthorizedRoundRequest, RecordAuthorizedRoundResponse, FileUploadResponse } from '@/types/financing';
import { toast } from 'sonner';

export interface UseRecordAuthorizedRoundReturn {
  // State
  isRecording: boolean;
  
  // Actions
  recordRound: (file: File, formData: Omit<RecordAuthorizedRoundRequest, 's3Key'>, companyId: string) => Promise<RecordAuthorizedRoundResponse['data'] | null>;
  reset: () => void;
}

export const useRecordAuthorizedRound = (): UseRecordAuthorizedRoundReturn => {
  const apiClient = new APIClient();

  const mutation = useMutation({
    mutationFn: async ({ file, formData, companyId }: { file: File; formData: Omit<RecordAuthorizedRoundRequest, 's3Key'>; companyId: string }) => {
      // Step 1: Upload file and get S3 key
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const key = `companies/${companyId}/others/authorized-round/${file.name.replace(/\.[^/.]+$/, '')}-${timestamp}.${file.name.split('.').pop()}`;
      
      const presignedResponse = await apiClient.getConvertibleNotesPresignedUrl(companyId, key, file.type) as FileUploadResponse;
      if (!presignedResponse?.data?.uploadUrl) {
        throw new Error('Failed to get upload URL');
      }

      // Upload to S3
      await fetch(presignedResponse.data.uploadUrl, {
        method: 'PUT',
        body: file,
        headers: { 'Content-Type': file.type },
      });

      // Step 2: Record the round with S3 key
      const payload: RecordAuthorizedRoundRequest = {
        ...formData,
        s3Key: presignedResponse.data.key
      };

      const roundResponse = await apiClient.recordAuthorizedRound(companyId, payload) as RecordAuthorizedRoundResponse;
      return roundResponse?.data;
    },
    onSuccess: () => {
      toast.success('Authorized round recorded successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to record authorized round');
    }
  });

  const recordRound = async (file: File, formData: Omit<RecordAuthorizedRoundRequest, 's3Key'>, companyId: string): Promise<RecordAuthorizedRoundResponse['data'] | null> => {
    try {
      return await mutation.mutateAsync({ file, formData, companyId });
    } catch (error) {
      return null;
    }
  };

  const reset = () => {
    mutation.reset();
  };

  return {
    isRecording: mutation.isPending,
    recordRound,
    reset
  };
};
