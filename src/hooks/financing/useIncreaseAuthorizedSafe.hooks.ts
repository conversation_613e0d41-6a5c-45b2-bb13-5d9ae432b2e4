import { useMutation, useQueryClient } from "@tanstack/react-query";
import { IncreaseAuthorizedSafeRequest, IncreaseAuthorizedSafeResponse } from "@/types/financing";
import { IncreaseAuthorizedSafeService } from "@/services/financing/increaseAuthorizedSafe.service";
import { toast } from "sonner";

export const useIncreaseAuthorizedSafe = (companyId: string) => {
  const queryClient = useQueryClient();
  const safeService = new IncreaseAuthorizedSafeService();

  return useMutation({
    mutationFn: async (payload: IncreaseAuthorizedSafeRequest): Promise<IncreaseAuthorizedSafeResponse> => {
      return safeService.increaseAuthorizedSafe(companyId, payload);
    },
    onSuccess: () => {
      // Invalidate and refetch the authorized SAFEs list
      queryClient.invalidateQueries({ queryKey: ["authorized-safes", companyId] });
      toast.success("SAFE round increased successfully", {
        description: "The authorized amount has been increased.",
      });
    },
    onError: (error: Error) => {
      toast.error("Failed to increase SAFE round", {
        description: error.message,
      });
    },
  });
};
