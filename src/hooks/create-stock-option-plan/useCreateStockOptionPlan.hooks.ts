import { useQuery, useMutation, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import { createSopService, CreateSopState } from "@/services/create-stock-option-plan/createStockOptionPlan.service";
import { stockOptionPlanService, StockOptionPlanSummary } from "@/services/stock-option-plan/stockOptionPlan.service";
import { toast } from "sonner";

export interface UseCreateSopReturn {
  data: CreateSopState | null;
  summary: StockOptionPlanSummary | null;
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  error: string | null;
  refetch: () => void;
  submitForBoardApproval: (shareAmount: number) => Promise<void>;
  submitForStockholderApproval: () => Promise<void>;
  abandonProcess: () => Promise<void>;
}

export const useCreateStockOptionPlan = (companyId: string, enabled: boolean = true): UseCreateSopReturn => {
  const queryClient = useQueryClient();

  const pendingQuery = useQuery({
    queryKey: ["createSop", companyId],
    queryFn: () => createSopService.getCurrentPending(companyId),
    enabled: !!companyId && enabled,
    staleTime: 30000,
    retry: 2,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    placeholderData: keepPreviousData,
  });

  const summaryQuery = useQuery({
    queryKey: ["sopSummary", companyId],
    queryFn: () => stockOptionPlanService.getSummary(companyId),
    enabled: !!companyId && enabled,
    staleTime: 30000,
    retry: 2,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    placeholderData: keepPreviousData,
  });

  const boardApprovalMutation = useMutation({
    mutationFn: (shareAmount: number) => createSopService.submitForBoardApproval(companyId, shareAmount),
    onSuccess: () => {
      toast.success("Board Approval Submitted Successfully");
      queryClient.invalidateQueries({ queryKey: ["createSop", companyId] });
      queryClient.invalidateQueries({ queryKey: ["sopSummary", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  const stockholderApprovalMutation = useMutation({
    mutationFn: () => createSopService.submitForStockholderApproval(companyId),
    onSuccess: () => {
      toast.success("Stockholder Approval Submitted Successfully");
      queryClient.invalidateQueries({ queryKey: ["createSop", companyId] });
      queryClient.invalidateQueries({ queryKey: ["sopSummary", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  const abandonMutation = useMutation({
    mutationFn: () => createSopService.abandonProcess(companyId),
    onSuccess: () => {
      toast.success("Process Abandoned Successfully");
      queryClient.invalidateQueries({ queryKey: ["createSop", companyId] });
      queryClient.invalidateQueries({ queryKey: ["sopSummary", companyId] });
    },
    onError: (e: Error) => toast.error(e.message),
  });

  return {
    data: pendingQuery.data || null,
    summary: summaryQuery.data || null,
    // exclude board approval pending from global loading to avoid full dialog spinner
    isLoading: pendingQuery.isLoading || summaryQuery.isLoading || stockholderApprovalMutation.isPending,
    isBoardApprovalLoading: boardApprovalMutation.isPending,
    isStockholderApprovalLoading: stockholderApprovalMutation.isPending,
    isAbandonLoading: abandonMutation.isPending,
    error: pendingQuery.error?.message || summaryQuery.error?.message || boardApprovalMutation.error?.message || stockholderApprovalMutation.error?.message || abandonMutation.error?.message || null,
    refetch: () => {
      pendingQuery.refetch();
      summaryQuery.refetch();
    },
    submitForBoardApproval: (n) => boardApprovalMutation.mutateAsync(n),
    submitForStockholderApproval: () => stockholderApprovalMutation.mutateAsync(),
    abandonProcess: () => abandonMutation.mutateAsync(),
  };
};
