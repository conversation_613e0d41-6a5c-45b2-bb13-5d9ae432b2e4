import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { getVotingRights, updateVotingRightEmail, updateVotingRightEmailStatus } from "@/services/cap-table/votingRights.service";
import { useAuth } from "@/contexts/AuthContext";
import { VotingRightsData, UpdateEmailRequest, UpdateEmailResponse, UpdateEmailStatusRequest, UpdateEmailStatusResponse } from "@/types/capTable";
import { toast } from "sonner";

// Custom debounce hook for API parameters
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
};

export const useVotingRights = (filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const debouncedFilters = useDebounce(filters, 500); // 500ms debounce delay

  return useQuery<VotingRightsData>({
    queryKey: ["votingRights", companyId, debouncedFilters],
    queryFn: () => getVotingRights(companyId!, debouncedFilters),
    enabled: !!companyId,
  });
};

export const useUpdateVotingRightEmail = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<UpdateEmailResponse, Error, UpdateEmailRequest>({
    mutationFn: (request) => updateVotingRightEmail(companyId!, request),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["votingRights", companyId] });
      toast.success("Shareholder email address has been updated successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update email address.");
    },
  });
};

export const useUpdateVotingRightEmailStatus = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<UpdateEmailStatusResponse, Error, UpdateEmailStatusRequest>({
    mutationFn: (request) => updateVotingRightEmailStatus(companyId!, request),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["votingRights", companyId] });
      toast.success("Shareholder email inclusion status has been updated successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update email status.");
    },
  });
};
