import { useQuery } from '@tanstack/react-query';
import { useState, useEffect } from 'react';
import { capTableListService } from '@/services/cap-table/capTableList.service';
import { capTableSummaryService } from '@/services/cap-table/capTableSummary.service';
import { convertibleNotesService } from '@/services/cap-table/convertibleNotes.service';
import { useAuth } from '@/contexts/AuthContext';
import { EnhancedCapTableData, EnhancedCapTableListItem, SOPPoolSummary } from '@/types/capTable';

// Custom debounce hook for API parameters
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useCapTableList = (filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500); // 500ms debounce delay

  // Transform API data to component format
  const transformApiData = (apiData: any): EnhancedCapTableData => {
    if (!apiData || !Array.isArray(apiData)) {
      return {
        stockholders: [],
        sopPool: {
          outstandingSOPShares: 0,
          promisedSOPShares: 0,
          availableSOPShares: 0,
          totalAuthorizedSOP: 0,
          outstandingOwnership: 0,
          promisedOwnership: 0,
          availableOwnership: 0,
        },
        lastUpdated: new Date().toLocaleDateString(),
      };
    }

    // Transform stockholders
    const stockholders: EnhancedCapTableListItem[] = apiData.map((item: any) => ({
      id: item.officerId || item.serviceProviderId || `temp-${Math.random()}`,
      name: item.name,
      role: item.role,
      commonStock: item.commonStock || 0,
      stockOptionPlan: item.sopCommonStock || 0,
      totalShares: item.totalShares || 0,
      fullyDilutedOwnership: item.fullyDilutedOwnership || 0,
      isTerminated: item.isTerminated || false,
      unvestedShares: item.unvestedShare ? item.unvestedShare.toString() : 'N/A',
      vestedShares: item.vestedShare ? item.vestedShare.toString() : 'N/A',
      officerId: item.officerId,
      serviceProviderId: item.serviceProviderId,
    }));

    // Extract SOP pool data from the first item if available
    const firstItem = apiData[0];
    const sopPool: SOPPoolSummary = {
      outstandingSOPShares: firstItem?.outstandingStockOptionPlanShares?.stockOptionPlan || 0,
      promisedSOPShares: firstItem?.promisedStockOptionPlanShares?.stockOptionPlan || 0,
      availableSOPShares: firstItem?.stockOptionPlanSharesAvailable?.stockOptionPlan || 0,
      totalAuthorizedSOP: 
        (firstItem?.outstandingStockOptionPlanShares?.stockOptionPlan || 0) +
        (firstItem?.promisedStockOptionPlanShares?.stockOptionPlan || 0) +
        (firstItem?.stockOptionPlanSharesAvailable?.stockOptionPlan || 0),
      outstandingOwnership: firstItem?.outstandingStockOptionPlanShares?.fullyDilutedOwnership || 0,
      promisedOwnership: firstItem?.promisedStockOptionPlanShares?.fullyDilutedOwnership || 0,
      availableOwnership: firstItem?.stockOptionPlanSharesAvailable?.fullyDilutedOwnership || 0,
    };

    return {
      stockholders,
      sopPool,
      lastUpdated: firstItem?.latestUpdatedDate || new Date().toLocaleDateString(),
    };
  };

  const {
    data: capTableList,
    isLoading: isListLoading,
    error: listError,
    refetch: refetchList,
  } = useQuery({
    queryKey: ['capTableList', companyId, debouncedFilters],
    queryFn: () => capTableListService.getCapTableList(companyId!, debouncedFilters),
    enabled: !!companyId,
  });

  const {
    data: capTableSummary,
    isLoading: isSummaryLoading,
    error: summaryError,
    refetch: refetchSummary,
  } = useQuery({
    queryKey: ['capTableSummary', companyId],
    queryFn: () => capTableSummaryService.getCapTableSummary(companyId!),
    enabled: !!companyId,
  });

  const {
    data: convertibleNotes,
    isLoading: isConvertibleNotesLoading,
    error: convertibleNotesError,
    refetch: refetchConvertibleNotes,
  } = useQuery({
    queryKey: ['convertibleNotes', companyId],
    queryFn: () => convertibleNotesService.getConvertibleNotes(companyId!),
    enabled: !!companyId,
  });

  // Transform the data for the component
  const capTableData = transformApiData(capTableList?.data);

  return {
    // Transformed data for table
    capTableData,
    isListLoading,
    listError: listError?.message || null,
    refetchList,
    
    // Summary data for dashboard widget
    capTableSummary: capTableSummary?.data || null,
    isSummaryLoading,
    summaryError: summaryError?.message || null,
    refetchSummary,

    // Convertible notes data
    convertibleNotes: convertibleNotes?.data || [],
    isConvertibleNotesLoading,
    convertibleNotesError: convertibleNotesError?.message || null,
    refetchConvertibleNotes,
  };
};
