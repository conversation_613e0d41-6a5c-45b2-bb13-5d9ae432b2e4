import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { updateCapTableItem, UpdateCapTableItemPayload } from '@/services/cap-table/capTableEdit.service';
import { terminateCapTableItem } from '@/services/cap-table/capTableTerminate.service';
import { EnhancedCapTableListItem, TerminateCapTableItemPayload } from '@/types/capTable';

export const useCapTableEdit = () => {
  const { user } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isTerminating, setIsTerminating] = useState(false);

  const updateStockholder = async (
    stockholder: EnhancedCapTableListItem,
    editData: { commonStock: number; stockOptionPlan: number }
  ) => {
    if (!user?.companyId) {
      toast.error('No company selected');
      return false;
    }

    setIsUpdating(true);
    
    try {
      const payload: UpdateCapTableItemPayload = {
        officerId: stockholder.officerId || stockholder.id,
        serviceProviderId: stockholder.serviceProviderId,
        commonStock: editData.commonStock,
        sopShares: editData.stockOptionPlan,
      };

      await updateCapTableItem(user.companyId, payload);
      
      toast.success('Stockholder updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating stockholder:', error);
      toast.error('Failed to update stockholder. Please try again.');
      return false;
    } finally {
      setIsUpdating(false);
    }
  };

  const terminateStockholder = async (
    stockholder: EnhancedCapTableListItem,
    payload: {
      terminationDate: string;
      severanceAmount: number;
      serviceProviderTerminationType: string;
    }
  ) => {
    if (!user?.companyId) {
      toast.error('No company selected');
      return false;
    }

    setIsTerminating(true);
    
    try {
      const terminatePayload: TerminateCapTableItemPayload = {
        officerId: stockholder.officerId || stockholder.id,
        serviceProviderId: stockholder.serviceProviderId,
        terminationDate: payload.terminationDate,
        severanceAmount: payload.severanceAmount,
        serviceProviderTerminationType: payload.serviceProviderTerminationType,
      };

      await terminateCapTableItem(user.companyId, terminatePayload);
      
      toast.success('Stockholder terminated successfully');
      return true;
    } catch (error) {
      console.error('Error terminating stockholder:', error);
      toast.error('Failed to terminate stockholder. Please try again.');
      return false;
    } finally {
      setIsTerminating(false);
    }
  };

  return {
    updateStockholder,
    terminateStockholder,
    isUpdating,
    isTerminating,
  };
};
