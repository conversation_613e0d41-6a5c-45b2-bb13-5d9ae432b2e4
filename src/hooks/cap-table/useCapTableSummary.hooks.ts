import { useQuery } from '@tanstack/react-query';
import { capTableSummaryService } from '@/services/cap-table';
import { useAuth } from '@/contexts/AuthContext';
import { CapTableSummaryData } from '@/types/capTable';

export const useCapTableSummary = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  const {
    data: capTableSummary,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['capTableSummary', companyId],
    queryFn: () => capTableSummaryService.getCapTableSummary(companyId!),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (React Query v5)
  });

  return {
    capTableSummary: capTableSummary?.data || null,
    isLoading,
    error: error?.message || null,
    refetch,
  };
};
