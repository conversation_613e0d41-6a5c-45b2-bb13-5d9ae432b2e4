import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo, useState, useEffect } from "react";
import { 
  getStockOptionPlanSummary, 
  getStockOptionPlanTable,
  updateStockOptionGrant,
  exerciseStockOption,
  deleteStockOptionGrant,
  addStockOptionGrant
} from "@/services/cap-table/stockOptionPlan.service";
import { useAuth } from "@/contexts/AuthContext";
import { 
  StockOptionPlanSummary, 
  StockOptionGrant, 
  StockOptionPlanData,
  UpdateGrantRequest,
  UpdateGrantResponse,
  ExerciseOptionRequest,
  ExerciseOptionResponse,
  DeleteGrantRequest,
  DeleteGrantResponse,
  AddGrantRequest,
  AddGrantResponse
} from "@/types/capTable";
import { toast } from "sonner";

// Custom debounce hook for API parameters
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useStockOptionPlan = (filters?: {
  proFormaRoleFilter?: string;
  proFormaShareClassFilter?: string;
  shareHolderName?: string;
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500); // 500ms debounce delay

  const summaryQuery = useQuery<StockOptionPlanSummary>({
    queryKey: ["stockOptionPlanSummary", companyId],
    queryFn: () => getStockOptionPlanSummary(companyId!),
    enabled: !!companyId,
  });

  const tableQuery = useQuery<StockOptionGrant[]>({
    queryKey: ["stockOptionPlanTable", companyId, debouncedFilters],
    queryFn: () => getStockOptionPlanTable(companyId!, debouncedFilters),
    enabled: !!companyId,
  });

  // Combine data and update lastUpdated from table API
  const combinedData = useMemo(() => {
    if (!summaryQuery.data || !tableQuery.data) return null;

    return {
      summary: {
        ...summaryQuery.data,
        lastUpdated: tableQuery.data.length > 0 ? 
          new Date(tableQuery.data[0]?.grantDate || new Date()).toLocaleDateString() : 
          summaryQuery.data.lastUpdated
      },
      grants: tableQuery.data
    } as StockOptionPlanData;
  }, [summaryQuery.data, tableQuery.data]);

  return {
    data: combinedData,
    isLoading: summaryQuery.isLoading || tableQuery.isLoading,
    error: summaryQuery.error || tableQuery.error,
    refetch: () => {
      summaryQuery.refetch();
      tableQuery.refetch();
    }
  };
};

export const useUpdateStockOptionGrant = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<UpdateGrantResponse, Error, UpdateGrantRequest>({
    mutationFn: (request) => updateStockOptionGrant(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast.success("Stock option grant has been updated successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update stock option grant.");
    },
  });
};

export const useExerciseStockOption = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<ExerciseOptionResponse, Error, ExerciseOptionRequest>({
    mutationFn: (request) => exerciseStockOption(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast.success("Stock option has been exercised successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to exercise stock option.");
    },
  });
};

export const useDeleteStockOptionGrant = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<DeleteGrantResponse, Error, DeleteGrantRequest>({
    mutationFn: (request) => deleteStockOptionGrant(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast.success("Stock option grant has been deleted successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to delete stock option grant.");
    },
  });
};

export const useAddStockOptionGrant = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  const queryClient = useQueryClient();

  return useMutation<AddGrantResponse, Error, AddGrantRequest>({
    mutationFn: (request) => addStockOptionGrant(companyId!, request),
    onSuccess: (data) => {
      // Invalidate and refetch stock option plan data
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanSummary", companyId] });
      queryClient.invalidateQueries({ queryKey: ["stockOptionPlanTable", companyId] });
      
      toast.success("Stock option grant has been created successfully.");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to create stock option grant.");
    },
  });
};
