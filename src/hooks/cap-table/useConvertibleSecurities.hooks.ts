import { useQuery } from "@tanstack/react-query";
import { getConvertibleSecurities } from "@/services/cap-table/convertibleSecurities.service";
import { useAuth } from "@/contexts/AuthContext";
import { ConvertibleSecuritiesData } from "@/types/capTable";

export const useConvertibleSecurities = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  return useQuery<ConvertibleSecuritiesData>({
    queryKey: ["convertibleSecurities", companyId],
    queryFn: () => getConvertibleSecurities(companyId!),
    enabled: !!companyId,
  });
};
