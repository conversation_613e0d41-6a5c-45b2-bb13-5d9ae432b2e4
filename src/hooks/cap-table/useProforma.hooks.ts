import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import { proformaService, ProFormaApiParams, UpdateInvestorParams } from '@/services/cap-table/proforma.service';
import { useAuth } from '@/contexts/AuthContext';
import { ProFormaInputs, ProFormaFilters } from '@/types/capTable';
import { useGlobalFilters } from '@/contexts/GlobalFilterContext';
import { toast } from 'sonner';

// Custom debounce hook for API parameters
export const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useProformaData = (params: ProFormaApiParams) => {
  const { user } = useAuth();
  const companyId = user?.companyId;
  
  // Debounce the API parameters to prevent excessive API calls
  const debouncedParams = useDebounce(params, 500); // 500ms debounce delay
  
  // Memoize the query key to prevent unnecessary re-renders
  const queryKey = useMemo(() => ['proforma', companyId, debouncedParams], [companyId, debouncedParams]);
  
  return useQuery({
    queryKey,
    queryFn: () => proformaService.getProformaData(companyId!, debouncedParams),
    enabled: !!companyId,
    retry: 2,
    retryDelay: 1000,
    // Add refetch on window focus to ensure data freshness
    refetchOnWindowFocus: false,
    // Prevent refetch on mount if data is fresh
    refetchOnMount: false,
  });
};

export const useUpdateInvestorInvestmentAmount = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const companyId = user?.companyId;
  
  return useMutation({
    mutationFn: (data: UpdateInvestorParams) => 
      proformaService.updateInvestorInvestmentAmount(companyId!, data),
    onSuccess: () => {
      // Invalidate and refetch proforma data
      queryClient.invalidateQueries({ queryKey: ['proforma'] });
    },
  });
};

export const useDeleteInvestorFromProforma = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const companyId = user?.companyId;
  
  return useMutation({
    mutationFn: (data: {
      id?: string;
      officerId?: string;
      serviceProviderId?: string;
      valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
    }) => proformaService.deleteInvestorFromProforma(companyId!, data),
    onSuccess: () => {
      // Invalidate and refetch proforma data
      queryClient.invalidateQueries({ queryKey: ['proforma'] });
    },
  });
};

// Convert UI inputs and filters to API format
export const useConvertToApiFormat = () => {
  return useCallback((inputs: ProFormaInputs, filters?: ProFormaFilters): ProFormaApiParams => {
    return {
      PrePostMoneyValuation: inputs.fixedValuation || undefined,
      InvestmentAmount: inputs.investmentAmount || undefined,
      OptionPoolAvailableForIssuance: inputs.optionPoolPercentage || undefined,
      CommonStockBuffer: inputs.commonStockBuffer || undefined,
      ValuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money',
      PreferredStockDesignation: inputs.preferredStockDesignation === 'Series A' ? 'SeriesA' : 'SeriesSeed',
      // Filter parameters
      ProFormaRoleFilter: filters?.roleFilter,
      ProFormaShareClassFilter: filters?.shareClassFilter,
      ShareHolderName: filters?.shareholderNameSearch
    };
  }, []);
};

// Convert API response to UI format for cap table (Updated for new API structure)
export const useConvertApiResponseToCapTable = () => {
  return useCallback((apiResponse: any, dynamicSeriesColumns: string[] = []) => {
    const { proFormaCapTable } = apiResponse;
    
    // Use provided dynamic series columns or generate from API response
    const seriesColumns = dynamicSeriesColumns.length > 0 
      ? dynamicSeriesColumns 
      : Object.keys(proFormaCapTable.preferredCommonStockholders)
          .filter(key => key !== 'Unallocated New Money')
          .map(key => key); // Series Seed1 -> Series Seed1 (without " Shares")
    
    // Convert Section 1: classACommonStockholders
    const section1Rows = proFormaCapTable.classACommonStockholders.map((stockholder: any) => {
      const row: any = {
        id: stockholder.id,
        name: stockholder.shareholderName,
        investmentAmount: stockholder.cashInvestment, // Updated field name
        commonStockShares: stockholder.commonStock,
        stockOptionPlanShares: stockholder.sopCommonStock,
        totalShares: stockholder.newTotal,
        fullyDilutedOwnership: stockholder.fullyDilutedOwnership,
        newFullyDilutedOwnership: stockholder.newFullyDilutedOwnership,
        ownershipChange: stockholder.newFullyDilutedOwnership - stockholder.fullyDilutedOwnership,
        catchInvestment: stockholder.actualWireAmount,
        seriesAShares: stockholder.series || 0, // New field for Series A/Series Seed column
        // Add new ID fields for different investor types
        officerId: stockholder.officerId,
        serviceProviderId: stockholder.serviceProviderId,
        hasValidId: !!(stockholder.id || stockholder.officerId || stockholder.serviceProviderId), // Check if any ID is present
      };

      // Add dynamic series columns - all 0 for common stockholders
      seriesColumns.forEach(series => {
        const seriesKey = series.toLowerCase().replace(' ', '') + 'Shares';
        row[seriesKey] = 0;
      });

      return row;
    });
    
    // Convert Section 2: SOP Summary rows (hardcoded names with API data)
    const section2Rows = [
      {
        id: 'outstanding-sop',
        name: 'Outstanding Stock Option Plan Shares',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        totalShares: proFormaCapTable.outstandingStockOptionPlanShares?.totalShares || 0,
        fullyDilutedOwnership: proFormaCapTable.outstandingStockOptionPlanShares?.fullyDilutedOwnership || 0,
        newFullyDilutedOwnership: proFormaCapTable.outstandingStockOptionPlanShares?.newFullyDilutedOwnership || 0,
        ownershipChange: (proFormaCapTable.outstandingStockOptionPlanShares?.newFullyDilutedOwnership || 0) - (proFormaCapTable.outstandingStockOptionPlanShares?.fullyDilutedOwnership || 0),
        catchInvestment: 0,
        isStockOptionPoolRow: true,
        ...seriesColumns.reduce((acc: any, series: string) => {
          acc[series.toLowerCase().replace(' ', '') + 'Shares'] = 0;
          return acc;
        }, {}),
      },
      {
        id: 'promised-sop',
        name: 'Promised Stock Option Plan Shares',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        totalShares: proFormaCapTable.promisedStockOptionPlanShares?.totalShares || 0,
        fullyDilutedOwnership: proFormaCapTable.promisedStockOptionPlanShares?.fullyDilutedOwnership || 0,
        newFullyDilutedOwnership: proFormaCapTable.promisedStockOptionPlanShares?.newFullyDilutedOwnership || 0,
        ownershipChange: (proFormaCapTable.promisedStockOptionPlanShares?.newFullyDilutedOwnership || 0) - (proFormaCapTable.promisedStockOptionPlanShares?.fullyDilutedOwnership || 0),
        catchInvestment: 0,
        isStockOptionPoolRow: true,
        ...seriesColumns.reduce((acc: any, series: string) => {
          acc[series.toLowerCase().replace(' ', '') + 'Shares'] = 0;
          return acc;
        }, {}),
      },
      {
        id: 'available-sop',
        name: 'Stock Option Plan Shares Available',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        totalShares: proFormaCapTable.stockOptionPlanSharesAvailable?.totalShares || 0,
        fullyDilutedOwnership: proFormaCapTable.stockOptionPlanSharesAvailable?.fullyDilutedOwnership || 0,
        newFullyDilutedOwnership: proFormaCapTable.stockOptionPlanSharesAvailable?.newFullyDilutedOwnership || 0,
        ownershipChange: (proFormaCapTable.stockOptionPlanSharesAvailable?.newFullyDilutedOwnership || 0) - (proFormaCapTable.stockOptionPlanSharesAvailable?.fullyDilutedOwnership || 0),
        catchInvestment: 0,
        isStockOptionPoolRow: true,
        ...seriesColumns.reduce((acc: any, series: string) => {
          acc[series.toLowerCase().replace(' ', '') + 'Shares'] = 0;
          return acc;
        }, {}),
      },
      {
        id: 'pool-increase',
        name: 'Stock Option Pool Increase',
        investmentAmount: 0,
        commonStockShares: 0,
        stockOptionPlanShares: 0,
        totalShares: proFormaCapTable.sopPoolIncrease?.totalShares || 0,
        fullyDilutedOwnership: proFormaCapTable.sopPoolIncrease?.fullyDilutedOwnership || 0,
        newFullyDilutedOwnership: proFormaCapTable.sopPoolIncrease?.newFullyDilutedOwnership || 0,
        ownershipChange: (proFormaCapTable.sopPoolIncrease?.newFullyDilutedOwnership || 0) - (proFormaCapTable.sopPoolIncrease?.fullyDilutedOwnership || 0),
        catchInvestment: 0,
        isStockOptionPoolRow: true,
        ...seriesColumns.reduce((acc: any, series: string) => {
          acc[series.toLowerCase().replace(' ', '') + 'Shares'] = 0;
          return acc;
        }, {}),
      },
    ];
    
    // Convert Section 3: preferredCommonStockholders (arrays) + seiresADetails
    const section3Rows: any[] = [];
    
    // Process each series array in preferredCommonStockholders
    Object.entries(proFormaCapTable.preferredCommonStockholders).forEach(([seriesKey, investors]: [string, any]) => {
      if (Array.isArray(investors)) {
        investors.forEach((investor: any) => {
          const row: any = {
            id: investor.id || `${seriesKey}-${investor.investor || 'unknown'}`,
            name: investor.investor || seriesKey,
            investmentAmount: investor.cashInvestment || 0, // Updated field name
            commonStockShares: 0,
            stockOptionPlanShares: 0,
            totalShares: investor.resultingShares,
            fullyDilutedOwnership: investor.newFullyDilutedOwnerShip,
            ownershipChange: investor.newFullyDilutedOwnerShip - (investor.fullyDilutedOwnership || 0),
            catchInvestment: investor.actualWireAmount,
            isSection3Row: true,
            hasValidId: !!investor.id, // Add flag to track if row has valid ID for editing
            seriesAShares: 0, // Will be populated from seiresADetails
          };
          
          // Add dynamic series columns - map resultingShares to appropriate series
          seriesColumns.forEach(series => {
            const seriesKeyForRow = series.toLowerCase().replace(' ', '') + 'Shares';
            row[seriesKeyForRow] = seriesKey === series ? investor.resultingShares : 0;
          });
          
          section3Rows.push(row);
        });
      }
    });
    
    // Add seiresADetails to Series A/Series Seed column for Section 3
    if (proFormaCapTable.seiresADetails && Array.isArray(proFormaCapTable.seiresADetails)) {
      proFormaCapTable.seiresADetails.forEach((seriesADetail: any, index: number) => {
        // Find corresponding row or create new one
        const existingRow = section3Rows.find((row: any) => row.id === seriesADetail.id);
        if (existingRow) {
          existingRow.seriesAShares = seriesADetail.resultingShares;
        } else {
          const newRow: any = {
            id: seriesADetail.id || `series-a-${index}`,
            name: seriesADetail.investor || 'Series A Investor',
            investmentAmount: seriesADetail.cashInvestment || 0,
            commonStockShares: 0,
            stockOptionPlanShares: 0,
            seriesAShares: seriesADetail.resultingShares,
            totalShares: seriesADetail.resultingShares,
            fullyDilutedOwnership: seriesADetail.newFullyDilutedOwnerShip,
            ownershipChange: seriesADetail.newFullyDilutedOwnerShip - (seriesADetail.fullyDilutedOwnership || 0),
            catchInvestment: seriesADetail.actualWireAmount,
            isSection3Row: true,
            hasValidId: !!seriesADetail.id,
          };
          
          // Add dynamic series columns - all 0 for seiresADetails
          seriesColumns.forEach(series => {
            const seriesKeyForRow = series.toLowerCase().replace(' ', '') + 'Shares';
            newRow[seriesKeyForRow] = 0;
          });
          
          section3Rows.push(newRow);
        }
      });
    }
    
    return {
      items: [...section1Rows, ...section2Rows, ...section3Rows],
      seriesColumns,
    };
  }, []);
};

// Convert Pro Rata Rights data from new API structure
export const useConvertProRataRights = () => {
  return useCallback((apiResponse: any) => {
    const { proRataRights } = apiResponse;
    
    if (!Array.isArray(proRataRights)) {
      return {
        items: [],
        totalProRataAmount: 0,
        roundType: 'Series A' as const,
        pricePerShare: 0,
      };
    }
    
    const items = proRataRights.map((item: any, index: number) => ({
      id: `pro-rata-${index}`,
      investorName: item.investor || 'Unknown Investor',
      currentOwnershipPercentage: item.currentOwnershipPercentage || 0,
      proRataAmount: item.prorataAmount || 0, // Updated field name
      numberOfSeriesAShares: item.seriesAShares || 0,
      isExistingInvestor: true,
    }));
    
    const totalProRataAmount = items.reduce((sum, item) => sum + item.proRataAmount, 0);
    
    return {
      items,
      totalProRataAmount,
      roundType: 'Series A' as const,
      pricePerShare: 0, // Will be calculated based on round details
    };
  }, []);
};

// Interface for investment amount update parameters (Single Responsibility Principle)
interface InvestmentUpdateParams {
  id?: string; // For investor type
  officerId?: string; // For officer type
  serviceProviderId?: string; // For service provider type
  shareholderName: string;
  investmentAmount: number;
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}

// Interface for debounced update state (Interface Segregation Principle)
interface DebouncedUpdateState {
  itemId: string;
  value: number;
  timestamp: number;
  // Include ID fields for API payload
  id?: string;
  officerId?: string;
  serviceProviderId?: string;
}

// Custom hook for debounced investment amount updates (Single Responsibility Principle)
export const useDebouncedInvestmentAmountUpdate = (
  inputs: ProFormaInputs, 
  capTableItems: any[], 
  setCapTableItems: any
) => {
  const updateInvestorMutation = useUpdateInvestorInvestmentAmount();
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, DebouncedUpdateState>>(new Map());
  const [isUpdating, setIsUpdating] = useState<Set<string>>(new Set());

  // Debounce delay constant (Open/Closed Principle - easy to modify)
  const DEBOUNCE_DELAY = 900; // 800ms for investment amount updates

  // Validation logic (Single Responsibility Principle)
  const validateInvestmentAmount = useCallback((value: number, itemName: string): boolean => {
    if (value < 0) {
      toast.error("Investment amount cannot be negative");
      return false;
    }

    if (value > 100000000) {
      toast.error("Investment amount seems unusually high. Please verify the amount.");
      return false;
    }

    return true;
  }, []);

  // API call logic (Single Responsibility Principle)
  const performApiUpdate = useCallback(async (params: InvestmentUpdateParams, originalValue: number) => {
    const { id, officerId, serviceProviderId, shareholderName, investmentAmount } = params;
    
    // Determine which ID to use for tracking
    const trackingId = id || officerId || serviceProviderId || 'unknown';
    
    let loadingToast: any = null;
    
    try {
      setIsUpdating(prev => new Set(prev).add(trackingId));
      
      loadingToast = toast.loading(`Updating investment amount for ${shareholderName}...`);

      const result = await updateInvestorMutation.mutateAsync(params);

      // Check if the result contains an error (API client returns { error: "..." } on error)
      if (result && typeof result === 'object' && 'error' in result) {
        // Only throw if the error is not null/undefined and not empty
        if (result.error && result.error !== null && result.error !== '') {
          throw new Error(result.error as string);
        }
      }

      toast.dismiss(loadingToast);
      toast.success(`Investment amount updated for ${shareholderName}`, {
        description: `New amount: $${investmentAmount.toLocaleString()}`,
        duration: 3000,
      });
    } catch (error: any) {
      if (loadingToast) {
        toast.dismiss(loadingToast);
      }
      toast.error(`Failed to update investment amount for ${shareholderName}`, {
        description: error?.message || error?.toString() || 'Please try again',
        duration: 5000,
      });
      
      // Revert local state on error (Dependency Inversion Principle)
      setCapTableItems((prev: any[]) => prev.map((prevItem: any) => 
        (prevItem.id === id || prevItem.officerId === officerId || prevItem.serviceProviderId === serviceProviderId)
          ? { ...prevItem, investmentAmount: originalValue }
          : prevItem
      ));
    } finally {
      setIsUpdating(prev => {
        const newSet = new Set(prev);
        newSet.delete(trackingId);
        return newSet;
      });
    }
  }, [updateInvestorMutation, setCapTableItems]);

  // Debounced update effect (Single Responsibility Principle)
  useEffect(() => {
    if (pendingUpdates.size === 0) return;

    const timeouts = new Map<string, NodeJS.Timeout>();

    pendingUpdates.forEach((updateState, itemId) => {
      const timeout = setTimeout(async () => {
        const item = capTableItems.find(capItem => capItem.id === itemId);
        if (!item || !item.hasValidId) return;

        // Validate before API call
        if (!validateInvestmentAmount(updateState.value, item.name)) {
          return;
        }

        // Determine which ID field to send based on section and available IDs
        let params: InvestmentUpdateParams;
        
        if (item.isSection3Row) {
          // Bottom section (Section 3) - send id
          params = {
            id: item.id,
            shareholderName: item.name,
            investmentAmount: updateState.value,
            valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
          };
        } else {
          // Top section (Section 1) - send officerId or serviceProviderId, whichever is present
          if (item.officerId) {
            params = {
              officerId: item.officerId,
              shareholderName: item.name,
              investmentAmount: updateState.value,
              valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
            };
          } else if (item.serviceProviderId) {
            params = {
              serviceProviderId: item.serviceProviderId,
              shareholderName: item.name,
              investmentAmount: updateState.value,
              valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
            };
          } else {
            // Fallback to id if neither officerId nor serviceProviderId is present
            params = {
              id: item.id,
              shareholderName: item.name,
              investmentAmount: updateState.value,
              valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
            };
          }
        }

        await performApiUpdate(params, item.investmentAmount);
        
        // Clean up pending update
        setPendingUpdates(prev => {
          const newMap = new Map(prev);
          newMap.delete(itemId);
          return newMap;
        });
      }, DEBOUNCE_DELAY);

      timeouts.set(itemId, timeout);
    });

    // Cleanup function
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [pendingUpdates, capTableItems, inputs.designation, validateInvestmentAmount, performApiUpdate]);

  // Main handler for investment amount changes (Single Responsibility Principle)
  const handleInvestmentAmountChange = useCallback((item: any, newValue: number) => {
    // Update local state immediately for responsive UI
    setCapTableItems((prev: any[]) => prev.map((prevItem: any) => 
      prevItem.id === item.id 
        ? { ...prevItem, investmentAmount: newValue }
        : prevItem
    ));

    // Only schedule API call if the item has a valid ID
    if (item.hasValidId) {
      // Add to pending updates (debounced) with ID fields
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.set(item.id, {
          itemId: item.id,
          value: newValue,
          timestamp: Date.now(),
          // Include ID fields for API payload
          id: item.id,
          officerId: item.officerId,
          serviceProviderId: item.serviceProviderId,
        });
        return newMap;
      });
    }
  }, [setCapTableItems]);

  // Check if an item is currently being updated
  const isItemUpdating = useCallback((itemId: string) => {
    return isUpdating.has(itemId);
  }, [isUpdating]);

  // Check if an item has pending updates
  const hasPendingUpdate = useCallback((itemId: string) => {
    return pendingUpdates.has(itemId);
  }, [pendingUpdates]);

  return {
    handleInvestmentAmountChange,
    isItemUpdating,
    hasPendingUpdate,
  };
};

// Main Pro Forma hook that combines all functionality
export const useProformaModel = (initialInputs: ProFormaInputs) => {
  const [inputs, setInputs] = useState<ProFormaInputs>(initialInputs);
  const [capTableItems, setCapTableItems] = useState<any[]>([]);
  const [dynamicSeriesColumns, setDynamicSeriesColumns] = useState<string[]>([]);
  const [proRataRights, setProRataRights] = useState<any>(null);
  
  // Use global filter state instead of local state
  const { filters } = useGlobalFilters();
  
  // Get conversion functions
  const convertToApiFormat = useConvertToApiFormat();
  const convertApiResponseToCapTable = useConvertApiResponseToCapTable();
  const convertProRataRights = useConvertProRataRights();
  
  // Get debounced investment update functionality
  const { handleInvestmentAmountChange, isItemUpdating, hasPendingUpdate } = useDebouncedInvestmentAmountUpdate(inputs, capTableItems, setCapTableItems);
  
  // Convert inputs and filters to API format and fetch data
  const apiParams = useMemo(() => convertToApiFormat(inputs, filters), [inputs, filters, convertToApiFormat]);
  const { data: apiData, isLoading, error } = useProformaData(apiParams);
  const typedApiData = apiData as any;

  // Update cap table when API data changes
  useEffect(() => {
    if (typedApiData?.proFormaCapTable) {
      const convertedData = convertApiResponseToCapTable(typedApiData, dynamicSeriesColumns);
      setCapTableItems(convertedData.items);
      // Don't set dynamic columns here - let the dropdown effect handle it
    }
    
    // Update Pro Rata Rights data
    if (typedApiData?.proRataRights) {
      const convertedProRataRights = convertProRataRights(typedApiData);
      setProRataRights(convertedProRataRights);
    }
  }, [typedApiData, convertApiResponseToCapTable, convertProRataRights, dynamicSeriesColumns]);

  // Update dynamic series columns when dropdown changes (for both API and static data)
  useEffect(() => {
    if (typedApiData?.proFormaCapTable) {
      // For API data, generate columns based on dropdown selection and API response
      const baseSeries = inputs.preferredStockDesignation === 'Series A' ? 'Series A' : 'Series Seed';
      const apiSeriesKeys = Object.keys(typedApiData.proFormaCapTable.preferredCommonStockholders)
        .filter(key => key !== 'Unallocated New Money');
      
      // Find ALL matching series keys based on dropdown selection
      const matchingSeries = apiSeriesKeys.filter(key => 
        key.includes(baseSeries) && key !== baseSeries
      );
      
      if (matchingSeries.length > 0) {
        setDynamicSeriesColumns(matchingSeries);
      } else {
        // Fallback to default pattern
        setDynamicSeriesColumns([`${baseSeries}1`]);
      }
    } else {
      // For static data, generate columns based on dropdown selection
      const baseSeries = inputs.preferredStockDesignation === 'Series A' ? 'Series A' : 'Series Seed';
      setDynamicSeriesColumns([`${baseSeries}1`]);
    }
  }, [inputs.preferredStockDesignation, typedApiData]);

  // Handle investment amount change with proper debouncing
  const handleInvestmentChange = useCallback((item: any, newValue: number) => {
    handleInvestmentAmountChange(item, newValue);
  }, [handleInvestmentAmountChange]);

  // Handle null/undefined values in UI
  const formatValue = useCallback((value: number | null | undefined): string => {
    if (value === null || value === undefined) return '-';
    if (value === 0) return '0';
    return value.toString();
  }, []);

  // Helper function to format currencies
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  }, []);

  // Helper function to format price per share (preserves decimal places from API)
  const formatPricePerShare = useCallback((amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 6, // Allow up to 6 decimal places to preserve API precision
    }).format(amount);
  }, []);

  // Helper function to format numbers
  const formatNumber = useCallback((num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  }, []);

  // Helper function to format percentages
  const formatPercentage = useCallback((num: number) => {
    return `${num.toFixed(2)}%`;
  }, []);

  // Negative value formatting functions for Pro Forma Cap Table
  const formatNegativeValue = useCallback((value: number | null | undefined): string => {
    if (value === null || value === undefined) return '-';
    if (value === 0) return '0';
    if (value < 0) return `(${Math.abs(value)})`;
    return value.toString();
  }, []);

  const formatCurrencyWithParentheses = useCallback((value: number | null | undefined): string => {
    if (value === null || value === undefined) return '-';
    if (value === 0) return '$0';
    if (value < 0) return `($${Math.abs(value).toLocaleString()})`;
    return `$${value.toLocaleString()}`;
  }, []);

  const formatNumberWithParentheses = useCallback((value: number | null | undefined): string => {
    if (value === null || value === undefined) return '-';
    if (value === 0) return '0';
    if (value < 0) return `(${Math.abs(value).toLocaleString()})`;
    return value.toLocaleString();
  }, []);

  const formatPercentageWithParentheses = useCallback((value: number | null | undefined): string => {
    if (value === null || value === undefined) return '-';
    if (value === 0) return '0.00%';
    if (value < 0) return `(${Math.abs(value).toFixed(2)}%)`;
    return `${value.toFixed(2)}%`;
  }, []);


  return {
    // State
    inputs,
    setInputs,
    capTableItems,
    dynamicSeriesColumns,
    
    // API Data
    apiData: typedApiData,
    isLoading,
    error,
    
    // Handlers
    handleInvestmentChange,
    
    // Debounced Update Status
    isItemUpdating,
    hasPendingUpdate,
    
    // Pro Rata Rights Data
    proRataRights,
    
    // Utilities
    formatValue,
    formatCurrency,
    formatPricePerShare,
    formatNumber,
    formatPercentage,
    
    // Negative Value Formatting (for Pro Forma Cap Table)
    formatNegativeValue,
    formatCurrencyWithParentheses,
    formatNumberWithParentheses,
    formatPercentageWithParentheses,
  };
};
