import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { manageDirectorsService, DirectorSummary, AddDirectorRequest, Director, RemoveDirectorRequest, ResignationDirectorRequest } from "@/services/manage-directors/manageDirectors.service";
import { useAuthState } from "@/contexts/auth/useAuthState";

export const useManageDirectors = () => {
  const { user } = useAuthState();
  const queryClient = useQueryClient();

  // Query for director summary
  const {
    data: directorSummary,
    isLoading: directorSummaryLoading,
    error: directorSummaryError,
  } = useQuery({
    queryKey: ["directorSummary", user?.companyId],
    queryFn: () => manageDirectorsService.getDirectorSummary(user?.companyId!),
    enabled: !!user?.companyId,
  });

  // Query for directors list
  const {
    data: directors = [],
    isLoading: directorsLoading,
    error: directorsError,
  } = useQuery({
    queryKey: ["directors", user?.companyId],
    queryFn: () => manageDirectorsService.getDirectors(user?.companyId!),
    enabled: !!user?.companyId,
  });

  // Mutation for adding director
  const addDirectorMutation = useMutation({
    mutationFn: (payload: AddDirectorRequest) => 
      manageDirectorsService.addDirector(user?.companyId!, payload),
    onSuccess: () => {
      // Invalidate and refetch both queries
      queryClient.invalidateQueries({ queryKey: ["directorSummary", user?.companyId] });
      queryClient.invalidateQueries({ queryKey: ["directors", user?.companyId] });
    },
  });

  // Mutation for removing director
  const removeDirectorMutation = useMutation({
    mutationFn: ({ directorId, payload }: { directorId: string; payload: RemoveDirectorRequest }) =>
      manageDirectorsService.removeDirector(user?.companyId!, {
        officerId: directorId,
        removalDate: payload.removalDate,
      }),
    onSuccess: () => {
      // Invalidate and refetch both queries
      queryClient.invalidateQueries({ queryKey: ["directorSummary", user?.companyId] });
      queryClient.invalidateQueries({ queryKey: ["directors", user?.companyId] });
    },
  });

  // Mutation for resigning director
  const resignDirectorMutation = useMutation({
    mutationFn: (payload: ResignationDirectorRequest) =>
      manageDirectorsService.resignationDirector(user?.companyId!, payload),
    onSuccess: () => {
      // Invalidate and refetch both queries
      queryClient.invalidateQueries({ queryKey: ["directorSummary", user?.companyId] });
      queryClient.invalidateQueries({ queryKey: ["directors", user?.companyId] });
    },
  });

  const addDirector = async (payload: AddDirectorRequest) => {
    if (!user?.companyId) throw new Error("No company selected");
    return addDirectorMutation.mutateAsync(payload);
  };

  const removeDirector = async (directorId: string) => {
    if (!user?.companyId) throw new Error("No company selected");
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    return removeDirectorMutation.mutateAsync({ 
      directorId, 
      payload: { officerId: directorId, removalDate: currentDate } 
    });
  };

  const resignDirector = async (directorId: string, resignationDate?: string) => {
    if (!user?.companyId) throw new Error("No company selected");
    const dateToUse = resignationDate || new Date().toISOString().split('T')[0]; // Use provided date or current date
    return resignDirectorMutation.mutateAsync({ 
      officerId: directorId, 
      resignationDate: dateToUse 
    });
  };

  const loading = directorSummaryLoading || directorsLoading;
  const error = directorSummaryError || directorsError;

  return {
    directorSummary,
    directors,
    loading,
    error: error?.message || null,
    addDirector,
    removeDirector,
    resignDirector,
    refetch: () => {
      queryClient.invalidateQueries({ queryKey: ["directorSummary", user?.companyId] });
      queryClient.invalidateQueries({ queryKey: ["directors", user?.companyId] });
    },
  };
};
