import React, { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import IncreaseOptionPlanInitialState from "./IncreaseOptionPlanInitialState";
import { useStockOptionPlan } from "@/hooks/stock-option-plan/useStockOptionPlan.hooks";
import IncreaseOptionPlanSkeleton from "@/components/shares/IncreaseOptionPlanSkeleton";

interface IncreaseOptionPlanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: string;
  onOpenIncreaseShares?: () => void;
}

const IncreaseOptionPlanDialog: React.FC<IncreaseOptionPlanDialogProps> = ({ isOpen, onClose, companyId, onOpenIncreaseShares }) => {
  // Fetch when modal opens
  const { data, isLoading, isBoardApprovalLoading, isStockholderApprovalLoading, isAbandonLoading, refetch, submitForBoardApproval, submitForStockholderApproval, abandonProcess } = useStockOptionPlan(companyId, isOpen);

  const [additionalShares, setAdditionalShares] = useState<string>("");

  useEffect(() => {
    if (!isOpen) {
      setAdditionalShares("");
    }
  }, [isOpen]);

  const handleAdditionalSharesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    setAdditionalShares(value ? Number(value).toLocaleString() : "");
  };

  // If data is not yet in cache, avoid rendering zeros; show loader until it arrives
  const hasData = !!data;
  const additionalSharesNum = additionalShares ? parseInt(additionalShares.replace(/,/g, ""), 10) : 0;
  const sharesReservedForIssuance = hasData ? data!.totalPoolSize : 0;
  const promisedGrants = hasData ? data!.promiseGrant : 0;
  const issuedGrants = hasData ? data!.allocated : 0;
  const sharesRemaining = hasData ? data!.remaining : 0;

  const apiAdditionalShare = hasData ? data!.pendingDetail.additionalShare || 0 : 0;
  const isAdditionalEditable = hasData ? apiAdditionalShare === 0 : true;

  const newTotalOptionShares = hasData ? sharesReservedForIssuance + (apiAdditionalShare || additionalSharesNum) : 0;
  const issuedAndOutstanding = hasData ? (data!.totalAuthorizedSize - data!.totalRemaining) : 0;
  const requiredAuthorizedShares = hasData ? issuedAndOutstanding + newTotalOptionShares : 0;
  const exceedsAuthorized = hasData ? requiredAuthorizedShares > data!.totalAuthorizedSize : false;

  const boardApprovalStatus = hasData ? data!.pendingDetail.isBoardApproved : "no";
  const stockholderApprovalStatus = hasData ? data!.pendingDetail.isStockHolderApproved : "no";

  const handleSubmit = async () => {
    const toSubmit = apiAdditionalShare > 0 ? apiAdditionalShare : additionalSharesNum;
    if (toSubmit && !isNaN(toSubmit)) {
      await submitForBoardApproval(toSubmit);
      await refetch();
    }
  };

  const handleClose = () => {
    setAdditionalShares("");
    onClose();
  };

  const handleAbandon = async () => {
    await abandonProcess();
    await refetch();
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md sm:max-w-lg">
        {(isLoading || !hasData) ? (
          <IncreaseOptionPlanSkeleton />
        ) : (
          <IncreaseOptionPlanInitialState
            sharesReservedForIssuance={sharesReservedForIssuance}
            promisedGrants={promisedGrants}
            sharesRemaining={sharesRemaining}
            additionalShares={additionalShares}
            apiAdditionalShare={apiAdditionalShare}
            isAdditionalEditable={isAdditionalEditable}
            totalOptionShares={newTotalOptionShares}
            exceedsAuthorized={exceedsAuthorized}
            requiredAuthorizedShares={requiredAuthorizedShares}
            boardApprovalStatus={boardApprovalStatus}
            stockholderApprovalStatus={stockholderApprovalStatus}
            isBoardApprovalLoading={isBoardApprovalLoading}
            isStockholderApprovalLoading={isStockholderApprovalLoading}
            handleAdditionalSharesChange={handleAdditionalSharesChange}
            handleClose={handleClose}
            onSubmit={handleSubmit}
            onAbandon={handleAbandon}
            isAbandonLoading={isAbandonLoading}
            onOpenIncreaseShares={onOpenIncreaseShares}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default IncreaseOptionPlanDialog;
