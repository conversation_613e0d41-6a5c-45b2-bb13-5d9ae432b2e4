import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { <PERSON><PERSON><PERSON>eader, DialogFooter, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Spinner } from "@/components/ui/spinner";

interface Props {
  shareAmountDisplay: string;
  isEditable: boolean;
  boardApprovalStatus: "yes" | "no" | "pending";
  stockholderApprovalStatus: "yes" | "no" | "pending";
  exceedsAuthorized: boolean;
  requiredAuthorizedShares: number;
  onChangeShareAmount: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void; // Changed from onSubmitBoard
  onAbandon: () => void;
  onClose: () => void;
  onOpenIncreaseShares?: () => void;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
}

const CreateStockOptionPlanInitialState: React.FC<Props> = ({
  shareAmountDisplay,
  isEditable,
  boardApprovalStatus,
  stockholderApprovalStatus,
  exceedsAuthorized,
  requiredAuthorizedShares,
  onChangeShareAmount,
  onSubmit,
  onAbandon,
  onClose,
  onOpenIncreaseShares,
  isBoardApprovalLoading,
  isStockholderApprovalLoading,
  isAbandonLoading,
}) => {
  const formatStatus = (s: "yes" | "no" | "pending") => (s === "yes" ? "Yes" : s === "pending" ? "Pending" : "No");
  const hasShareAmount = !!shareAmountDisplay && shareAmountDisplay !== "0";
  const canSubmit = hasShareAmount && boardApprovalStatus === "no" && !exceedsAuthorized;

  const getSubmitButtonTooltip = () => {
    if (!hasShareAmount) return "Please enter share amount first";
    if (boardApprovalStatus === "yes") return "Board approval already completed";
    if (boardApprovalStatus === "pending") return "Board approval is in progress";
    if (exceedsAuthorized) return "Exceeds authorized shares - increase authorized shares first";
    return "Submit for board approval";
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Create Stock Option Plan</DialogTitle>
        <DialogDescription>Set up your company's initial stock option plan</DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="shareAmount" className="text-right">Share Amount:</Label>
          <Input
            id="shareAmount"
            value={shareAmountDisplay}
            onChange={isEditable ? onChangeShareAmount : undefined}
            className="col-span-3"
            placeholder="Enter number of shares"
            readOnly={!isEditable}
          />
        </div>

        {exceedsAuthorized && (
          <Alert className="mt-2 border-amber-500">
            <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
            <AlertDescription className="italic">
              The total amount of shares reserved will exceed the authorized share
              capital of the company. To create the plan, please first
              <button type="button" className="ml-1 text-blue-500 underline" onClick={onOpenIncreaseShares}>Increase Authorized Shares</button>
              to at least {requiredAuthorizedShares.toLocaleString()}.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Board Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{formatStatus(boardApprovalStatus)}</span>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Stockholders Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{formatStatus(stockholderApprovalStatus)}</span>
          </div>
        </div>
      </div>

      <DialogFooter className="flex items-center justify-between">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline" 
                onClick={canSubmit && !isBoardApprovalLoading ? onSubmit : undefined}
                className={!canSubmit || isBoardApprovalLoading ? "opacity-50 cursor-not-allowed" : ""}
              >
                {isBoardApprovalLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{getSubmitButtonTooltip()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default CreateStockOptionPlanInitialState;
