import React from "react";
import { DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";

const SkeletonRow: React.FC<{ labelWidth?: string; inputWidth?: string }> = ({ labelWidth = "w-28", inputWidth = "w-full" }) => (
  <div className="grid grid-cols-4 items-center gap-4">
    <div className={`justify-self-end h-4 bg-gray-200 rounded ${labelWidth} animate-pulse`} />
    <div className={`col-span-3 h-10 bg-gray-200 rounded ${inputWidth} animate-pulse`} />
  </div>
);

const IncreaseOptionPlanSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      <DialogHeader>
        <DialogTitle>Increase Stock Option Plan</DialogTitle>
        <DialogDescription>Loading plan details…</DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-2">
        <SkeletonRow />
        <SkeletonRow />
        <SkeletonRow />
        <SkeletonRow />
        <div className="grid grid-cols-4 items-center gap-4">
          <div className="justify-self-end h-4 bg-gray-200 rounded w-28 animate-pulse" />
          <div className="col-span-3 h-10 bg-gray-200 rounded w-full animate-pulse" />
        </div>
        <div className="flex justify-between pt-2">
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse" />
          <div className="h-10 bg-gray-200 rounded w-24 animate-pulse" />
        </div>
      </div>
    </div>
  );
};

export default IncreaseOptionPlanSkeleton;
