import React from "react";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Spinner } from "@/components/ui/spinner";

interface IncreaseSharesInitialStateProps {
  data: {
    authorizedShare: number;
    additionalShare: number;
    isBoardApproved: "yes" | "no" | "pending";
    isStockHolderApproved: "yes" | "no" | "pending";
    isFileOfCertificateOfAmendmentUploaded: "yes" | "no";
  };
  isLoading: boolean;
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  isAbandonLoading: boolean;
  isFileUploadLoading: boolean;
  additionalShares: string;
  onAdditionalSharesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClose: () => void;
  onSubmit: () => void; // New single submit handler
}

const IncreaseSharesInitialState: React.FC<IncreaseSharesInitialStateProps> = ({
  data,
  isLoading,
  isBoardApprovalLoading,
  isStockholderApprovalLoading,
  isAbandonLoading,
  isFileUploadLoading,
  additionalShares,
  onAdditionalSharesChange,
  onClose,
  onSubmit,
}) => {
  // Calculate total shares - use local input if available, otherwise use API data
  const localAdditionalShares = additionalShares ? parseInt(additionalShares.replace(/,/g, ""), 10) : 0;
  const totalShares = data?.authorizedShare + (data?.additionalShare > 0 ? data?.additionalShare : localAdditionalShares);
  
  // Determine if additional shares field should be editable
  const isAdditionalSharesEditable = data?.additionalShare === 0;
  
  // Submit button logic
  const hasAdditionalShares = data?.additionalShare > 0 || (!!additionalShares && additionalShares !== "0");
  const canSubmit = hasAdditionalShares && data?.isBoardApproved === "no";
  
  // Use API data or local input for additional shares
  const displayAdditionalShares = data?.additionalShare > 0 ? data?.additionalShare.toLocaleString() : additionalShares;

  const getSubmitButtonTooltip = () => {
    if (!hasAdditionalShares) return "Please enter additional shares first";
    if (data?.isBoardApproved === "yes") return "Board approval already completed";
    if (data?.isBoardApproved === "pending") return "Board approval is in progress";
    return "Submit for board approval";
  };

  const formatStatus = (s: "yes" | "no" | "pending") => (s === "yes" ? "Yes" : s === "pending" ? "Pending" : "No");
  const formatFileStatus = (s: "yes" | "no") => (s === "yes" ? "Submitted" : "Not Submitted");

  return (
    <>
      <DialogHeader>
        <DialogTitle>Increase Authorized Shares</DialogTitle>
        <DialogDescription>
          Add more shares to your company's authorized capital
        </DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="currentShares" className="text-right">
            Authorized Shares:
          </Label>
          <Input
            id="currentShares"
            value={data?.authorizedShare.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="additionalShares" className="text-right">
            Additional Shares:
          </Label>
          <Input
            id="additionalShares"
            value={displayAdditionalShares}
            onChange={onAdditionalSharesChange}
            className="col-span-3"
            placeholder="Enter number of shares"
            disabled={!isAdditionalSharesEditable}
          />
        </div>

        {(data?.additionalShare > 0 || additionalShares) && (
          <Alert className="mt-2">
            <AlertDescription className="italic">
              The authorized share capital of the Company will be{" "}
              {totalShares.toLocaleString()} shares. To authorize this amount of
              shares, you will need to obtain board consent and stockholder
              consent.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Board Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">
              {formatStatus(data?.isBoardApproved)}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Stockholders Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">
              {formatStatus(data?.isStockHolderApproved)}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">File Amendment:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">
              {formatFileStatus(data?.isFileOfCertificateOfAmendmentUploaded)}
            </span>
          </div>
        </div>
      </div>

      <DialogFooter className="flex items-center justify-between">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline" 
                onClick={canSubmit && !isBoardApprovalLoading ? onSubmit : undefined}
                className={!canSubmit || isBoardApprovalLoading ? "opacity-50 cursor-not-allowed" : ""}
              >
                {isBoardApprovalLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{getSubmitButtonTooltip()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default IncreaseSharesInitialState;
