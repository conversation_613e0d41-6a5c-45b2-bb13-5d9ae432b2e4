import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import IncreaseSharesInitialState from "./IncreaseSharesInitialState";
import { useAuthorizedShares } from "@/hooks/authorized-shares/useAuthorizedShares.hooks";
import { Spinner } from "@/components/ui/spinner";

interface IncreaseSharesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: string;
}

const IncreaseSharesDialog: React.FC<IncreaseSharesDialogProps> = ({
  isOpen,
  onClose,
  companyId,
}) => {
  const { toast } = useToast();
  const [additionalShares, setAdditionalShares] = useState<string>("");

  // Use the new API hook
  const {
    data,
    isLoading,
    isBoardApprovalLoading,
    isStockholderApprovalLoading,
    isAbandonLoading,
    isFileUploadLoading,
    error,
    refetch,
    submitForBoardApproval,
    submitForStockholderApproval,
    abandonProcess,
    uploadFileAmendment,
  } = useAuthorizedShares(companyId);

  const handleAdditionalSharesChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    // Remove commas and ensure only numbers are entered
    const value = e.target.value.replace(/[^0-9]/g, "");
    // Format with commas
    const formattedValue = value ? parseInt(value, 10).toLocaleString() : "";
    setAdditionalShares(formattedValue);
  };

  const handleSubmit = async () => {
    if (!data?.additionalShare && !additionalShares) return;
    
    const sharesToSubmit = data?.additionalShare || parseInt(additionalShares.replace(/,/g, ""), 10);
    
    try {
      // Submit for board approval
      await submitForBoardApproval(sharesToSubmit);
      
      // The backend will automatically handle the entire workflow:
      // 1. Board approval
      // 2. Stockholder approval  
      // 3. File amendment (email to agent)
      
      toast({
        title: "Submitted Successfully",
        description: "Your request has been submitted. The approval process will continue automatically.",
      });
      
      await refetch();
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your request. Please try again.",
        variant: "destructive",
      });
    }
  };

  const resetState = () => {
    setAdditionalShares("");
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  // Show loading state
  if (isLoading && !data) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md sm:max-w-lg">
          <div className="flex items-center justify-center py-8">
            <Spinner className="h-8 w-8" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state
  if (error && !data) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md sm:max-w-lg">
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error loading authorized shares data</p>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md sm:max-w-lg">
        {data ? (
          <IncreaseSharesInitialState
            data={data}
            isLoading={isLoading}
            isBoardApprovalLoading={isBoardApprovalLoading}
            isStockholderApprovalLoading={isStockholderApprovalLoading}
            isAbandonLoading={isAbandonLoading}
            isFileUploadLoading={isFileUploadLoading}
            additionalShares={additionalShares}
            onAdditionalSharesChange={handleAdditionalSharesChange}
            onClose={handleClose}
            onSubmit={handleSubmit}
          />
        ) : (
          <div className="flex items-center justify-center py-8">
            <Spinner className="h-8 w-8" />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default IncreaseSharesDialog;
