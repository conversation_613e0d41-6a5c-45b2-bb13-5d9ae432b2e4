import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>eader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Spinner } from "@/components/ui/spinner";

interface IncreaseOptionPlanInitialStateProps {
  sharesReservedForIssuance: number;
  promisedGrants: number;
  sharesRemaining: number;
  additionalShares: string;
  apiAdditionalShare: number;
  isAdditionalEditable: boolean;
  totalOptionShares: number;
  exceedsAuthorized: boolean;
  requiredAuthorizedShares: number;
  boardApprovalStatus: "yes" | "no" | "pending";
  stockholderApprovalStatus: "yes" | "no" | "pending";
  isBoardApprovalLoading: boolean;
  isStockholderApprovalLoading: boolean;
  handleAdditionalSharesChange: (
    e: React.ChangeEvent<HTMLInputElement>
  ) => void;
  handleClose: () => void;
  onSubmit: () => void; // New prop for submit action
  onAbandon: () => Promise<void>;
  isAbandonLoading: boolean;
  onOpenIncreaseShares?: () => void;
}

const IncreaseOptionPlanInitialState: React.FC<
  IncreaseOptionPlanInitialStateProps
> = ({
  sharesReservedForIssuance,
  promisedGrants,
  sharesRemaining,
  additionalShares,
  apiAdditionalShare,
  isAdditionalEditable,
  totalOptionShares,
  exceedsAuthorized,
  requiredAuthorizedShares,
  boardApprovalStatus,
  stockholderApprovalStatus,
  isBoardApprovalLoading,
  isStockholderApprovalLoading,
  handleAdditionalSharesChange,
  handleClose,
  onSubmit,
  onAbandon,
  isAbandonLoading,
  onOpenIncreaseShares,
}) => {
  const formatStatus = (s: "yes" | "no" | "pending") => (s === "yes" ? "Yes" : s === "pending" ? "Pending" : "No");
  const hasAdditionalShares = (apiAdditionalShare || 0) > 0 || (!!additionalShares && additionalShares !== "0");
  const canSubmit = hasAdditionalShares && boardApprovalStatus === "no" && !exceedsAuthorized;

  const additionalDisplayValue = isAdditionalEditable
    ? additionalShares
    : (apiAdditionalShare || 0).toLocaleString();

  const handleOpenIncreaseShares = () => {
    handleClose();
    onOpenIncreaseShares?.();
  };

  const getSubmitButtonTooltip = () => {
    if (!hasAdditionalShares) return "Please enter additional shares first";
    if (boardApprovalStatus === "yes") return "Board approval already completed";
    if (boardApprovalStatus === "pending") return "Board approval is in progress";
    if (exceedsAuthorized) return "Exceeds authorized shares - increase authorized shares first";
    return "Submit for board approval";
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Increase Stock Option Plan</DialogTitle>
        <DialogDescription>
          Add more shares to your company's stock option plan
        </DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="sharesReserved" className="text-right">
            Shares Reserved:
          </Label>
          <Input
            id="sharesReserved"
            value={sharesReservedForIssuance.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="promisedGrants" className="text-right">
            Promised Grants:
          </Label>
          <Input
            id="promisedGrants"
            value={promisedGrants.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="sharesRemaining" className="text-right">
            Shares Remaining:
          </Label>
          <Input
            id="sharesRemaining"
            value={sharesRemaining.toLocaleString()}
            className="col-span-3"
            readOnly
          />
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="additionalShares" className="text-right">
            Additional Shares:
          </Label>
          <Input
            id="additionalShares"
            value={additionalDisplayValue}
            onChange={isAdditionalEditable ? handleAdditionalSharesChange : undefined}
            className="col-span-3"
            placeholder="Enter number of shares"
            readOnly={!isAdditionalEditable}
          />
        </div>

        {hasAdditionalShares && !exceedsAuthorized && (
          <Alert className="mt-2">
            <AlertDescription className="italic">
              The total amount of shares reserved under the stock option plan
              will be {totalOptionShares.toLocaleString()} shares. To authorize
              the increase to stock option plan, you will need to obtain board
              consent and stockholder consent.
            </AlertDescription>
          </Alert>
        )}

        {exceedsAuthorized && (
          <Alert className="mt-2 border-amber-500">
            <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
            <AlertDescription className="italic">
              The total amount of shares reserved under the stock option plan, {" "}
              {totalOptionShares.toLocaleString()}, exceeds the authorized share
              capital of the company. To increase the stock option plan, please
              first {" "}
              <button type="button" className="text-blue-500 underline" onClick={handleOpenIncreaseShares}>
                Increase Authorized Shares
              </button>{" "}
              to at least {requiredAuthorizedShares.toLocaleString()}.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Board Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{formatStatus(boardApprovalStatus)}</span>
          </div>
        </div>

        <div className="grid grid-cols-4 items-center gap-4">
          <Label className="text-right">Stockholders Approved:</Label>
          <div className="col-span-3 flex items-center gap-2">
            <span className="font-medium">{formatStatus(stockholderApprovalStatus)}</span>
          </div>
        </div>
      </div>

      <DialogFooter className="flex items-center justify-between">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline" 
                onClick={canSubmit && !isBoardApprovalLoading ? onSubmit : undefined}
                className={!canSubmit || isBoardApprovalLoading ? "opacity-50 cursor-not-allowed" : ""}
              >
                {isBoardApprovalLoading ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  "Submit"
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{getSubmitButtonTooltip()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Button variant="outline" onClick={handleClose}>
          Cancel
        </Button>
      </DialogFooter>
    </>
  );
};

export default IncreaseOptionPlanInitialState;
