import React, { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import CreateStockOptionPlanInitialState from "@/components/shares/CreateStockOptionPlanInitialState";
import { useCreateStockOptionPlan } from "@/hooks/create-stock-option-plan/useCreateStockOptionPlan.hooks";
import { Spinner } from "@/components/ui/spinner";

interface CreateStockOptionPlanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: string;
  onOpenIncreaseShares?: () => void;
}

const CreateStockOptionPlanDialog: React.FC<CreateStockOptionPlanDialogProps> = ({ isOpen, onClose, companyId, onOpenIncreaseShares }) => {
  const { data, summary, isLoading, isBoardApprovalLoading, isStockholderApprovalLoading, isAbandonLoading, refetch, submitForBoardApproval, submitForStockholderApproval, abandonProcess } = useCreateStockOptionPlan(companyId, isOpen);

  const [shareAmount, setShareAmount] = useState<string>("");

  useEffect(() => {
    if (!isOpen) {
      setShareAmount("");
    }
  }, [isOpen]);

  const handleShareAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, "");
    setShareAmount(value ? Number(value).toLocaleString() : "");
  };

  const shareAmountNum = shareAmount ? parseInt(shareAmount.replace(/,/g, ""), 10) : 0;
  const apiShareAmount = data?.additionalShare || 0;
  const isEditable = apiShareAmount === 0;

  const reservedPool = summary?.totalPoolSize || 0;
  const newTotalOptionShares = (reservedPool || 0) + (apiShareAmount || shareAmountNum);
  const issuedAndOutstanding = (summary?.totalAuthorizedSize || 0) - (summary?.totalRemaining || 0);
  const requiredAuthorizedShares = issuedAndOutstanding + newTotalOptionShares;
  const exceedsAuthorized = requiredAuthorizedShares > (summary?.totalAuthorizedSize || 0);

  const handleSubmit = async () => {
    const toSubmit = apiShareAmount > 0 ? apiShareAmount : shareAmountNum;
    if (!toSubmit || isNaN(toSubmit)) return;
    await submitForBoardApproval(toSubmit);
    await refetch();
  };

  const handleClose = () => {
    setShareAmount("");
    onClose();
  };

  const handleAbandon = async () => {
    await abandonProcess();
    await refetch();
    handleClose();
  };

  const handleOpenIas = () => {
    onClose();
    onOpenIncreaseShares?.();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md sm:max-w-lg">
        {isLoading ? (
          <div className="py-10 flex items-center justify-center"><Spinner /></div>
        ) : (
          <CreateStockOptionPlanInitialState
            shareAmountDisplay={isEditable ? shareAmount : (apiShareAmount || 0).toLocaleString()}
            isEditable={isEditable}
            boardApprovalStatus={data?.isBoardApproved || "no"}
            stockholderApprovalStatus={data?.isStockHolderApproved || "no"}
            exceedsAuthorized={exceedsAuthorized}
            requiredAuthorizedShares={requiredAuthorizedShares}
            onChangeShareAmount={handleShareAmountChange}
            onSubmit={handleSubmit}
            onAbandon={handleAbandon}
            onClose={handleClose}
            onOpenIncreaseShares={handleOpenIas}
            isBoardApprovalLoading={isBoardApprovalLoading}
            isStockholderApprovalLoading={isStockholderApprovalLoading}
            isAbandonLoading={isAbandonLoading}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateStockOptionPlanDialog;
