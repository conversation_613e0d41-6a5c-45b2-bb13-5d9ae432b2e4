import { Comment } from "@/components/collaboration/types";
import { DocumentCommentsContext } from "@/components/signature/DocumentSigningContainer";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { api } from "@/integrations/legal-concierge/api";
import { useDocumentById } from "@/integrations/legal-concierge/hooks/useDocuments";
import {
  DocumentWithContent,
  Document as FFDocument,
} from "@/integrations/legal-concierge/types/Document";
import { formatDistanceToNow } from "date-fns";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import DocumentComments from "../collaboration/DocumentComments";
import { Editor } from "../tiptap/editor";
import { Toolbar } from "../tiptap/toolbar";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";

interface DocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  ffdocument: FFDocument;
  mode?: "view" | "edit";
}

// Type for document with content

const DocumentModal: React.FC<DocumentModalProps> = ({
  isOpen,
  onClose,
  ffdocument,
  mode = "view",
}) => {
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);

  // Use the useDocumentById hook to fetch document content
  const {
    data: documentData,
    isLoading: documentLoading,
    error: documentError,
  } = useDocumentById(
    user?.companyId || "",
    ffdocument.id,
    isOpen // Only fetch when modal is open
  ) as {
    data: DocumentWithContent | undefined;
    isLoading: boolean;
    error: Error | null;
  };

  // Reset loading state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setLoading(true);
    } else {
      setLoading(false);
      setComments([]); // Clear comments when modal closes
    }
  }, [isOpen]);

  // Fetch comments when the modal opens
  useEffect(() => {
    const fetchComments = async () => {
      if (!isOpen || !ffdocument?.id || !user?.companyId) {
        setLoading(false); // Set loading to false if we're not going to fetch
        return;
      }

      try {
        setLoading(true);
        const response = await api.getDocumentComments(
          user.companyId,
          ffdocument.id
        );

        console.log("API Response:", response);

        if (response.error) {
          throw new Error(response.error);
        }

        // Ensure response.data is an array
        const commentsData = Array.isArray(response.data)
          ? response.data
          : [response.data].filter(Boolean);

        console.log("Formatted Comments Data:", commentsData);

        // Transform the API response to match our Comment type
        const formattedComments: Comment[] = commentsData.map((comment) => {
          const timestamp = (() => {
            try {
              const date = new Date(comment.timestamp);
              return isNaN(date.getTime()) ? new Date() : date;
            } catch {
              return new Date();
            }
          })();

          return {
            id: comment.id,
            userId: comment.userId,
            userName: comment.userName || comment.userId || "Anonymous",
            documentId: comment.documentIdentifier,
            content: comment.comment,
            timestamp,
            position: comment.position,
            resolved: comment.resolved,
            statusText: comment.statusText,
          };
        });

        console.log("Final Formatted Comments:", formattedComments);
        setComments(formattedComments);
      } catch (error) {
        console.error("Error fetching comments:", error);
        toast.error("Failed to load comments");
      } finally {
        setLoading(false);
      }
    };

    fetchComments();
  }, [isOpen, ffdocument?.id, user?.companyId]);

  const addComment = async (commentData: {
    content: string;
    documentId: string;
    position?: { x: number; y: number };
  }) => {
    if (!user?.companyId) {
      toast.error("User or company ID not found");
      return;
    }

    if (!commentData.documentId) {
      toast.error("Document name is missing");
      return;
    }

    try {
      const response = await api.createDocumentComment(
        user.companyId,
        commentData.documentId,
        {
          comment: commentData.content,
          position: commentData.position,
          resolved: false,
        }
      );

      if (response.error || !response.data) {
        throw new Error(response.error || "Failed to create comment");
      }

      const timestamp = (() => {
        try {
          const date = new Date(response.data.createdAt);
          return isNaN(date.getTime()) ? new Date() : date;
        } catch {
          return new Date();
        }
      })();

      // Add the new comment to state
      const formattedComment: Comment = {
        id: response.data.id,
        userId: response.data.userId,
        userName: response.data.userName || response.data.userId || "Anonymous",
        documentId: response.data.documentIdentifier,
        content: response.data.comment,
        timestamp,
        position: response.data.position,
        resolved: response.data.resolved,
        statusText: response.data.statusText,
      };

      setComments((prevComments) => [formattedComment, ...prevComments]);
      toast.success("Comment added successfully");
    } catch (error) {
      console.error("Error in addComment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to add comment"
      );
    }
  };

  // Handle document loading error
  if (documentError) {
    console.error("Error loading document:", documentError);
    toast.error("Failed to load document content");
  }

  console.log({ ffdocument, documentData });
  return (
    <DocumentCommentsContext.Provider
      value={{
        comments,
        addComment: async (commentData) => {
          if (!ffdocument?.id) {
            toast.error("Document name is missing");
            return;
          }
          return addComment({
            ...commentData,
            documentId: ffdocument.id,
          });
        },
        resolveComment: async (id) => {
          try {
            const response = await api.updateDocumentComment(id, {
              comment: comments.find((c) => c.id === id)?.content || "",
              resolved: true,
            });

            if (response.error) {
              throw new Error(response.error);
            }

            setComments((prevComments) =>
              prevComments.map((comment) =>
                comment.id === id
                  ? {
                      ...comment,
                      resolved: true,
                      statusText:
                        "Resolved " +
                        formatDistanceToNow(new Date(), { addSuffix: true }),
                    }
                  : comment
              )
            );
            toast.success("Comment resolved successfully");
          } catch (error) {
            console.error("Error in resolveComment:", error);
            toast.error("Failed to resolve comment");
          }
        },
      }}
    >
      <EditorCommentsProvider initialDocumentId={ffdocument.id}>
        <Dialog open={isOpen} onOpenChange={onClose}>
          <DialogContent className="max-w-[95vw] w-[95vw] h-[95vh] p-4 flex flex-col">
            <DialogHeader className="mb-2">
              <DialogTitle>
                {ffdocument?.fileName || "Document"} Viewer
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-[2fr,1fr] gap-4 h-[calc(95vh-5rem)]">
              <div className="bg-editor-bg flex flex-col h-full justify-start align-top ">
                <div className=" px-4 pt-2 bg-[#FAFBFD] print:hidden ">
                  {mode == "edit" && <Toolbar />}
                </div>

                <div className="flex-1 print:pt-0 overflow-auto  max-h-[calc(100vh-160px)]">
                  <Editor
                    documentContent={documentData}
                    documentId={ffdocument.id}
                    documentMode={mode}
                  />
                </div>
              </div>
              <div className="h-full">
                <DocumentComments
                  loading={loading}
                  useEditorIntegration={true}
                  isDashboard={false}
                  documentId={ffdocument.id}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </EditorCommentsProvider>
    </DocumentCommentsContext.Provider>
  );
};

export default DocumentModal;
