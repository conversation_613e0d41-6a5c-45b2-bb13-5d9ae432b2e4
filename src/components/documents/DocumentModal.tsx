import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { useDocumentById } from "@/integrations/legal-concierge/hooks/useDocuments";
import {
  DocumentWithContent,
  Document as FFDocument,
} from "@/integrations/legal-concierge/types/Document";
import React from "react";
import { toast } from "sonner";
import DocumentComments from "../collaboration/DocumentComments";
import { Editor } from "../tiptap/editor";
import { Toolbar } from "../tiptap/toolbar";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";

interface DocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  ffdocument: FFDocument;
  mode?: "view" | "edit";
}

// Type for document with content

const DocumentModal: React.FC<DocumentModalProps> = ({
  isOpen,
  onClose,
  ffdocument,
  mode = "view",
}) => {
  const { user } = useAuth();

  // Use the useDocumentById hook to fetch document content
  const { data: documentData, error: documentError } = useDocumentById(
    user?.companyId || "",
    ffdocument.id,
    isOpen // Only fetch when modal is open
  ) as {
    data: DocumentWithContent | undefined;
    isLoading: boolean;
    error: Error | null;
  };

  // Handle document loading error
  if (documentError) {
    console.error("Error loading document:", documentError);
    toast.error("Failed to load document content");
  }

  return (
    <EditorCommentsProvider initialDocumentId={ffdocument.id}>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-[95vw] w-[95vw] h-[95vh] p-4 flex flex-col">
          <DialogHeader className="mb-2">
            <DialogTitle>
              {ffdocument?.fileName || "Document"} Viewer
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-[2fr,1fr] gap-4 h-[calc(95vh-5rem)]">
            <div className="bg-editor-bg flex flex-col h-full justify-start align-top ">
              <div className=" px-4 pt-2 bg-[#FAFBFD] print:hidden ">
                {mode == "edit" && <Toolbar />}
              </div>

              <div className="flex-1 print:pt-0 overflow-auto  max-h-[calc(100vh-160px)]">
                <Editor
                  documentContent={documentData}
                  documentId={ffdocument.id}
                  documentMode={mode}
                />
              </div>
            </div>
            <div className="h-full">
              <DocumentComments
                useEditorIntegration={true}
                isDashboard={false}
                documentId={ffdocument.id}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </EditorCommentsProvider>
  );
};

export default DocumentModal;
