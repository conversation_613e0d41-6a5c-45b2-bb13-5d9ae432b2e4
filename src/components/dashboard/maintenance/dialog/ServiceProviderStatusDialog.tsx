import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { DataTable } from "@/components/shared/data-table/data-table";

interface ServiceProviderStatusDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any[];
  columns: any[];
  loading?: boolean;
  className?: string;
}

const ServiceProviderStatusDialog: React.FC<ServiceProviderStatusDialogProps> = ({ isOpen, onClose, title, data, columns, loading, className }) => {
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl w-full">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className={"py-4 " + (className || "") }>
          <DataTable 
            columns={columns} 
            data={data} 
            loading={loading} 
            loadingDataNum={5}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ServiceProviderStatusDialog; 