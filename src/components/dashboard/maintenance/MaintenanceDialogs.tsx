import React from "react";
import IncreaseSharesDialog from "../../shares/IncreaseSharesDialog";
import IncreaseOptionPlanDialog from "../../shares/IncreaseOptionPlanDialog";
import BoardMeetingDialog from "../BoardMeetingDialog";
import AdvisorDialog from "../../service-providers/AdvisorDialog";
import ContractorDialog from "../../service-providers/ContractorDialog";
import EmployeeDialog from "../../service-providers/EmployeeDialog";
import TerminateDialog from "../../service-providers/TerminateDialog";
import PromisedGrantsDialog from "../../service-providers/PromisedGrantsDialog";

interface MaintenanceDialogsProps {
  isSharesDialogOpen: boolean;
  setIsSharesDialogOpen: (open: boolean) => void;
  isOptionPlanDialogOpen: boolean;
  setIsOptionPlanDialogOpen: (open: boolean) => void;
  isBoardMeetingDialogOpen: boolean;
  setIsBoardMeetingDialogOpen: (open: boolean) => void;
  isAdvisorDialogOpen: boolean;
  setIsAdvisorDialogOpen: (open: boolean) => void;
  isContractorDialogOpen: boolean;
  setIsContractorDialogOpen: (open: boolean) => void;
  isEmployeeDialogOpen: boolean;
  setIsEmployeeDialogOpen: (open: boolean) => void;
  isTerminateDialogOpen: boolean;
  setIsTerminateDialogOpen: (open: boolean) => void;
  isPromisedGrantsDialogOpen: boolean;
  setIsPromisedGrantsDialogOpen: (open: boolean) => void;
  companyId: string;
}

const MaintenanceDialogs: React.FC<MaintenanceDialogsProps> = ({
  isSharesDialogOpen,
  setIsSharesDialogOpen,
  isOptionPlanDialogOpen,
  setIsOptionPlanDialogOpen,
  isBoardMeetingDialogOpen,
  setIsBoardMeetingDialogOpen,
  isAdvisorDialogOpen,
  setIsAdvisorDialogOpen,
  isContractorDialogOpen,
  setIsContractorDialogOpen,
  isEmployeeDialogOpen,
  setIsEmployeeDialogOpen,
  isTerminateDialogOpen,
  setIsTerminateDialogOpen,
  isPromisedGrantsDialogOpen,
  setIsPromisedGrantsDialogOpen,
  companyId,
}) => {
  const handleOpenIncreaseShares = () => {
    setIsOptionPlanDialogOpen(false);
    setIsSharesDialogOpen(true);
  };

  return (
    <>
      <IncreaseSharesDialog
        isOpen={isSharesDialogOpen}
        onClose={() => setIsSharesDialogOpen(false)}
        companyId={companyId}
      />

      <IncreaseOptionPlanDialog
        isOpen={isOptionPlanDialogOpen}
        onClose={() => setIsOptionPlanDialogOpen(false)}
        companyId={companyId}
        onOpenIncreaseShares={handleOpenIncreaseShares}
      />

      <BoardMeetingDialog
        isOpen={isBoardMeetingDialogOpen}
        onClose={() => setIsBoardMeetingDialogOpen(false)}
      />

      <AdvisorDialog
        isOpen={isAdvisorDialogOpen}
        onClose={() => setIsAdvisorDialogOpen(false)}
      />

      <ContractorDialog
        isOpen={isContractorDialogOpen}
        onClose={() => setIsContractorDialogOpen(false)}
      />

      <EmployeeDialog
        isOpen={isEmployeeDialogOpen}
        onClose={() => setIsEmployeeDialogOpen(false)}
      />

      <TerminateDialog
        isOpen={isTerminateDialogOpen}
        onClose={() => setIsTerminateDialogOpen(false)}
      />

      <PromisedGrantsDialog
        isOpen={isPromisedGrantsDialogOpen}
        onClose={() => setIsPromisedGrantsDialogOpen(false)}
      />
    </>
  );
};

export default MaintenanceDialogs;
