import React from "react";
import {
  User,
  FileText,
  UserPlus,
  UserX,
  Gift,
  LucideIcon,
} from "lucide-react";
import { MaintenanceItemType } from "./types";

interface ServiceProviderItemsProps {
  onAdvisorClick: () => void;
  onContractorClick: () => void;
  onEmployeeClick: () => void;
  onTerminateClick: () => void;
  onPromisedGrantsClick: () => void;
  advisorCounts: { active: number; pending: number; terminated: number };
  contractorCounts: { active: number; pending: number; terminated: number };
  employeeCounts: { active: number; pending: number; terminated: number };
  terminateCounts: { pending: number; terminated: number };
  onAdvisorActiveClick?: () => void;
  onAdvisorPendingClick?: () => void;
  onAdvisorTerminatedClick?: () => void;
  onEmployeeActiveClick?: () => void;
  onEmployeePendingClick?: () => void;
  onEmployeeTerminatedClick?: () => void;
  onContractorActiveClick?: () => void;
  onContractorPendingClick?: () => void;
  onContractorTerminatedClick?: () => void;
  onTerminatePendingClick?: () => void;
  onTerminateTerminatedClick?: () => void;
}

export const getServiceProviderItems = ({
  onAdvisorClick,
  onContractorClick,
  onEmployeeClick,
  onTerminateClick,
  onPromisedGrantsClick,
  advisorCounts,
  contractorCounts,
  employeeCounts,
  terminateCounts,
  onAdvisorActiveClick,
  onAdvisorPendingClick,
  onAdvisorTerminatedClick,
  onEmployeeActiveClick,
  onEmployeePendingClick,
  onEmployeeTerminatedClick,
  onContractorActiveClick,
  onContractorPendingClick,
  onContractorTerminatedClick,
  onTerminatePendingClick,
  onTerminateTerminatedClick,
}: ServiceProviderItemsProps): MaintenanceItemType[] => {
  return [
    {
      icon: User,
      title: "Advisor",
      description: "Onboard advisors to your company.",
      buttonText: "Start",
      onClick: onAdvisorClick,
      activeCount: advisorCounts.active,
      pendingCount: advisorCounts.pending,
      terminatedCount: advisorCounts.terminated,
      onActiveClick: onAdvisorActiveClick,
      onPendingClick: onAdvisorPendingClick,
      onTerminatedClick: onAdvisorTerminatedClick,
    },
    {
      icon: FileText,
      title: "Independent Contractor/Consultant",
      description: "Onboard contractors and consultants.",
      buttonText: "Start",
      onClick: onContractorClick,
      activeCount: contractorCounts.active,
      pendingCount: contractorCounts.pending,
      terminatedCount: contractorCounts.terminated,
      onActiveClick: onContractorActiveClick,
      onPendingClick: onContractorPendingClick,
      onTerminatedClick: onContractorTerminatedClick,
    },
    {
      icon: UserPlus,
      title: "Employee",
      description: "Onboard new employees to your company.",
      buttonText: "Start",
      onClick: onEmployeeClick,
      activeCount: employeeCounts.active,
      pendingCount: employeeCounts.pending,
      terminatedCount: employeeCounts.terminated,
      onActiveClick: onEmployeeActiveClick,
      onPendingClick: onEmployeePendingClick,
      onTerminatedClick: onEmployeeTerminatedClick,
    },
    {
      icon: UserX,
      title: "Terminate",
      description: "Terminate employees, consultants, or advisors.",
      buttonText: "Start",
      onClick: onTerminateClick,
      pendingCount: terminateCounts.pending,
      terminatedCount: terminateCounts.terminated,
      onPendingClick: onTerminatePendingClick,
      onTerminatedClick: onTerminateTerminatedClick,
    },
    {
      icon: Gift,
      title: "Promised Grants",
      description: "Manage and issue promised equity grants.",
      buttonText: "View",
      linkTo: "/promised-grants",
      // onClick: onPromisedGrantsClick,
    },
  ];
};
