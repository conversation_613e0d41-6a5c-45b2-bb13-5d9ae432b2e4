import { LucideIcon } from "lucide-react";

export interface MaintenanceItemType {
  icon: LucideIcon;
  title: string;
  description: string;
  buttonText: string;
  onClick?: () => void;
  linkTo?: string;
  activeCount?: number;
  pendingCount?: number;
  terminatedCount?: number;
  onActiveClick?: () => void;
  onPendingClick?: () => void;
  onTerminatedClick?: () => void;
  onAdvisorActiveClick?: () => void;
  onAdvisorPendingClick?: () => void;
  onEmployeeActiveClick?: () => void;
  onEmployeePendingClick?: () => void;
  onContractorActiveClick?: () => void;
  onContractorPendingClick?: () => void;
}
