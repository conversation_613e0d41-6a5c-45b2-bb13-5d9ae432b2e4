import React from "react";
import { Link } from "react-router-dom";
import { LucideIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CheckCircle, Clock, XCircle } from "lucide-react";
import { useState } from "react";

interface MaintenanceItemProps {
  icon: LucideIcon;
  title: string;
  description: string;
  buttonText: string;
  onClick?: () => void;
  linkTo?: string;
  activeCount?: number;
  pendingCount?: number;
  terminatedCount?: number;
  onActiveClick?: () => void;
  onPendingClick?: () => void;
  onTerminatedClick?: () => void;
}

const MaintenanceItem: React.FC<MaintenanceItemProps> = ({
  icon: Icon,
  title,
  description,
  buttonText,
  onClick,
  linkTo,
  activeCount,
  pendingCount,
  terminatedCount,
  onActiveClick,
  onPendingClick,
  onTerminatedClick,
}) => {
  const [activeTooltipOpen, setActiveTooltipOpen] = useState(false);
  const [pendingTooltipOpen, setPendingTooltipOpen] = useState(false);
  const [terminatedTooltipOpen, setTerminatedTooltipOpen] = useState(false);

  const renderActionButton = () => {
    const buttonClasses = "flex-shrink-0 mt-1";

    return linkTo ? (
      <Button size="sm" variant="outline" className={buttonClasses} asChild>
        <Link to={linkTo}>{buttonText}</Link>
      </Button>
    ) : (
      <Button
        size="sm"
        variant="outline"
        className={buttonClasses}
        onClick={onClick}
      >
        {buttonText}
      </Button>
    );
  };

  const renderCounts = () => {
    if (activeCount === undefined && pendingCount === undefined && terminatedCount === undefined) return null;
    return (
      <div className="flex items-center gap-2 mr-2">
        {typeof activeCount === 'number' && (
          <div className="relative flex items-center">
            <span
              className="flex items-center text-green-600 text-xs font-medium cursor-pointer"
              onMouseEnter={() => setActiveTooltipOpen(true)}
              onMouseLeave={() => setActiveTooltipOpen(false)}
              onClick={onActiveClick}
              tabIndex={0}
              aria-label="Active count"
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              {activeCount}
            </span>
            {activeTooltipOpen && (
              <div className="absolute z-10 left-1/2 -translate-x-1/2 top-full mt-2 px-3 py-1.5 rounded bg-white border border-gray-200 shadow text-xs text-gray-900 whitespace-nowrap">
                Active: {activeCount}
              </div>
            )}
          </div>
        )}
        {typeof pendingCount === 'number' && (
          <div className="relative flex items-center">
            <span
              className="flex items-center text-yellow-600 text-xs font-medium cursor-pointer"
              onMouseEnter={() => setPendingTooltipOpen(true)}
              onMouseLeave={() => setPendingTooltipOpen(false)}
              onClick={onPendingClick}
              tabIndex={0}
              aria-label="Pending count"
            >
              <Clock className="h-4 w-4 mr-1" />
              {pendingCount}
            </span>
            {pendingTooltipOpen && (
              <div className="absolute z-10 left-1/2 -translate-x-1/2 top-full mt-2 px-3 py-1.5 rounded bg-white border border-gray-200 shadow text-xs text-gray-900 whitespace-nowrap">
                Pending: {pendingCount}
              </div>
            )}
          </div>
        )}
        {typeof terminatedCount === 'number' && (
          <div className="relative flex items-center">
            <span
              className="flex items-center text-red-600 text-xs font-medium cursor-pointer"
              onMouseEnter={() => setTerminatedTooltipOpen(true)}
              onMouseLeave={() => setTerminatedTooltipOpen(false)}
              onClick={onTerminatedClick}
              tabIndex={0}
              aria-label="Terminated count"
            >
              <XCircle className="h-4 w-4 mr-1" />
              {terminatedCount}
            </span>
            {terminatedTooltipOpen && (
              <div className="absolute z-10 left-1/2 -translate-x-1/2 top-full mt-2 px-3 py-1.5 rounded bg-white border border-gray-200 shadow text-xs text-gray-900 whitespace-nowrap">
                Terminated: {terminatedCount}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex items-start p-4 rounded-lg hover:bg-gray-50 transition-colors">
      <div
        className={cn(
          "flex-shrink-0 bg-amber-100 rounded-full h-10 w-10",
          "flex items-center justify-center mr-4"
        )}
      >
        <Icon className="h-5 w-5 text-amber-600" />
      </div>

      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-gray-600 text-sm mt-1">{description}</p>
      </div>

      <div className="flex items-center space-x-2 ml-4">
        {renderCounts()}
        {renderActionButton()}
      </div>
    </div>
  );
};

export default MaintenanceItem;
