import React from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { FileText, Shield, TrendingUp, RotateCcw } from "lucide-react";

interface PreSeedFinancingItemProps {
  icon: string;
  title: string;
  description: string;
  buttonText: string;
  linkTo: string;
}

const PreSeedFinancingItem: React.FC<PreSeedFinancingItemProps> = ({
  icon,
  title,
  description,
  buttonText,
  linkTo,
}) => {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case "FileText":
        return <FileText className="h-5 w-5" />;
      case "Shield":
        return <Shield className="h-5 w-5" />;
      case "TrendingUp":
        return <TrendingUp className="h-5 w-5" />;
      case "RotateCcw":
        return <RotateCcw className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  return (
    <div className="flex items-start p-4 rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex-shrink-0 bg-amber-100 rounded-full h-10 w-10 flex items-center justify-center mr-4">
        <div className="text-amber-600">
          {getIcon(icon)}
        </div>
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-gray-600 text-sm mt-1">{description}</p>
      </div>
      <div className="flex items-center space-x-2 ml-4">
        <Button size="sm" variant="outline" className="flex-shrink-0 mt-1" asChild>
          <Link to={linkTo}>{buttonText}</Link>
        </Button>
      </div>
    </div>
  );
};

export default PreSeedFinancingItem;
