import React from "react";
import { PreSeedFinancingItemType } from "./types";
import PreSeedFinancingItem from "./PreSeedFinancingItem";

interface PreSeedFinancingItemsListProps {
  items: PreSeedFinancingItemType[];
}

const PreSeedFinancingItemsList: React.FC<PreSeedFinancingItemsListProps> = ({
  items,
}) => {
  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <PreSeedFinancingItem
          key={index}
          icon={item.icon}
          title={item.title}
          description={item.description}
          buttonText={item.buttonText}
          linkTo={item.linkTo}
        />
      ))}
    </div>
  );
};

export default PreSeedFinancingItemsList;
