import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { getMaintenanceItems } from "./maintenance/MaintenanceItems";
import MaintenanceItemsList from "./maintenance/MaintenanceItemsList";
import MaintenanceDialogs from "./maintenance/MaintenanceDialogs";
import { useMaintenanceDialogs } from "./maintenance/useMaintenanceDialogs";
import Maintenance409Valuation from "@/components/service-providers/dialog/Maintenance409Valuation";
import { useAuth } from "@/contexts/AuthContext";
import { APIClient } from "@/integrations/legal-concierge/client";
import CreateStockOptionPlanDialog from "@/components/shares/CreateStockOptionPlanDialog";

const MaintenanceCard: React.FC = () => {
  const { user } = useAuth();
  const api = new APIClient();
  const {
    isSharesDialogOpen,
    setIsSharesDialogOpen,
    isOptionPlanDialogOpen,
    setIsOptionPlanDialogOpen,
    isBoardMeetingDialogOpen,
    setIsBoardMeetingDialogOpen,
    isAdvisorDialogOpen,
    setIsAdvisorDialogOpen,
    isContractorDialogOpen,
    setIsContractorDialogOpen,
    isEmployeeDialogOpen,
    setIsEmployeeDialogOpen,
    isTerminateDialogOpen,
    setIsTerminateDialogOpen,
    isPromisedGrantsDialogOpen,
    setIsPromisedGrantsDialogOpen,
  } = useMaintenanceDialogs();

  const [is409ADialogOpen, setIs409ADialogOpen] = useState(false);
  const [isCreateSopDialogOpen, setIsCreateSopDialogOpen] = useState(false);
  const [includeStockOptionPlan, setIncludeStockOptionPlan] = useState<boolean | null>(null);

  // Get companyId from auth context
  const companyId = user?.companyId || "";

  useEffect(() => {
    let active = true;
    (async () => {
      if (!companyId) return;
      try {
        const company = await api.getCompanyById(companyId);
        if (!active) return;
        // company has numberOfAuthorizedShares etc. We need includeStockOptionPlan boolean
        // Our client.getCompanyById returns CompanyDetails
        // Safely access includeStockOptionPlan
        // @ts-ignore
        setIncludeStockOptionPlan(Boolean((company as any)?.includeStockOptionPlan));
      } catch {
        setIncludeStockOptionPlan(true); // fallback to Increase SOP
      }
    })();
    return () => {
      active = false;
    };
  }, [companyId]);

  const maintenanceItems = getMaintenanceItems({
    onSharesDialogClick: () => setIsSharesDialogOpen(true),
    onOptionPlanDialogClick: () => setIsOptionPlanDialogOpen(true),
    onBoardMeetingDialogClick: () => setIsBoardMeetingDialogOpen(true),
    onAdvisorClick: () => setIsAdvisorDialogOpen(true),
    onContractorClick: () => setIsContractorDialogOpen(true),
    onEmployeeClick: () => setIsEmployeeDialogOpen(true),
    onTerminateClick: () => setIsTerminateDialogOpen(true),
    onPromisedGrantsClick: () => setIsPromisedGrantsDialogOpen(true),
    on409AValuationClick: () => setIs409ADialogOpen(true),
    includeStockOptionPlan: includeStockOptionPlan ?? true,
    onCreateSopClick: () => setIsCreateSopDialogOpen(true),
  });

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Maintenance</CardTitle>
          <CardDescription>
            Ongoing tasks to keep your company in good standing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MaintenanceItemsList items={maintenanceItems} />
        </CardContent>
      </Card>

      <MaintenanceDialogs
        isSharesDialogOpen={isSharesDialogOpen}
        setIsSharesDialogOpen={setIsSharesDialogOpen}
        isOptionPlanDialogOpen={isOptionPlanDialogOpen}
        setIsOptionPlanDialogOpen={setIsOptionPlanDialogOpen}
        isBoardMeetingDialogOpen={isBoardMeetingDialogOpen}
        setIsBoardMeetingDialogOpen={setIsBoardMeetingDialogOpen}
        isAdvisorDialogOpen={isAdvisorDialogOpen}
        setIsAdvisorDialogOpen={setIsAdvisorDialogOpen}
        isContractorDialogOpen={isContractorDialogOpen}
        setIsContractorDialogOpen={setIsContractorDialogOpen}
        isEmployeeDialogOpen={isEmployeeDialogOpen}
        setIsEmployeeDialogOpen={setIsEmployeeDialogOpen}
        isTerminateDialogOpen={isTerminateDialogOpen}
        setIsTerminateDialogOpen={setIsTerminateDialogOpen}
        isPromisedGrantsDialogOpen={isPromisedGrantsDialogOpen}
        setIsPromisedGrantsDialogOpen={setIsPromisedGrantsDialogOpen}
        companyId={companyId}
      />

      <CreateStockOptionPlanDialog
        isOpen={isCreateSopDialogOpen}
        onClose={() => setIsCreateSopDialogOpen(false)}
        companyId={companyId}
        onOpenIncreaseShares={() => setIsSharesDialogOpen(true)}
      />
      
      <Maintenance409Valuation
        isOpen={is409ADialogOpen}
        onClose={() => setIs409ADialogOpen(false)}
      />
    </AnimatedTransition>
  );
};

export default MaintenanceCard;
