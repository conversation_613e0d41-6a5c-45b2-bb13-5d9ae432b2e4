import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import PreSeedFinancingItemsList from "./preSeedFinancing/PreSeedFinancingItemsList";

const PreSeedFinancingCard: React.FC = () => {
  const financingItems = [
    {
      icon: "FileText", // We'll use Lucide icons
      title: "Convertible Notes",
      description: "Manage convertible note rounds, authorize new rounds, issue individual notes, and track investments.",
      buttonText: "Start",
      linkTo: "/convertible-notes",
    },
    {
      icon: "Shield",
      title: "SAFEs",
      description: "Manage SAFE agreements, authorize rounds, issue individual SAFEs, and track SAFE investments.",
      buttonText: "Start",
      linkTo: "/safes",
    },
    {
      icon: "TrendingUp",
      title: "MFN",
      description: "Manage Most Favored Nation clauses and related financing agreements.",
      buttonText: "Start",
      linkTo: "/mfn",
    },
    {
      icon: "RotateCcw",
      title: "SAFE Repurchase and Cancel",
      description: "Manage SAFE repurchase agreements and cancellation workflows.",
      buttonText: "Start",
      linkTo: "/safe-repurchase",
    },
  ];

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Pre-Seed Financing</CardTitle>
          <CardDescription>
            Tools and resources to help with your initial fundraising
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PreSeedFinancingItemsList items={financingItems} />
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

export default PreSeedFinancingCard;
