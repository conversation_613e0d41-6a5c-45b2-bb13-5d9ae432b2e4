// import React from "react";
// import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
// import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
// import { Link } from "react-router-dom";
// import { useCapTableList } from "@/hooks/cap-table/useCapTableList.hooks";
// import { TrendingUp, <PERSON>, <PERSON><PERSON><PERSON> as PieChartIcon, BarChart3 } from "lucide-react";

// const COLORS = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"];

// const DashboardCapTable: React.FC = () => {
//   const { capTableSummary, isSummaryLoading, summaryError } = useCapTableList();

//   if (isSummaryLoading) {
//     return (
//       <Card className="col-span-2">
//         <CardHeader>
//           <CardTitle className="text-lg">Cap Table Summary</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="flex items-center justify-center h-32">
//             <p className="text-muted-foreground">Loading cap table data...</p>
//           </div>
//         </CardContent>
//       </Card>
//     );
//   }

//   if (summaryError) {
//     return (
//       <Card className="col-span-2">
//         <CardHeader>
//           <CardTitle className="text-lg">Cap Table Summary</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="flex items-center justify-center h-32">
//             <p className="text-muted-foreground text-red-600">
//               Unable to load cap table data
//             </p>
//           </div>
//         </CardContent>
//       </Card>
//     );
//   }

//   if (!capTableSummary) {
//     return (
//       <Card className="col-span-2">
//         <CardHeader>
//           <CardTitle className="text-lg">Cap Table Summary</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="flex items-center justify-center h-32">
//             <p className="text-muted-foreground">No cap table data available</p>
//           </div>
//         </CardContent>
//       </Card>
//     );
//   }

//   // Transform pie chart data for recharts
//   const chartData = capTableSummary.pieChartData.map((item, index) => ({
//     name: item.name,
//     value: item.percentage,
//     color: COLORS[index % COLORS.length],
//   }));

//   // Custom tooltip component
//   const CustomTooltip = ({ active, payload }: any) => {
//     if (active && payload && payload.length) {
//       return (
//         <div className="bg-white p-3 shadow-lg rounded-lg border border-gray-200">
//           <p className="font-medium text-gray-800">{payload[0].payload.name}</p>
//           <p className="text-sm text-gray-600">{`Ownership: ${payload[0].value.toFixed(1)}%`}</p>
//         </div>
//       );
//     }
//     return null;
//   };

//   return (
//     <Card className="col-span-2">
//       <CardHeader>
//         <div className="flex items-center justify-between">
//           <div>
//             <CardTitle className="text-lg">Cap Table Summary</CardTitle>
//             <p className="text-sm text-muted-foreground mt-1">Real-time ownership distribution & metrics</p>
//           </div>
//           <Link
//             to="/cap-table"
//             className="inline-flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
//           >
//             View Full Cap Table
//             <TrendingUp className="w-4 h-4" />
//           </Link>
//         </div>
//       </CardHeader>
//       <CardContent>
//         <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
//           {/* Ownership Distribution Chart */}
//           <Card className="bg-gray-50">
//             <CardContent className="p-4">
//               <div className="flex items-center gap-2 mb-4">
//                 <PieChartIcon className="w-4 h-4 text-gray-600" />
//                 <h3 className="text-sm font-medium text-gray-700">Ownership Distribution asdf</h3>
//               </div>
              
//               <div className="h-48 mb-4">
//                 <ResponsiveContainer width="100%" height="100%">
//                   <PieChart>
//                     <Pie
//                       data={chartData}
//                       cx="50%"
//                       cy="50%"
//                       innerRadius={40}
//                       outerRadius={80}
//                       paddingAngle={2}
//                       dataKey="value"
//                       labelLine={false}
//                       label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
//                     >
//                       {chartData.map((entry, index) => (
//                         <Cell key={`cell-${index}`} fill={entry.color} />
//                       ))}
//                     </Pie>
//                     <Tooltip content={<CustomTooltip />} />
//                   </PieChart>
//                 </ResponsiveContainer>
//               </div>

//               {/* Legend */}
//               <div className="flex flex-wrap gap-3">
//                 {chartData.map((entry, index) => (
//                   <div key={`legend-${index}`} className="flex items-center gap-2">
//                     <div
//                       className="w-3 h-3 rounded-full"
//                       style={{ backgroundColor: entry.color }}
//                     />
//                     <span className="text-sm text-gray-700">{entry.name}</span>
//                   </div>
//                 ))}
//               </div>
//             </CardContent>
//           </Card>

//           {/* Summary Metrics */}
//           <Card className="bg-gray-50">
//             <CardContent className="p-4">
//               <div className="flex items-center gap-2 mb-4">
//                 <BarChart3 className="w-4 h-4 text-gray-600" />
//                 <h3 className="text-sm font-medium text-gray-700">Key Metrics</h3>
//               </div>
              
//               <div className="space-y-3">
//                 <div className="p-3 bg-white rounded-lg">
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center gap-2">
//                       <Users className="w-4 h-4 text-gray-500" />
//                       <span className="text-sm text-gray-600">Total Shareholders</span>
//                     </div>
//                     <span className="font-semibold text-gray-900">{capTableSummary.shareholders}</span>
//                   </div>
//                 </div>

//                 <div className="p-3 bg-white rounded-lg">
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center gap-2">
//                       <TrendingUp className="w-4 h-4 text-gray-500" />
//                       <span className="text-sm text-gray-600">Outstanding Shares</span>
//                     </div>
//                     <span className="font-semibold text-gray-900">
//                       {capTableSummary.totalShares.toLocaleString()}
//                     </span>
//                   </div>
//                 </div>

//                 <div className="p-3 bg-white rounded-lg">
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center gap-2">
//                       <BarChart3 className="w-4 h-4 text-gray-500" />
//                       <span className="text-sm text-gray-600">Fully Diluted Shares</span>
//                     </div>
//                     <span className="font-semibold text-gray-900">
//                       {capTableSummary.fullyDiluted.toLocaleString()}
//                     </span>
//                   </div>
//                 </div>

//                 <div className="p-3 bg-white rounded-lg">
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center gap-2">
//                       <PieChartIcon className="w-4 h-4 text-gray-500" />
//                       <span className="text-sm text-gray-600">Share Classes</span>
//                     </div>
//                     <span className="font-semibold text-gray-900">{capTableSummary.shareClasses}</span>
//                   </div>
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default DashboardCapTable;
