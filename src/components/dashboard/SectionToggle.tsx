import React from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  ListTodo,
  Settings,
  DollarSign,
  Briefcase,
  Table,
  FileText,
} from "lucide-react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { useNavigate } from "react-router-dom";

interface SectionToggleProps {
  activeSection: string;
  setActiveSection: (value: string) => void;
}

const SectionToggle: React.FC<SectionToggleProps> = ({
  activeSection,
  setActiveSection,
}) => {
  const { user } = useAuth();
  const permissions = useUserPermissions(user);
  const navigate = useNavigate();

  const handleSectionChange = (value: string) => {
    if (value === "cap-table") {
      // Navigate to the cap table page instead of changing state
      navigate("/cap-table");
    } else if (value) {
      // For other sections, use the existing state change behavior
      setActiveSection(value);
    }
  };

  return (
    <AnimatedTransition delay={0.4} className="mt-8 mb-4">
      <div className="flex justify-center">
        <ToggleGroup
          type="single"
          value={activeSection}
          onValueChange={handleSectionChange}
          className="mb-4"
        >
          <ToggleGroupItem
            value="getting-started"
            aria-label="Getting Started"
            className="flex items-center gap-2"
          >
            <ListTodo size={16} />
            <span>Getting Started</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="maintenance"
            aria-label="Maintenance"
            className="flex items-center gap-2"
          >
            <Settings size={16} />
            <span>Maintenance</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="cap-table"
            aria-label="Cap Table"
            className="flex items-center gap-2"
          >
            <Table size={16} />
            <span>Cap Table</span>
          </ToggleGroupItem>
          {/* <ToggleGroupItem
            value="commercial-agreements"
            aria-label="Commercial Agreements"
            className="flex items-center gap-2"
          >
            <FileText size={16} />
            <span>Commercial Agreements</span>
          </ToggleGroupItem> */}
          <ToggleGroupItem
            value="pre-seed-financing"
            aria-label="Pre-Seed Financing"
            className="flex items-center gap-2"
          >
            <DollarSign size={16} />
            <span>Pre-Seed Financing</span>
          </ToggleGroupItem>
          {/* <ToggleGroupItem
            value="series-seed-financing"
            aria-label="Series Seed Financing"
            className="flex items-center gap-2"
          >
            <Briefcase size={16} />
            <span>Series Seed Financing</span>
          </ToggleGroupItem> */}
        </ToggleGroup>
      </div>
    </AnimatedTransition>
  );
};

export default SectionToggle;
