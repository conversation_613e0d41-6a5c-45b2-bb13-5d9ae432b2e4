import React from 'react';
import EmptyState from '@/components/common/EmptyState';
import { Users, FileText, Vote, Briefcase, Plus, RefreshCw, ExternalLink } from 'lucide-react';

interface ProFormaCapTableEmptyStateProps {
  onAddInvestor: () => void;
  onClearFilters: () => void;
}

export const ProFormaCapTableEmptyState: React.FC<ProFormaCapTableEmptyStateProps> = ({
  onAddInvestor,
  onClearFilters
}) => {
  return (
    <EmptyState
      icon={Users}
      title="No shareholders found for the current filters"
      description="Try adjusting your filters or add new investors to see data here. You can also clear all filters to view all shareholders."
      action={{
        label: "Add Investor",
        onClick: onAddInvestor,
        variant: "default"
      }}
      secondaryAction={{
        label: "Clear Filters",
        onClick: onClearFilters,
        variant: "outline"
      }}
    />
  );
};

interface ProRataRightsEmptyStateProps {
  onLearnMore?: () => void;
}

export const ProRataRightsEmptyState: React.FC<ProRataRightsEmptyStateProps> = ({
  onLearnMore
}) => {
  return (
    <EmptyState
      icon={FileText}
      title="No pro rata rights configured"
      description="Pro rata rights will appear here once investors are added with pro rata entitlements. This feature helps existing investors maintain their ownership percentage in future rounds."
      action={onLearnMore ? {
        label: "Learn More",
        onClick: onLearnMore,
        variant: "outline"
      } : undefined}
    />
  );
};

export const VotingRightsEmptyState: React.FC = () => {
  return (
    <EmptyState
      icon={Vote}
      title="Voting rights analysis not available"
      description="This analysis requires shareholder data to calculate voting power distribution. Make sure you have shareholders added to see voting rights data."
    />
  );
};

export const StockOptionPlanEmptyState: React.FC = () => {
  return (
    <EmptyState
      icon={Briefcase}
      title="No stock option plan details available"
      description="Set up your stock option plan to see detailed allocation information. This will help you track employee equity grants and vesting schedules."
    />
  );
};

// Loading state component
export const ProFormaLoadingState: React.FC = () => {
  return (
    <div className="space-y-4">
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Error state component
interface ProFormaErrorStateProps {
  error: string;
  onRetry: () => void;
}

export const ProFormaErrorState: React.FC<ProFormaErrorStateProps> = ({
  error,
  onRetry
}) => {
  return (
    <EmptyState
      icon={RefreshCw}
      title="Failed to load data"
      description={`There was an error loading the pro forma data: ${error}. Please try again.`}
      action={{
        label: "Try Again",
        onClick: onRetry,
        variant: "default"
      }}
    />
  );
};
