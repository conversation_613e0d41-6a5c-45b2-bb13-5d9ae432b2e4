import React from "react";
import { Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Tooltip } from "recharts";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import {
  Tooltip as UITooltip,
  Tooltip<PERSON>ontent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { useCapTableList } from "@/hooks/cap-table/useCapTableList.hooks";

interface CapTableChartProps {
  currentView: "standard" | "fullyDiluted";
}

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

const CapTableChart: React.FC<CapTableChartProps> = ({
  currentView,
}) => {
  const { capTableSummary, isSummaryLoading, summaryError } = useCapTableList();

  if (isSummaryLoading) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              {currentView === "standard"
                ? "Ownership Distribution"
                : "Fully Diluted Ownership"}
            </CardTitle>
            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <button className="text-gray-500 hover:text-gray-700">
                    <Info size={18} />
                  </button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>
                    {currentView === "standard"
                      ? "Shows the current ownership based on issued shares only."
                      : "Shows ownership if all options and convertible securities are exercised."}
                  </p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <p className="text-muted-foreground">Loading chart data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (summaryError || !capTableSummary) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              {currentView === "standard"
                ? "Ownership Distribution"
                : "Fully Diluted Ownership"}
            </CardTitle>
            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <button className="text-gray-500 hover:text-gray-700">
                    <Info size={18} />
                  </button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>
                    {currentView === "standard"
                      ? "Shows the current ownership based on issued shares only."
                      : "Shows ownership if all options and convertible securities are exercised."}
                  </p>
                </TooltipContent>
              </UITooltip>
            </TooltipProvider>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <p className="text-muted-foreground text-red-600">
              {summaryError || "Unable to load chart data"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Transform pie chart data for recharts
  const chartData = capTableSummary.pieChartData.map((item, index) => ({
    name: item.name,
    value: item.percentage,
    color: COLORS[index % COLORS.length],
  }));

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 shadow-lg rounded-lg border">
          <p className="font-medium text-gray-800">{payload[0].payload.name}</p>
          <p className="text-sm font-medium">{`Ownership: ${payload[0].value.toFixed(2)}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            {currentView === "standard"
              ? "Ownership Distribution"
              : "Fully Diluted Ownership"}
          </CardTitle>
          <TooltipProvider>
            <UITooltip>
              <TooltipTrigger asChild>
                <button className="text-gray-500 hover:text-gray-700">
                  <Info size={18} />
                </button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>
                  {currentView === "standard"
                    ? "Shows the current ownership based on issued shares only."
                    : "Shows ownership if all options and convertible securities are exercised."}
                </p>
              </TooltipContent>
            </UITooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                fill="#8884d8"
                paddingAngle={1}
                dataKey="value"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
          {chartData.map((entry, index) => (
            <div key={`legend-${index}`} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm truncate">
                {entry.name}: {entry.value.toFixed(1)}%
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CapTableChart;
