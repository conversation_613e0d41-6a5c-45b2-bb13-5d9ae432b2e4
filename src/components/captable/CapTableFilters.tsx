import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ShareholderRole, ShareClass, ProFormaRoleFilter, ProFormaShareClassFilter } from "@/types/capTable";
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";
import {
  Calendar,
  Download,
  Filter,
  Printer,
  Search,
  SlidersHorizontal,
  Loader2,
} from "lucide-react";

interface CapTableFiltersProps {
  onToggleView: (view: "standard" | "fullyDiluted") => void;
  onExport: (format: "pdf" | "csv") => void;
  currentView: "standard" | "fullyDiluted";
}

const CapTableFilters: React.FC<CapTableFiltersProps> = ({
  onToggleView,
  onExport,
  currentView,
}) => {
  // Use global filter state instead of props
  const { 
    filters, 
    setShareholderNameSearch, 
    setRoleFilter, 
    setShareClassFilter,
    resetFilters
  } = useGlobalFilters();
  return (
    <div className="mb-6 space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search shareholders..."
            className="pl-9"
            value={filters.shareholderNameSearch}
            onChange={(e) => setShareholderNameSearch(e.target.value)}
          />
        </div>

        {/* <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport("pdf")}
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span>Export PDF</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onExport("csv")}
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span>Export CSV</span>
          </Button>
        </div> */}
      </div>

      <div className="flex flex-wrap gap-2 items-center">
        <div className="flex items-center">
          <Filter size={16} className="mr-2 text-gray-500" />
          <span className="text-sm font-medium">Filters:</span>
        </div>

        <Select
          value={filters.roleFilter}
          onValueChange={(value) =>
            setRoleFilter(value as ProFormaRoleFilter)
          }
        >
          <SelectTrigger className="w-[150px] h-8">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All</SelectItem>
            <SelectItem value="Founders">Founders</SelectItem>
            <SelectItem value="Investors">Investors</SelectItem>
            <SelectItem value="Advisors">Advisors</SelectItem>
            <SelectItem value="Contractors">Contractors</SelectItem>
            <SelectItem value="Employees">Employees</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filters.shareClassFilter}
          onValueChange={(value) =>
            setShareClassFilter(value as ProFormaShareClassFilter)
          }
        >
          <SelectTrigger className="w-[180px] h-8">
            <SelectValue placeholder="Share Class" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All</SelectItem>
            <SelectItem value="Common">Common</SelectItem>
            <SelectItem value="SeriesAPreferred">SeriesAPreferred</SelectItem>
            <SelectItem value="StockOptionPool">StockOptionPool</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={resetFilters}
          className="h-8 px-3"
          disabled={filters.isFiltering}
        >
          Clear Filters
        </Button>

        {/* Loading Indicator */}
        {filters.isFiltering && (
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Applying filters...</span>
          </div>
        )}

        {/* <Select
          value={currentView}
          onValueChange={(value) =>
            onToggleView(value as "standard" | "fullyDiluted")
          }
        >
          <SelectTrigger className="w-[180px] h-8">
            <SelectValue placeholder="View" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="standard">Standard View</SelectItem>
            <SelectItem value="fullyDiluted">Fully Diluted View</SelectItem>
          </SelectContent>
        </Select> */}
      </div>
    </div>
  );
};

export default CapTableFilters;
