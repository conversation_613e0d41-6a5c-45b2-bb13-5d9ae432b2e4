import React, { useState, use<PERSON>emo } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RefreshCw, AlertCircle, Info, Edit, Loader2 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  useVotingRights,
  useUpdateVotingRightEmail,
  useUpdateVotingRightEmailStatus,
} from "@/hooks/cap-table/useVotingRights.hooks";
import { VotingShareholder, VotingPowerAnalysis } from "@/types/capTable";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Toaster } from "sonner";
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat("en-US").format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

const getVotingPowerBadgeVariant = (power: string) => {
  switch (power) {
    case 'High': return 'default';
    case 'Medium': return 'secondary';
    case 'Low': return 'outline';
    default: return 'outline';
  }
};

const getShareTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'Common Stock': return 'default';
    case 'Restricted Stock': return 'secondary';
    case 'Exercised Options': return 'outline';
    default: return 'outline';
  }
};

const calculateVotingAnalysis = (shareholders: VotingShareholder[]): VotingPowerAnalysis => {
  const highPowerShareholders = shareholders.filter(sh => sh.votingPercentage >= 33);
  const mediumPowerShareholders = shareholders.filter(sh => sh.votingPercentage >= 10 && sh.votingPercentage < 33);
  const lowPowerShareholders = shareholders.filter(sh => sh.votingPercentage < 10);
  const majorityBlockers = shareholders.filter(sh => sh.canBlockMajority);
  
  return {
    highPowerShareholders,
    mediumPowerShareholders,
    lowPowerShareholders,
    majorityBlockers,
    totalHighPowerPercentage: highPowerShareholders.reduce((sum, sh) => sum + sh.votingPercentage, 0),
    totalMediumPowerPercentage: mediumPowerShareholders.reduce((sum, sh) => sum + sh.votingPercentage, 0),
    totalLowPowerPercentage: lowPowerShareholders.reduce((sum, sh) => sum + sh.votingPercentage, 0),
  };
};

export const VotingRights: React.FC = () => {
  // Use global filter state
  const { filters } = useGlobalFilters();
  
  const { data, isLoading, error, refetch } = useVotingRights({
    proFormaRoleFilter: filters.roleFilter,
    proFormaShareClassFilter: filters.shareClassFilter,
    shareHolderName: filters.shareholderNameSearch
  });
  const { mutate: updateEmail, isPending: isUpdatingEmail } = useUpdateVotingRightEmail();
  const { mutate: updateEmailStatus, isPending: isUpdatingStatus } = useUpdateVotingRightEmailStatus();
  
  // Email edit modal state
  const [editEmailModal, setEditEmailModal] = useState<{
    isOpen: boolean;
    data: VotingShareholder | null;
  }>({ isOpen: false, data: null });
  const [emailValue, setEmailValue] = useState<string>("");
  
  // Track invalid exclusion attempts
  const [invalidExclusionAttempt, setInvalidExclusionAttempt] = useState<boolean>(false);

  const votingAnalysis = useMemo(() => {
    if (!data?.shareholders) return null;
    return calculateVotingAnalysis(data.shareholders);
  }, [data?.shareholders]);

  const handleEmailStatusChange = (shareholder: VotingShareholder, checked: boolean) => {
    const newSendEmail = checked;
    
    // Validate before making API call
    if (!newSendEmail) {
      const currentIncludedPercentage = data?.emailExclusionSummary.includedVotingPercentage || 0;
      const shareholderPercentage = shareholder.votingPercentage;
      const newIncludedPercentage = currentIncludedPercentage - shareholderPercentage;
      
      if (newIncludedPercentage < 50) {
        setInvalidExclusionAttempt(true);
        setTimeout(() => setInvalidExclusionAttempt(false), 5000);
        return;
      }
    }
    
    // Clear any previous invalid attempt alerts
    setInvalidExclusionAttempt(false);
    
    updateEmailStatus({
      id: shareholder.id,
      sendEmail: newSendEmail,
    });
  };

  const handleEmailEdit = (shareholder: VotingShareholder) => {
    setEmailValue(shareholder.email);
    setEditEmailModal({ isOpen: true, data: shareholder });
  };

  const handleEmailSubmit = () => {
    if (!editEmailModal.data) return;
    
    updateEmail({
      id: editEmailModal.data.id,
      email: emailValue,
    });
    
    setEditEmailModal({ isOpen: false, data: null });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Voting Rights Analysis</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Last updated: <Skeleton className="h-4 w-24" />
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <Skeleton className="h-6 w-48 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Voting Rights Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Failed to load voting rights data</span>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data || !data.shareholders.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Voting Rights Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No voting rights data found
          </div>
        </CardContent>
      </Card>
    );
  }

  const { shareholders, emailExclusionSummary, lastUpdated } = data;

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Voting Rights Analysis</h2>
            <p className="text-sm text-muted-foreground">
              Last Updated: {format(new Date(lastUpdated), "MMM dd, yyyy")}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>

        {/* Voting Thresholds */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Voting Thresholds
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  Majority (50%) required for standard decisions like electing directors
                </TooltipContent>
              </Tooltip>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Majority (50%) required for standard decisions like electing directors
            </p>
          </CardContent>
        </Card>

        {/* Shareholder Table */}
        <Card>
          <CardHeader>
            <CardTitle>Shareholders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-center w-16">
                      <Tooltip>
                        <TooltipTrigger>Email Status</TooltipTrigger>
                        <TooltipContent>
                          Include/exclude shareholder from email communications
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead>Shareholder</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Share Type</TableHead>
                    <TableHead className="text-right">Voting Shares</TableHead>
                    <TableHead className="text-right">Voting %</TableHead>
                    <TableHead>Voting Power</TableHead>
                    <TableHead className="text-center">Can Block Majority?</TableHead>
                    <TableHead className="text-center w-16">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shareholders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        No shareholders found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    shareholders.map((shareholder) => (
                      <TableRow key={shareholder.id}>
                        <TableCell className="text-center">
                          <Checkbox
                            checked={shareholder.sendEmail}
                            onCheckedChange={(checked) => 
                              handleEmailStatusChange(shareholder, checked as boolean)
                            }
                            disabled={isUpdatingStatus}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          {shareholder.name}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {shareholder.email || "No email available"}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              shareholder.shareType === "Common Stock"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {shareholder.shareType}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatNumber(shareholder.votingShares)}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatPercentage(shareholder.votingPercentage)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              shareholder.votingPower === "High"
                                ? "default"
                                : shareholder.votingPower === "Medium"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {shareholder.votingPower}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <span
                            className={cn(
                              "text-sm",
                              shareholder.canBlockMajority
                                ? "text-green-600 font-medium"
                                : "text-muted-foreground"
                            )}
                          >
                            {shareholder.canBlockMajority ? "Yes" : "No"}
                          </span>
                        </TableCell>
                        <TableCell className="text-center">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEmailEdit(shareholder)}
                                disabled={isUpdatingEmail}
                              >
                                {isUpdatingEmail ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Edit className="h-4 w-4" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Edit Email</TooltipContent>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Email Exclusion Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Email Exclusion Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-sm text-muted-foreground">Excluded Voting Power:</span>
                <div className="text-2xl font-bold">
                  {emailExclusionSummary.excludedVotingPercentage.toFixed(2)}%
                </div>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Remaining Voting Power:</span>
                <div className="text-2xl font-bold">
                  {emailExclusionSummary.includedVotingPercentage.toFixed(2)}%
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2 mb-4">
              <span className="text-sm">Status:</span>
              <Badge variant={emailExclusionSummary.isEmailExclusionValid ? "default" : "destructive"}>
                {emailExclusionSummary.isEmailExclusionValid ? "Valid" : "Invalid"}
              </Badge>
            </div>
            {(!emailExclusionSummary.isEmailExclusionValid || invalidExclusionAttempt) && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Cannot exclude shareholders: must maintain at least 50% of voting power for email communications.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Voting Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Majority Vote Threshold</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                50% required for standard decisions like electing directors.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Voting Power Classification</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>High: ≥33%</div>
                <div>Medium: ≥10%</div>
                <div>Low: &lt;10%</div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {votingAnalysis && votingAnalysis.majorityBlockers.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {votingAnalysis.majorityBlockers.map(blocker => blocker.name).join(', ')} can block majority decisions.
            </AlertDescription>
          </Alert>
        )}

        {/* Email Edit Modal */}
        <Dialog
          open={editEmailModal.isOpen}
          onOpenChange={(open) => setEditEmailModal({ isOpen: open, data: null })}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Email Address</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="shareholder-name">Shareholder</Label>
                <div className="text-sm text-muted-foreground">
                  {editEmailModal.data?.name}
                </div>
              </div>
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={emailValue}
                  onChange={(e) => setEmailValue(e.target.value)}
                  placeholder="Enter email address"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setEditEmailModal({ isOpen: false, data: null })}
                  disabled={isUpdatingEmail}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleEmailSubmit}
                  disabled={!emailValue || emailValue === editEmailModal.data?.email || isUpdatingEmail}
                >
                  {isUpdatingEmail ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Email"
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      <Toaster />
    </TooltipProvider>
  );
};

export default VotingRights;
