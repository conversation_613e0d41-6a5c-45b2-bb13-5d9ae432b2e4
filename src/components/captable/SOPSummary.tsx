import React, { useState, use<PERSON>emo } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { RefreshCw, AlertCircle, Edit, Zap, Trash2, Loader2, MoreVertical, Plus } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  useStockOptionPlan, 
  useUpdateStockOptionGrant, 
  useExerciseStockOption, 
  useDeleteStockOptionGrant 
} from "@/hooks/cap-table/useStockOptionPlan.hooks";
import { getVestingScheduleDisplay } from "@/services/cap-table/stockOptionPlan.service";
import { Toaster } from "sonner";
import { StockOptionGrant } from "@/types/capTable";
import AddGrantDialog from "./AddGrantDialog";
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat("en-US").format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case 'Employee': return 'default';
    case 'Advisor': return 'secondary';
    case 'Contractor': return 'outline';
    default: return 'outline';
  }
};

const getGrantStatusBadgeVariant = (status: string) => {
  switch (status) {
    case 'Granted': return 'default';
    case 'Promised': return 'secondary';
    default: return 'outline';
  }
};

const getGrantTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'Option': return 'default';
    case 'Restricted Stock': return 'secondary';
    case 'Exercised Option': return 'outline';
    default: return 'outline';
  }
};

const canExercise = (grant: StockOptionGrant) => {
  return (
    grant.grantType === 'Option' && 
    grant.grantStatus === 'Granted' &&
    grant.optionsGranted > 0
  );
};

export const SOPSummary: React.FC = () => {
  // Use global filter state
  const { filters } = useGlobalFilters();
  
  // Pass filters to the hook
  const { data, isLoading, error, refetch } = useStockOptionPlan({
    proFormaRoleFilter: filters.roleFilter,
    proFormaShareClassFilter: filters.shareClassFilter,
    shareHolderName: filters.shareholderNameSearch
  });
  
  const { mutate: updateGrant, isPending: isUpdating } = useUpdateStockOptionGrant();
  const { mutate: exerciseOption, isPending: isExercising } = useExerciseStockOption();
  const { mutate: deleteGrant, isPending: isDeleting } = useDeleteStockOptionGrant();
  
  // Modal states
  const [editModal, setEditModal] = useState<{
    isOpen: boolean;
    data: StockOptionGrant | null;
  }>({ isOpen: false, data: null });
  
  const [exerciseModal, setExerciseModal] = useState<{
    isOpen: boolean;
    data: StockOptionGrant | null;
  }>({ isOpen: false, data: null });
  
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    data: StockOptionGrant | null;
  }>({ isOpen: false, data: null });

  const [addGrantModal, setAddGrantModal] = useState(false);

  // Form states
  const [editForm, setEditForm] = useState({
    recipient: "",
    role: "",
    grantStatus: "",
    grantType: "",
    optionsGranted: 0,
  });

  const [exerciseForm, setExerciseForm] = useState({
    sharesToExercise: 0,
    exerciseDate: new Date().toISOString().split('T')[0],
  });

  const handleEdit = (grant: StockOptionGrant) => {
    setEditForm({
      recipient: grant.recipient,
      role: grant.role,
      grantStatus: grant.grantStatus,
      grantType: grant.grantType,
      optionsGranted: grant.optionsGranted,
    });
    setEditModal({ isOpen: true, data: grant });
  };

  const handleExercise = (grant: StockOptionGrant) => {
    setExerciseForm({
      sharesToExercise: 0,
      exerciseDate: new Date().toISOString().split('T')[0],
    });
    setExerciseModal({ isOpen: true, data: grant });
  };

  const handleDelete = (grant: StockOptionGrant) => {
    setDeleteModal({ isOpen: true, data: grant });
  };

  const handleEditSubmit = () => {
    if (!editModal.data) return;
    
    updateGrant({
      id: editModal.data.id,
      role: editForm.role,
      grantStatus: editForm.grantStatus.toLowerCase(),
      grantType: editForm.grantType,
      optionsGranted: editForm.optionsGranted,
    });
    
    setEditModal({ isOpen: false, data: null });
  };

  const handleExerciseSubmit = () => {
    if (!exerciseModal.data) return;
    
    exerciseOption({
      id: exerciseModal.data.id,
      exerciseDate: exerciseForm.exerciseDate,
      sharesToExercise: exerciseForm.sharesToExercise,
    });
    
    setExerciseModal({ isOpen: false, data: null });
  };

  const handleDeleteConfirm = () => {
    if (!deleteModal.data) return;
    
    deleteGrant({
      id: deleteModal.data.id,
      grantType: deleteModal.data.grantType.toLowerCase(),
    });
    
    setDeleteModal({ isOpen: false, data: null });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Stock Option Plan</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Last updated: <Skeleton className="h-4 w-24" />
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Summary Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <Skeleton className="h-4 w-24 mb-2" />
                  <Skeleton className="h-8 w-32 mb-2" />
                  <Skeleton className="h-4 w-40" />
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* Pool Allocation Skeleton */}
          <div className="mb-6">
            <Skeleton className="h-6 w-32 mb-2" />
            <Skeleton className="h-2 w-full" />
          </div>
          
          {/* Table Skeleton */}
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Stock Option Plan</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Failed to load stock option plan data</span>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data || !data.grants.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Stock Option Plan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No stock option plan data found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Stock Option Plan</span>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAddGrantModal(true)}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Grant
              </Button>
              <span className="text-sm text-muted-foreground">
                Last updated: {data.summary.lastUpdated}
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Total Plan Size</div>
                <div className="text-2xl font-bold">{formatNumber(data.summary.totalPool)}</div>
                <div className="text-sm text-muted-foreground">{data.summary.percentages.totalPoolSizePercentage}% of outstanding shares</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Allocated Shares</div>
                <div className="text-2xl font-bold">{formatNumber(data.summary.allocated)}</div>
                <div className="text-sm text-muted-foreground">{data.summary.percentages.poolAllocatedPercentage}% of pool allocated</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Remaining Plan</div>
                <div className={`text-2xl font-bold ${data.summary.remaining < 0 ? 'text-red-600' : ''}`}>
                  {data.summary.remaining < 0 ? `(${formatNumber(Math.abs(data.summary.remaining))})` : formatNumber(data.summary.remaining)}
                </div>
                <div className="text-sm text-muted-foreground">{data.summary.percentages.remainingPoolPercentage}% of pool available</div>
              </CardContent>
            </Card>
          </div>

          {/* Pool Allocation */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Pool Allocation</h3>
            <div className="flex items-center gap-4">
              <Progress value={data.summary.percentages.poolAllocatedPercentage} className="flex-1 h-2" />
              <span className="text-sm text-muted-foreground">
                {data.summary.percentages.poolAllocatedPercentage.toFixed(1)}% Used
              </span>
            </div>
          </div>

          {/* Grant Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Recipient</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Grant Date</TableHead>
                  <TableHead className="text-right">Options</TableHead>
                  <TableHead>Grant Status</TableHead>
                  <TableHead>Grant Type</TableHead>
                  <TableHead className="text-right">Vested Shares</TableHead>
                  <TableHead className="text-right">Unvested Shares</TableHead>
                  <TableHead>Vesting Schedule</TableHead>
                  <TableHead className="text-center">Vesting Progress</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.grants.map((grant) => (
                  <TableRow key={grant.id}>
                    <TableCell className="font-medium">{grant.recipient}</TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(grant.role)}>
                        {grant.role}
                      </Badge>
                    </TableCell>
                    <TableCell>{grant.grantDate.toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">{formatNumber(grant.optionsGranted)}</TableCell>
                    <TableCell>
                      <Badge variant={getGrantStatusBadgeVariant(grant.grantStatus)}>
                        {grant.grantStatus}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getGrantTypeBadgeVariant(grant.grantType)}>
                        {grant.grantType}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">{formatNumber(grant.vestedShares)}</TableCell>
                    <TableCell className="text-right">{formatNumber(grant.unvestedShares)}</TableCell>
                    <TableCell>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="flex items-center gap-1">
                            {getVestingScheduleDisplay(grant.vestingSchedule, grant.vestingPeriod, grant.cliff).display}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          {getVestingScheduleDisplay(grant.vestingSchedule, grant.vestingPeriod, grant.cliff).tooltip}
                        </TooltipContent>
                      </Tooltip>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center gap-2">
                        <Progress value={grant.percentVested} className="w-16 h-2" />
                        <span className="text-sm">{formatPercentage(grant.percentVested)}</span>
                      </div>
                    </TableCell>
                                         <TableCell className="text-center">
                       <DropdownMenu>
                         <DropdownMenuTrigger asChild>
                           <Button
                             variant="ghost"
                             size="icon"
                             disabled={isUpdating || isExercising || isDeleting}
                           >
                             {isUpdating || isExercising || isDeleting ? (
                               <Loader2 className="h-4 w-4 animate-spin" />
                             ) : (
                               <MoreVertical className="h-4 w-4" />
                             )}
                           </Button>
                         </DropdownMenuTrigger>
                         <DropdownMenuContent align="end">
                           <DropdownMenuItem onClick={() => handleEdit(grant)}>
                             <Edit className="mr-2 h-4 w-4" />
                             Edit Grant
                           </DropdownMenuItem>
                           {canExercise(grant) && (
                             <DropdownMenuItem onClick={() => handleExercise(grant)}>
                               <Zap className="mr-2 h-4 w-4" />
                               Exercise Option
                             </DropdownMenuItem>
                           )}
                           <DropdownMenuItem 
                             onClick={() => handleDelete(grant)}
                             className="text-red-600 focus:text-red-600"
                           >
                             <Trash2 className="mr-2 h-4 w-4" />
                             Terminate Grant
                           </DropdownMenuItem>
                         </DropdownMenuContent>
                       </DropdownMenu>
                     </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>

        {/* Edit Grant Modal */}
        <Dialog 
          open={editModal.isOpen} 
          onOpenChange={(open) => setEditModal({ isOpen: open, data: null })}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Grant</DialogTitle>
            </DialogHeader>
                         <div className="space-y-4">
               <div>
                 <Label htmlFor="recipient">Recipient</Label>
                 <Input
                   id="recipient"
                   value={editForm.recipient}
                   disabled
                   className="bg-muted"
                 />
               </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select value={editForm.role} onValueChange={(value) => setEditForm({ ...editForm, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Employee">Employee</SelectItem>
                    <SelectItem value="Advisor">Advisor</SelectItem>
                    <SelectItem value="Contractor">Contractor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="grantStatus">Grant Status</Label>
                <Select value={editForm.grantStatus} onValueChange={(value) => setEditForm({ ...editForm, grantStatus: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Promised">Promised</SelectItem>
                    <SelectItem value="Granted">Granted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="grantType">Grant Type</Label>
                {editForm.grantType === "Exercised Option" ? (
                  <Input
                    value="Exercised Option"
                    disabled
                    className="bg-muted"
                  />
                ) : (
                  <Select value={editForm.grantType} onValueChange={(value) => setEditForm({ ...editForm, grantType: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Option">Option</SelectItem>
                      <SelectItem value="Restricted Stock">Restricted Stock</SelectItem>
                      <SelectItem value="Exercised Option">Exercised Option</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
              <div>
                <Label htmlFor="optionsGranted">Options Granted</Label>
                <Input
                  id="optionsGranted"
                  type="number"
                  value={editForm.optionsGranted}
                  onChange={(e) => setEditForm({ ...editForm, optionsGranted: parseInt(e.target.value) || 0 })}
                  placeholder="Enter number of options"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => setEditModal({ isOpen: false, data: null })}
                >
                  Cancel
                </Button>
                                 <Button 
                   onClick={handleEditSubmit}
                   disabled={isUpdating}
                 >
                   {isUpdating ? (
                     <>
                       <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                       Saving...
                     </>
                   ) : (
                     'Save Changes'
                   )}
                 </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Exercise Option Modal */}
        <Dialog 
          open={exerciseModal.isOpen} 
          onOpenChange={(open) => setExerciseModal({ isOpen: open, data: null })}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Exercise Option</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="grantee">Grantee</Label>
                <Input
                  id="grantee"
                  value={exerciseModal.data?.recipient || ""}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="totalOptions">Total Options</Label>
                <Input
                  id="totalOptions"
                  value={exerciseModal.data?.optionsGranted || 0}
                  disabled
                  className="bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="sharesToExercise">Shares to Exercise</Label>
                <Input
                  id="sharesToExercise"
                  type="number"
                  value={exerciseForm.sharesToExercise}
                  onChange={(e) => setExerciseForm({ ...exerciseForm, sharesToExercise: parseInt(e.target.value) || 0 })}
                  placeholder="Enter number of shares to exercise"
                />
              </div>
              <div>
                <Label htmlFor="exerciseDate">Exercise Date</Label>
                <Input
                  id="exerciseDate"
                  type="date"
                  value={exerciseForm.exerciseDate}
                  onChange={(e) => setExerciseForm({ ...exerciseForm, exerciseDate: e.target.value })}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => setExerciseModal({ isOpen: false, data: null })}
                >
                  Cancel
                </Button>
                                 <Button 
                   onClick={handleExerciseSubmit}
                   disabled={isExercising || exerciseForm.sharesToExercise <= 0}
                 >
                   {isExercising ? (
                     <>
                       <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                       Exercising...
                     </>
                   ) : (
                     'Exercise Option'
                   )}
                 </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Modal */}
        <AlertDialog 
          open={deleteModal.isOpen} 
          onOpenChange={(open) => setDeleteModal({ isOpen: open, data: null })}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Grant</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the grant for {deleteModal.data?.recipient}? 
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDeleteConfirm}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Add Grant Dialog */}
        <AddGrantDialog
          isOpen={addGrantModal}
          onClose={() => setAddGrantModal(false)}
        />
      </Card>
      <Toaster />
    </TooltipProvider>
  );
};
