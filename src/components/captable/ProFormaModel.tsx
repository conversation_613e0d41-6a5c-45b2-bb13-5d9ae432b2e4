import React, { useState, use<PERSON>emo, useRef, use<PERSON>allback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, TrendingUp, DollarSign, Users, FileText, PieChart, Vote, Briefcase, Download, FileDown, Plus, Loader2, Edit3, Lock, Trash2, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ProFormaModelData,
  ProFormaInputs,
  ValuationMode,
  PreferredStockSeries,
  ProRataRightsData,
} from "@/types/capTable";
import { useProformaModel, useDeleteInvestorFromProforma } from "@/hooks/cap-table/useProforma.hooks";
import { useStockOptionPlan } from "@/hooks/cap-table/useStockOptionPlan.hooks";
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";
import { PieChart as RechartsPieChart, Cell, ResponsiveContainer, Pie, Tooltip as RechartsTooltip, Legend as RechartsLegend } from "recharts";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import AddInvestorModal from "./AddInvestorModal";
import ProRataRightsTable from "./ProRataRightsTable";
import { 
  ProFormaCapTableEmptyState, 
  ProFormaLoadingState, 
  ProFormaErrorState,
  VotingRightsEmptyState,
  StockOptionPlanEmptyState
} from "./ProFormaEmptyStates";
import { mockProRataRights } from "@/data/mockProForma";
import { toast } from "sonner";


// Default inputs for the Pro Forma Model
const DEFAULT_INPUTS: ProFormaInputs = {
    designation: 'Fixed Pre-Money',
    fixedValuation: 0,
    investmentAmount: 0,
    optionPoolPercentage: 0,
    preferredStockDesignation: 'Series A',
    commonStockBuffer: 0,
};

// Static data for sections below Pro Forma Cap Table

const STATIC_OWNERSHIP_DISTRIBUTION = [
  { category: "Founders", shares: 4500000, percentage: "44.12%" },
  { category: "Series A", shares: 3000000, percentage: "29.41%" },
  { category: "Series B", shares: 2000000, percentage: "19.61%" },
  { category: "Option Pool", shares: 720000, percentage: "7.06%" },
];

const STATIC_VOTING_RIGHTS = [
  { 
    shareholder: "John Doe", 
    overallVotingPercent: "25.50%", 
    overallVotingPower: "High",
    commonStockVotingPercent: "30.00%",
    commonStockVotingPower: "High",
    preferredStockVotingPercent: "20.00%",
    preferredStockVotingPower: "Medium"
  },
  { 
    shareholder: "Jane Smith", 
    overallVotingPercent: "18.75%", 
    overallVotingPower: "Medium",
    commonStockVotingPercent: "22.00%",
    commonStockVotingPower: "Medium",
    preferredStockVotingPercent: "15.00%",
    preferredStockVotingPower: "Low"
  },
  { 
    shareholder: "Series A Investors", 
    overallVotingPercent: "29.41%", 
    overallVotingPower: "High",
    commonStockVotingPercent: "0.00%",
    commonStockVotingPower: "None",
    preferredStockVotingPercent: "35.00%",
    preferredStockVotingPower: "High"
  },
];

const STATIC_STOCK_OPTION_PLAN = {
  totalPlanSize: 1000000,
  allocatedShares: 280000,
  remainingPlan: 720000,
  postMoneyAllocation: "7.06%",
    grants: [
      {
      name: "Alice Johnson",
      role: "CEO",
      currentShares: 50000,
      newShares: 25000,
      totalShares: 75000,
      vestingSchedule: "4 years, 1 year cliff",
      status: "Active",
      type: "ISO"
    },
    {
      name: "Bob Wilson",
      role: "CTO",
      currentShares: 40000,
      newShares: 20000,
      totalShares: 60000,
      vestingSchedule: "4 years, 1 year cliff",
      status: "Active",
      type: "ISO"
    },
    {
      name: "Carol Davis",
      role: "VP Engineering",
      currentShares: 30000,
      newShares: 15000,
      totalShares: 45000,
      vestingSchedule: "4 years, 1 year cliff",
      status: "Active",
      type: "NSO"
    },
  ]
};

const ProFormaModel: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>('inputs');
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [isAddInvestorModalOpen, setIsAddInvestorModalOpen] = useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);
  const [investorToDelete, setInvestorToDelete] = useState<any>(null);
  
  const proFormaRef = useRef<HTMLDivElement>(null);

  // Use the comprehensive Pro Forma hook
  const {
    inputs,
    setInputs,
    capTableItems,
    dynamicSeriesColumns,
    apiData: typedApiData,
    isLoading,
    error,
    handleInvestmentChange,
    isItemUpdating,
    hasPendingUpdate,
    proRataRights,
    formatValue,
    formatCurrency,
  formatPricePerShare,
    formatNumber,
    formatPercentage,
    formatNegativeValue,
    formatCurrencyWithParentheses,
    formatNumberWithParentheses,
    formatPercentageWithParentheses,
  } = useProformaModel(DEFAULT_INPUTS);

  const deleteInvestorMutation = useDeleteInvestorFromProforma();
  const { data: stockOptionPlanData, isLoading: isStockOptionPlanLoading } = useStockOptionPlan();

  // Clear filters handler - now uses global filter context
  const { resetFilters } = useGlobalFilters();
  const handleClearFilters = () => {
    resetFilters();
    toast.info("Filters cleared successfully");
  };

  // Process ownership distribution data
  const ownershipDistributionData = useMemo(() => {
    if (!typedApiData?.proFormaOwnershipDistribution) {
      return STATIC_OWNERSHIP_DISTRIBUTION.map(item => ({
        name: item.category,
        value: parseFloat(item.percentage),
        shares: item.shares,
        fill: item.category === 'Founders' ? '#3B82F6' : 
              item.category === 'Series A' ? '#10B981' : 
              item.category === 'Series B' ? '#F59E0B' : '#8B5CF6'
      }));
    }

    const { nonSeriesData, seriesData } = typedApiData.proFormaOwnershipDistribution;
    const data = [];

    // Add non-series data (Founders, Option Pool)
    nonSeriesData.forEach(item => {
      if (item.percentage > 0) {
        data.push({
          name: item.type,
          value: item.percentage,
          shares: item.shares,
          fill: item.type === 'Founders' ? '#3B82F6' : '#8B5CF6'
        });
      }
    });

    // Add series data
    Object.entries(seriesData).forEach(([seriesName, seriesInfo]) => {
      const seriesData = seriesInfo as { percentage: number; shares: number };
      if (seriesData.percentage > 0) {
        data.push({
          name: seriesName,
          value: seriesData.percentage,
          shares: seriesData.shares,
          fill: seriesName === 'Series A' ? '#10B981' : 
                seriesName === 'Series A1' ? '#F59E0B' : '#6B7280'
        });
      }
    });

    return data;
  }, [typedApiData?.proFormaOwnershipDistribution]);

  // Show error message if API fails
  if (error) {
    console.error('Pro Forma API Error:', error);
  }


  // Section navigation
  const sections = [
    { id: 'inputs', label: 'Inputs', icon: TrendingUp },
    { id: 'summary', label: 'Summary', icon: DollarSign },
    { id: 'cap-table', label: 'Cap Table', icon: Users },
    { id: 'pro-rata', label: 'Pro Rata Rights', icon: FileText },
    { id: 'ownership', label: 'Ownership', icon: PieChart },
    { id: 'voting', label: 'Voting Rights', icon: Vote },
    { id: 'sop', label: 'Stock Option Plan', icon: Briefcase },
  ];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  };

  // Handle adding new investor - simplified callback for data refresh
  const handleAddInvestor = () => {
    // The modal now handles the API call directly
    // This callback is just for refreshing data after successful addition
    console.log('Investor added successfully, data will be refreshed automatically');
  };

  const handleDeleteInvestor = (investor: any) => {
    setInvestorToDelete(investor);
    setDeleteModalOpen(true);
  };

  const confirmDeleteInvestor = async () => {
    if (!investorToDelete) return;

    try {
      // Prepare the delete payload with the appropriate ID fields
      const deletePayload = {
        id: investorToDelete.id,
        officerId: investorToDelete.officerId,
        serviceProviderId: investorToDelete.serviceProviderId,
        valuationModeType: (inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money') as 'fixed_pre_money' | 'fixed_post_money'
      };
      
      const result = await deleteInvestorMutation.mutateAsync(deletePayload);
      
      // Check if the result contains an error (API client returns { error: "..." } on error)
      if (result && typeof result === 'object' && 'error' in result) {
        // Only throw if the error is not null/undefined and not empty
        if (result.error && result.error !== null && result.error !== '') {
          throw new Error(result.error as string);
        }
      }
      
      toast.success(`Successfully deleted ${investorToDelete.name} from proforma`);
      setDeleteModalOpen(false);
      setInvestorToDelete(null);
    } catch (error: any) {
      toast.error(`Failed to delete investor: ${error.message || 'Please try again'}`);
    }
  };

  const handleCancelDelete = () => {
    if (!deleteInvestorMutation.isPending) {
      setDeleteModalOpen(false);
      setInvestorToDelete(null);
    }
  };

  // PDF Export Function
  const exportToPDF = async () => {
    if (!proFormaRef.current) return;
    
    setIsExporting(true);
    try {
      const canvas = await html2canvas(proFormaRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
      });
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;
      
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
      
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      
      pdf.save(`pro-forma-model-${new Date().toISOString().split('T')[0]}.pdf`);
    } catch (error) {
      console.error('PDF export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div ref={proFormaRef} className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            Failed to load pro forma data. Using static data as fallback. Error: {error.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Sticky Navigation with Export */}
      <div className="sticky top-0 bg-white border-b z-10">
        <div className="flex justify-between items-center p-4">
          <div className="flex gap-2 overflow-x-auto">
            {sections.map((section) => {
              const Icon = section.icon;
              return (
                <Button
                  key={section.id}
                  variant={activeSection === section.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => scrollToSection(section.id)}
                  className="flex items-center gap-2 whitespace-nowrap"
                >
                  <Icon className="h-4 w-4" />
                  {section.label}
                </Button>
              );
            })}
          </div>
          
          {/* Export Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={exportToPDF}
              disabled={isExporting}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <FileDown className="h-4 w-4" />
              {isExporting ? 'Exporting...' : 'Export PDF'}
            </Button>
          </div>
        </div>
      </div>

      {/* Input Section - Now Editable */}
      <section id="inputs" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Pro Forma Inputs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Valuation Mode */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium mb-2 block">Valuation Mode</Label>
                <div className="flex gap-2">
                  <Button
                    variant={inputs.designation === 'Fixed Pre-Money' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInputs({ ...inputs, designation: 'Fixed Pre-Money' })}
                  >
                    Fixed Pre-Money
                  </Button>
                  <Button
                    variant={inputs.designation === 'Fixed Post-Money' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setInputs({ ...inputs, designation: 'Fixed Post-Money' })}
                  >
                    Fixed Post-Money
                  </Button>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium mb-2 block">
                  {inputs.designation === 'Fixed Pre-Money' ? 'Pre-Money Valuation' : 'Post-Money Valuation'}
                </Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <Input
                    type="number"
                    step="0.01"
                    value={inputs.fixedValuation || ''}
                    onChange={(e) => setInputs({ ...inputs, fixedValuation: e.target.value === '' ? 0 : Number(e.target.value) })}
                    placeholder="0.00"
                    className="text-lg font-semibold pl-7"
                  />
                </div>
              </div>
            </div>

            {/* Loading Indicator */}
            {isLoading && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm font-medium">Updating calculations...</span>
                </div>
              </div>
            )}

            {/* Investment and Pool - Now Editable with Improved UI/UX */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label className="text-sm font-medium mb-2 block">Investment Amount</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <Input
                    type="number"
                    step="0.01"
                    value={inputs.investmentAmount || ''}
                    onChange={(e) => setInputs({ ...inputs, investmentAmount: e.target.value === '' ? 0 : Number(e.target.value) })}
                    placeholder="0.00"
                    className="text-lg font-semibold pl-7"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium mb-2 block">Option Pool Available for Issuance Post-Money</Label>
                <div className="relative">
                  <Input
                    type="number"
                    step="0.01"
                    value={inputs.optionPoolPercentage || ''}
                    onChange={(e) => setInputs({ ...inputs, optionPoolPercentage: e.target.value === '' ? 0 : Number(e.target.value) })}
                    placeholder="0.00"
                    className="text-lg font-semibold pr-8"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 font-medium">
                    %
                  </span>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium mb-2 block">Common Stock Buffer</Label>
                <div className="relative">
                  <Input
                    type="number"
                    step="0.01"
                    value={inputs.commonStockBuffer || ''}
                    onChange={(e) => setInputs({ ...inputs, commonStockBuffer: e.target.value === '' ? 0 : Number(e.target.value) })}
                    placeholder="0.00"
                    className="text-lg font-semibold pr-8"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 font-medium">
                    %
                  </span>
                </div>
              </div>
            </div>

            {/* Preferred Stock Designation - Now Editable */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Preferred Stock Designation</Label>
              <Select 
                value={inputs.preferredStockDesignation} 
                onValueChange={(value: PreferredStockSeries) => setInputs({ ...inputs, preferredStockDesignation: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Series Seed">Series Seed</SelectItem>
                  <SelectItem value="Series A">Series A</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Professional Disclaimer */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This pro forma calculator assumes that all Convertible Securities will convert 
                in the pre-money value of the Company as this is industry-standard. 
                For more information, please contact Founders Form.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </section>

      {/* Summary Metrics Section - Updated with new fields */}
      <section id="summary" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Summary Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Conditional Valuation Card */}
              <Card>
                <CardContent className="p-4">
                  <div className="text-sm text-muted-foreground mb-2">
                    {inputs.designation === 'Fixed Pre-Money' ? 'Effective Post-Money Valuation' : 'Effective Pre-Money Valuation'}
                  </div>
                  <div className="text-2xl font-bold text-green-600 flex items-center gap-2">
                    {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                    {formatCurrency(
                      typedApiData?.proformaSummaryMetrics?.effectivePrePostMoneyValuation ?? 0
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {inputs.designation === 'Fixed Pre-Money' 
                      ? 'Pre-money + Investment + Convertible conversions' 
                      : 'Post-money - Investment - Convertible conversions'}
                  </div>
                </CardContent>
              </Card>
              
              {/* Series Pricing Cards */}
              {(() => {
                const apiSeriesData = typedApiData?.proformaSummaryMetrics?.seriesPricePerShare;
                
                if (apiSeriesData) {
                  return Object.entries(apiSeriesData).map(([seriesName, seriesData]) => (
                    <Card key={seriesName}>
                      <CardContent className="p-4">
                        <div className="text-sm text-muted-foreground mb-2">
                          {seriesName} Price Per Share
                        </div>
                        <div className="text-2xl font-bold text-blue-600 flex items-center gap-2">
                          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                          {formatPricePerShare((seriesData as any).pricePerShare)}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatNumber((seriesData as any).shareIssued)} shares issued
                        </div>
                      </CardContent>
                    </Card>
                  ));
                } else {
                  return (
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-sm text-muted-foreground mb-2">
                          No Series Data Available
                        </div>
                        <div className="text-2xl font-bold text-gray-400">
                          -
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Load data to see series pricing
                        </div>
                      </CardContent>
                    </Card>
                  );
                }
              })()}
            </div>

            {/* Authorized Stock Breakdown - Updated with new fields */}
            <div className="space-y-4">
              <h4 className="font-semibold">Authorized Stock Breakdown</h4>
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-muted-foreground">Common Stock</div>
                  <div className="text-xl font-bold flex items-center justify-center gap-2">
                    {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                    {formatValue(typedApiData?.authorizedStockBreakdown?.commonStock ?? 0)}
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-muted-foreground">Preferred Stock</div>
                  <div className="text-xl font-bold flex items-center justify-center gap-2">
                    {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                    {formatValue(typedApiData?.authorizedStockBreakdown?.preferredStock ?? 0)}
                  </div>
                </div>
                {Object.entries(typedApiData?.authorizedStockBreakdown?.series ?? {}).map(([series, shares]) => (
                  <div key={series} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-muted-foreground">{series}</div>
                    <div className="text-xl font-bold flex items-center justify-center gap-2">
                      {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                      {formatValue(shares as number)}
                    </div>
                  </div>
                ))}
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Pool</div>
                  <div className="text-xl font-bold flex items-center justify-center gap-2">
                    {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                    {formatValue(typedApiData?.authorizedStockBreakdown?.totalPool ?? 0)}
                  </div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-sm text-muted-foreground">Pool Remaining for Issuance</div>
                  <div className="text-xl font-bold flex items-center justify-center gap-2">
                    {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                    {formatValue(typedApiData?.authorizedStockBreakdown?.poolRemainingForIssuance ?? 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Calculation Notes */}
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Calculation Notes</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                  Effective Post-Money Valuation includes convertible security conversions
                  </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  Convertible securities convert at pre-money valuation (industry standard)
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  Option pool calculations based on post-money valuation
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Pro Forma Cap Table Section - Updated with Pre-Round/Post-Round Split and Add Investor */}
      <section id="cap-table" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Pro Forma Cap Table
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Add Investor Button */}
            <div className="mb-4">
              <Button onClick={() => setIsAddInvestorModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Investor
              </Button>
            </div>
            
            {/* Show loading state */}
            {isLoading && (
              <ProFormaLoadingState />
            )}

            {/* Show error state */}
            {error && (
              <ProFormaErrorState 
                error={error.message || 'Unknown error occurred'} 
                onRetry={() => window.location.reload()} 
              />
            )}

            {/* Show empty state when no data */}
            {!isLoading && !error && (!capTableItems || capTableItems.length === 0) && (
              <ProFormaCapTableEmptyState
                onAddInvestor={() => setIsAddInvestorModalOpen(true)}
                onClearFilters={handleClearFilters}
              />
            )}

            {/* Show table when data is available */}
            {!isLoading && !error && capTableItems && capTableItems.length > 0 && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-left p-3 font-medium w-48">Stockholder</th>
                      <th className="text-right p-3 font-medium w-40">Cash Investment</th>
                      <th className="text-right p-3 font-medium w-32 whitespace-nowrap">Common Stock</th>
                      <th className="text-right p-3 font-medium w-32">Stock Option Plan</th>
                      <th className="text-right p-3 font-medium w-32">
                        {inputs.preferredStockDesignation === 'Series A' ? 'Series A Shares' : 'Series Seed Shares'}
                      </th>
                      {/* Dynamic Series Columns */}
                      {dynamicSeriesColumns.length > 0 ? (
                        dynamicSeriesColumns.map((series) => (
                          <th key={series} className="text-right p-3 font-medium w-32">
                            {series} Shares
                          </th>
                        ))
                      ) : (
                        <>
                          <th className="text-right p-3 font-medium w-32">Series A Shares</th>
                          <th className="text-right p-3 font-medium w-32">Series A-1 Shares</th>
                        </>
                      )}
                      <th className="text-right p-3 font-medium w-40">Total Shares</th>
                      <th className="text-right p-3 font-medium w-48">Fully Diluted Ownership</th>
                      <th className="text-right p-3 font-medium w-40 whitespace-nowrap">Ownership Change</th>
                    </tr>
                  </thead>
                  <tbody>
                  {/* Section 1: Regular investor rows */}
                  {capTableItems.filter(item => !item.isSection3Row && !item.isStockOptionPoolRow).map((item) => (
                    <tr key={item.id} className="border-b hover:bg-gray-50">
                <td className="p-3 font-medium w-48">{item.name}</td>
                <td className="p-3 text-right w-40">
                  {item.hasValidId ? (
                    <div className="flex items-center justify-end gap-2">
                      <div className="relative">
                        <input
                          type="number"
                          value={item.investmentAmount === 0 ? '' : item.investmentAmount}
                          onChange={(e) => {
                            const value = e.target.value === '' ? 0 : parseFloat(e.target.value) || 0;
                            handleInvestmentChange(item, value);
                          }}
                          className={`w-24 px-2 py-1 text-right border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            isItemUpdating(item.id) 
                              ? 'border-yellow-400 bg-yellow-50' 
                              : hasPendingUpdate(item.id) 
                                ? 'border-orange-400 bg-orange-50' 
                                : 'border-gray-300'
                          }`}
                          placeholder="0"
                        />
                        {isItemUpdating(item.id) && (
                          <div className="absolute -right-1 -top-1">
                            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                          </div>
                        )}
                        {hasPendingUpdate(item.id) && !isItemUpdating(item.id) && (
                          <div className="absolute -right-1 -top-1">
                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                      <span className="text-gray-500 text-sm">$</span>
                      {/* Cash Investment tooltip for editable rows */}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-blue-600 cursor-help text-sm">(i)</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Actual wire amount: {formatCurrencyWithParentheses(item.catchInvestment)}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                    </div>
                  ) : (
                    <div className="flex items-center justify-end gap-1">
                      {formatCurrencyWithParentheses(item.investmentAmount)}
                    </div>
                  )}
                      </td>
                <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.commonStockShares)}</td>
                <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.stockOptionPlanShares)}</td>
                <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesAShares || 0)}</td>
                      {/* Dynamic Series Columns */}
                      {dynamicSeriesColumns.length > 0 ? (
                        dynamicSeriesColumns.map((series) => {
                          const seriesKey = series.toLowerCase().replace(' ', '') + 'Shares';
                          return (
                            <td key={series} className="p-3 text-right w-32">
                              {formatNumberWithParentheses(item[seriesKey] || 0)}
                            </td>
                          );
                        })
                      ) : (
                        <>
                          <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesAShares || 0)}</td>
                          <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesA1Shares || 0)}</td>
                        </>
                      )}
                      <td className="p-3 text-right w-40">{formatNumberWithParentheses(item.totalShares)}</td>
                      <td className="p-3 text-right w-48">{formatPercentageWithParentheses(item.fullyDilutedOwnership)}</td>
                      <td className="p-3 text-right w-40">
                        <span className={item.ownershipChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {item.ownershipChange >= 0 ? '+' : ''}{formatPercentageWithParentheses(item.ownershipChange)}
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-blue-600 cursor-help ml-1">(i)</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {item.ownershipChange >= 0 
                                  ? `[increased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item.newFullyDilutedOwnership?.toFixed(2)}%]`
                                  : `[decreased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item.newFullyDilutedOwnership?.toFixed(2)}%]`
                                }
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                      <td className="p-3 text-center w-16">
                        {item.hasValidId && item.isSection3Row && (
                          <button
                            onClick={() => handleDeleteInvestor(item)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                            title="Delete Investor"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                  
                  {/* First Horizontal Divider */}
                  <tr>
                    <td colSpan={dynamicSeriesColumns.length > 0 ? 5 + dynamicSeriesColumns.length + 3 : 9} className="border-t-4 border-gray-400"></td>
                  </tr>
                  
                  {/* Section 2: SOP Summary Rows - Now rendered from API data */}
                  {capTableItems.filter(item => item.isStockOptionPoolRow).map((item) => (
                    <tr key={item.id} className="border-b bg-muted/30">
                      <td className="p-3 font-medium w-48">{item.name}</td>
                      <td className="p-3 text-right w-40">-</td>
                      <td className="p-3 text-right w-32">-</td>
                      <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.stockOptionPlanShares)}</td>
                      <td className="p-3 text-right w-32">-</td>
                    {/* Dynamic Series Columns */}
                    {dynamicSeriesColumns.length > 0 ? (
                      dynamicSeriesColumns.map((series) => (
                          <td key={series} className="p-3 text-right w-32">-</td>
                      ))
                    ) : (
                      <>
                          <td className="p-3 text-right w-32">-</td>
                          <td className="p-3 text-right w-32">-</td>
                      </>
                    )}
                      <td className="p-3 text-right w-40">{formatNumberWithParentheses(item.totalShares)}</td>
                      <td className="p-3 text-right w-48">{formatPercentageWithParentheses(item.fullyDilutedOwnership)}</td>
                      <td className="p-3 text-right w-40">
                        <span className={item.ownershipChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {item.ownershipChange >= 0 ? '+' : ''}{formatPercentageWithParentheses(item.ownershipChange)}
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-blue-600 cursor-help ml-1">(i)</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {item.ownershipChange >= 0 
                                  ? `[increased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item.newFullyDilutedOwnership.toFixed(2)}%]`
                                  : `[decreased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item.newFullyDilutedOwnership.toFixed(2)}%]`
                                }
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                  </tr>
                  ))}
                  
                  {/* Second Horizontal Divider */}
                  <tr>
                    <td colSpan={dynamicSeriesColumns.length > 0 ? 5 + dynamicSeriesColumns.length + 3 : 9} className="border-t-4 border-gray-400"></td>
                  </tr>
                  
                  {/* Section 3: Additional rows (placeholder for API population) */}
                  {capTableItems.filter(item => item.isSection3Row).map((item) => {
                    // Check if this row should be editable based on API data - only if it has a valid ID
                    const isEditable = item.hasValidId;
                    
                    return (
                      <tr key={item.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium flex items-center gap-2 w-48">
                          {item.name}
                          {isEditable ? (
                            <Edit3 className="h-3 w-3 text-blue-500" />
                          ) : (
                            <Lock className="h-3 w-3 text-gray-400" />
                          )}
                        </td>
                        <td className="p-3 text-right w-40">
                          {isEditable ? (
                            <div className="flex items-center justify-end gap-2">
                                <div className="relative group flex items-center gap-2">
                                <Input
                                  type="number"
                                  value={item.investmentAmount || ''}
                                  onChange={(e) => {
                                    const newValue = e.target.value === '' ? 0 : Number(e.target.value);
                                    handleInvestmentChange(item, newValue);
                                  }}
                                  className={`w-36 text-right transition-all duration-200 ${
                                    isItemUpdating(item.id) 
                                      ? 'border-orange-300 bg-orange-50 focus:border-orange-500 focus:ring-2 focus:ring-orange-200' 
                                      : hasPendingUpdate(item.id)
                                      ? 'border-yellow-300 bg-yellow-50 focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200'
                                      : 'border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
                                  }`}
                                  placeholder="0"
                                  min="0"
                                  step="1000"
                                  disabled={isItemUpdating(item.id)}
                                />
                                {/* Delete button */}
                                <button
                                  onClick={() => handleDeleteInvestor(item)}
                                  className="text-red-500 hover:text-red-700 transition-colors"
                                  title="Delete Investor"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                                {/* Cash Investment tooltip */}
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <span className="text-blue-600 cursor-help ml-2">(i)</span>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Actual wire amount: {formatCurrencyWithParentheses(item.catchInvestment)}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                                {/* Visual indicators for update status */}
                                {isItemUpdating(item.id) && (
                                  <div className="absolute -right-6 top-1/2 -translate-y-1/2">
                                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-orange-500"></div>
                                  </div>
                                )}
                                {hasPendingUpdate(item.id) && !isItemUpdating(item.id) && (
                                  <div className="absolute -right-6 top-1/2 -translate-y-1/2">
                                    <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse"></div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center justify-end gap-2 group">
                              <span className="text-gray-500">{formatCurrencyWithParentheses(item.investmentAmount)}</span>
                              <Lock className="h-3 w-3 text-gray-400" />
                            </div>
                          )}
                        </td>
                      <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.commonStockShares)}</td>
                      <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.stockOptionPlanShares)}</td>
                      <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesAShares || 0)}</td>
                      {/* Dynamic Series Columns */}
                      {dynamicSeriesColumns.length > 0 ? (
                        dynamicSeriesColumns.map((series) => {
                          const seriesKey = series.toLowerCase().replace(' ', '') + 'Shares';
                          return (
                            <td key={series} className="p-3 text-right w-32">
                              {formatNumberWithParentheses(item[seriesKey] || 0)}
                            </td>
                          );
                        })
                      ) : (
                        <>
                          <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesAShares || 0)}</td>
                          <td className="p-3 text-right w-32">{formatNumberWithParentheses(item.seriesA1Shares || 0)}</td>
                        </>
                      )}
                      <td className="p-3 text-right w-40">{formatNumberWithParentheses(item.totalShares)}</td>
                      <td className="p-3 text-right w-48">{formatPercentageWithParentheses(item.fullyDilutedOwnership)}</td>
                      <td className="p-3 text-right w-40">
                        <span className={item.ownershipChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {item.ownershipChange >= 0 ? '+' : ''}{formatPercentageWithParentheses(item.ownershipChange)}
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-blue-600 cursor-help ml-1">(i)</span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {item.ownershipChange >= 0 
                                  ? `[increased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item?.newFullyDilutedOwnership?.toFixed(2)}%]`
                                  : `[decreased from ${item?.fullyDilutedOwnership?.toFixed(2)}% to ${item?.newFullyDilutedOwnership?.toFixed(2)}%]`
                                }
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                    </tr>
                  );
                  })}
                </tbody>
              </table>
            </div>
            )}
            

          </CardContent>
        </Card>
      </section>

      {/* Pro Rata Rights Table Section */}
      <section id="pro-rata" className="space-y-4">
                  <ProRataRightsTable data={proRataRights || mockProRataRights} />
      </section>

      {/* Ownership Distribution Section */}
      <section id="ownership" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Ownership Distribution
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-gray-400 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Interactive ownership distribution visualization based on current cap table data</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Interactive Pie Chart */}
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={ownershipDistributionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {ownershipDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <RechartsTooltip 
                      formatter={(value: number, name: string, props: any) => [
                        `${value.toFixed(2)}%`,
                        name
                      ]}
                      labelFormatter={(label: string, payload: any) => {
                        if (payload && payload[0]) {
                          return `${label}: ${payload[0].payload.shares.toLocaleString()} shares`;
                        }
                        return label;
                      }}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
              
              {/* Legend */}
              <div className="space-y-3">
                <h4 className="font-semibold">Ownership Breakdown</h4>
                <div className="space-y-2">
                  {ownershipDistributionData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: item.fill }}
                        ></div>
                        <span className="font-medium">{item.name}</span>
                    </div>
                      <div className="text-right">
                        <div className="font-semibold">{item.value.toFixed(2)}%</div>
                        <div className="text-sm text-gray-500">{item.shares.toLocaleString()} shares</div>
                      </div>
                  </div>
                ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Voting Rights Section - Streamlined Structure */}
      <section id="voting" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Vote className="h-5 w-5" />
              Voting Rights Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Check if voting rights data is available */}
            {!typedApiData?.votingRightsAnalysis || typedApiData.votingRightsAnalysis.length === 0 ? (
              <VotingRightsEmptyState />
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">Shareholder</th>
                        <th className="text-right p-3 font-medium">Overall Voting %</th>
                        <th className="text-center p-3 font-medium">Overall Voting Power</th>
                        <th className="text-right p-3 font-medium">Common Stock Voting %</th>
                        <th className="text-center p-3 font-medium">Common Stock Voting Power</th>
                        <th className="text-right p-3 font-medium">Preferred Stock Voting %</th>
                        <th className="text-center p-3 font-medium">Preferred Stock Voting Power</th>
                      </tr>
                    </thead>
                    <tbody>
                      {typedApiData?.votingRightsAnalysis?.map((item: any, index: number) => (
                    <tr key={item.id || index} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium">{item.shareholderName || item.shareholder}</td>
                      <td className="p-3 text-right font-medium">
                        {typedApiData?.votingRightsAnalysis 
                          ? `${item.overallVotingPercentage?.toFixed(2)}%`
                          : item.overallVotingPercent
                        }
                      </td>
                      <td className="p-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (item.overallStockPower || item.overallVotingPower) === 'High' 
                            ? 'bg-red-100 text-red-800' 
                            : (item.overallStockPower || item.overallVotingPower) === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {item.overallStockPower || item.overallVotingPower}
                        </span>
                      </td>
                      <td className="p-3 text-right font-medium">
                        {typedApiData?.votingRightsAnalysis 
                          ? `${item.commonStockVotingPercentage?.toFixed(2)}%`
                          : item.commonStockVotingPercent
                        }
                      </td>
                      <td className="p-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (item.commonStockPower || item.commonStockVotingPower) === 'High' 
                            ? 'bg-red-100 text-red-800' 
                            : (item.commonStockPower || item.commonStockVotingPower) === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {item.commonStockPower || item.commonStockVotingPower}
                        </span>
                      </td>
                      <td className="p-3 text-right font-medium">
                        {typedApiData?.votingRightsAnalysis 
                          ? `${item.preferredStockVotingPercentage?.toFixed(2)}%`
                          : item.preferredStockVotingPercent
                        }
                      </td>
                      <td className="p-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (item.preferredStockPower || item.preferredStockVotingPower) === 'High' 
                            ? 'bg-red-100 text-red-800' 
                            : (item.preferredStockPower || item.preferredStockVotingPower) === 'Medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {item.preferredStockPower || item.preferredStockVotingPower}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
                </div>
                
                {/* Info Capsule */}
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-blue-700 font-medium">Information</span>
                  </div>
                  <p className="text-sm text-blue-600 mt-2">
                    Voting power is calculated based on total shares outstanding. Overall voting percentage represents the shareholder's total voting influence across all stock classes.
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </section>

      {/* Stock Option Plan Section - Client signed off */}
      <section id="sop" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Stock Option Plan
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Pool Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Total Plan Size</div>
                <div className="text-xl font-bold">
                  {isStockOptionPlanLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                  ) : (
                    (stockOptionPlanData?.summary?.totalPool || STATIC_STOCK_OPTION_PLAN.totalPlanSize).toLocaleString()
                  )}
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Allocated Shares</div>
                <div className="text-xl font-bold">
                  {isStockOptionPlanLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                  ) : (
                    (stockOptionPlanData?.summary?.allocated || STATIC_STOCK_OPTION_PLAN.allocatedShares).toLocaleString()
                  )}
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Remaining Plan</div>
                <div className="text-xl font-bold">
                  {isStockOptionPlanLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                  ) : (
                    (stockOptionPlanData?.summary?.remaining || STATIC_STOCK_OPTION_PLAN.remainingPlan).toLocaleString()
                  )}
              </div>
              </div>
            </div>

            {/* Check if stock option plan data is available */}
            {!typedApiData?.stockOptionPlan || typedApiData.stockOptionPlan.length === 0 ? (
              <StockOptionPlanEmptyState />
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 font-medium">Name</th>
                      <th className="text-left p-3 font-medium">Role</th>
                      <th className="text-right p-3 font-medium">Current Shares</th>
                      <th className="text-right p-3 font-medium">New Shares</th>
                      <th className="text-right p-3 font-medium">Total Shares</th>
                      <th className="text-left p-3 font-medium">Vesting Schedule</th>
                      <th className="text-center p-3 font-medium">Status</th>
                      <th className="text-center p-3 font-medium">Type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {typedApiData?.stockOptionPlan?.map((grant: any, index: number) => (
                    <tr key={grant.id || index} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium">{grant.name || grant.recipient || "-"}</td>
                      <td className="p-3">{grant.role || "-"}</td>
                      <td className="p-3 text-right">{(grant.currentShares || grant.optionsGranted || 0).toLocaleString()}</td>
                      <td className="p-3 text-right">{(grant.newShares || 0).toLocaleString()}</td>
                      <td className="p-3 text-right font-medium">{(grant.totalShares || grant.optionsGranted || 0).toLocaleString()}</td>
                      <td className="p-3">{grant.vestingSchedule || grant.vestingScheduleType || "-"}</td>
                      <td className="p-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (grant.status || grant.grantStatus) === 'Active' || (grant.status || grant.grantStatus) === 'granted'
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {grant.status || grant.grantStatus || 'Active'}
                        </span>
                      </td>
                      <td className="p-3 text-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (grant.type || grant.grantType) === 'ISO' || (grant.type || grant.grantType) === 'Option'
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {(grant.type || grant.grantType) === 'restricted-stock-grant-inside-of-stock-option-plan' ? 'Restricted' : 
                           (grant.type || grant.grantType) === 'option' ? 'Option' : 
                           grant.type || grant.grantType || 'Option'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            )}
          </CardContent>
        </Card>
      </section>

      {/* Add Investor Modal */}
      <AddInvestorModal
        isOpen={isAddInvestorModalOpen}
        onClose={() => setIsAddInvestorModalOpen(false)}
        onAdd={handleAddInvestor}
        inputs={inputs}
      />

      {/* Delete Investor Confirmation Modal */}
      <AlertDialog open={deleteModalOpen} onOpenChange={handleCancelDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Investor</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this investor from the proforma?
              <br />
              <br />
              <strong>Investor:</strong> {investorToDelete?.name}
              <br />
              <strong>Investment Amount:</strong> {investorToDelete && formatCurrencyWithParentheses(investorToDelete.investmentAmount)}
              <br />
              <br />
              <span className="text-red-600 font-medium">⚠️ This action cannot be undone.</span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete} disabled={deleteInvestorMutation.isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteInvestor}
              disabled={deleteInvestorMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteInvestorMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Investor'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ProFormaModel;
