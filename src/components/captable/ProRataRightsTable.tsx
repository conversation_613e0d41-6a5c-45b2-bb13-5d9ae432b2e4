import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ProRataRightsData } from "@/types/capTable";
import { ProRataRightsEmptyState } from "./ProFormaEmptyStates";

interface ProRataRightsTableProps {
  data: ProRataRightsData;
}

const ProRataRightsTable: React.FC<ProRataRightsTableProps> = ({ data }) => {
  // Helper function to format currencies
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Helper function to format price per share (preserves decimal places from API)
  const formatPricePerShare = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 6, // Allow up to 6 decimal places to preserve API precision
    }).format(amount);
  };

  // Helper function to format numbers
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  // Check if data is empty
  const isEmpty = !data.items || data.items.length === 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pro Rata Rights</CardTitle>
        <p className="text-sm italic text-muted-foreground">
          The Investors noted below are entitled to invest the amounts listed in this table for the round. 
          Please include the investment amount in the Pro Forma Cap Table for an illustration of the cap table with their pro rata.
        </p>
      </CardHeader>
      <CardContent>
        {isEmpty ? (
          <ProRataRightsEmptyState />
        ) : (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Investor</TableHead>
                    <TableHead className="text-right">Pro Rata Amount</TableHead>
                    <TableHead className="text-right">Number of Series A Shares</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.investorName}</TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.proRataAmount)}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatNumber(item.numberOfSeriesAShares)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            <div className="mt-4 p-3 bg-muted/30 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Total Pro Rata Amount:</span>
                <span className="text-lg font-semibold">
                  {formatCurrency(data.totalProRataAmount)}
                </span>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span className="text-sm text-muted-foreground">Price per Share:</span>
                <span className="text-sm font-medium">
                  {formatPricePerShare(data.pricePerShare)}
                </span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ProRataRightsTable;
