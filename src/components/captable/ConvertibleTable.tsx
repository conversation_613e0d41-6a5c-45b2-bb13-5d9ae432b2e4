import React, { use<PERSON>em<PERSON>, useState } from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { RefreshCw, AlertCircle } from "lucide-react";
import { useConvertibleSecurities } from "@/hooks/cap-table/useConvertibleSecurities.hooks";
import { ConvertibleSecurity } from "@/types/capTable";

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value}%`;
};

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat("en-US").format(value);
};

const formatDate = (dateString: string): string => {
  if (!dateString || dateString === "-") return "-";
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "numeric",
    });
  } catch {
    return "-";
  }
};

const getSecurityTypeBadge = (securityType: string) => {
  if (securityType === "SAFE") {
    return (
      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
        SAFE
      </Badge>
    );
  }
  return (
    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
      Convertible Note
    </Badge>
  );
};

export const ConvertibleTable: React.FC = () => {
  const { data, isLoading, error, refetch } = useConvertibleSecurities();
  const [sortBy, setSortBy] = useState<'investor' | 'securityType' | 'principal'>('investor');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const sortedSecurities = useMemo(() => {
    if (!data?.securities) return [];
    
    return [...data.securities].sort((a, b) => {
      if (sortBy === 'investor') {
        return sortOrder === 'asc' 
          ? a.investor.localeCompare(b.investor)
          : b.investor.localeCompare(a.investor);
      }
      if (sortBy === 'securityType') {
        return sortOrder === 'asc'
          ? a.securityType.localeCompare(b.securityType)
          : b.securityType.localeCompare(a.securityType);
      }
      if (sortBy === 'principal') {
        return sortOrder === 'asc' ? a.principal - b.principal : b.principal - a.principal;
      }
      return 0;
    });
  }, [data?.securities, sortBy, sortOrder]);

  const handleSort = (column: 'investor' | 'securityType' | 'principal') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Convertible Securities</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Last updated: <Skeleton className="h-4 w-24" />
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16" />
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Convertible Securities</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Failed to load convertible securities data</span>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data || !data.securities.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Convertible Securities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No convertible securities found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
            <span>Convertible Securities</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Last updated: {data.summary.lastUpdated}
              </span>
            </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
          {/* Summary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Total Principal</div>
                <div className="text-2xl font-bold">{formatCurrency(data.summary.totalPrincipal)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">SAFEs</div>
                <div className="text-2xl font-bold">{data.summary.safes}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Convertible Notes</div>
                <div className="text-2xl font-bold">{data.summary.convertibleNotes}</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-muted-foreground">Total Investors</div>
                <div className="text-2xl font-bold">{data.summary.totalInvestors}</div>
              </CardContent>
            </Card>
          </div>

          {/* Securities Table */}
          <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('investor')}
                  >
                    <Tooltip>
                      <TooltipTrigger>Investor</TooltipTrigger>
                      <TooltipContent>Investor name</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('securityType')}
                  >
                    <Tooltip>
                      <TooltipTrigger>Convertible Security</TooltipTrigger>
                      <TooltipContent>Type of convertible security</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50 text-right"
                    onClick={() => handleSort('principal')}
                  >
                    <Tooltip>
                      <TooltipTrigger>Principal</TooltipTrigger>
                      <TooltipContent>Principal investment amount</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-right">
                    <Tooltip>
                      <TooltipTrigger>Interest Rate</TooltipTrigger>
                      <TooltipContent>Interest rate (0% for SAFEs)</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger>Issue Date</TooltipTrigger>
                      <TooltipContent>Date of security issuance</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger>Maturity</TooltipTrigger>
                      <TooltipContent>Maturity date (N/A for SAFEs)</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-right">
                    <Tooltip>
                      <TooltipTrigger>Valuation Cap</TooltipTrigger>
                      <TooltipContent>Valuation cap for conversion</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-right">
                    <Tooltip>
                      <TooltipTrigger>Discount</TooltipTrigger>
                      <TooltipContent>Discount rate for conversion</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-right">
                    <Tooltip>
                      <TooltipTrigger>Interest Accrued</TooltipTrigger>
                      <TooltipContent>Accrued interest ($0 for SAFEs)</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-right">
                    <Tooltip>
                      <TooltipTrigger>Total Value</TooltipTrigger>
                      <TooltipContent>Total value including accrued interest</TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center">
                    <Tooltip>
                      <TooltipTrigger>MFN</TooltipTrigger>
                      <TooltipContent>Most Favored Nation clause (Yes/No)</TooltipContent>
                    </Tooltip>
                  </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
                {sortedSecurities.map((security) => (
                  <TableRow key={security.id}>
                    <TableCell className="font-medium">{security.investor}</TableCell>
                    <TableCell>{getSecurityTypeBadge(security.securityType)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(security.principal)}</TableCell>
                    <TableCell className="text-right">
                      {security.securityType === 'SAFE' ? '0%' : formatPercentage(security.interestRate)}
                  </TableCell>
                    <TableCell className="text-center">{formatDate(security.issueDate)}</TableCell>
                    <TableCell className="text-center">
                      {security.securityType === 'SAFE' ? 'N/A' : formatDate(security.maturityDate || '')}
                  </TableCell>
                    <TableCell className="text-right">{formatCurrency(security.valuationCap)}</TableCell>
                    <TableCell className="text-right">{formatPercentage(security.discount)}</TableCell>
                    <TableCell className="text-right">
                      {security.securityType === 'SAFE' ? '$0' : formatCurrency(security.interestAccrued)}
                  </TableCell>
                    <TableCell className="text-right">{formatCurrency(security.totalValue)}</TableCell>
                    <TableCell className="text-center">
                      <Badge variant={security.mfn ? "default" : "secondary"}>
                        {security.mfn ? "Yes" : "No"}
                      </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
    </TooltipProvider>
  );
};
