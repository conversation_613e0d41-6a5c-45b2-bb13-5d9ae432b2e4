import React from "react";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CapTableGrid from "./CapTableGrid";
import { ConvertibleTable } from "./ConvertibleTable";
import { SOPSummary } from "./SOPSummary";
import ProFormaModel from "./ProFormaModel";
import VotingRights from "./VotingRights";
import CapTableFilters from "./CapTableFilters";
import { useCapTableList } from "@/hooks/cap-table/useCapTableList.hooks";
import {
  CapTableSummary,
  ProFormaInvestment,
  ConvertibleNote,
  SOPSummary as SOPSummaryType,
  VotingRights as VotingRightsType,
} from "@/types/capTable";

interface CapTableTabsProps {
  capTableData: CapTableSummary;
  proFormaInvestment?: ProFormaInvestment;
  convertibleNotes?: ConvertibleNote[];
  sopSummary?: SOPSummaryType;
  votingRights?: VotingRightsType;
  currentView: "standard" | "fullyDiluted";
}

const CapTableTabs: React.FC<CapTableTabsProps> = ({
  capTableData,
  proFormaInvestment,
  convertibleNotes,
  sopSummary,
  votingRights,
  currentView,
}) => {
  const { 
    capTableList, 
    isListLoading, 
    listError,
    capTableSummary,
    isSummaryLoading,
    summaryError 
  } = useCapTableList();

  return (
    <div className="w-full">
      {/* Global Filters - Available across all tabs */}
      <CapTableFilters
        onToggleView={() => {}} // Not used in Cap Table tabs
        onExport={() => {}} // Not used in Cap Table tabs
        currentView="standard" // Not used in Cap Table tabs
      />
      
      <Tabs defaultValue="cap-table" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="cap-table">Cap Table</TabsTrigger>
          <TabsTrigger value="voting-rights">Voting Rights</TabsTrigger>
          <TabsTrigger value="stock-option-plan">Stock Option Plan</TabsTrigger>
          <TabsTrigger value="pro-forma-model">Pro Forma Model</TabsTrigger>
          <TabsTrigger value="convertible-notes">Convertible Securities</TabsTrigger>
        </TabsList>

      <TabsContent value="cap-table" className="mt-6">
        <CapTableGrid />
      </TabsContent>

      <TabsContent value="pro-forma-model" className="mt-6">
        <ProFormaModel />
      </TabsContent>

      <TabsContent value="convertible-notes" className="mt-6">
        <ConvertibleTable />
      </TabsContent>

      <TabsContent value="stock-option-plan" className="mt-6">
        <SOPSummary />
      </TabsContent>

      <TabsContent value="voting-rights" className="mt-6">
        <VotingRights />
      </TabsContent>
      </Tabs>
    </div>
  );
};

export default CapTableTabs;
