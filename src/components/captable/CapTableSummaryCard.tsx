import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { useCapTableList } from "@/hooks/cap-table/useCapTableList.hooks";

const CapTableSummaryCard: React.FC = () => {
  const { capTableSummary, isSummaryLoading, summaryError } = useCapTableList();

  if (isSummaryLoading) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Summary</CardTitle>
          <CardDescription>Key metrics about your cap table</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-500 mb-1">Total Shareholders</div>
              <div className="text-2xl font-bold">Loading...</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-500 mb-1">Outstanding Shares</div>
              <div className="text-2xl font-bold">Loading...</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-500 mb-1">Fully Diluted Shares</div>
              <div className="text-2xl font-bold">Loading...</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-500 mb-1">Share Classes</div>
              <div className="text-2xl font-bold">Loading...</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (summaryError || !capTableSummary) {
    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Summary</CardTitle>
          <CardDescription>Key metrics about your cap table</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-600">
            {summaryError || "Unable to load cap table summary"}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Summary</CardTitle>
        <CardDescription>Key metrics about your cap table</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Total Shareholders</div>
            <div className="text-2xl font-bold">
              {capTableSummary.shareholders}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Outstanding Shares</div>
            <div className="text-2xl font-bold">
              {capTableSummary.totalShares.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">
              Fully Diluted Shares
            </div>
            <div className="text-2xl font-bold">
              {capTableSummary.fullyDiluted.toLocaleString()}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-500 mb-1">Share Classes</div>
            <div className="text-2xl font-bold">
              1
              {/* {capTableSummary.shareClasses} */}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CapTableSummaryCard;
