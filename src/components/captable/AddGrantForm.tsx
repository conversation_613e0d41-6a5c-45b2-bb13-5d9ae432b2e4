import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface AddGrantFormProps {
  onSubmit: (data: any) => void;
}

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  address: z.string().min(5, { message: "Please enter a valid address." }),
  services: z.string().min(1, { message: "Services is required." }),
  type: z.enum(["Advisor", "Employee", "Contractor"]),
  grantType: z.enum(["Option", "Restricted Stock", "Restricted Stock Outside", "None"]),
  shares: z.number().min(0, "Shares cannot be negative").optional(),
  startDate: z.date(),
  vestingSchedule: z.enum(["Standard", "Custom"]),
  vestingPeriod: z.number().min(1, "Vesting period must be at least 1 month").optional(),
  cliff: z.number().min(0, "Cliff cannot be negative").optional(),
  compensation: z.number().min(0, "Compensation cannot be negative").optional(),
  compensationPeriod: z.enum(["Hourly", "Monthly", "Annually"]),
}).refine((data) => {
  if (data.grantType !== "None" && (!data.shares || data.shares <= 0)) {
    return false;
  }
  return true;
}, {
  message: "Shares must be greater than 0 when grant type is not None",
  path: ["shares"],
}).refine((data) => {
  if (data.vestingSchedule === "Custom" && (!data.vestingPeriod || !data.cliff)) {
    return false;
  }
  return true;
}, {
  message: "Vesting period and cliff are required for custom vesting",
  path: ["vestingPeriod"],
}).refine((data) => {
  if (data.compensation === undefined || data.compensation < 0) {
    return false;
  }
  return true;
}, {
  message: "Compensation is required and cannot be negative",
  path: ["compensation"],
});

const AddGrantForm: React.FC<AddGrantFormProps> = ({ onSubmit }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      address: "",
      services: "",
      type: "Advisor",
      grantType: "Option",
      shares: undefined,
      startDate: new Date(),
      vestingSchedule: "Standard",
      vestingPeriod: undefined,
      cliff: undefined,
      compensation: undefined,
      compensationPeriod: "Annually",
    },
  });

  const watchedGrantType = form.watch("grantType");
  const watchedVestingSchedule = form.watch("vestingSchedule");

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter full name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter full address"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="services"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Services to be provided</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the services to be provided"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Advisor">Advisor</SelectItem>
                  <SelectItem value="Employee">Employee</SelectItem>
                  <SelectItem value="Contractor">Contractor</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="grantType"
          render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel>Type of Grant</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Option" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Option (Default to Non-Statutory Option Grant)
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Restricted Stock" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Restricted Stock Grant (These shares will be issued from the Stock Option Plan)
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Restricted Stock Outside" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Restricted Stock Grant (These shares will be issued from outside the Stock Option Plan)
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="None" />
                    </FormControl>
                    <FormLabel className="font-normal">None</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchedGrantType !== "None" && (
          <FormField
            control={form.control}
            name="shares"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Shares</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter number of shares"
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? undefined : parseInt(value) || 0);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Start Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchedGrantType !== "None" && (
          <FormField
            control={form.control}
            name="vestingSchedule"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Vesting Schedule</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Standard" />
                      </FormControl>
                      <FormLabel className="font-normal">Standard (2 years monthly)</FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Custom" />
                      </FormControl>
                      <FormLabel className="font-normal">Custom</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {watchedGrantType !== "None" && watchedVestingSchedule === "Custom" && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="vestingPeriod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vesting Period (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter vesting period"
                      value={field.value || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === "" ? undefined : parseInt(value) || 0);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cliff"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cliff (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter cliff period"
                      value={field.value || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === "" ? undefined : parseInt(value) || 0);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="compensation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Compensation</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter compensation amount"
                    value={field.value || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value === "" ? undefined : parseInt(value) || 0);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="compensationPeriod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Compensation Period</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Hourly">Hourly</SelectItem>
                    <SelectItem value="Monthly">Monthly</SelectItem>
                    <SelectItem value="Annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="submit">Continue</Button>
        </div>
      </form>
    </Form>
  );
};

export default AddGrantForm;
