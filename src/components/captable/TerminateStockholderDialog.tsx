import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { useCapTableEdit } from '@/hooks/cap-table/useCapTableEdit.hooks';
import { EnhancedCapTableListItem } from '@/types/capTable';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface TerminateStockholderDialogProps {
  isOpen: boolean;
  stockholder: EnhancedCapTableListItem | null;
  onClose: () => void;
  onSuccess: () => void;
}

const terminationTypes = [
  { value: 'involuntary_termination_without_cause', label: 'Involuntary Termination (Without Cause)' },
  { value: 'involuntary_termination_with_cause', label: 'Involuntary Termination (With Cause)' },
  { value: 'resignation', label: 'Resignation' },
];

const TerminateStockholderDialog: React.FC<TerminateStockholderDialogProps> = ({
  isOpen,
  stockholder,
  onClose,
  onSuccess,
}) => {
  const { terminateStockholder, isTerminating } = useCapTableEdit();
  const [terminationDate, setTerminationDate] = useState(new Date().toISOString().split('T')[0]);
  const [severanceAmount, setSeveranceAmount] = useState(0);
  const [terminationType, setTerminationType] = useState('involuntary-termination-without-cause');

  const handleTerminate = async () => {
    if (!stockholder) return;

    // Validate date
    const selectedDate = new Date(terminationDate);
    const today = new Date();
    if (selectedDate > today) {
      toast.error('Termination date cannot be in the future');
      return;
    }

    // Validate severance amount
    if (severanceAmount < 0) {
      toast.error('Severance amount cannot be negative');
      return;
    }

    const success = await terminateStockholder(stockholder, {
      terminationDate,
      severanceAmount,
      serviceProviderTerminationType: terminationType,
    });

    if (success) {
      onSuccess();
      onClose();
    }
  };

  if (!stockholder) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Terminate Stockholder</DialogTitle>
          <DialogDescription>
            You are about to terminate {stockholder.name} ({stockholder.role}). 
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">Name</Label>
            <div className="col-span-3 font-medium">{stockholder.name}</div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="role" className="text-right">Role</Label>
            <div className="col-span-3 font-medium">{stockholder.role}</div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="terminationDate" className="text-right">Termination Date</Label>
            <Input
              id="terminationDate"
              type="date"
              value={terminationDate}
              onChange={(e) => setTerminationDate(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="severanceAmount" className="text-right">Severance Amount</Label>
            <Input
              id="severanceAmount"
              type="number"
              min="0"
              step="0.01"
              value={severanceAmount === 0 ? '' : severanceAmount}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '') {
                  setSeveranceAmount(0);
                } else {
                  const numValue = parseFloat(value);
                  if (!isNaN(numValue)) {
                    setSeveranceAmount(numValue);
                  }
                }
              }}
              className="col-span-3"
              placeholder="0.00"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">Termination Type</Label>
            <RadioGroup
              value={terminationType}
              onValueChange={setTerminationType}
              className="col-span-3"
            >
              {terminationTypes.map((type) => (
                <div key={type.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={type.value} id={type.value} />
                  <Label htmlFor={type.value} className="text-sm">
                    {type.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isTerminating}>
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleTerminate}
            disabled={isTerminating}
          >
            {isTerminating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Terminating...
              </>
            ) : (
              'Terminate Stockholder'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TerminateStockholderDialog;
