import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useUpdateInvestorInvestmentAmount } from "@/hooks/cap-table/useProforma.hooks";
import { ProFormaInputs } from "@/types/capTable";

interface AddInvestorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: () => void; // Simplified callback for data refresh
  inputs: ProFormaInputs; // Pass inputs to get valuation mode
}

const AddInvestorModal: React.FC<AddInvestorModalProps> = ({
  isOpen,
  onClose,
  onAdd,
  inputs,
}) => {
  const [investorN<PERSON>, setInvestorName] = useState("");
  const [investmentAmount, setInvestmentAmount] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  
  // Reuse existing hook following SOLID principles
  const updateInvestorMutation = useUpdateInvestorInvestmentAmount();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!investorName.trim()) {
      toast.error("Investor name is required");
      return;
    }

    const amount = Number(investmentAmount);
    if (!investmentAmount.trim() || isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid cash investment amount greater than 0");
      return;
    }

    setIsLoading(true);
    
    try {
      // Reuse existing API infrastructure for new investor (no ID needed)
      const result = await updateInvestorMutation.mutateAsync({
        shareholderName: investorName.trim(),
        investmentAmount: amount,
        valuationModeType: inputs.designation === 'Fixed Pre-Money' ? 'fixed_pre_money' : 'fixed_post_money'
      });
      
      // Check if the result contains an error (API client returns { error: "..." } on error)
      if (result && typeof result === 'object' && 'error' in result) {
        // Only throw if the error is not null/undefined and not empty
        if (result.error && result.error !== null && result.error !== '') {
          throw new Error(result.error as string);
        }
      }
      
      toast.success(`Successfully added ${investorName} as new investor`);
      resetForm();
      onClose();
      onAdd(); // Refresh cap table data
    } catch (error: any) {
      toast.error(`Failed to add investor: ${error.message || 'Please try again'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setInvestorName("");
    setInvestmentAmount("");
  };

  const handleCancel = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Investor</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="investorName" className="text-sm font-medium">
              Investor Name
            </Label>
            <Input
              id="investorName"
              value={investorName}
              onChange={(e) => setInvestorName(e.target.value)}
              placeholder="Enter investor name"
              required
              disabled={isLoading}
            />
          </div>
          
          <div>
            <Label htmlFor="investmentAmount" className="text-sm font-medium">
              Cash Investment
            </Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                $
              </span>
              <Input
                id="investmentAmount"
                type="number"
                min="0"
                value={investmentAmount}
                onChange={(e) => setInvestmentAmount(e.target.value)}
                placeholder="0"
                className="pl-7"
                required
                disabled={isLoading}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Adding...
                </>
              ) : (
                'Add Investor'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddInvestorModal;
