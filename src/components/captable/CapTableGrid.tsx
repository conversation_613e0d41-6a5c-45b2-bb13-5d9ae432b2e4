import React, { useState } from "react";
import {
  EnhancedCapTableListItem,
  SOPPoolSummary,
  CapTableViewMode,
  EditCapTableItem,
} from "@/types/capTable";
import { useCapTableList } from "@/hooks/cap-table/useCapTableList.hooks";
import { useCapTableEdit } from "@/hooks/cap-table/useCapTableEdit.hooks";
import { useGlobalFilters } from "@/contexts/GlobalFilterContext";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Info, Edit2, Calendar, MoreVertical, Trash2, Plus, Loader2 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import TerminateStockholderDialog from './TerminateStockholderDialog';

// Utility function to format numbers with commas
const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

const CapTableGrid: React.FC = () => {
  // Use global filter state
  const { filters } = useGlobalFilters();
  
  const [viewMode, setViewMode] = useState<CapTableViewMode>('detailed');
  const [editStockholderModal, setEditStockholderModal] = useState<{
    isOpen: boolean;
    data: EditCapTableItem | null;
  }>({ isOpen: false, data: null });
  const [addGrantModal, setAddGrantModal] = useState<{
    isOpen: boolean;
    data: EditCapTableItem | null;
  }>({ isOpen: false, data: null });
  const [selectedStockholder, setSelectedStockholder] = useState<EnhancedCapTableListItem | null>(null);
  const [terminateDialog, setTerminateDialog] = useState<{
    isOpen: boolean;
    stockholder: EnhancedCapTableListItem | null;
  }>({ isOpen: false, stockholder: null });

  // Pass filters to the hook
  const { capTableData, isListLoading, listError, refetchList } = useCapTableList({
    proFormaRoleFilter: filters.roleFilter,
    proFormaShareClassFilter: filters.shareClassFilter,
    shareHolderName: filters.shareholderNameSearch
  });
  const { updateStockholder, isUpdating } = useCapTableEdit();



  // Early return for loading and error states
  if (isListLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle error state
  if (listError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading cap table data: {listError}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!capTableData || !capTableData.stockholders || !capTableData.stockholders.length) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-600">
            <p>No cap table data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { stockholders, sopPool, lastUpdated } = capTableData;

  // Helper function to format numbers
  const formatNumberWithCommas = (num: number): string => {
    return num.toLocaleString();
  };

  // Calculate totals for individual stockholders
  const totalCommonStock = stockholders.reduce((sum, stockholder) => sum + stockholder.commonStock, 0);
  const totalSOPShares = stockholders.reduce((sum, stockholder) => sum + stockholder.stockOptionPlan, 0);
  const totalShares = totalCommonStock + totalSOPShares;
  const totalFullyDilutedOwnership = stockholders.reduce((sum, stockholder) => sum + stockholder.fullyDilutedOwnership, 0);
  
  // Calculate totals including SOP pool
  const totalSOPSharesWithPool = totalSOPShares + sopPool.outstandingSOPShares + sopPool.promisedSOPShares;
  const totalSharesWithPool = totalShares + sopPool.outstandingSOPShares + sopPool.promisedSOPShares;
  const totalFullyDilutedOwnershipWithPool = totalFullyDilutedOwnership + sopPool.outstandingOwnership + sopPool.promisedOwnership + sopPool.availableOwnership;

  // Helper function to handle number input changes
  const handleNumberInputChange = (value: string, currentValue: number): number => {
    // Allow empty string for better UX
    if (value === '' || value === '-') {
      return 0;
    }
    // Parse as integer, return 0 if invalid
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 0 : Math.max(0, parsed); // Ensure non-negative
  };

  // Aggregated data for summary view
  const aggregatedStockholders = {
    name: "Aggregated Stockholders",
    commonStock: totalCommonStock,
    stockOptionPlan: totalSOPShares,
    totalShares: totalShares,
    fullyDilutedOwnership: 83.0, // This would be calculated based on total company shares
  };

  const handleEditStockholder = (stockholder: EnhancedCapTableListItem) => {
    setEditStockholderModal({
      isOpen: true,
      data: {
        id: stockholder.id,
        name: stockholder.name,
        commonStock: stockholder.commonStock,
        stockOptionPlan: stockholder.stockOptionPlan,
      },
    });
    // Store the selected stockholder for API call
    setSelectedStockholder(stockholder);
  };

  const handleAddGrant = () => {
    setAddGrantModal({
      isOpen: true,
      data: {
        id: '',
        name: '',
        commonStock: 0,
        stockOptionPlan: 0,
      },
    });
  };

  const handleSaveStockholder = async (data: EditCapTableItem) => {
    if (!selectedStockholder) {
      toast.error('No stockholder selected');
      return;
    }

    // Validate input data
    if (data.commonStock < 0 || data.stockOptionPlan < 0) {
      toast.error('Share values cannot be negative');
      return;
    }

    const success = await updateStockholder(selectedStockholder, {
      commonStock: data.commonStock,
      stockOptionPlan: data.stockOptionPlan,
    });
    
    if (success) {
      setEditStockholderModal({ isOpen: false, data: null });
      setSelectedStockholder(null);
      // Refetch data to show updated values
      refetchList();
    }
  };

  const handleSaveGrant = (data: EditCapTableItem) => {
    // In a real implementation, this would save to backend
    console.log('Saving new grant data:', data);
    setAddGrantModal({ isOpen: false, data: null });
  };

  const handleTerminate = (stockholder: EnhancedCapTableListItem) => {
    setTerminateDialog({ isOpen: true, stockholder });
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <CardTitle className="text-lg">Cap Table</CardTitle>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>Last Updated: {lastUpdated}</span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddGrant} variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Grant
            </Button>
            <ToggleGroup
              type="single"
              value={viewMode}
              onValueChange={(value) => value && setViewMode(value as CapTableViewMode)}
              className="bg-muted p-1 rounded-lg"
            >
              <ToggleGroupItem
                value="summary"
                className="text-xs px-3 py-1 data-[state=on]:bg-background data-[state=on]:text-foreground"
              >
                Summary
              </ToggleGroupItem>
              <ToggleGroupItem
                value="detailed"
                className="text-xs px-3 py-1 data-[state=on]:bg-background data-[state=on]:text-foreground"
              >
                Detailed
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Stockholder Name</TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Common Stock
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Common shares held by this shareholder</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Stock Option Plan
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Stock option plan shares held by this shareholder</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Total
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Total shares (Common Stock + Stock Option Plan)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Fully-Diluted Ownership %
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Percentage ownership of total company shares</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Unvested Shares
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Unvested shares for terminated individuals</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    Vested Shares
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground">
                            <Info size={14} />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Vested shares for this shareholder</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableHead>
                <TableHead className="w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Individual Stockholders (Detailed View) or Aggregated (Summary View) */}
              {viewMode === 'detailed' ? (
                stockholders.map((stockholder) => (
                  <TableRow key={stockholder.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <span>{stockholder.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {stockholder.role}
                        </Badge>
                        {stockholder.isTerminated && (
                          <Badge variant="destructive" className="text-xs">
                            Terminated
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {stockholder.commonStock > 0 ? formatNumber(stockholder.commonStock) : "-"}
                    </TableCell>
                    <TableCell className="text-right">
                      {stockholder.stockOptionPlan > 0 ? formatNumber(stockholder.stockOptionPlan) : "-"}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatNumber(stockholder.totalShares)}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {stockholder.fullyDilutedOwnership.toFixed(2)}%
                    </TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {stockholder.unvestedShares}
                    </TableCell>
                    <TableCell className="text-right text-muted-foreground">
                      {stockholder.vestedShares}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditStockholder(stockholder)}>
                            <Edit2 className="mr-2 h-4 w-4" />
                            Edit Grant
                          </DropdownMenuItem>
                          
                                                     <DropdownMenuItem 
                             onClick={() => handleTerminate(stockholder)}
                             className="text-red-600"
                           >
                             <Trash2 className="mr-2 h-4 w-4" />
                             Terminate
                           </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell className="font-medium">
                    <span className="text-muted-foreground">Aggregated Stockholders</span>
                  </TableCell>
                  <TableCell className="text-right">
                    {aggregatedStockholders.commonStock > 0 ? formatNumber(aggregatedStockholders.commonStock) : "-"}
                  </TableCell>
                  <TableCell className="text-right">
                    {aggregatedStockholders.stockOptionPlan > 0 ? formatNumber(aggregatedStockholders.stockOptionPlan) : "-"}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatNumber(aggregatedStockholders.totalShares)}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {aggregatedStockholders.fullyDilutedOwnership.toFixed(2)}%
                  </TableCell>
                  <TableCell className="text-right text-muted-foreground">
                    -
                  </TableCell>
                  <TableCell className="text-right text-muted-foreground">
                    -
                  </TableCell>
                  <TableCell></TableCell>
                </TableRow>
              )}

              {/* SOP Pool Summary Rows */}
              <TableRow className="bg-muted/30">
                <TableCell colSpan={8} className="font-medium text-muted-foreground">
                  Pool Summary
                </TableCell>
              </TableRow>
              
              <TableRow className="bg-muted/20">
                <TableCell className="font-medium pl-8">Outstanding Stock Option Plan Shares</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right">
                  {formatNumber(sopPool.outstandingSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatNumber(sopPool.outstandingSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {sopPool.outstandingOwnership.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell></TableCell>
              </TableRow>

              <TableRow className="bg-muted/20">
                <TableCell className="font-medium pl-8">Promised Stock Option Plan Shares</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right">
                  {formatNumber(sopPool.promisedSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatNumber(sopPool.promisedSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {sopPool.promisedOwnership.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell></TableCell>
              </TableRow>

              <TableRow className="bg-muted/20">
                <TableCell className="font-medium pl-8">Stock Option Plan Shares Available</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right">
                  {formatNumber(sopPool.availableSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatNumber(sopPool.availableSOPShares)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {sopPool.availableOwnership.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell></TableCell>
              </TableRow>

              {/* Totals row */}
              <TableRow className="bg-muted/50 font-semibold">
                <TableCell>Total</TableCell>
                <TableCell className="text-right">
                  {totalCommonStock > 0 ? formatNumber(totalCommonStock) : "-"}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(totalSOPSharesWithPool)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(totalSharesWithPool)}
                </TableCell>
                <TableCell className="text-right">
                  {totalFullyDilutedOwnershipWithPool.toFixed(2)}%
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell className="text-right text-muted-foreground">
                  -
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Stockholder Edit Modal */}
        <Dialog open={editStockholderModal.isOpen} onOpenChange={(open) => !open && setEditStockholderModal({ isOpen: false, data: null })}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Stockholder</DialogTitle>
            </DialogHeader>
            {editStockholderModal.data && (
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={editStockholderModal.data.name}
                    disabled
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="commonStock" className="text-right">
                    Common Stock
                  </Label>
                  <Input
                    id="commonStock"
                    type="number"
                    min="0"
                    step="1"
                    value={editStockholderModal.data.commonStock || ''}
                    onChange={(e) => setEditStockholderModal({
                      ...editStockholderModal,
                      data: { 
                        ...editStockholderModal.data!, 
                        commonStock: handleNumberInputChange(e.target.value, editStockholderModal.data!.commonStock)
                      }
                    })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="stockOptionPlan" className="text-right">
                    SOP Shares
                  </Label>
                  <Input
                    id="stockOptionPlan"
                    type="number"
                    min="0"
                    step="1"
                    value={editStockholderModal.data.stockOptionPlan || ''}
                    onChange={(e) => setEditStockholderModal({
                      ...editStockholderModal,
                      data: { 
                        ...editStockholderModal.data!, 
                        stockOptionPlan: handleNumberInputChange(e.target.value, editStockholderModal.data!.stockOptionPlan)
                      }
                    })}
                    className="col-span-3"
                  />
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setEditStockholderModal({ isOpen: false, data: null })}
                    disabled={isUpdating}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={() => handleSaveStockholder(editStockholderModal.data!)} 
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Add Grant Modal */}
        <Dialog open={addGrantModal.isOpen} onOpenChange={(open) => !open && setAddGrantModal({ isOpen: false, data: null })}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add Grant</DialogTitle>
            </DialogHeader>
            {addGrantModal.data && (
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    value={addGrantModal.data.name}
                    onChange={(e) => setAddGrantModal({
                      ...addGrantModal,
                      data: { ...addGrantModal.data!, name: e.target.value }
                    })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="commonStock" className="text-right">
                    Common Stock
                  </Label>
                  <Input
                    id="commonStock"
                    type="number"
                    min="0"
                    step="1"
                    value={addGrantModal.data.commonStock || ''}
                    onChange={(e) => setAddGrantModal({
                      ...addGrantModal,
                      data: { 
                        ...addGrantModal.data!, 
                        commonStock: handleNumberInputChange(e.target.value, addGrantModal.data!.commonStock)
                      }
                    })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="stockOptionPlan" className="text-right">
                    SOP Shares
                  </Label>
                  <Input
                    id="stockOptionPlan"
                    type="number"
                    min="0"
                    step="1"
                    value={addGrantModal.data.stockOptionPlan || ''}
                    onChange={(e) => setAddGrantModal({
                      ...addGrantModal,
                      data: { 
                        ...addGrantModal.data!, 
                        stockOptionPlan: handleNumberInputChange(e.target.value, addGrantModal.data!.stockOptionPlan)
                      }
                    })}
                    className="col-span-3"
                  />
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setAddGrantModal({ isOpen: false, data: null })}
                  >
                    Cancel
                  </Button>
                  <Button onClick={() => handleSaveGrant(addGrantModal.data!)}>
                    Add Grant
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Termination Dialog */}
        <TerminateStockholderDialog
          isOpen={terminateDialog.isOpen}
          stockholder={terminateDialog.stockholder}
          onClose={() => setTerminateDialog({ isOpen: false, stockholder: null })}
          onSuccess={() => {
            refetchList(); // Refetch data after termination
          }}
        />
      </CardContent>
    </Card>
  );
};

export default CapTableGrid;
