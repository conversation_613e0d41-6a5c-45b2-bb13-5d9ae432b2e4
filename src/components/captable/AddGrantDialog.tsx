import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useAddStockOptionGrant } from "@/hooks/cap-table/useStockOptionPlan.hooks";
import { AddGrantRequest } from "@/types/capTable";
import AddGrantForm from "./AddGrantForm";

interface AddGrantDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddGrantDialog: React.FC<AddGrantDialogProps> = ({ isOpen, onClose }) => {
  const { mutate: addGrant, isPending } = useAddStockOptionGrant();
  const [step, setStep] = useState<"form" | "confirmation">("form");
  const [formData, setFormData] = useState<any>(null);

  const handleSubmit = async (data: any) => {
    setFormData(data);
    setStep("confirmation");
  };

  const handleFinish = () => {
    if (!formData) return;
    
    // Transform form data to match API expectations
    const transformedData: AddGrantRequest = {
      name: formData.name,
      email: formData.email,
      address: formData.address,
      services: formData.services,
      grantType: mapGrantTypeToApi(formData.grantType),
      shares: formData.shares || 0,
      startDate: formData.startDate.toISOString().split('T')[0],
      vestingSchedule: mapVestingScheduleToApi(formData.vestingSchedule),
      compensation: formData.compensation || 0,
      compensationPeriod: formData.compensationPeriod.toLowerCase(),
      vestingPeriod: formData.vestingPeriod || 0,
      cliff: formData.cliff || 0,
      type: mapTypeToApi(formData.type),
    };

    addGrant(transformedData, {
      onSuccess: () => {
        resetAndClose();
      },
      onError: () => {
        // Error is handled in the hook (toast)
      },
    });
  };

  const resetAndClose = () => {
    setStep("form");
    setFormData(null);
    onClose();
  };

  // Mapping functions
  const mapGrantTypeToApi = (displayType: string): string => {
    const grantTypeMap: Record<string, string> = {
      'Option': 'option',
      'Restricted Stock': 'restricted-stock-grant-inside-of-stock-option-plan',
      'Restricted Stock Outside': 'restricted-stock-grant-outside-of-stock-option-plan',
      'None': 'none',
    };
    return grantTypeMap[displayType] || displayType;
  };

  const mapVestingScheduleToApi = (displaySchedule: string): string => {
    return displaySchedule === "Standard" ? "standard-two-years-monthly-vesting" : "custom";
  };

  const mapTypeToApi = (displayType: string): string => {
    const typeMap: Record<string, string> = {
      'Advisor': 'advisor',
      'Employee': 'employee',
      'Contractor': 'independent-contractor-consultant',
    };
    return typeMap[displayType] || displayType;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === "form" ? "Add New Grant" : "Confirm Grant Details"}
          </DialogTitle>
          <DialogDescription>
            {step === "form"
              ? "Create a new stock option grant for a team member."
              : "Review the information before finalizing."}
          </DialogDescription>
        </DialogHeader>

        {step === "form" ? (
          <AddGrantForm
            onSubmit={handleSubmit}
          />
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium">Name</h3>
                <p className="text-sm">{formData.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Email</h3>
                <p className="text-sm">{formData.email}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Address</h3>
                <p className="text-sm">{formData.address}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Services</h3>
                <p className="text-sm">{formData.services}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Type</h3>
                <p className="text-sm">{formData.type}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Grant Type</h3>
                <p className="text-sm">{formData.grantType}</p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Shares</h3>
                  <p className="text-sm">{formData.shares || 0}</p>
                </div>
              )}
              <div>
                <h3 className="text-sm font-medium">Start Date</h3>
                <p className="text-sm">
                  {formData.startDate.toLocaleDateString()}
                </p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Vesting Schedule</h3>
                  <p className="text-sm">
                    {formData.vestingSchedule === "Standard"
                      ? "2 years monthly"
                      : `Custom (${formData.vestingPeriod || 0} months, ${formData.cliff || 0} months cliff)`}
                  </p>
                </div>
              )}
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Compensation</h3>
                <p className="text-sm">
                  ${formData.compensation || 0}{" "}
                  {formData.compensationPeriod.toLowerCase()}
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setStep("form")}>
                Back
              </Button>
              <Button onClick={handleFinish} disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Grant"
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddGrantDialog;
