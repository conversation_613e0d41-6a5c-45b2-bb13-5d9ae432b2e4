import React from "react";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import CapTableMetadata from "./CapTableMetadata";
import CapTableInfoAlert from "./CapTableInfoAlert";
import CapTableFilters from "./CapTableFilters";
import CapTableChart from "./CapTableChart";
import CapTableSummaryCard from "./CapTableSummaryCard";
import CapTableTabs from "./CapTableTabs";
import {
  CapTableSummary,
  ShareholderRole,
  ShareClass,
  Shareholder,
  ProFormaInvestment,
  ConvertibleNote,
  SOPSummary,
  ProFormaVoting,
} from "@/types/capTable";

interface CapTableContentProps {
  currentCapTable: CapTableSummary;
  proFormaInvestment?: ProFormaInvestment;
  convertibleNotes?: ConvertibleNote[];
  sopSummary?: SOPSummary;
  proFormaVoting?: ProFormaVoting;
  view: "standard" | "fullyDiluted";
  onSearch: (term: string) => void;
  onFilterRole: (role: ShareholderRole | "All") => void;
  onFilterShareClass: (shareClass: ShareClass | "All") => void;
  onToggleView: (view: "standard" | "fullyDiluted") => void;
  onExport: (format: "pdf" | "csv") => void;
  filteredShareholders: Shareholder[];
}

const CapTableContent: React.FC<CapTableContentProps> = ({
  currentCapTable,
  proFormaInvestment,
  convertibleNotes,
  sopSummary,
  proFormaVoting,
  view,
  onSearch,
  onFilterRole,
  onFilterShareClass,
  onToggleView,
  onExport,
  filteredShareholders,
}) => {
  return (
    <AnimatedTransition delay={0.1}>
      <CapTableMetadata
        lastUpdated={currentCapTable.lastUpdated}
        companyName={currentCapTable.companyName}
      />

      <CapTableInfoAlert />

      <CapTableFilters
        onToggleView={onToggleView}
        onExport={onExport}
        currentView={view}
      />

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="lg:col-span-5">
          <CapTableChart currentView={view} />
        </div>
        <div className="lg:col-span-7">
          <CapTableSummaryCard />
        </div>
      </div>

      <CapTableTabs
        capTableData={{
          ...currentCapTable,
          shareholders: filteredShareholders,
        }}
        proFormaInvestment={proFormaInvestment}
        convertibleNotes={convertibleNotes}
        sopSummary={sopSummary}
        currentView={view}
      />
    </AnimatedTransition>
  );
};

export default CapTableContent;
