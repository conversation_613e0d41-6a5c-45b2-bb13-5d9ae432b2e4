import React from "react";
import { useMfnSummary } from "@/hooks/mfn/useMfnSummary.hooks";
import MfnSummaryTable from "./mfn/MfnSummaryTable";
import MfnTriggeringNoteTable from "./mfn/MfnTriggeringNoteTable";
import MfnNotifyHolderButton from "./mfn/MfnNotifyHolderButton";
import { Card, CardContent } from "@/components/common/Card";

const MfnTab: React.FC = () => {
  const { data, loading, error, refetch } = useMfnSummary();

  if (loading) {
    return (
      <div className="mt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-sm text-gray-500">Loading MFN data...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }



  if (data === null) {
    return (
      <div className="mt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">
                {!data ? "Failed to load MFN data" : "No MFN data available"}
              </p>
              <p className="text-sm text-gray-500 mb-4">
                {!data 
                  ? "No response received from server" 
                  : "This company has no MFN (Most Favored Nation) data available. MFN data will appear here once convertible securities or SAFE agreements are added to the cap table."
                }
              </p>
              <button
                onClick={refetch}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { currentIssues, newTerm } = data.data || {};
  

  if (!newTerm) {
    return (
      <div className="mt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">No MFN triggered</p>
              <p className="text-sm text-gray-500 mb-4">
                The server response is missing required MFN term information
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mt-6 space-y-6">
      {/* MFN Summary Table */}
      <MfnSummaryTable currentIssues={currentIssues || []} />
      
      {/* Triggering Note Table with Notify Button */}
      <Card>
        <CardContent className="pt-6">
          <MfnTriggeringNoteTable newTerm={newTerm} />
          {/* Only show notify button if there are current issues */}
          {(currentIssues && currentIssues.length > 0) && (
            <MfnNotifyHolderButton onSuccess={refetch} />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MfnTab;
