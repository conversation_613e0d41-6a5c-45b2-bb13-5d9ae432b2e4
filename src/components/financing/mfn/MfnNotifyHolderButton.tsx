import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bell, Loader2 } from "lucide-react";
import { useMfnNotifyHolders } from "@/hooks/mfn/useMfnNotifyHolders.hooks";
import { toast } from "sonner";

interface MfnNotifyHolderButtonProps {
  onSuccess?: () => void;
}

const MfnNotifyHolderButton: React.FC<MfnNotifyHolderButtonProps> = ({ onSuccess }) => {
  const { notify, loading, error } = useMfnNotifyHolders();

  const handleNotify = async () => {
    try {
      const response = await notify();
      if (response) {
        toast.success("Notifications sent to all affected holders successfully.");
        
        // Call the success callback to refetch data
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      toast.error("Failed to send notifications. Please try again.");
    }
  };

  return (
    <div className="flex justify-end mt-6">
      <Button
        onClick={handleNotify}
        disabled={loading}
        className="shadow-md"
        size="lg"
      >
        {loading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Bell className="mr-2 h-4 w-4" />
        )}
        Notify Holders
      </Button>
      {error && (
        <div className="mt-2 text-sm text-red-500 max-w-xs">
          {error}
        </div>
      )}
    </div>
  );
};

export default MfnNotifyHolderButton;
