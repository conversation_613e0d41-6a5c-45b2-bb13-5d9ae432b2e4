import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { MfnNewTerm } from "@/types/mfn";

interface MfnTriggeringNoteTableProps {
  newTerm: MfnNewTerm;
}

const MfnTriggeringNoteTable: React.FC<MfnTriggeringNoteTableProps> = ({ newTerm }) => {
  const getRoundTypeDisplay = (roundType: string) => {
    return roundType === "authorized-round" ? "Convertible" : "SAFE";
  };

  return (
    <div>
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Triggering {newTerm.roundType === "authorized-round" ? "Note" : "SAFE"}</h3>
        <p className="text-sm text-muted-foreground">
          The new terms that triggered the MFN provisions for existing investors.
        </p>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="font-semibold">Round Type</TableHead>
              <TableHead className="font-semibold">Interest Rate</TableHead>
              <TableHead className="font-semibold">Valuation Cap</TableHead>
              <TableHead className="font-semibold">Discount Rate</TableHead>
              <TableHead className="font-semibold">Approved Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow className="bg-blue-50">
              <TableCell className="font-medium">
                {getRoundTypeDisplay(newTerm.roundType)}
              </TableCell>
              <TableCell>
                {newTerm.interestRate !== null ? `${newTerm.interestRate}%` : "N/A"}
              </TableCell>
              <TableCell>
                ${newTerm.valuationCap.toLocaleString()}
              </TableCell>
              <TableCell>
                {newTerm.discountRate}%
              </TableCell>
              <TableCell>
                {newTerm.approvedDate
                  ? format(new Date(newTerm.approvedDate), "MMM dd, yyyy")
                  : "N/A"}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default MfnTriggeringNoteTable;
