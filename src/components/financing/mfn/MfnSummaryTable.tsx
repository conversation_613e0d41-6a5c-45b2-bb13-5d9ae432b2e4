import React from "react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { MfnCurrentIssue } from "@/types/mfn";
import { CheckCircle } from "lucide-react";

interface MfnSummaryTableProps {
  currentIssues: MfnCurrentIssue[];
}

const MfnSummaryTable: React.FC<MfnSummaryTableProps> = ({ currentIssues }) => {
  // Check if there are no current issues (all resolved)
  if (!currentIssues || currentIssues.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">MFN Summary</h3>
            <p className="text-sm text-muted-foreground">
              All MFN-triggered investments have been resolved.
            </p>
          </div>
          
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                No Active MFN Issues
              </h4>
              <p className="text-sm text-gray-500 max-w-md">
                All Most Favored Nation provisions have been addressed. 
                No further action is required at this time.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Group issues by investor ID to show current and updated terms together
  const groupedIssues = currentIssues.reduce((acc, issue) => {
    if (!acc[issue.id]) {
      acc[issue.id] = [];
    }
    acc[issue.id].push(issue);
    return acc;
  }, {} as Record<string, MfnCurrentIssue[]>);

  const getRoundTypeDisplay = (roundType: string) => {
    return roundType === "authorized-round" ? "Convertible" : "SAFE";
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">MFN Summary</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Investments that have been triggered by MFN provisions. Each investor shows both current and updated terms.
          </p>
          
          {/* Color Legend */}
          <div className="flex items-center gap-6 mb-4 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-100 border-2 border-orange-300 rounded"></div>
              <span className="text-sm font-medium text-gray-700">Current Terms</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-100 border-2 border-green-300 rounded"></div>
              <span className="text-sm font-medium text-gray-700">Updated Terms</span>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold">Round Type</TableHead>
                <TableHead className="font-semibold">Investor Name</TableHead>
                <TableHead className="font-semibold">Principal Amount</TableHead>
                <TableHead className="font-semibold">Date of Investment</TableHead>
                <TableHead className="font-semibold">Interest Rate</TableHead>
                <TableHead className="font-semibold">Valuation Cap</TableHead>
                <TableHead className="font-semibold">Discount Rate</TableHead>
                <TableHead className="font-semibold">Email</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Object.entries(groupedIssues).map(([investorId, issues], groupIndex) => {
                // Sort issues so current terms come first, then updated terms
                const sortedIssues = issues.sort((a, b) => {
                  if (a.isTriggerUpdateData === b.isTriggerUpdateData) return 0;
                  return a.isTriggerUpdateData ? 1 : -1;
                });

                return (
                  <React.Fragment key={investorId}>
                    {sortedIssues.map((issue, index) => (
                      <TableRow
                        key={`${investorId}-${index}`}
                        className={`
                          ${issue.isTriggerUpdateData 
                            ? 'bg-green-50 border-l-4 border-l-green-400 hover:bg-green-100 transition-colors' 
                            : 'bg-orange-50 border-l-4 border-l-orange-400 hover:bg-orange-100 transition-colors'
                          }
                          ${index === 0 ? 'border-t-2 border-t-gray-300' : ''}
                          ${index === sortedIssues.length - 1 ? 'border-b-2 border-b-gray-300' : ''}
                          ${groupIndex > 0 && index === 0 ? 'mt-4' : ''}
                        `}
                      >
                        <TableCell className="font-medium">
                          <span className="font-semibold text-gray-900">
                            {getRoundTypeDisplay(issue.roundType)}
                          </span>
                        </TableCell>
                        <TableCell className="font-semibold text-gray-900">
                          {issue.investorName}
                        </TableCell>
                        <TableCell className="font-medium">
                          ${issue.principalAmount.toLocaleString()}
                        </TableCell>
                        <TableCell>
                          {issue.dateOfInvestment
                            ? format(new Date(issue.dateOfInvestment), "MMM dd, yyyy")
                            : "N/A"}
                        </TableCell>
                        <TableCell className="font-medium">
                          {issue.interestRate !== null ? `${issue.interestRate}%` : "N/A"}
                        </TableCell>
                        <TableCell className="font-medium">
                          ${issue.valuationCap.toLocaleString()}
                        </TableCell>
                        <TableCell className="font-medium">
                          {issue.discountRate}%
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {issue.email}
                        </TableCell>
                      </TableRow>
                    ))}
                    {/* Add spacing between investor groups */}
                    {groupIndex < Object.keys(groupedIssues).length - 1 && (
                      <TableRow>
                        <TableCell colSpan={8} className="h-4 bg-white"></TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default MfnSummaryTable;
