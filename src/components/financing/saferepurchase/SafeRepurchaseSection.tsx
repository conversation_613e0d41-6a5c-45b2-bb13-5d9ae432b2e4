import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/common/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { toast } from "sonner";
import { SafeRepurchase } from "@/types/financing";
import { useSafeRepurchaseManagement } from "@/hooks/financing/useSafeRepurchaseManagement.hooks";
import { useAuth } from "@/contexts/AuthContext";
import { Spinner } from "@/components/ui/spinner";
import { isSafeSelectable, isSafeDisabled } from "@/types/financing";

// Mock data for demonstration (will be replaced by API)
const mockSafes: SafeRepurchase[] = [
  {
    id: "1",
    authorizedRoundId: "mock-round-1",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: "no",
    investorName: "Angel Investor A",
    dateIssued: new Date("2023-02-15"),
    purchaseAmount: 250000,
    valuationCap: 8000000,
    discount: 20,
    mfn: true,
  },
  {
    id: "2",
    authorizedRoundId: "mock-round-2",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: "yes",
    investorName: "VC Firm B",
    dateIssued: new Date("2023-03-10"),
    purchaseAmount: 500000,
    valuationCap: 10000000,
    discount: 15,
    mfn: false,
  },
  {
    id: "3",
    authorizedRoundId: "mock-round-3",
    selected: false,
    repurchaseAmount: 0,
    boardApproved: "pending",
    investorName: "Strategic Partner C",
    dateIssued: new Date("2023-04-05"),
    purchaseAmount: 350000,
    valuationCap: 9000000,
    discount: 18,
    mfn: true,
  },
];

const SafeRepurchaseSection: React.FC = () => {
  const { user } = useAuth();
  const companyId = user?.companyId;

  // Use the main management hook that contains all business logic
  const {
    safes,
    isLoading,
    error,
    refetch,
    selectedSafes,
    repurchaseAmounts,
    anySelected,
    handleCheckboxChange,
    handleRepurchaseAmountChange,
    handleRepurchaseClick,
    handleConfirmRepurchase,
    clearSelections,
    isSubmitting,
    processingSafes,
    isProcessing,
  } = useSafeRepurchaseManagement(companyId);

  // Component only manages UI state (dialogs)
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [boardConsentDialogOpen, setBoardConsentDialogOpen] = useState(false);

  const handleRepurchaseClickWrapper = () => {
    const validationPassed = handleRepurchaseClick();
    if (validationPassed) {
      setConfirmDialogOpen(true);
    }
  };

  const handleConfirmRepurchaseWrapper = async () => {
    const success = await handleConfirmRepurchase();
    if (success) {
      setConfirmDialogOpen(false);
      // Additional UI logic if needed
    }
  };

  const handleBoardConsentSign = () => {
    toast.success(
      "Board consent has been submitted for approval. You will be notified when all directors have signed."
    );

    // In a real app, this would trigger emails to directors

    setBoardConsentDialogOpen(false);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">
            SAFE Repurchase and Cancel
          </h3>

          {/* Loading and Error States */}
          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <Spinner className="mr-2" />
              <span>Loading SAFEs...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <p className="text-red-800 text-sm">
                Error loading SAFEs: {error.message}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          )}

          {/* Beautiful Progress Interface for SAFE Processing */}
          {isProcessing && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-blue-900">
                  Processing SAFE Repurchases
                </h4>
                <div className="flex items-center space-x-2">
                  <Spinner className="h-5 w-5 text-blue-600" />
                  <span className="text-sm text-blue-700 font-medium">
                    Processing...
                  </span>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-blue-200 rounded-full h-3 mb-4">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${(Object.values(processingSafes).filter((status) => status !== "pending").length / Object.keys(processingSafes).length) * 100}%`,
                  }}
                />
              </div>

              {/* Individual SAFE Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {Object.entries(processingSafes).map(([safeId, status]) => {
                  const safe = safes.find((s) => s.id === safeId);
                  if (!safe) return null;

                  return (
                    <div
                      key={safeId}
                      className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-300 ${
                        status === "pending"
                          ? "bg-white border-blue-200"
                          : status === "success"
                            ? "bg-green-50 border-green-200"
                            : "bg-red-50 border-red-200"
                      }`}
                    >
                      {/* Status Icon */}
                      <div className="flex-shrink-0">
                        {status === "pending" && (
                          <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                        )}
                        {status === "success" && (
                          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                        {status === "error" && (
                          <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* SAFE Details */}
                      <div className="flex-1 min-w-0">
                        <p
                          className={`text-sm font-medium truncate ${
                            status === "pending"
                              ? "text-blue-900"
                              : status === "success"
                                ? "text-green-900"
                                : "text-red-900"
                          }`}
                        >
                          {safe.investorName}
                        </p>
                        <p
                          className={`text-xs ${
                            status === "pending"
                              ? "text-blue-600"
                              : status === "success"
                                ? "text-green-600"
                                : "text-red-600"
                          }`}
                        >
                          {status === "pending" && "Processing..."}
                          {status === "success" && "Completed successfully"}
                          {status === "error" && "Failed to process"}
                        </p>
                      </div>

                      {/* Amount */}
                      <div className="text-right">
                        <p
                          className={`text-sm font-semibold ${
                            status === "pending"
                              ? "text-blue-900"
                              : status === "success"
                                ? "text-green-900"
                                : "text-red-900"
                          }`}
                        >
                          ${(repurchaseAmounts[safeId] || 0).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Progress Summary */}
              <div className="mt-4 pt-4 border-t border-blue-200">
                <div className="flex justify-between text-sm text-blue-700">
                  <span>
                    {
                      Object.values(processingSafes).filter(
                        (status) => status === "success"
                      ).length
                    }{" "}
                    completed
                  </span>
                  <span>
                    {
                      Object.values(processingSafes).filter(
                        (status) => status === "error"
                      ).length
                    }{" "}
                    failed
                  </span>
                  <span>
                    {
                      Object.values(processingSafes).filter(
                        (status) => status === "pending"
                      ).length
                    }{" "}
                    remaining
                  </span>
                </div>
              </div>
            </div>
          )}

          {anySelected && (
            <div className="mb-4">
              <Button
                onClick={handleRepurchaseClickWrapper}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Processing...
                  </>
                ) : (
                  `Repurchase SAFE${selectedSafes.size > 1 ? `s (${selectedSafes.size})` : ""}`
                )}
              </Button>
            </div>
          )}
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Select</TableHead>
              <TableHead>Repurchase Amount</TableHead>
              <TableHead>Board Approved</TableHead>
              <TableHead>Investor Name</TableHead>
              <TableHead>Date Issued</TableHead>
              <TableHead>Purchase Amount</TableHead>
              <TableHead>Valuation Cap</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>MFN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {safes.map((safe) => (
              <TableRow
                key={safe.id}
                className={isSafeDisabled(safe) ? "opacity-50 bg-gray-50" : ""}
              >
                <TableCell className="text-center">
                  <Checkbox
                    checked={selectedSafes.has(safe.id)}
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(safe.id, checked === true)
                    }
                    disabled={!isSafeSelectable(safe)}
                  />
                </TableCell>
                <TableCell>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      $
                    </span>
                    <Input
                      value={
                        repurchaseAmounts[safe.id]
                          ? repurchaseAmounts[safe.id].toString()
                          : ""
                      }
                      onChange={(e) =>
                        handleRepurchaseAmountChange(
                          safe.id,
                          parseFloat(e.target.value) || 0
                        )
                      }
                      type="text"
                      placeholder="0.00"
                      className="pl-8"
                      disabled={
                        !selectedSafes.has(safe.id) || isSafeDisabled(safe)
                      }
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      safe.boardApproved === "yes"
                        ? "bg-green-100 text-green-800"
                        : safe.boardApproved === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                    }`}
                  >
                    {safe.boardApproved.charAt(0).toUpperCase() +
                      safe.boardApproved.slice(1)}
                  </span>
                </TableCell>
                <TableCell>{safe.investorName}</TableCell>
                <TableCell>
                  {safe.dateIssued ? format(safe.dateIssued, "PP") : "N/A"}
                </TableCell>
                <TableCell>${safe.purchaseAmount.toLocaleString()}</TableCell>
                <TableCell>${safe.valuationCap.toLocaleString()}</TableCell>
                <TableCell>{safe.discount}%</TableCell>
                <TableCell>{safe.mfn ? "Yes" : "No"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm SAFE Repurchase</DialogTitle>
              <DialogDescription>
                Are you sure you want to repurchase the selected SAFE(s)? This
                action requires board approval.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Investor</TableHead>
                    <TableHead>Board Approved</TableHead>
                    <TableHead>Purchase Amount</TableHead>
                    <TableHead>Repurchase Amount</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {safes
                    .filter((s) => selectedSafes.has(s.id))
                    .map((safe) => (
                      <TableRow key={safe.id}>
                        <TableCell>{safe.investorName}</TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              safe.boardApproved === "yes"
                                ? "bg-green-100 text-green-800"
                                : safe.boardApproved === "pending"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                            }`}
                          >
                            {safe.boardApproved.charAt(0).toUpperCase() +
                              safe.boardApproved.slice(1)}
                          </span>
                        </TableCell>
                        <TableCell>
                          ${safe.purchaseAmount.toLocaleString()}
                        </TableCell>
                        <TableCell>
                          ${(repurchaseAmounts[safe.id] || 0).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setConfirmDialogOpen(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmRepurchaseWrapper}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Processing...
                  </>
                ) : (
                  "Confirm Repurchase"
                )}
              </Button>
            </DialogFooter>

            {/* Show processing status in dialog if processing */}
            {isProcessing && (
              <div className="mt-4 pt-4 border-t">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <Spinner className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-600 font-medium">
                      Processing repurchases...
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    Please wait while we process your repurchase requests. You
                    can close this dialog and monitor progress above.
                  </p>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        <Dialog
          open={boardConsentDialogOpen}
          onOpenChange={setBoardConsentDialogOpen}
        >
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Board Consent for SAFE Repurchase</DialogTitle>
              <DialogDescription>
                Review and sign the board consent for SAFE repurchase
              </DialogDescription>
            </DialogHeader>

            <div className="h-96 border rounded-md p-4 overflow-y-auto bg-gray-50 my-4">
              <h3 className="text-lg font-semibold mb-2">Board Consent</h3>
              <p className="text-sm mb-4">
                This is where the board consent document would appear. In a real
                application, this would be a document that can be reviewed.
              </p>

              <h4 className="text-md font-medium mt-6 mb-2">
                Form of Repurchase and Cancellation Agreement
              </h4>
              <p className="text-sm">
                This is where the form of repurchase and cancellation agreement
                would appear.
              </p>
            </div>

            <div className="border p-4 rounded-md">
              <h4 className="font-medium mb-2">Signature</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Please sign this document to submit for board approval
              </p>
              <div className="border h-20 rounded-md mb-4 bg-gray-50">
                {/* Signature pad would go here */}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setBoardConsentDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleBoardConsentSign}>Sign and Submit</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default SafeRepurchaseSection;
