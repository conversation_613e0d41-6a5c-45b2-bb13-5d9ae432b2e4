import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Upload, X } from "lucide-react";
import { format } from "date-fns";
import { SafeRoundResponse } from "@/types/financing";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface IssuedSafeWithUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  safes: SafeRoundResponse[];
  companyId: string;
}

const IssuedSafeWithUploadDialog: React.FC<IssuedSafeWithUploadDialogProps> = ({
  open,
  onOpenChange,
  safes,
  companyId,
}) => {
  // Filter to show only approved safes (isActive: true)
  const approvedSafes = safes.filter(safe => safe.isActive === true);
  
  const [selectedSafeId, setSelectedSafeId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    principalAmount: 0,
    name: "",
    email: "",
    dateOfInvestment: "",
  });
  const [emailError, setEmailError] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleSafeSelection = (safeId: string) => {
    setSelectedSafeId(safeId);
    // Reset form when selecting a different safe
    setFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
    });
    setEmailError("");
  };

  // Set default selection to first approved safe if none selected
  React.useEffect(() => {
    if (approvedSafes.length > 0 && !selectedSafeId) {
      setSelectedSafeId(approvedSafes[0].id);
    }
  }, [approvedSafes, selectedSafeId]);

  const selectedSafe = approvedSafes.find(s => s.id === selectedSafeId);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear email error when user starts typing
    if (field === 'email') {
      setEmailError("");
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        dateOfInvestment: format(date, "yyyy-MM-dd"),
      }));
    }
  };

  const handleSubmit = async () => {
    if (!selectedSafeId) {
      toast.error("Please select a safe first");
      return;
    }

    if (!formData.principalAmount || !formData.name || !formData.email || !formData.dateOfInvestment) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!validateEmail(formData.email)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    try {
      // TODO: Implement actual API call for issuing SAFE
      toast.success("SAFE issued successfully", {
        description: `SAFE for ${formData.name} has been created.`
      });
      
      // Close dialog and reset form
      onOpenChange(false);
      setSelectedSafeId(null);
      setFormData({
        principalAmount: 0,
        name: "",
        email: "",
        dateOfInvestment: "",
      });
      setEmailError("");
      setSelectedFile(null);
    } catch (error) {
      toast.error("Failed to issue SAFE", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[900px] w-[95vw] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Issue Individual SAFE with Document Upload</DialogTitle>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[calc(90vh-180px)] pr-2 space-y-6">
          {/* Select Safe Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Select Approved Safe</h3>
            {approvedSafes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">No approved safes available</p>
                <p className="text-sm mt-2">Please authorize a safe first before issuing individual SAFEs</p>
              </div>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <div className="max-h-[300px] overflow-y-auto">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background z-10">
                        <TableRow>
                          <TableHead className="w-16 px-4 py-3">Select</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Date Authorized</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Authorized Amount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Valuation Cap</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Discount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">MFN</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {approvedSafes.map((safe) => (
                          <TableRow 
                            key={safe.id} 
                            className={cn(
                              "hover:bg-muted/50 cursor-pointer",
                              selectedSafeId === safe.id && "bg-blue-50 border-blue-200"
                            )}
                            onClick={() => handleSafeSelection(safe.id)}
                          >
                            <TableCell className="px-4 py-3">
                              <RadioGroup
                                value={selectedSafeId || ""}
                                onValueChange={handleSafeSelection}
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value={safe.id} id={safe.id} />
                                  <Label htmlFor={safe.id} className="sr-only">Select safe</Label>
                                </div>
                              </RadioGroup>
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {safe.boardApprovedDate ? format(new Date(safe.boardApprovedDate), "PP") : "N/A"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap font-medium">
                              ${safe.totalAuthorizedAmountOfRound.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              ${safe.valuationCap.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">{safe.discount}%</TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {safe.mostFavoredNation ? "Yes" : "No"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Section */}
          <div className="border-t pt-8 pb-6">
            <h3 className="text-lg font-semibold mb-6">SAFE Details</h3>
            
            {!selectedSafeId ? (
              <div className="text-center py-12 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">Please select a safe above to issue individual SAFEs</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6 p-1">
                <div className="space-y-3">
                  <Label htmlFor="principalAmount" className="text-sm font-medium">
                    Principal Amount ($) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="principalAmount"
                    type="number"
                    placeholder="0.00"
                    value={formData.principalAmount || ""}
                    onChange={(e) => handleInputChange("principalAmount", parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                    className="h-11"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Investor Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Enter investor name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className="h-11"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Investor Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter investor email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className={cn("h-11", emailError && "border-red-500 focus:border-red-500")}
                  />
                  {emailError && (
                    <p className="text-sm text-red-500 mt-1">{emailError}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="dateOfInvestment" className="text-sm font-medium">
                    Date of Investment <span className="text-red-500">*</span>
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal h-11",
                          !formData.dateOfInvestment && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.dateOfInvestment ? format(new Date(formData.dateOfInvestment), "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.dateOfInvestment ? new Date(formData.dateOfInvestment) : undefined}
                        onSelect={handleDateSelect}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            )}
          </div>

          {/* Document Upload Section */}
          <div className="border-t pt-8 pb-6">
            <h3 className="text-lg font-semibold mb-6">Document Upload (Optional)</h3>
            <div className="space-y-2">
              {selectedFile ? (
                <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/50">
                  <span className="text-sm text-muted-foreground">{selectedFile.name}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedFile(null)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                      <p className="mb-2 text-sm text-muted-foreground">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-muted-foreground">PDF or DOCX (MAX. 5MB)</p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".pdf,.docx"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          setSelectedFile(file);
                        }
                      }}
                    />
                  </label>
                </div>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Note: Documents uploaded here will not be sent out to investors. This is for internal record keeping only.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t bg-muted/20 px-6 -mx-6">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedSafeId || approvedSafes.length === 0}
            className="w-full sm:w-auto"
          >
            Issue SAFE
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IssuedSafeWithUploadDialog;
