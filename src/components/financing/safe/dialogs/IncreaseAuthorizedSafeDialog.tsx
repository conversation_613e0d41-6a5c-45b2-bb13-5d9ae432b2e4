import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { SafeRoundResponse } from "@/types/financing";
import { useIncreaseAuthorizedSafe } from "@/hooks/financing/useIncreaseAuthorizedSafe.hooks";
import { toast } from "sonner";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  safes: SafeRoundResponse[];
  companyId: string;
}

interface FormData {
  selectedSafeId: string;
  authorizedAmount: string;
}

const IncreaseAuthorizedSafeDialog: React.FC<Props> = ({
  open,
  onOpenChange,
  safes,
  companyId,
}) => {
  const [selectedSafe, setSelectedSafe] = useState<SafeRoundResponse | null>(
    null
  );
  const increaseSafeMutation = useIncreaseAuthorizedSafe(companyId);

  const form = useForm<FormData>({
    defaultValues: {
      selectedSafeId: "",
      authorizedAmount: "",
    },
  });

  // Filter to show only approved safes
  const approvedSafes = safes.filter((safe) => safe.isActive === true);

  // Set the top row as selected by default when component mounts or safes change
  useEffect(() => {
    if (approvedSafes.length > 0 && !selectedSafe) {
      const firstSafe = approvedSafes[0];
      setSelectedSafe(firstSafe);
      form.setValue("selectedSafeId", firstSafe.id);
    }
  }, [approvedSafes, selectedSafe, form]);

  const handleSafeSelection = (safeId: string) => {
    const safe = approvedSafes.find((s) => s.id === safeId);
    setSelectedSafe(safe || null);
    form.setValue("selectedSafeId", safeId);
  };

  const handleSubmit = async (data: FormData) => {
    if (!data.selectedSafeId || !data.authorizedAmount) {
      toast.error("Please select a SAFE and enter an increase amount");
      return;
    }

    const increaseAmount = parseFloat(data.authorizedAmount);
    if (isNaN(increaseAmount) || increaseAmount <= 0) {
      toast.error("Please enter a valid increase amount");
      return;
    }

    try {
      await increaseSafeMutation.mutateAsync({
        authorizedRoundId: data.selectedSafeId,
        authorizedAmount: increaseAmount,
      });

      toast.success("SAFE increase request submitted successfully");
      onOpenChange(false);
      form.reset();
      setSelectedSafe(null);
    } catch (error) {
      // Error is already handled by the service
      console.error("Failed to submit SAFE increase:", error);
    }
  };

  const currentAmount = selectedSafe?.totalAuthorizedAmountOfRound || 0;
  const increaseAmount = parseFloat(form.watch("authorizedAmount") || "0");
  const newTotal = currentAmount + increaseAmount;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[900px] max-h-[85vh] md:max-h-[80vh] lg:max-h-[75vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Increase Authorized Round</DialogTitle>
        </DialogHeader>
        <div className="py-4 overflow-y-auto max-h-[calc(85vh-120px)] md:max-h-[calc(80vh-120px)] lg:max-h-[calc(75vh-120px)]">
          <div className="max-w-[900px] mx-auto">
            <div className="space-y-6">
              {/* SAFE Selection Table */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-semibold mb-4">
                    Select SAFE to Increase
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Only approved SAFEs are available for increase requests.
                  </p>

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Select</TableHead>
                          <TableHead>Date Authorized</TableHead>
                          <TableHead>Authorized Amount</TableHead>
                          <TableHead>Valuation Cap</TableHead>
                          <TableHead>Discount</TableHead>
                          <TableHead>MFN</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {approvedSafes.map((safe) => (
                          <TableRow
                            key={safe.id}
                            className={`cursor-pointer hover:bg-gray-50 ${
                              selectedSafe?.id === safe.id
                                ? "bg-blue-50 border-blue-200"
                                : ""
                            }`}
                            onClick={() => handleSafeSelection(safe.id)}
                          >
                            <TableCell>
                              <RadioGroup
                                value={form.watch("selectedSafeId")}
                                onValueChange={handleSafeSelection}
                              >
                                <RadioGroupItem value={safe.id} id={safe.id} />
                              </RadioGroup>
                            </TableCell>
                            <TableCell>
                              {safe.boardApprovedDate
                                ? format(new Date(safe.boardApprovedDate), "PP")
                                : "N/A"}
                            </TableCell>
                            <TableCell>
                              $
                              {safe.totalAuthorizedAmountOfRound.toLocaleString()}
                            </TableCell>
                            <TableCell>
                              ${safe.valuationCap.toLocaleString()}
                            </TableCell>
                            <TableCell>{safe.discount}%</TableCell>
                            <TableCell>
                              {safe.mostFavoredNation ? "Yes" : "No"}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant="default"
                                className="bg-green-100 text-green-800"
                              >
                                Approved
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {approvedSafes.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">
                        No approved SAFEs available for increase.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Increase Amount Form */}
              {selectedSafe && (
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-lg font-semibold mb-4">
                      Increase Amount
                    </h3>

                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(handleSubmit)}
                        className="space-y-6"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              Current Amount
                            </Label>
                            <div className="p-3 bg-gray-50 rounded-md border">
                              <span className="text-lg font-semibold">
                                ${currentAmount.toLocaleString()}
                              </span>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <FormField
                              control={form.control}
                              name="authorizedAmount"
                              rules={{
                                required: "Increase amount is required",
                                min: {
                                  value: 0.01,
                                  message: "Amount must be greater than 0",
                                },
                                validate: (value) => {
                                  const num = parseFloat(value);
                                  if (isNaN(num))
                                    return "Please enter a valid number";
                                  if (num <= 0)
                                    return "Amount must be greater than 0";
                                  return true;
                                },
                              }}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Increase Amount</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                                        $
                                      </span>
                                      <Input
                                        {...field}
                                        type="number"
                                        placeholder="0.00"
                                        className="pl-8"
                                        step="0.01"
                                        min="0.01"
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              New Total
                            </Label>
                            <div className="p-3 bg-blue-50 rounded-md border border-blue-200">
                              <span className="text-lg font-semibold text-blue-700">
                                ${newTotal.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="py-10">
                          <div className="flex justify-end space-x-3">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => onOpenChange(false)}
                              disabled={increaseSafeMutation.isPending}
                            >
                              Cancel
                            </Button>
                            <Button
                              type="submit"
                              disabled={
                                increaseSafeMutation.isPending ||
                                !selectedSafe ||
                                !form.watch("authorizedAmount")
                              }
                            >
                              {increaseSafeMutation.isPending
                                ? "Submitting..."
                                : "Submit for Board Approval"}
                            </Button>
                          </div>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              )}

              {/* Error Display - Only show when there's an actual error */}
              {increaseSafeMutation.error &&
                increaseSafeMutation.error.message !== "null" && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-red-600 text-sm">
                      {increaseSafeMutation.error.message}
                    </p>
                  </div>
                )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncreaseAuthorizedSafeDialog;
