import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Upload, X, FileText } from "lucide-react";
import { format } from "date-fns";
import { SafeRoundResponse, IssueIndividualSafeRequest } from "@/types/financing";
import { cn } from "@/lib/utils";
import { useIssueIndividualSafe } from "@/hooks/financing/useIssueIndividualSafe.hooks";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { IssueIndividualSafeService } from "@/services/financing/issueIndividualSafe.service";

interface IssueIndividualSafesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  safes: SafeRoundResponse[];
  selectedSafeId: string | null;
  onSelectSafe: (safeId: string | null) => void;
  companyId: string;
}

const IssueIndividualSafesDialog: React.FC<IssueIndividualSafesDialogProps> = ({
  open,
  onOpenChange,
  safes,
  selectedSafeId,
  onSelectSafe,
  companyId,
}) => {
  // Filter to show only approved SAFEs (isActive: true)
  const approvedSafes = safes.filter(safe => safe.isActive === true);
  
  const [formData, setFormData] = useState<Omit<IssueIndividualSafeRequest, 'authorizedRoundId'>>({
    principalAmount: 0,
    name: "",
    email: "",
    dateOfInvestment: "",
    includeProRataSideLetter: false,
    includeSideLetter: false,
  });
  const [emailError, setEmailError] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [principalAmountError, setPrincipalAmountError] = useState<string>("");
  const [prorataSelectedFile, setProrataSelectedFile] = useState<File | null>(null);
  const [sideLetterSelectedFile, setSideLetterSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const issueIndividualSafeMutation = useIssueIndividualSafe(companyId);
  const issueService = new IssueIndividualSafeService();

  const handleSafeSelection = (safeId: string) => {
    onSelectSafe(safeId);
    // Reset form when selecting a different SAFE
    setFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
      includeProRataSideLetter: false,
      includeSideLetter: false,
    });
    setEmailError("");
    setPrincipalAmountError("");
    setProrataSelectedFile(null);
    setSideLetterSelectedFile(null);
  };

  const handleClose = () => {
    onOpenChange(false);
    onSelectSafe(null);
    
    // Reset all form states
    setFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
      includeProRataSideLetter: false,
      includeSideLetter: false,
    });
    setEmailError("");
    setPrincipalAmountError("");
    setValidationErrors({});
    setProrataSelectedFile(null);
    setSideLetterSelectedFile(null);
  };

  // Set default selection to first approved SAFE if none selected
  React.useEffect(() => {
    if (approvedSafes.length > 0 && !selectedSafeId) {
      onSelectSafe(approvedSafes[0].id);
    }
  }, [approvedSafes, selectedSafeId, onSelectSafe]);

  const selectedSafe = approvedSafes.find(s => s.id === selectedSafeId);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    // Principal Amount validation
    if (!formData.principalAmount || formData.principalAmount <= 0) {
      errors.principalAmount = "Principal amount must be greater than 0";
    }
    
    // Name validation
    if (!formData.name.trim()) {
      errors.name = "Investor name is required";
    }
    
    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Investor email is required";
    } else if (!validateEmail(formData.email)) {
      errors.email = "Please enter a valid email address";
    }
    
    // Date validation
    if (!formData.dateOfInvestment) {
      errors.dateOfInvestment = "Date of investment is required";
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Calculate available amount for the selected SAFE
  const getAvailableAmount = (safe: SafeRoundResponse | undefined): number => {
    if (!safe) return 0;
    return safe.totalAuthorizedAmountOfRound - safe.outstandingPrincipal;
  };

  // Validate principal amount against available amount
  const validatePrincipalAmount = (amount: number, safe: SafeRoundResponse | undefined): string => {
    if (!safe) return "";
    
    const availableAmount = getAvailableAmount(safe);
    if (amount > availableAmount) {
      return `Amount cannot exceed available amount of $${availableAmount.toLocaleString()}`;
    }
    return "";
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear validation errors when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
    
    // Clear email error when user starts typing
    if (field === 'email') {
      setEmailError("");
    }

    // Validate principal amount in real-time
    if (field === 'principalAmount') {
      const error = validatePrincipalAmount(value as number, selectedSafe);
      setPrincipalAmountError(error);
    }

    // Reset file and S3 key when unchecking prorata sideletter
    if (field === 'includeProRataSideLetter' && value === false) {
      setProrataSelectedFile(null);
    }

    // Reset file and S3 key when unchecking side letter
    if (field === 'includeSideLetter' && value === false) {
      setSideLetterSelectedFile(null);
    }
  };

  const handleProrataFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type - check both MIME type and file extension
    const allowedMimeTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    const isValidMimeType = allowedMimeTypes.includes(file.type);
    const isValidExtension = allowedExtensions.includes(fileExtension);
    
    if (!isValidMimeType && !isValidExtension) {
      toast.error("Please select a PDF, DOC, or DOCX file");
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB");
      return;
    }

    setProrataSelectedFile(file);
  };

  const handleSideLetterFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type - check both MIME type and file extension
    const allowedMimeTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    const isValidMimeType = allowedMimeTypes.includes(file.type);
    const isValidExtension = allowedExtensions.includes(fileExtension);
    
    if (!isValidMimeType && !isValidExtension) {
      toast.error("Please select a PDF, DOC, or DOCX file");
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB");
      return;
    }

    setSideLetterSelectedFile(file);
  };

  const removeProrataFile = () => {
    setProrataSelectedFile(null);
  };

  const removeSideLetterFile = () => {
    setSideLetterSelectedFile(null);
  };

  const uploadProrataFile = async (): Promise<string | undefined> => {
    if (!prorataSelectedFile) return undefined;

    try {
      // Generate unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const extension = prorataSelectedFile.name.split('.').pop();
      const key = `prorata-sideletter-${timestamp}.${extension}`;

      // Get pre-signed URL
      const response = await issueService.getProrataSideletterUploadUrl(companyId, {
        key,
        contentType: prorataSelectedFile.type,
      });

      if (!response.data?.uploadUrl) {
        throw new Error('Failed to get upload URL');
      }

      // Upload file to S3
      const uploadResponse = await fetch(response.data.uploadUrl, {
        method: 'PUT',
        body: prorataSelectedFile,
        headers: {
          'Content-Type': prorataSelectedFile.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file to S3');
      }

      return response.data.key;
    } catch (error) {
      console.error('File upload failed:', error);
      toast.error('Failed to upload prorata sideletter file');
      return undefined;
    }
  };

  const uploadSideLetterFile = async (): Promise<string | undefined> => {
    if (!sideLetterSelectedFile) return undefined;

    try {
      // Generate unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const extension = sideLetterSelectedFile.name.split('.').pop();
      const key = `side-letter-${timestamp}.${extension}`;

      // Get pre-signed URL
      const response = await issueService.getSideLetterUploadUrl(companyId, {
        key,
        contentType: sideLetterSelectedFile.type,
      });

      if (!response.data?.uploadUrl) {
        throw new Error('Failed to get upload URL');
      }

      // Upload file to S3
      const uploadResponse = await fetch(response.data.uploadUrl, {
        method: 'PUT',
        body: sideLetterSelectedFile,
        headers: {
          'Content-Type': sideLetterSelectedFile.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file to S3');
      }

      return response.data.key;
    } catch (error) {
      console.error('File upload failed:', error);
      toast.error('Failed to upload side letter file');
      return undefined;
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        dateOfInvestment: format(date, "yyyy-MM-dd"),
      }));
    }
  };

  const handleSubmit = async () => {
    if (!selectedSafeId) {
      toast.error("Please select a SAFE round first");
      return;
    }

    // Validate principal amount
    const principalAmountError = validatePrincipalAmount(formData.principalAmount, selectedSafe);
    if (principalAmountError) {
      setPrincipalAmountError(principalAmountError);
      toast.error(principalAmountError);
      return;
    }

    // Validate all form fields
    if (!validateForm()) {
      return;
    }

    // If prorata sideletter is included but no file is selected, show error
    if (formData.includeProRataSideLetter && !prorataSelectedFile) {
      toast.error("Please select a file for the prorata sideletter");
      return;
    }

    // If side letter is included but no file is selected, show error
    if (formData.includeSideLetter && !sideLetterSelectedFile) {
      toast.error("Please select a file for the side letter");
      return;
    }

    // If prorata sideletter is not included, ensure no file is selected
    if (!formData.includeProRataSideLetter && prorataSelectedFile) {
      setProrataSelectedFile(null);
    }

    // If side letter is not included, ensure no file is selected
    if (!formData.includeSideLetter && sideLetterSelectedFile) {
      setSideLetterSelectedFile(null);
    }

    try {
      let finalProrataS3Key: string | undefined;
      let finalSideLetterS3Key: string | undefined;

      // Upload prorata file first if included
      if (formData.includeProRataSideLetter && prorataSelectedFile) {
        setIsUploading(true);
        finalProrataS3Key = await uploadProrataFile();
        if (!finalProrataS3Key) {
          toast.error("Failed to upload prorata sideletter document");
          setIsUploading(false);
          return;
        }
        setIsUploading(false);
      }

      // Upload side letter file if included
      if (formData.includeSideLetter && sideLetterSelectedFile) {
        setIsUploading(true);
        finalSideLetterS3Key = await uploadSideLetterFile();
        if (!finalSideLetterS3Key) {
          toast.error("Failed to upload side letter document");
          setIsUploading(false);
          return;
        }
        setIsUploading(false);
      }

      const payload: IssueIndividualSafeRequest = {
        authorizedRoundId: selectedSafeId,
        principalAmount: formData.principalAmount,
        name: formData.name,
        email: formData.email,
        dateOfInvestment: formData.dateOfInvestment,
        includeProRataSideLetter: formData.includeProRataSideLetter,
        includeSideLetter: formData.includeSideLetter,
        prorataS3Key: finalProrataS3Key,
        s3Key: finalSideLetterS3Key,
      };

      await issueIndividualSafeMutation.mutateAsync(payload);
      
      // Close dialog and reset form
      onOpenChange(false);
      onSelectSafe(null);
      setFormData({
        principalAmount: 0,
        name: "",
        email: "",
        dateOfInvestment: "",
        includeProRataSideLetter: false,
        includeSideLetter: false,
      });
      setEmailError("");
      setProrataSelectedFile(null);
      setSideLetterSelectedFile(null);
    } catch (error) {
      // Error is already handled by the service
      console.error("Failed to issue individual SAFE:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[900px] w-[95vw] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Issue Individual SAFEs</DialogTitle>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[calc(90vh-180px)] pr-2 space-y-6">
          {/* Select SAFE Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Select Approved SAFE Round</h3>
            {approvedSafes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">No approved SAFE rounds available</p>
                <p className="text-sm mt-2">Please authorize a SAFE round first before issuing individual SAFEs</p>
              </div>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <div className="max-h-[300px] overflow-y-auto">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background z-10">
                        <TableRow>
                          <TableHead className="w-16 px-4 py-3">Select</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Date Authorized</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Authorized Amount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Outstanding Principal</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Valuation Cap</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Discount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">MFN</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Board Approved</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {approvedSafes.map((safe) => (
                          <TableRow 
                            key={safe.id} 
                            className={cn(
                              "hover:bg-muted/50 cursor-pointer",
                              selectedSafeId === safe.id && "bg-blue-50 border-blue-200"
                            )}
                            onClick={() => handleSafeSelection(safe.id)}
                          >
                            <TableCell className="px-4 py-3">
                              <RadioGroup
                                value={selectedSafeId || ""}
                                onValueChange={handleSafeSelection}
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value={safe.id} id={safe.id} />
                                  <Label htmlFor={safe.id} className="sr-only">Select SAFE round</Label>
                                </div>
                              </RadioGroup>
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {safe.boardApprovedDate ? format(new Date(safe.boardApprovedDate), "PP") : "N/A"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap font-medium">
                              ${safe.totalAuthorizedAmountOfRound.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              ${safe.outstandingPrincipal.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              ${safe.valuationCap.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">{safe.discount}%</TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {safe.mostFavoredNation ? "Yes" : "No"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                safe.isActive 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {safe.isActive ? 'Approved' : 'Pending'}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Section - Always visible */}
          <div className="border-t pt-8 pb-6">
            <h3 className="text-lg font-semibold mb-6">SAFE Details</h3>
            
            {!selectedSafeId ? (
              <div className="text-center py-12 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">Please select a SAFE round above to issue individual SAFEs</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6 p-1">
                <div className="space-y-3">
                  <Label htmlFor="principalAmount" className="text-sm font-medium">
                    Principal Amount ($) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="principalAmount"
                    type="number"
                    placeholder="0.00"
                    value={formData.principalAmount || ""}
                    onChange={(e) => handleInputChange("principalAmount", parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                    className={cn("h-11", (validationErrors.principalAmount || principalAmountError) && "border-red-500 focus:border-red-500")}
                  />
                  {selectedSafe && (
                    <p className="text-sm text-gray-600">
                      Available amount: ${getAvailableAmount(selectedSafe).toLocaleString()}
                    </p>
                  )}
                  {(validationErrors.principalAmount || principalAmountError) && (
                    <p className="text-sm text-red-500 mt-1">{validationErrors.principalAmount || principalAmountError}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Investor Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Enter investor name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className={cn("h-11", validationErrors.name && "border-red-500 focus:border-red-500")}
                  />
                  {validationErrors.name && (
                    <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Investor Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter investor email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className={cn("h-11", (validationErrors.email || emailError) && "border-red-500 focus:border-red-500")}
                  />
                  {(validationErrors.email || emailError) && (
                    <p className="text-sm text-red-500 mt-1">{validationErrors.email || emailError}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="dateOfInvestment" className="text-sm font-medium">
                    Date of Investment <span className="text-red-500">*</span>
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal h-11",
                          !formData.dateOfInvestment && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.dateOfInvestment ? format(new Date(formData.dateOfInvestment), "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.dateOfInvestment ? new Date(formData.dateOfInvestment) : undefined}
                        onSelect={handleDateSelect}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Document Upload Section */}
                <div className="lg:col-span-2 space-y-4">
                  <div className="text-sm font-medium text-muted-foreground mb-3">Additional Documents</div>
                  
                  {/* Prorata Side Letter */}
                  <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                    <Checkbox
                      id="includeProRataSideLetter"
                      checked={formData.includeProRataSideLetter}
                      onCheckedChange={(checked) => handleInputChange("includeProRataSideLetter", checked === true)}
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <Label htmlFor="includeProRataSideLetter" className="text-sm font-medium cursor-pointer">
                        Include prorata
                      </Label>
                      {formData.includeProRataSideLetter && (
                        <div className="mt-2">
                          {!prorataSelectedFile ? (
                            <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                              <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-muted-foreground">Upload prorata document</p>
                                <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                              </div>
                              <Input
                                type="file"
                                accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                onChange={handleProrataFileSelect}
                                className="hidden"
                                id="prorata-file-upload"
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => document.getElementById('prorata-file-upload')?.click()}
                                className="h-7 px-2 text-xs"
                              >
                                Browse
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs font-medium text-green-800 truncate">{prorataSelectedFile.name}</p>
                                <p className="text-xs text-green-600">{(prorataSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={removeProrataFile}
                                className="h-6 w-6 p-0 hover:bg-green-100"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Side Letter */}
                  <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                    <Checkbox
                      id="includeSideLetter"
                      checked={formData.includeSideLetter}
                      onCheckedChange={(checked) => handleInputChange("includeSideLetter", checked === true)}
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <Label htmlFor="includeSideLetter" className="text-sm font-medium cursor-pointer">
                        Include side letter
                      </Label>
                      {formData.includeSideLetter && (
                        <div className="mt-2">
                          {!sideLetterSelectedFile ? (
                            <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                              <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-muted-foreground">Upload side letter document</p>
                                <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                              </div>
                              <Input
                                type="file"
                                accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                onChange={handleSideLetterFileSelect}
                                className="hidden"
                                id="side-letter-file-upload"
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => document.getElementById('side-letter-file-upload')?.click()}
                                className="h-7 px-2 text-xs"
                              >
                                Browse
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs font-medium text-green-800 truncate">{sideLetterSelectedFile.name}</p>
                                <p className="text-xs text-green-600">{(sideLetterSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={removeSideLetterFile}
                                className="h-6 w-6 p-0 hover:bg-green-100"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t bg-muted/20 px-6 -mx-6">
          <Button 
            variant="outline" 
            onClick={handleClose}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedSafeId || issueIndividualSafeMutation.isPending || approvedSafes.length === 0 || isUploading}
            className="w-full sm:w-auto"
          >
            {issueIndividualSafeMutation.isPending || isUploading ? "Processing..." : "Issue"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IssueIndividualSafesDialog;
