import React from "react";
import { format } from "date-fns";
import { Eye, X, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useIndividualSafesList } from "@/hooks/financing/useIndividualSafesList.hooks";
import { SafeRoundResponse } from "@/types/financing";

interface ViewIndividualSafesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roundData: SafeRoundResponse | null;
  companyId: string;
}

export const ViewIndividualSafesDialog: React.FC<
  ViewIndividualSafesDialogProps
> = ({ open, onOpenChange, roundData, companyId }) => {
  const { individualSafes, isLoading, isError, error, refetch } =
    useIndividualSafesList(companyId, roundData?.id || "");

  const handleClose = () => {
    onOpenChange(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const getActiveStatusBadge = (isActive: boolean) => {
    if (isActive) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 hover:bg-green-100"
        >
          Active
        </Badge>
      );
    }
    return (
      <Badge
        variant="destructive"
        className="bg-red-100 text-red-800 hover:bg-red-100"
      >
        Inactive
      </Badge>
    );
  };

  const renderTableContent = () => {
    if (isLoading) {
      return (
        <TableBody>
          {[...Array(3)].map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      );
    }

    if (isError) {
      return (
        <TableBody>
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8">
              <div className="flex flex-col items-center gap-3">
                <p className="text-red-600 text-sm">
                  {error?.message || "Failed to load individual SAFEs"}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetch()}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Retry
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      );
    }

    if (!individualSafes || individualSafes.length === 0) {
      return (
        <TableBody>
          <TableRow>
            <TableCell colSpan={6} className="text-center py-8">
              <div className="flex flex-col items-center gap-2">
                <Eye className="h-8 w-8 text-muted-foreground" />
                <p className="text-muted-foreground text-sm">
                  No individual SAFEs found for this round
                </p>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      );
    }

    return (
      <TableBody>
        {individualSafes.map((safe) => (
          <TableRow key={safe.id} className="hover:bg-muted/50">
            <TableCell className="font-medium">{safe.name}</TableCell>
            <TableCell className="text-muted-foreground">
              {safe.email}
            </TableCell>
            <TableCell className="font-mono">
              {formatCurrency(safe.principalAmount)}
            </TableCell>
            <TableCell>{formatDate(safe.dateOfInvestment)}</TableCell>
            <TableCell>{getActiveStatusBadge(safe.isActive)}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    );
  };

  const getDialogTitle = () => {
    if (!roundData) return "Individual SAFEs";

    const date = roundData.boardApprovedDate
      ? formatDate(roundData.boardApprovedDate)
      : "Unknown Date";

    return `Individual SAFEs - ${date} - ${formatCurrency(roundData.totalAuthorizedAmountOfRound)}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {getDialogTitle()}
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Investor Name</TableHead>
                  <TableHead className="w-[250px]">Email</TableHead>
                  <TableHead className="w-[150px]">Principal Amount</TableHead>
                  <TableHead className="w-[150px]">
                    Date of Investment
                  </TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                </TableRow>
              </TableHeader>
              {renderTableContent()}
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
