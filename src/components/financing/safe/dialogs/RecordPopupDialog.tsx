import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { SafeRoundResponse } from "@/types/financing";
import IssuedSafeWithUploadDialog from "@/components/financing/safe/dialogs/IssuedSafeWithUploadDialog";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Upload, X, FileText } from "lucide-react";
import { format } from "date-fns";
import { useRecordAuthorizedSafe } from "@/hooks/financing/useRecordAuthorizedSafe.hooks";
import { 
  useIssueSafeNoteUpload, 
  useRecordIssueIndividualSafeNote,
  useSafeProrataUploadUrl,
  useSafeSideLetterUploadUrl,
  useSafeOptionalDocumentUploadUrl
} from "@/hooks/financing/useIssueSafeNote.hooks";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { uploadFileToS3, generateFileName } from "@/utils/s3Upload";

interface RecordPopupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  safes: SafeRoundResponse[];
  companyId: string;
}

const RecordPopupDialog: React.FC<RecordPopupDialogProps> = ({
  open,
  onOpenChange,
  safes,
  companyId,
}) => {
  const [selectedOption, setSelectedOption] = useState<"issued-safe" | "authorized-round">("issued-safe");
  const [showIssuedSafeDialog, setShowIssuedSafeDialog] = useState(false);
  
  // Form state for Record Authorized Round
  const [fileUpload, setFileUpload] = useState<{ file: File | null }>({ file: null });
  const { recordRound, isRecording, reset, mutation } = useRecordAuthorizedSafe(companyId);

  // Form state for Issued SAFE
  const [selectedSafeId, setSelectedSafeId] = useState<string | null>(null);
  const [issuedSafeFormData, setIssuedSafeFormData] = useState({
    principalAmount: 0,
    name: "",
    email: "",
    dateOfInvestment: "",
    includeProrataSideLetter: false,
    includeSideLetter: false,
  });
  const [issuedSafeEmailError, setIssuedSafeEmailError] = useState<string>("");
  const [issuedSafeSelectedFile, setIssuedSafeSelectedFile] = useState<File | null>(null);
  const [prorataSelectedFile, setProrataSelectedFile] = useState<File | null>(null);
  const [sideLetterSelectedFile, setSideLetterSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Hooks for Issue SAFE Note
  const { mutateAsync: getUploadUrl, isPending: isGettingUploadUrl } = useIssueSafeNoteUpload(companyId);
  const { mutateAsync: recordSafeNote, isPending: isRecordingSafeNote } = useRecordIssueIndividualSafeNote(companyId);
  
  // NEW: Hooks for SAFE presigned URLs
  const { mutateAsync: getProrataUploadUrl, isPending: isGettingProrataUrl } = useSafeProrataUploadUrl(companyId);
  const { mutateAsync: getSideLetterUploadUrl, isPending: isGettingSideLetterUrl } = useSafeSideLetterUploadUrl(companyId);
  const { mutateAsync: getOptionalDocumentUploadUrl, isPending: isGettingOptionalUrl } = useSafeOptionalDocumentUploadUrl(companyId);

  const form = useForm({
    defaultValues: {
      totalAuthorizedAmountOfRound: "",
      valuationCap: "",
      discount: "",
      mostFavoredNation: "",
      boardApprovedDate: null as Date | null,
    },
  });

  // Set default selection to first approved safe if none selected
  React.useEffect(() => {
    if (safes.length > 0 && !selectedSafeId) {
      const firstApprovedSafe = safes.find(safe => safe.isActive === true);
      if (firstApprovedSafe) {
        setSelectedSafeId(firstApprovedSafe.id);
      }
    }
  }, [safes, selectedSafeId]);

  const handleClose = () => {
    onOpenChange(false);
    setSelectedOption("issued-safe");
    
    // Reset all form states for Issued SAFE
    setSelectedSafeId(null);
    setIssuedSafeFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
      includeProrataSideLetter: false,
      includeSideLetter: false,
    });
    setIssuedSafeEmailError("");
    setIssuedSafeSelectedFile(null);
    setProrataSelectedFile(null);
    setSideLetterSelectedFile(null);
    
    // Reset form for Authorized Round
    form.reset();
    setFileUpload({ file: null });
  };

  const handleIssuedSafeClose = () => {
    setShowIssuedSafeDialog(false);
  };

  const handleIssuedSafeClick = () => {
    setShowIssuedSafeDialog(true);
    onOpenChange(false);
  };

  // File handling functions
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/octet-stream'
      ];
      
      const allowedExtensions = ['.pdf', '.doc', '.docx'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      
      const isValidMimeType = allowedTypes.includes(file.type);
      const isValidExtension = allowedExtensions.includes(fileExtension);
      
      if (!isValidMimeType && !isValidExtension) {
        toast.error("Please select a PDF or Word document");
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size must be less than 5MB");
        return;
      }

      setFileUpload({ file });
    }
  };

  const removeFile = () => {
    setFileUpload({ file: null });
  };

  const handleAuthorizedRoundSubmit = async (data: any) => {
    if (!data.boardApprovedDate) {
      toast.error("Please select a board approved date");
      return;
    }

    if (!fileUpload.file) {
      toast.error("Please upload a document");
      return;
    }

    try {
      await recordRound(fileUpload.file, {
        totalAuthorizedAmountOfRound: parseFloat(data.totalAuthorizedAmountOfRound.replace(/[$,]/g, "")),
        valuationCap: parseFloat(data.valuationCap.replace(/[$,]/g, "")),
        discount: parseFloat(data.discount),
        mostFavoredNation: data.mostFavoredNation === "true",
        boardApprovedDate: data.boardApprovedDate.toISOString().split('T')[0],
      });
      
      onOpenChange(false);
      form.reset();
      setFileUpload({ file: null });
    } catch (error) {
      console.error("Failed to record SAFE round:", error);
    }
  };

  // Handler functions for Issued SAFE form
  const handleIssuedSafeInputChange = (field: keyof typeof issuedSafeFormData, value: string | number | boolean) => {
    setIssuedSafeFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear email error when user starts typing
    if (field === 'email') {
      setIssuedSafeEmailError("");
    }
  };

  const handleIssuedSafeDateSelect = (date: Date | undefined) => {
    if (date) {
      setIssuedSafeFormData(prev => ({
        ...prev,
        dateOfInvestment: format(date, "yyyy-MM-dd"),
      }));
    }
  };

  const handleIssuedSafeSubmit = async () => {
    if (!selectedSafeId) {
      toast.error("Please select a safe first");
      return;
    }

    if (!issuedSafeFormData.principalAmount || !issuedSafeFormData.name || !issuedSafeFormData.email || !issuedSafeFormData.dateOfInvestment) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(issuedSafeFormData.email)) {
      setIssuedSafeEmailError("Please enter a valid email address");
      return;
    }

    // All documents are optional, so we don't require files even when checkboxes are checked

    try {
      setIsUploading(true); // New state for overall upload progress
      
      const uploadPromises: Promise<{ type: string; key?: string }>[] = [];

      // Main optional document upload
      if (issuedSafeSelectedFile) {
        const fileName = generateFileName(issuedSafeSelectedFile.name);
        uploadPromises.push(
          getOptionalDocumentUploadUrl({
            key: fileName,
            contentType: issuedSafeSelectedFile.type,
          }).then(async (response) => {
            if (response.error) throw new Error(response.error);
            await uploadFileToS3(issuedSafeSelectedFile, response.data.uploadUrl);
            return { type: 's3Key', key: response.data.key };
          })
        );
      }

      // Prorata side letter upload
      if (issuedSafeFormData.includeProrataSideLetter && prorataSelectedFile) {
        const fileName = generateFileName(prorataSelectedFile.name);
        uploadPromises.push(
          getProrataUploadUrl({
            key: fileName,
            contentType: prorataSelectedFile.type,
          }).then(async (response) => {
            if (response.error) throw new Error(response.error);
            await uploadFileToS3(prorataSelectedFile, response.data.uploadUrl);
            return { type: 'prorataS3Key', key: response.data.key };
          })
        );
      }

      // Side letter upload
      if (issuedSafeFormData.includeSideLetter && sideLetterSelectedFile) {
        const fileName = generateFileName(sideLetterSelectedFile.name);
        uploadPromises.push(
          getSideLetterUploadUrl({
            key: fileName,
            contentType: sideLetterSelectedFile.type,
          }).then(async (response) => {
            if (response.error) throw new Error(response.error);
            await uploadFileToS3(sideLetterSelectedFile, response.data.uploadUrl);
            return { type: 'optionalS3Key', key: response.data.key }; // User specified optionalS3Key for side letter
          })
        );
      }

      const uploadedKeys: { [key: string]: string } = {};
      if (uploadPromises.length > 0) {
        const results = await Promise.all(uploadPromises);
        results.forEach(result => {
          if (result.key) {
            uploadedKeys[result.type] = result.key;
          }
        });
      }

      // Record the SAFE note
      await recordSafeNote({
        authorizedRoundId: selectedSafeId,
        principalAmount: issuedSafeFormData.principalAmount,
        name: issuedSafeFormData.name,
        email: issuedSafeFormData.email,
        dateOfInvestment: issuedSafeFormData.dateOfInvestment,
        approvedDate: new Date().toISOString().split('T')[0], // Current date as approved date
        prorataS3Key: uploadedKeys.prorataS3Key,
        s3Key: uploadedKeys.s3Key,
        optionalS3Key: uploadedKeys.optionalS3Key,
      });
      
      // Close dialog and reset form
      onOpenChange(false);
      setSelectedSafeId(null);
      setIssuedSafeFormData({
        principalAmount: 0,
        name: "",
        email: "",
        dateOfInvestment: "",
        includeProrataSideLetter: false,
        includeSideLetter: false,
      });
      setIssuedSafeEmailError("");
      setIssuedSafeSelectedFile(null);
      setProrataSelectedFile(null);
      setSideLetterSelectedFile(null);
    } catch (error) {
      toast.error("Failed to issue SAFE", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Record</DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto max-h-[calc(90vh-120px)] pr-2">
            <div className="space-y-6">
              <div className="space-y-4">
                <RadioGroup
                  value={selectedOption}
                  onValueChange={(value: "issued-safe" | "authorized-round") => setSelectedOption(value)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="issued-safe" id="issued-safe" />
                    <Label htmlFor="issued-safe" className="text-sm font-medium cursor-pointer">
                      Issued SAFE
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="authorized-round" id="authorized-round" />
                    <Label htmlFor="authorized-round" className="text-sm font-medium cursor-pointer">
                      Authorized Round
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Show form immediately when Authorized Round is selected */}
              {selectedOption === "authorized-round" && (
                <div className="border-t pt-6">
                  <Card>
                    <CardContent className="pt-6">
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(handleAuthorizedRoundSubmit)} className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Total Authorized Amount */}
                            <FormField
                              control={form.control}
                              name="totalAuthorizedAmountOfRound"
                              rules={{
                                required: "Total Authorized Amount is required",
                                min: { value: 0.01, message: "Amount must be greater than 0" },
                                validate: (value) => {
                                  const num = parseFloat(value.replace(/[$,]/g, ""));
                                  return !isNaN(num) && num > 0;
                                }
                              }}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Total Authorized Amount of Round *</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                                      <Input 
                                        {...field} 
                                        placeholder="0.00" 
                                        className="pl-8" 
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Valuation Cap */}
                            <FormField
                              control={form.control}
                              name="valuationCap"
                              rules={{
                                required: "Valuation Cap is required",
                                min: { value: 0.01, message: "Amount must be greater than 0" },
                                validate: (value) => {
                                  const num = parseFloat(value.replace(/[$,]/g, ""));
                                  return !isNaN(num) && num > 0;
                                }
                              }}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Valuation Cap *</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                                      <Input 
                                        {...field} 
                                        placeholder="0.00" 
                                        className="pl-8" 
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Discount */}
                            <FormField
                              control={form.control}
                              name="discount"
                              rules={{
                                required: "Discount is required",
                                min: { value: 0, message: "Discount must be 0 or greater" },
                                max: { value: 100, message: "Discount cannot exceed 100%" },
                                validate: (value) => {
                                  const num = parseFloat(value);
                                  return !isNaN(num) && num >= 0 && num <= 100;
                                }
                              }}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Discount *</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input 
                                        {...field} 
                                        placeholder="0.00" 
                                        className="pr-8" 
                                      />
                                      <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500">%</span>
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* MFN */}
                            <FormField
                              control={form.control}
                              name="mostFavoredNation"
                              rules={{ required: "MFN selection is required" }}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>MFN (Most Favored Nation) *</FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select MFN status" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="true">Yes</SelectItem>
                                      <SelectItem value="false">No</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Board Approved Date */}
                            <FormField
                              control={form.control}
                              name="boardApprovedDate"
                              rules={{ required: "Board Approved Date is required" }}
                              render={({ field }) => (
                                <FormItem className="flex flex-col">
                                  <FormLabel>Board Approved Date *</FormLabel>
                                  <Popover>
                                    <PopoverTrigger asChild>
                                      <FormControl>
                                        <Button
                                          variant={"outline"}
                                          className={cn(
                                            "w-full pl-3 text-left font-normal",
                                            !field.value && "text-muted-foreground"
                                          )}
                                        >
                                          {field.value ? (
                                            format(field.value, "PPP")
                                          ) : (
                                            <span>Pick a date</span>
                                          )}
                                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                        </Button>
                                      </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                      <Calendar
                                        mode="single"
                                        selected={field.value || undefined}
                                        onSelect={field.onChange}
                                        disabled={(date) =>
                                          date > new Date() || date < new Date("1900-01-01")
                                        }
                                        initialFocus
                                      />
                                    </PopoverContent>
                                  </Popover>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* File Upload Section */}
                          <div className="space-y-4">
                            <Label className="text-sm font-medium">Upload Authorized Round Form *</Label>
                            
                            {!fileUpload.file ? (
                              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                                <input
                                  type="file"
                                  accept=".pdf,.doc,.docx"
                                  onChange={handleFileSelect}
                                  className="hidden"
                                  id="file-upload"
                                />
                                <label htmlFor="file-upload" className="cursor-pointer">
                                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                                  <p className="mt-2 text-sm text-gray-600">
                                    Click to upload or drag and drop
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    PDF or DOCX (MAX. 5MB)
                                  </p>
                                </label>
                              </div>
                            ) : (
                              <div className="border rounded-lg p-4 bg-gray-50">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <FileText className="h-5 w-5 text-gray-500" />
                                    <div>
                                      <p className="text-sm font-medium">{fileUpload.file.name}</p>
                                      <p className="text-xs text-gray-500">
                                        {(fileUpload.file.size / 1024 / 1024).toFixed(2)} MB
                                      </p>
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={removeFile}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded">
                                  <p className="text-xs text-blue-700">✓ File selected and ready for upload</p>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Submit Section */}
                          <div className="flex justify-end space-x-3 pt-4 border-t">
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => onOpenChange(false)}
                              disabled={isRecording}
                            >
                              Cancel
                            </Button>
                            <Button 
                              type="submit" 
                              disabled={isRecording || !fileUpload.file}
                            >
                              {isRecording ? "Recording..." : "Record SAFE"}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>

                  {/* Error Display */}
                  {mutation.error && mutation.error.message !== "null" && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-red-600 text-sm">{mutation.error.message}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Show Issued SAFE form immediately when Issued SAFE is selected */}
              {selectedOption === "issued-safe" && (
                <div className="border-t pt-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-6">
                        {/* Select Safe Section */}
                        <div>
                          <h3 className="text-lg font-semibold mb-4">Select Approved Safe</h3>
                          {safes.filter(safe => safe.isActive === true).length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                              <p className="text-base">No approved safes available</p>
                              <p className="text-sm mt-2">Please authorize a safe first before issuing individual SAFEs</p>
                            </div>
                          ) : (
                            <div className="border rounded-lg overflow-hidden">
                              <div className="overflow-x-auto">
                                <div className="max-h-[300px] overflow-y-auto">
                                  <Table>
                                    <TableHeader className="sticky top-0 bg-background z-10">
                                      <TableRow>
                                        <TableHead className="w-16 px-4 py-3">Select</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Date Authorized</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Authorized Amount</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Valuation Cap</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Discount</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">MFN</TableHead>
                                      </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                      {safes.filter(safe => safe.isActive === true).map((safe) => (
                                        <TableRow 
                                          key={safe.id} 
                                          className={cn(
                                            "hover:bg-muted/50 cursor-pointer",
                                            selectedSafeId === safe.id && "bg-blue-50 border-blue-200"
                                          )}
                                          onClick={() => setSelectedSafeId(safe.id)}
                                        >
                                          <TableCell className="px-4 py-3">
                                            <RadioGroup
                                              value={selectedSafeId || ""}
                                              onValueChange={setSelectedSafeId}
                                            >
                                              <div className="flex items-center space-x-2">
                                                <RadioGroupItem value={safe.id} id={safe.id} />
                                                <Label htmlFor={safe.id} className="sr-only">Select safe</Label>
                                              </div>
                                            </RadioGroup>
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            {safe.boardApprovedDate ? format(new Date(safe.boardApprovedDate), "PP") : "N/A"}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap font-medium">
                                            ${safe.totalAuthorizedAmountOfRound.toLocaleString()}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            ${safe.valuationCap.toLocaleString()}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">{safe.discount}%</TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            {safe.mostFavoredNation ? "Yes" : "No"}
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* SAFE Details Form */}
                        <div className="border-t pt-8 pb-6">
                          <h3 className="text-lg font-semibold mb-6">SAFE Details</h3>
                          
                          {!selectedSafeId ? (
                            <div className="text-center py-12 text-muted-foreground bg-muted/30 rounded-lg">
                              <p className="text-base">Please select a safe above to issue individual SAFEs</p>
                            </div>
                          ) : (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6 p-1">
                              <div className="space-y-3">
                                <Label htmlFor="principalAmount" className="text-sm font-medium">
                                  Principal Amount ($) <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="principalAmount"
                                  type="number"
                                  placeholder="0.00"
                                  value={issuedSafeFormData.principalAmount || ""}
                                  onChange={(e) => handleIssuedSafeInputChange("principalAmount", parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="h-11"
                                />
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="name" className="text-sm font-medium">
                                  Investor Name <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="name"
                                  type="text"
                                  placeholder="Enter investor name"
                                  value={issuedSafeFormData.name}
                                  onChange={(e) => handleIssuedSafeInputChange("name", e.target.value)}
                                  className="h-11"
                                />
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="email" className="text-sm font-medium">
                                  Investor Email <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="email"
                                  type="email"
                                  placeholder="Enter investor email"
                                  value={issuedSafeFormData.email}
                                  onChange={(e) => handleIssuedSafeInputChange("email", e.target.value)}
                                  className={cn("h-11", issuedSafeEmailError && "border-red-500 focus:border-red-500")}
                                />
                                {issuedSafeEmailError && (
                                  <p className="text-sm text-red-500 mt-1">{issuedSafeEmailError}</p>
                                )}
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="dateOfInvestment" className="text-sm font-medium">
                                  Date of Investment <span className="text-red-500">*</span>
                                </Label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "w-full justify-start text-left font-normal h-11",
                                        !issuedSafeFormData.dateOfInvestment && "text-muted-foreground"
                                      )}
                                    >
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      {issuedSafeFormData.dateOfInvestment ? format(new Date(issuedSafeFormData.dateOfInvestment), "PPP") : "Pick a date"}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <Calendar
                                      mode="single"
                                      selected={issuedSafeFormData.dateOfInvestment ? new Date(issuedSafeFormData.dateOfInvestment) : undefined}
                                      onSelect={handleIssuedSafeDateSelect}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                              </div>
                              
                              {/* Document Upload Section */}
                              <div className="col-span-1 lg:col-span-2 space-y-4">
                                <div className="text-sm font-medium text-muted-foreground mb-3">Additional Documents</div>
                                
                                {/* Prorata Side Letter */}
                                <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                                  <Checkbox
                                    id="includeProrata"
                                    checked={issuedSafeFormData.includeProrataSideLetter}
                                    onCheckedChange={(checked) => handleIssuedSafeInputChange("includeProrataSideLetter", checked === true)}
                                    className="mt-0.5"
                                  />
                                  <div className="flex-1 min-w-0">
                                    <Label htmlFor="includeProrata" className="text-sm font-medium cursor-pointer">
                                      Include prorata
                                    </Label>
                                    {issuedSafeFormData.includeProrataSideLetter && (
                                      <div className="mt-2">
                                        {!prorataSelectedFile ? (
                                          <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                                            <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <p className="text-xs text-muted-foreground">Upload prorata document</p>
                                              <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                                            </div>
                                            <input
                                              type="file"
                                              className="hidden"
                                              accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                              onChange={(e) => {
                                                const file = e.target.files?.[0];
                                                if (file) {
                                                  // Validate file type and size
                                                  const allowedTypes = [
                                                    'application/pdf',
                                                    'application/msword',
                                                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                                  ];
                                                  
                                                  const allowedExtensions = ['.pdf', '.doc', '.docx'];
                                                  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
                                                  
                                                  const isValidMimeType = allowedTypes.includes(file.type);
                                                  const isValidExtension = allowedExtensions.includes(fileExtension);
                                                  
                                                  if (!isValidMimeType && !isValidExtension) {
                                                    toast.error("Please select a PDF or Word document");
                                                    return;
                                                  }
                                                  
                                                  if (file.size > 5 * 1024 * 1024) {
                                                    toast.error("File size must be less than 5MB");
                                                    return;
                                                  }
                                                  
                                                  setProrataSelectedFile(file);
                                                }
                                              }}
                                              id="prorata-file-upload"
                                            />
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => document.getElementById('prorata-file-upload')?.click()}
                                              className="h-7 px-2 text-xs"
                                            >
                                              Browse
                                            </Button>
                                          </div>
                                        ) : (
                                          <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                            <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <p className="text-xs font-medium text-green-800 truncate">{prorataSelectedFile.name}</p>
                                              <p className="text-xs text-green-600">{(prorataSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                                            </div>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => setProrataSelectedFile(null)}
                                              className="h-6 w-6 p-0 hover:bg-green-100"
                                            >
                                              <X className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Side Letter */}
                                <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                                  <Checkbox
                                    id="includeSideLetter"
                                    checked={issuedSafeFormData.includeSideLetter}
                                    onCheckedChange={(checked) => handleIssuedSafeInputChange("includeSideLetter", checked === true)}
                                    className="mt-0.5"
                                  />
                                  <div className="flex-1 min-w-0">
                                    <Label htmlFor="includeSideLetter" className="text-sm font-medium cursor-pointer">
                                      Include side letter
                                    </Label>
                                    {issuedSafeFormData.includeSideLetter && (
                                      <div className="mt-2">
                                        {!sideLetterSelectedFile ? (
                                          <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                                            <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <p className="text-xs text-muted-foreground">Upload side letter document</p>
                                              <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                                            </div>
                                            <input
                                              type="file"
                                              className="hidden"
                                              accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                              onChange={(e) => {
                                                const file = e.target.files?.[0];
                                                if (file) {
                                                  // Validate file type and size
                                                  const allowedTypes = [
                                                    'application/pdf',
                                                    'application/msword',
                                                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                                  ];
                                                  
                                                  const allowedExtensions = ['.pdf', '.doc', '.docx'];
                                                  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
                                                  
                                                  const isValidMimeType = allowedTypes.includes(file.type);
                                                  const isValidExtension = allowedExtensions.includes(fileExtension);
                                                  
                                                  if (!isValidMimeType && !isValidExtension) {
                                                    toast.error("Please select a PDF or Word document");
                                                    return;
                                                  }
                                                  
                                                  if (file.size > 5 * 1024 * 1024) {
                                                    toast.error("File size must be less than 5MB");
                                                    return;
                                                  }
                                                  
                                                  setSideLetterSelectedFile(file);
                                                }
                                              }}
                                              id="side-letter-file-upload"
                                            />
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => document.getElementById('side-letter-file-upload')?.click()}
                                              className="h-7 px-2 text-xs"
                                            >
                                              Browse
                                            </Button>
                                          </div>
                                        ) : (
                                          <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                            <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                              <p className="text-xs font-medium text-green-800 truncate">{sideLetterSelectedFile.name}</p>
                                              <p className="text-xs text-green-600">{(sideLetterSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                                            </div>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => setSideLetterSelectedFile(null)}
                                              className="h-6 w-6 p-0 hover:bg-green-100"
                                            >
                                              <X className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Document Upload Section */}
                        <div className="border-t pt-8 pb-6">
                          <h3 className="text-lg font-semibold mb-6">Executed SAFE</h3>
                          <div className="space-y-2">
                            {issuedSafeSelectedFile ? (
                              <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/50">
                                <span className="text-sm text-muted-foreground">{issuedSafeSelectedFile.name}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setIssuedSafeSelectedFile(null)}
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="flex items-center justify-center w-full">
                                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                                    <p className="mb-2 text-sm text-muted-foreground">
                                      <span className="font-semibold">Click to upload</span> or drag and drop
                                    </p>
                                    <p className="text-xs text-muted-foreground">PDF or DOCX (MAX. 5MB)</p>
                                  </div>
                                  <input
                                    type="file"
                                    className="hidden"
                                    accept=".pdf,.docx"
                                    onChange={(e) => {
                                      const file = e.target.files?.[0];
                                      if (file) {
                                        // Validate file type and size
                                        const allowedTypes = [
                                          'application/pdf',
                                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                        ];
                                        
                                        if (!allowedTypes.includes(file.type)) {
                                          toast.error("Please select a PDF or DOCX file");
                                          return;
                                        }
                                        
                                        if (file.size > 5 * 1024 * 1024) {
                                          toast.error("File size must be less than 5MB");
                                          return;
                                        }
                                        
                                        setIssuedSafeSelectedFile(file);
                                      }
                                    }}
                                  />
                                </label>
                              </div>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-2">
                            Note: Documents uploaded here will not be sent out to investors. This is for internal record keeping only.
                          </p>
                        </div>



                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t bg-muted/20 px-6 -mx-6">
                          <Button 
                            variant="outline" 
                            onClick={handleClose}
                            className="w-full sm:w-auto"
                          >
                            Cancel
                          </Button>
                          <Button 
                            onClick={handleIssuedSafeSubmit}
                            disabled={!selectedSafeId || safes.filter(safe => safe.isActive === true).length === 0 || isUploading}
                            className="w-full sm:w-auto"
                          >
                            {isUploading ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                {isUploading ? "Uploading..." : "Issuing SAFE..."}
                              </>
                            ) : (
                              "Issue SAFE"
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Issued SAFE with Upload Dialog */}
      <IssuedSafeWithUploadDialog
        open={showIssuedSafeDialog}
        onOpenChange={handleIssuedSafeClose}
        safes={safes}
        companyId={companyId}
      />
    </>
  );
};

export default RecordPopupDialog;
