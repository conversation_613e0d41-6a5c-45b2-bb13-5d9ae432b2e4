import React, { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/common/Card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Eye } from "lucide-react";
import { useAuthorizedSafeList } from "@/hooks/financing/useAuthorizedSafeList.hooks";
import { SafeRoundResponse } from "@/types/financing";
import { format } from "date-fns";
import { ViewIndividualSafesDialog } from "./dialogs/ViewIndividualSafesDialog";
import AuthorizeSafeForm from "./forms/AuthorizeSafeForm";
import IssueIndividualSafesDialog from "./dialogs/IssueIndividualSafesDialog";
import IncreaseAuthorizedSafeDialog from "./dialogs/IncreaseAuthorizedSafeDialog";
import RecordPopupDialog from "./dialogs/RecordPopupDialog";
import ActionButton from "./ActionButton";
import { useAuth } from "@/contexts/AuthContext";

const SafesFlow: React.FC = () => {
  const { user } = useAuth();
  const [authorizeOpen, setAuthorizeOpen] = useState(false);
  const [issueOpen, setIssueOpen] = useState(false);
  const [increaseOpen, setIncreaseOpen] = useState(false);
  const [recordOpen, setRecordOpen] = useState(false);
  const [viewSafesOpen, setViewSafesOpen] = useState(false);
  const [selectedSafeRound, setSelectedSafeRound] = useState<SafeRoundResponse | null>(null);
  const [selectedSafeId, setSelectedSafeId] = useState<string | null>(null);

  // Use the hook to fetch data from API with filtering
  const { 
    safes, 
    filteredSafes, 
    isLoadingSafes, 
    error, 
    refetchSafes,
    filterType,
    setFilterType 
  } = useAuthorizedSafeList(user?.companyId || "");

  const handleViewSafes = (safe: SafeRoundResponse) => {
    setSelectedSafeRound(safe);
    setViewSafesOpen(true);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Error loading SAFEs: {error}</p>
            <Button onClick={refetchSafes} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoadingSafes) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <p>Loading SAFEs...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>SAFEs</CardTitle>
        <ActionButton
          onAuthorizeRound={() => setAuthorizeOpen(true)}
          onIssueIndividualSafes={() => setIssueOpen(true)}
          onIncreaseAuthorizedRound={() => setIncreaseOpen(true)}
          onRecordAuthorizedRound={() => setRecordOpen(true)}
        />
      </CardHeader>
      <CardContent className="space-y-8">

        {/* SAFEs Table */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
              <div>
                <h3 className="text-lg font-semibold">Aggregate SAFE Rounds</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Click the eye icon (👁️) on any row to view individual SAFEs for that round
                </p>
              </div>
              
              {/* Filter Controls */}
              <div className="flex items-center space-x-4">
                <Label className="text-sm font-medium">Filter:</Label>
                <RadioGroup 
                  value={filterType} 
                  onValueChange={(value) => setFilterType(value as 'all' | 'approved' | 'pending')}
                  className="flex items-center space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="approved" id="approved" />
                    <Label htmlFor="approved" className="text-sm">Approved Only</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pending" id="pending" />
                    <Label htmlFor="pending" className="text-sm">Pending</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="all" />
                    <Label htmlFor="all" className="text-sm">Show All</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {isLoadingSafes ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading SAFEs...</p>
              </div>
            ) : filteredSafes.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  {filterType === 'approved' && 'No approved SAFEs found.'}
                  {filterType === 'pending' && 'No pending SAFEs found.'}
                  {filterType === 'all' && 'No SAFEs found.'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date Authorized</TableHead>
                      <TableHead>Authorized Amount</TableHead>
                      <TableHead>Outstanding Principal</TableHead>
                      <TableHead>Valuation Cap</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>MFN</TableHead>
                      <TableHead>Board Approved</TableHead>
                      <TableHead className="w-16">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSafes.map((safe) => (
                      <TableRow key={safe.id}>
                        <TableCell>
                          {safe.boardApprovedDate 
                            ? format(new Date(safe.boardApprovedDate), "PP")
                            : 'N/A'
                          }
                        </TableCell>
                        <TableCell>${safe.totalAuthorizedAmountOfRound.toLocaleString()}</TableCell>
                        <TableCell>${safe.outstandingPrincipal.toLocaleString()}</TableCell>
                        <TableCell>${safe.valuationCap.toLocaleString()}</TableCell>
                        <TableCell>{safe.discount}%</TableCell>
                        <TableCell>{safe.mostFavoredNation ? 'Yes' : 'No'}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={safe.isActive ? "default" : "secondary"}
                            className={safe.isActive ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}
                          >
                            {safe.isActive ? 'Approved' : 'Pending'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewSafes(safe)}
                            className="h-8 w-8 p-0 hover:bg-muted"
                            title="View Individual SAFEs"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Authorize SAFE Round Dialog */}
        <Dialog open={authorizeOpen} onOpenChange={setAuthorizeOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Authorize SAFE Round</DialogTitle>
            </DialogHeader>
            <div className="py-4 overflow-auto">
              <AuthorizeSafeForm
                companyId={user?.companyId || ""}
                onSuccess={() => setAuthorizeOpen(false)}
              />
            </div>
          </DialogContent>
        </Dialog>

        {/* Issue Individual SAFEs Dialog */}
        <IssueIndividualSafesDialog
          open={issueOpen}
          onOpenChange={setIssueOpen}
          safes={safes}
          selectedSafeId={selectedSafeId}
          onSelectSafe={setSelectedSafeId}
          companyId={user?.companyId || ""}
        />

        <IncreaseAuthorizedSafeDialog
          open={increaseOpen}
          onOpenChange={setIncreaseOpen}
          safes={safes}
          companyId={user?.companyId || ""}
        />

        <RecordPopupDialog
          open={recordOpen}
          onOpenChange={setRecordOpen}
          safes={safes}
          companyId={user?.companyId || ""}
        />

        {/* View Individual SAFEs Dialog */}
        <ViewIndividualSafesDialog
          open={viewSafesOpen}
          onOpenChange={setViewSafesOpen}
          roundData={selectedSafeRound}
          companyId={user?.companyId || ""}
        />
      </CardContent>
    </Card>
  );
};

export default SafesFlow;
