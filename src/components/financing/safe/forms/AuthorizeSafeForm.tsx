import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { useAuthorizeSafe } from "@/hooks/financing/useAuthorizeSafe.hooks";
import { AuthorizeSafeRequest } from "@/types/financing";

interface Props {
  onSuccess: () => void;
  companyId: string;
}

// Form validation schema - only SAFE-specific fields
const formSchema = z.object({
  totalAuthorizedAmountOfRound: z
    .string()
    .min(1, "Total Authorized Amount is required")
    .refine((val) => {
      const num = parseFloat(val.replace(/[$,]/g, ""));
      return !isNaN(num) && num > 0;
    }, "Must be a valid amount greater than 0"),
  valuationCap: z
    .string()
    .min(1, "Valuation Cap is required")
    .refine((val) => {
      const num = parseFloat(val.replace(/[$,]/g, ""));
      return !isNaN(num) && num > 0;
    }, "Must be a valid amount greater than 0"),
  discount: z
    .string()
    .min(1, "Discount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 100;
    }, "Must be a valid percentage between 0 and 100"),
  mostFavoredNation: z.enum(["yes", "no"], {
    required_error: "MFN selection is required",
  }),
});

type FormData = z.infer<typeof formSchema>;

const AuthorizeSafeForm: React.FC<Props> = ({ onSuccess, companyId }) => {
  const authorizeSafeMutation = useAuthorizeSafe(companyId);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      totalAuthorizedAmountOfRound: "",
      valuationCap: "",
      discount: "",
      mostFavoredNation: "no",
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      const payload: AuthorizeSafeRequest = {
        totalAuthorizedAmountOfRound: parseFloat(
          data.totalAuthorizedAmountOfRound.replace(/[$,]/g, "")
        ),
        valuationCap: parseFloat(data.valuationCap.replace(/[$,]/g, "")),
        discount: parseFloat(data.discount),
        mostFavoredNation: data.mostFavoredNation === "yes",
      };

      await authorizeSafeMutation.mutateAsync(payload);
      form.reset();
      onSuccess();
    } catch (err) {
      // Error is already handled in the service with toast notifications
      console.error("Failed to create SAFE round:", err);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {authorizeSafeMutation.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">
              {authorizeSafeMutation.error.message}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
          <FormField
            control={form.control}
            name="totalAuthorizedAmountOfRound"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Authorized Amount of Round</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="$ 0.00"
                    className="font-mono"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="valuationCap"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Valuation Cap</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="$ 0.00"
                    className="font-mono"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="discount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Discount</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.00" className="font-mono" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="mostFavoredNation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>MFN (Most Favored Nation)</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select MFN" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Board Approval Section */}
        <div className="py-10 flex justify-end space-x-3">
          <Button type="submit" disabled={authorizeSafeMutation.isPending}>
            {authorizeSafeMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {authorizeSafeMutation.isPending
              ? "Submitting..."
              : "Submit for Board Approval"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default AuthorizeSafeForm;
