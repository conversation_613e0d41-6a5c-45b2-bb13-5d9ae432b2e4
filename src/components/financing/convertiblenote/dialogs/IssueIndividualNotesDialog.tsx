import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Upload, X, FileText } from "lucide-react";
import { format } from "date-fns";
import { ConvertibleNoteRoundResponse, IssueIndividualNoteRequest } from "@/types/financing";
import { cn } from "@/lib/utils";
import { useIssueIndividualNote } from "@/hooks/financing/useIssueIndividualNote.hooks";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { IssueIndividualNoteService } from "@/services/financing/issueIndividualNote.service";

interface IssueIndividualNotesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rounds: ConvertibleNoteRoundResponse[];
  selectedRoundId: string | null;
  onSelectRound: (roundId: string | null) => void;
  companyId: string;
}

const IssueIndividualNotesDialog: React.FC<IssueIndividualNotesDialogProps> = ({
  open,
  onOpenChange,
  rounds,
  selectedRoundId,
  onSelectRound,
  companyId,
}) => {
  // Filter to show only approved rounds (isActive: true)
  const approvedRounds = rounds.filter(round => round.isActive === true);
  
  const [formData, setFormData] = useState<Omit<IssueIndividualNoteRequest, 'authorizedRoundId'>>({
    principalAmount: 0,
    name: "",
    email: "",
    dateOfInvestment: "",
    includeProRataSideLetter: false,
    includeSideLetter: false,
    prorataS3Key: undefined,
    s3Key: undefined,
  });
  const [emailError, setEmailError] = useState<string>("");
  const [principalAmountError, setPrincipalAmountError] = useState<string>("");
  const [prorataSelectedFile, setProrataSelectedFile] = useState<File | null>(null);
  const [sideLetterSelectedFile, setSideLetterSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const { mutateAsync: issueIndividualNote, isPending: isIssuingNote } = useIssueIndividualNote(companyId);

  const handleRoundSelection = (roundId: string) => {
    onSelectRound(roundId);
    // Reset form when selecting a different round
    setFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
      includeProRataSideLetter: false,
      includeSideLetter: false,
      prorataS3Key: undefined,
      s3Key: undefined,
    });
    setEmailError("");
    setPrincipalAmountError("");
    setProrataSelectedFile(null);
    setSideLetterSelectedFile(null);
  };

  // Set default selection to first approved round if none selected
  React.useEffect(() => {
    if (approvedRounds.length > 0 && !selectedRoundId) {
      onSelectRound(approvedRounds[0].id);
    }
  }, [approvedRounds, selectedRoundId, onSelectRound]);

  const selectedRound = approvedRounds.find(r => r.id === selectedRoundId);

  // Calculate available amount for the selected round
  const getAvailableAmount = (round: ConvertibleNoteRoundResponse | undefined): number => {
    if (!round) return 0;
    return round.totalAuthorizedAmountOfRound - round.outstandingPrincipal;
  };

  const validatePrincipalAmount = (amount: number, round: ConvertibleNoteRoundResponse | undefined): string => {
    if (!round) return "";
    
    const availableAmount = getAvailableAmount(round);
    if (amount > availableAmount) {
      return `Amount cannot exceed available amount of $${availableAmount.toLocaleString()}`;
    }
    if (amount <= 0) {
      return "Amount must be greater than 0";
    }
    return "";
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleInputChange = (field: keyof typeof formData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear email error when user starts typing
    if (field === 'email') {
      setEmailError("");
    }

    // Validate principal amount in real-time
    if (field === 'principalAmount') {
      const error = validatePrincipalAmount(value as number, selectedRound);
      setPrincipalAmountError(error);
    }

    // Reset file and S3 key when unchecking prorata sideletter
    if (field === 'includeProRataSideLetter' && !value) {
      setProrataSelectedFile(null);
      setFormData(prev => ({
        ...prev,
        prorataS3Key: undefined,
      }));
    }

    // Reset file and S3 key when unchecking side letter
    if (field === 'includeSideLetter' && !value) {
      setSideLetterSelectedFile(null);
      setFormData(prev => ({
        ...prev,
        s3Key: undefined,
      }));
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        dateOfInvestment: format(date, "yyyy-MM-dd"),
      }));
    }
  };

  const handleProrataFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      
      // Validate file type - check both MIME type and file extension
      const allowedMimeTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      const allowedExtensions = ['.pdf', '.doc', '.docx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      const isValidMimeType = allowedMimeTypes.includes(file.type);
      const isValidExtension = allowedExtensions.includes(fileExtension);
      
      if (!isValidMimeType && !isValidExtension) {
        toast.error('Please select a PDF, DOC, or DOCX file');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setProrataSelectedFile(file);
    }
  };

  const handleSideLetterFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      
      // Validate file type - check both MIME type and file extension
      const allowedMimeTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      const allowedExtensions = ['.pdf', '.doc', '.docx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      const isValidMimeType = allowedMimeTypes.includes(file.type);
      const isValidExtension = allowedExtensions.includes(fileExtension);
      
      if (!isValidMimeType && !isValidExtension) {
        toast.error('Please select a PDF, DOC, or DOCX file');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setSideLetterSelectedFile(file);
    }
  };

  const removeProrataFile = () => {
    setProrataSelectedFile(null);
    setFormData(prev => ({
      ...prev,
      prorataS3Key: undefined,
    }));
  };

  const removeSideLetterFile = () => {
    setSideLetterSelectedFile(null);
    setFormData(prev => ({
      ...prev,
      s3Key: undefined,
    }));
  };

  const uploadProrataFile = async (): Promise<string | undefined> => {
    if (!prorataSelectedFile) return undefined;

    try {
      
      // Generate unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = prorataSelectedFile.name.split('.').pop();
      const key = `prorata-sideletter-${timestamp}.${extension}`;

      // Get presigned URL
      const uploadResponse = await IssueIndividualNoteService.getProrataUploadUrl(companyId, {
        key,
        contentType: prorataSelectedFile.type,
      });

      // Upload file to S3
      const uploadResult = await fetch(uploadResponse.data.uploadUrl, {
        method: 'PUT',
        body: prorataSelectedFile,
        headers: {
          'Content-Type': prorataSelectedFile.type,
        },
      });

      if (!uploadResult.ok) {
        throw new Error('Failed to upload file to S3');
      }

      return uploadResponse.data.key;
    } catch (error) {
      toast.error('Failed to upload prorata sideletter file');
      console.error('uploadProrataFile: Upload error:', error);
      return undefined;
    }
  };

  const uploadSideLetterFile = async (): Promise<string | undefined> => {
    if (!sideLetterSelectedFile) return undefined;

    try {
      
      // Generate unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = sideLetterSelectedFile.name.split('.').pop();
      const key = `side-letter-${timestamp}.${extension}`;

      // Get presigned URL
      const uploadResponse = await IssueIndividualNoteService.getSideLetterUploadUrl(companyId, {
        key,
        contentType: sideLetterSelectedFile.type,
      });

      // Upload file to S3
      const uploadResult = await fetch(uploadResponse.data.uploadUrl, {
        method: 'PUT',
        body: sideLetterSelectedFile,
        headers: {
          'Content-Type': sideLetterSelectedFile.type,
        },
      });

      if (!uploadResult.ok) {
        throw new Error('Failed to upload file to S3');
      }

      return uploadResponse.data.key;
    } catch (error) {
      toast.error('Failed to upload side letter file');
      console.error('uploadSideLetterFile: Upload error:', error);
      return undefined;
    }
  };

  const handleSubmit = async () => {
    if (!selectedRoundId) {
      toast.error("Please select a round first");
      return;
    }

    if (!formData.principalAmount || !formData.name || !formData.email || !formData.dateOfInvestment) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!validateEmail(formData.email)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    // Validate principal amount
    const principalAmountError = validatePrincipalAmount(formData.principalAmount, selectedRound);
    if (principalAmountError) {
      setPrincipalAmountError(principalAmountError);
      toast.error(principalAmountError);
      return;
    }

    // If prorata sideletter is included but no file is selected, show error
    if (formData.includeProRataSideLetter && !prorataSelectedFile) {
      toast.error("Please select a file for the prorata sideletter");
      return;
    }

    // If side letter is included but no file is selected, show error
    if (formData.includeSideLetter && !sideLetterSelectedFile) {
      toast.error("Please select a file for the side letter");
      return;
    }

    // If prorata sideletter is not included, ensure no file is selected
    if (!formData.includeProRataSideLetter && prorataSelectedFile) {
      setProrataSelectedFile(null);
    }

    // If side letter is not included, ensure no file is selected
    if (!formData.includeSideLetter && sideLetterSelectedFile) {
      setSideLetterSelectedFile(null);
    }

    try {
      let finalProrataS3Key: string | undefined;
      let finalSideLetterS3Key: string | undefined;

      // Upload prorata file first if included
      if (formData.includeProRataSideLetter && prorataSelectedFile) {
        setIsUploading(true);
        finalProrataS3Key = await uploadProrataFile();
        if (!finalProrataS3Key) {
          toast.error("Failed to upload prorata sideletter document");
          setIsUploading(false);
          return;
        }
        setIsUploading(false);
      }

      // Upload side letter file if included
      if (formData.includeSideLetter && sideLetterSelectedFile) {
        setIsUploading(true);
        finalSideLetterS3Key = await uploadSideLetterFile();
        if (!finalSideLetterS3Key) {
          toast.error("Failed to upload side letter document");
          setIsUploading(false);
          return;
        }
        setIsUploading(false);
      }

      // Create payload with the uploaded S3 keys
      const payload: IssueIndividualNoteRequest = {
        authorizedRoundId: selectedRoundId!,
        principalAmount: formData.principalAmount,
        name: formData.name,
        email: formData.email,
        dateOfInvestment: formData.dateOfInvestment,
        includeProRataSideLetter: formData.includeProRataSideLetter,
        includeSideLetter: formData.includeSideLetter,
        prorataS3Key: finalProrataS3Key,
        s3Key: finalSideLetterS3Key,
      };

      await issueIndividualNote(payload);
      
      // Close dialog and reset form
      onOpenChange(false);
      onSelectRound(null);
      setFormData({
        principalAmount: 0,
        name: "",
        email: "",
        dateOfInvestment: "",
        includeProRataSideLetter: false,
        includeSideLetter: false,
        prorataS3Key: undefined,
        s3Key: undefined,
      });
      setEmailError("");
      setPrincipalAmountError("");
      setProrataSelectedFile(null);
      setSideLetterSelectedFile(null);
    } catch (error) {
      // Error is already handled by the service
      console.error("Failed to issue individual note:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[900px] w-[95vw] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Issue Individual Notes</DialogTitle>
        </DialogHeader>
        
        <div className="overflow-y-auto max-h-[calc(90vh-180px)] pr-2 space-y-6">
          {/* Select Round Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Select Approved Round</h3>
            {approvedRounds.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">No approved rounds available</p>
                <p className="text-sm mt-2">Please authorize a round first before issuing individual notes</p>
              </div>
            ) : (
              <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <div className="max-h-[300px] overflow-y-auto">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background z-10">
                        <TableRow>
                          <TableHead className="w-16 px-4 py-3">Select</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Date Authorized</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Maturity Date</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Authorized Amount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Outstanding Principal</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Interest Rate</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Valuation Cap</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">Discount</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">MFN</TableHead>
                          <TableHead className="px-4 py-3 whitespace-nowrap">QFT</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {approvedRounds.map((round) => (
                          <TableRow 
                            key={round.id} 
                            className={cn(
                              "hover:bg-muted/50 cursor-pointer",
                              selectedRoundId === round.id && "bg-blue-50 border-blue-200"
                            )}
                            onClick={() => handleRoundSelection(round.id)}
                          >
                            <TableCell className="px-4 py-3">
                              <RadioGroup
                                value={selectedRoundId || ""}
                                onValueChange={handleRoundSelection}
                              >
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem value={round.id} id={round.id} />
                                  <Label htmlFor={round.id} className="sr-only">Select round</Label>
                                </div>
                              </RadioGroup>
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {round.boardApprovedDate ? format(new Date(round.boardApprovedDate), "PP") : "N/A"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {round.maturityDate ? format(new Date(round.maturityDate), "PP") : "N/A"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap font-medium">
                              ${round.totalAuthorizedAmountOfRound.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">${round.outstandingPrincipal.toLocaleString()}</TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">{round.interestRate}%</TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              ${round.valuationCap.toLocaleString()}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">{round.discount}%</TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              {round.mostFavoredNation ? "Yes" : "No"}
                            </TableCell>
                            <TableCell className="px-4 py-3 whitespace-nowrap">
                              ${round.qualifiedFinancingThreshold.toLocaleString()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Section - Always visible */}
          <div className="border-t pt-8 pb-6">
            <h3 className="text-lg font-semibold mb-6">Note Details</h3>
            
            {!selectedRoundId ? (
              <div className="text-center py-12 text-muted-foreground bg-muted/30 rounded-lg">
                <p className="text-base">Please select a round above to issue individual notes</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6 p-1">
                <div className="space-y-3">
                  <Label htmlFor="principalAmount" className="text-sm font-medium">
                    Principal Amount ($) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="principalAmount"
                    type="number"
                    placeholder="0.00"
                    value={formData.principalAmount || ""}
                    onChange={(e) => handleInputChange("principalAmount", parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                    className={cn("h-11", principalAmountError && "border-red-500 focus:border-red-500")}
                  />
                  {selectedRound && (
                    <p className="text-xs text-muted-foreground">
                      Available amount: ${getAvailableAmount(selectedRound).toLocaleString()}
                    </p>
                  )}
                  {principalAmountError && (
                    <p className="text-sm text-red-500">{principalAmountError}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Investor Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    type="text"
                    placeholder="Enter investor name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    className="h-11"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Investor Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter investor email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className={cn("h-11", emailError && "border-red-500 focus:border-red-500")}
                  />
                  {emailError && (
                    <p className="text-sm text-red-500 mt-1">{emailError}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="dateOfInvestment" className="text-sm font-medium">
                    Date of Investment <span className="text-red-500">*</span>
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal h-11",
                          !formData.dateOfInvestment && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.dateOfInvestment ? format(new Date(formData.dateOfInvestment), "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.dateOfInvestment ? new Date(formData.dateOfInvestment) : undefined}
                        onSelect={handleDateSelect}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Document Upload Section */}
                <div className="lg:col-span-2 space-y-4">
                  <div className="text-sm font-medium text-muted-foreground mb-3">Additional Documents</div>
                  
                  {/* Prorata Side Letter */}
                  <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                    <Checkbox
                      id="prorataSideLetter"
                      checked={formData.includeProRataSideLetter}
                      onCheckedChange={(checked) => {
                        const booleanValue = checked === true;
                        handleInputChange("includeProRataSideLetter", booleanValue);
                      }}
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <Label htmlFor="prorataSideLetter" className="text-sm font-medium cursor-pointer">
                        Include prorata
                      </Label>
                      {formData.includeProRataSideLetter && (
                        <div className="mt-2">
                          {!prorataSelectedFile ? (
                            <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                              <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-muted-foreground">Upload prorata document</p>
                                <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                              </div>
                              <Input
                                type="file"
                                accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                onChange={handleProrataFileSelect}
                                className="hidden"
                                id="prorataSideLetterFileInput"
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => document.getElementById('prorataSideLetterFileInput')?.click()}
                                className="h-7 px-2 text-xs"
                              >
                                Browse
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs font-medium text-green-800 truncate">{prorataSelectedFile.name}</p>
                                <p className="text-xs text-green-600">{(prorataSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={removeProrataFile}
                                className="h-6 w-6 p-0 hover:bg-green-100"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Side Letter */}
                  <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                    <Checkbox
                      id="sideLetter"
                      checked={formData.includeSideLetter}
                      onCheckedChange={(checked) => {
                        const booleanValue = checked === true;
                        handleInputChange("includeSideLetter", booleanValue);
                      }}
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <Label htmlFor="sideLetter" className="text-sm font-medium cursor-pointer">
                        Include side letter
                      </Label>
                      {formData.includeSideLetter && (
                        <div className="mt-2">
                          {!sideLetterSelectedFile ? (
                            <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                              <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-muted-foreground">Upload side letter document</p>
                                <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                              </div>
                              <Input
                                type="file"
                                accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                onChange={handleSideLetterFileSelect}
                                className="hidden"
                                id="sideLetterFileInput"
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => document.getElementById('sideLetterFileInput')?.click()}
                                className="h-7 px-2 text-xs"
                              >
                                Browse
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs font-medium text-green-800 truncate">{sideLetterSelectedFile.name}</p>
                                <p className="text-xs text-green-600">{(sideLetterSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={removeSideLetterFile}
                                className="h-6 w-6 p-0 hover:bg-green-100"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t bg-muted/20 px-6 -mx-6">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={!selectedRoundId || isIssuingNote || isUploading || approvedRounds.length === 0}
            className="w-full sm:w-auto"
          >
            {isIssuingNote || isUploading ? "Processing..." : "Issue"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IssueIndividualNotesDialog;
