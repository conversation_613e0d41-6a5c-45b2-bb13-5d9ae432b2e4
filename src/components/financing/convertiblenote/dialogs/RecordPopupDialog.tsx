import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import RecordAuthorizedRoundForm from "@/components/financing/convertiblenote/forms/RecordAuthorizedRoundForm";
import { ConvertibleNoteRoundResponse, ConvertibleNoteRound } from "@/types/financing";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Upload, X, FileText } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useRecordIssueIndividualNote } from "@/hooks/financing/useIssueIndividualNote.hooks";
import { uploadFileToS3, generateFileName } from "@/utils/s3Upload";
import { IssueIndividualNoteService } from "@/services/financing/issueIndividualNote.service";

interface RecordPopupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rounds: ConvertibleNoteRoundResponse[];
  companyId: string;
}

const RecordPopupDialog: React.FC<RecordPopupDialogProps> = ({
  open,
  onOpenChange,
  rounds,
  companyId,
}) => {
  const [selectedOption, setSelectedOption] = useState<"issued-note" | "authorized-round">("issued-note");
  
  // Form state for Record Authorized Round
  const [fileUpload, setFileUpload] = useState<{ file: File | null }>({ file: null });

  // Form state for Issue Individual Notes
  const [selectedRoundId, setSelectedRoundId] = useState<string | null>(null);
  const [issuedNoteFormData, setIssuedNoteFormData] = useState({
    principalAmount: 0,
    name: "",
    email: "",
    dateOfInvestment: "",
    includeProrataSideLetter: false,
    includeSideLetter: false,
  });
  const [issuedNoteEmailError, setIssuedNoteEmailError] = useState<string>("");
  const [issuedNoteSelectedFile, setIssuedNoteSelectedFile] = useState<File | null>(null);
  const [prorataSelectedFile, setProrataSelectedFile] = useState<File | null>(null);
  const [sideLetterSelectedFile, setSideLetterSelectedFile] = useState<File | null>(null);

  // Hooks for Issue Individual Note
  const { mutateAsync: recordNote, isPending: isRecordingNote } = useRecordIssueIndividualNote(companyId);
  const [isUploading, setIsUploading] = useState(false);

  const form = useForm({
    defaultValues: {
      totalAuthorizedAmountOfRound: "",
      valuationCap: "",
      discount: "",
      mostFavoredNation: "",
      boardApprovedDate: null as Date | null,
    },
  });

  // Set default selection to first approved round if none selected
  React.useEffect(() => {
    if (rounds.length > 0 && !selectedRoundId) {
      const firstApprovedRound = rounds.find(round => round.isActive === true);
      if (firstApprovedRound) {
        setSelectedRoundId(firstApprovedRound.id);
      }
    }
  }, [rounds, selectedRoundId]);

  const handleClose = () => {
    onOpenChange(false);
    setSelectedOption("issued-note");
    
    // Reset all form states
    setSelectedRoundId(null);
    setIssuedNoteFormData({
      principalAmount: 0,
      name: "",
      email: "",
      dateOfInvestment: "",
      includeProrataSideLetter: false,
      includeSideLetter: false,
    });
    setIssuedNoteEmailError("");
    setIssuedNoteSelectedFile(null);
    setProrataSelectedFile(null);
    setSideLetterSelectedFile(null);
    setFileUpload({ file: null });
    
    // Reset the form for Authorized Round
    form.reset();
  };

  const handleAuthorizedRoundCreated = (round: ConvertibleNoteRound) => {
    // Handle successful creation
    onOpenChange(false);
  };

  // Handler functions for Issue Individual Notes form
  const handleIssuedNoteInputChange = (field: keyof typeof issuedNoteFormData, value: string | number | boolean) => {
    setIssuedNoteFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear email error when user starts typing
    if (field === 'email') {
      setIssuedNoteEmailError("");
    }
  };

  const handleIssuedNoteDateSelect = (date: Date | undefined) => {
    if (date) {
      setIssuedNoteFormData(prev => ({
        ...prev,
        dateOfInvestment: format(date, "yyyy-MM-dd"),
      }));
    }
  };

  const handleIssuedNoteSubmit = async () => {
    if (!selectedRoundId) {
      toast.error("Please select a round first");
      return;
    }

    if (!issuedNoteFormData.principalAmount || !issuedNoteFormData.name || !issuedNoteFormData.email || !issuedNoteFormData.dateOfInvestment) {
      toast.error("Please fill in all required fields");
      return;
    }

    // All documents are optional, so we don't require files even when checkboxes are checked

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(issuedNoteFormData.email)) {
      setIssuedNoteEmailError("Please enter a valid email address");
      return;
    }

    try {
      setIsUploading(true);
      let s3Key = "";
      let prorataS3Key = "";
      let optionalS3Key = "";

      // Build presigned URL requests in parallel
      const presignTasks: Array<Promise<{ kind: "optional" | "prorata" | "sideletter"; uploadUrl: string; key: string; file: File }>> = [];

      if (issuedNoteSelectedFile) {
        const fileName = generateFileName(issuedNoteSelectedFile.name);
        presignTasks.push(
          IssueIndividualNoteService.getOptionalDocumentUploadUrl(companyId, {
            key: fileName,
            contentType: issuedNoteSelectedFile.type,
          }).then((res) => {
            if ((res as any).error) throw new Error("Failed to get optional upload URL");
            return { kind: "optional", uploadUrl: res.data.uploadUrl, key: res.data.key, file: issuedNoteSelectedFile };
          })
        );
      }

      if (issuedNoteFormData.includeProrataSideLetter && prorataSelectedFile) {
        const fileName = generateFileName(prorataSelectedFile.name);
        presignTasks.push(
          IssueIndividualNoteService.getProrataUploadUrl(companyId, {
            key: fileName,
            contentType: prorataSelectedFile.type,
          }).then((res) => {
            if ((res as any).error) throw new Error("Failed to get prorata upload URL");
            return { kind: "prorata", uploadUrl: res.data.uploadUrl, key: res.data.key, file: prorataSelectedFile };
          })
        );
      }

      if (issuedNoteFormData.includeSideLetter && sideLetterSelectedFile) {
        const fileName = generateFileName(sideLetterSelectedFile.name);
        presignTasks.push(
          IssueIndividualNoteService.getSideLetterUploadUrl(companyId, {
            key: fileName,
            contentType: sideLetterSelectedFile.type,
          }).then((res) => {
            if ((res as any).error) throw new Error("Failed to get side letter upload URL");
            return { kind: "sideletter", uploadUrl: res.data.uploadUrl, key: res.data.key, file: sideLetterSelectedFile };
          })
        );
      }

      const presigned = await Promise.all(presignTasks);

      // Upload all files concurrently
      const uploadResults = await Promise.all(
        presigned.map(async (item) => {
          const result = await uploadFileToS3(item.file, item.uploadUrl);
          if (!result.success) {
            throw new Error(result.error || "Upload failed");
          }
          return { kind: item.kind, key: item.key };
        })
      );

      // Assign keys by kind
      uploadResults.forEach((r) => {
        if (r.kind === "optional") s3Key = r.key;
        if (r.kind === "prorata") prorataS3Key = r.key;
        if (r.kind === "sideletter") optionalS3Key = r.key;
      });

      // Record the convertible note
      await recordNote({
        authorizedRoundId: selectedRoundId,
        principalAmount: issuedNoteFormData.principalAmount,
        name: issuedNoteFormData.name,
        email: issuedNoteFormData.email,
        dateOfInvestment: issuedNoteFormData.dateOfInvestment,
        approvedDate: new Date().toISOString().split('T')[0], // Current date as approved date
        prorataS3Key: prorataS3Key || undefined,
        s3Key: s3Key || undefined,
        optionalS3Key: optionalS3Key || undefined,
      });
      
      // Close dialog and reset form
      onOpenChange(false);
      setSelectedRoundId(null);
      setIssuedNoteFormData({
        principalAmount: 0,
        name: "",
        email: "",
        dateOfInvestment: "",
        includeProrataSideLetter: false,
        includeSideLetter: false,
      });
      setIssuedNoteEmailError("");
      setIssuedNoteSelectedFile(null);
      setProrataSelectedFile(null);
      setSideLetterSelectedFile(null);
      setIsUploading(false);
    } catch (error) {
      toast.error("Failed to issue convertible note", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
     setIsUploading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Record</DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto max-h-[calc(90vh-120px)] pr-2">
            <div className="space-y-6">
              <div className="space-y-4">
                <RadioGroup
                  value={selectedOption}
                  onValueChange={(value: "issued-note" | "authorized-round") => setSelectedOption(value)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="issued-note" id="issued-note" />
                    <Label htmlFor="issued-note" className="text-sm font-medium cursor-pointer">
                      Issued Note
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="authorized-round" id="authorized-round" />
                    <Label htmlFor="authorized-round" className="text-sm font-medium cursor-pointer">
                      Authorized Round
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Show form immediately when Authorized Round is selected */}
              {selectedOption === "authorized-round" && (
                <div className="border-t pt-6">
                  <RecordAuthorizedRoundForm onCreated={handleAuthorizedRoundCreated} />
                </div>
              )}

              {/* Show form immediately when Issued Note is selected */}
              {selectedOption === "issued-note" && (
                <div className="border-t pt-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-6">
                        {/* Select Round Section */}
                        <div>
                          <h3 className="text-lg font-semibold mb-4">Select Approved Round</h3>
                          {rounds.filter(round => round.isActive === true).length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                              <p className="text-base">No approved rounds available</p>
                              <p className="text-sm mt-2">Please authorize a round first before issuing individual notes</p>
                            </div>
                          ) : (
                            <div className="border rounded-lg overflow-hidden">
                              <div className="overflow-x-auto">
                                <div className="max-h-[300px] overflow-y-auto">
                                  <Table>
                                    <TableHeader className="sticky top-0 bg-background z-10">
                                      <TableRow>
                                        <TableHead className="w-16 px-4 py-3">Select</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Date Authorized</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Authorized Amount</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Valuation Cap</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">Discount</TableHead>
                                        <TableHead className="px-4 py-3 whitespace-nowrap">MFN</TableHead>
                                      </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                      {rounds.filter(round => round.isActive === true).map((round) => (
                                        <TableRow 
                                          key={round.id} 
                                          className={cn(
                                            "hover:bg-muted/50 cursor-pointer",
                                            selectedRoundId === round.id && "bg-blue-50 border-blue-200"
                                          )}
                                          onClick={() => setSelectedRoundId(round.id)}
                                        >
                                          <TableCell className="px-4 py-3">
                                            <RadioGroup
                                              value={selectedRoundId || ""}
                                              onValueChange={setSelectedRoundId}
                                            >
                                              <div className="flex items-center space-x-2">
                                                <RadioGroupItem value={round.id} id={round.id} />
                                                <Label htmlFor={round.id} className="sr-only">Select round</Label>
                                              </div>
                                            </RadioGroup>
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            {round.boardApprovedDate ? format(new Date(round.boardApprovedDate), "PP") : "N/A"}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap font-medium">
                                            ${round.totalAuthorizedAmountOfRound.toLocaleString()}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            ${round.valuationCap.toLocaleString()}
                                          </TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">{round.discount}%</TableCell>
                                          <TableCell className="px-4 py-3 whitespace-nowrap">
                                            {round.mostFavoredNation ? "Yes" : "No"}
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Note Details Form */}
                        <div className="border-t pt-8 pb-6">
                          <h3 className="text-lg font-semibold mb-6">Note Details</h3>
                          
                          {!selectedRoundId ? (
                            <div className="text-center py-12 text-muted-foreground bg-muted/30 rounded-lg">
                              <p className="text-base">Please select a round above to issue individual notes</p>
                            </div>
                          ) : (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-8 gap-y-6 p-1">
                              <div className="space-y-3">
                                <Label htmlFor="principalAmount" className="text-sm font-medium">
                                  Principal Amount ($) <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="principalAmount"
                                  type="number"
                                  placeholder="0.00"
                                  value={issuedNoteFormData.principalAmount || ""}
                                  onChange={(e) => handleIssuedNoteInputChange("principalAmount", parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="h-11"
                                />
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="name" className="text-sm font-medium">
                                  Investor Name <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="name"
                                  type="text"
                                  placeholder="Enter investor name"
                                  value={issuedNoteFormData.name}
                                  onChange={(e) => handleIssuedNoteInputChange("name", e.target.value)}
                                  className="h-11"
                                />
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="email" className="text-sm font-medium">
                                  Investor Email <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="email"
                                  type="email"
                                  placeholder="Enter investor email"
                                  value={issuedNoteFormData.email}
                                  onChange={(e) => handleIssuedNoteInputChange("email", e.target.value)}
                                  className={cn("h-11", issuedNoteEmailError && "border-red-500 focus:border-red-500")}
                                />
                                {issuedNoteEmailError && (
                                  <p className="text-sm text-red-500 mt-1">{issuedNoteEmailError}</p>
                                )}
                              </div>

                              <div className="space-y-3">
                                <Label htmlFor="dateOfInvestment" className="text-sm font-medium">
                                  Date of Investment <span className="text-red-500">*</span>
                                </Label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "w-full justify-start text-left font-normal h-11",
                                        !issuedNoteFormData.dateOfInvestment && "text-muted-foreground"
                                      )}
                                    >
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      {issuedNoteFormData.dateOfInvestment ? format(new Date(issuedNoteFormData.dateOfInvestment), "PPP") : "Pick a date"}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <Calendar
                                      mode="single"
                                      selected={issuedNoteFormData.dateOfInvestment ? new Date(issuedNoteFormData.dateOfInvestment) : undefined}
                                      onSelect={handleIssuedNoteDateSelect}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                              </div>
                            </div>
                          )}

                          {/* Document Upload Section */}
                          <div className="mt-6 space-y-4">
                            <div className="text-sm font-medium text-muted-foreground mb-3">Additional Documents</div>
                            
                            {/* Prorata Side Letter */}
                            <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                              <input
                                type="checkbox"
                                id="includeProrataSideLetter"
                                checked={issuedNoteFormData.includeProrataSideLetter}
                                onChange={(e) => handleIssuedNoteInputChange("includeProrataSideLetter", e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                              />
                              <div className="flex-1 min-w-0">
                                <Label htmlFor="includeProrataSideLetter" className="text-sm font-medium cursor-pointer">
                                  Include prorata
                                </Label>
                                {issuedNoteFormData.includeProrataSideLetter && (
                                  <div className="mt-2">
                                    {!prorataSelectedFile ? (
                                      <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                                        <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                        <div className="flex-1 min-w-0">
                                          <p className="text-xs text-muted-foreground">Upload prorata document</p>
                                          <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                                        </div>
                                        <input
                                          type="file"
                                          className="hidden"
                                          accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                              // Validate file type and size
                                              const allowedTypes = [
                                                'application/pdf',
                                                'application/msword',
                                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                              ];
                                              
                                              if (!allowedTypes.includes(file.type)) {
                                                toast.error("Please select a PDF, DOC, or DOCX file");
                                                return;
                                              }
                                              
                                              if (file.size > 5 * 1024 * 1024) {
                                                toast.error("File size must be less than 5MB");
                                                return;
                                              }
                                              
                                              setProrataSelectedFile(file);
                                            }
                                          }}
                                          id="prorataFileInput"
                                        />
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => document.getElementById('prorataFileInput')?.click()}
                                          className="h-7 px-2 text-xs"
                                        >
                                          Browse
                                        </Button>
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                        <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                                        <div className="flex-1 min-w-0">
                                          <p className="text-xs font-medium text-green-800 truncate">{prorataSelectedFile.name}</p>
                                          <p className="text-xs text-green-600">{(prorataSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                                        </div>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => setProrataSelectedFile(null)}
                                          className="h-6 w-6 p-0 hover:bg-green-100"
                                        >
                                          <X className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Side Letter */}
                            <div className="flex items-center gap-3 p-3 rounded-lg border border-muted/50 hover:border-muted transition-colors">
                              <input
                                type="checkbox"
                                id="includeSideLetter"
                                checked={issuedNoteFormData.includeSideLetter}
                                onChange={(e) => handleIssuedNoteInputChange("includeSideLetter", e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                              />
                              <div className="flex-1 min-w-0">
                                <Label htmlFor="includeSideLetter" className="text-sm font-medium cursor-pointer">
                                  Include side letter
                                </Label>
                                {issuedNoteFormData.includeSideLetter && (
                                  <div className="mt-2">
                                    {!sideLetterSelectedFile ? (
                                      <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-md border border-dashed border-muted-foreground/30">
                                        <Upload className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                        <div className="flex-1 min-w-0">
                                          <p className="text-xs text-muted-foreground">Upload side letter document</p>
                                          <p className="text-xs text-muted-foreground/70">PDF, DOC, DOCX • Max 5MB</p>
                                        </div>
                                        <input
                                          type="file"
                                          className="hidden"
                                          accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                              // Validate file type and size
                                              const allowedTypes = [
                                                'application/pdf',
                                                'application/msword',
                                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                              ];
                                              
                                              if (!allowedTypes.includes(file.type)) {
                                                toast.error("Please select a PDF, DOC, or DOCX file");
                                                return;
                                              }
                                              
                                              if (file.size > 5 * 1024 * 1024) {
                                                toast.error("File size must be less than 5MB");
                                                return;
                                              }
                                              
                                              setSideLetterSelectedFile(file);
                                            }
                                          }}
                                          id="sideLetterFileInput"
                                        />
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => document.getElementById('sideLetterFileInput')?.click()}
                                          className="h-7 px-2 text-xs"
                                        >
                                          Browse
                                        </Button>
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                        <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                                        <div className="flex-1 min-w-0">
                                          <p className="text-xs font-medium text-green-800 truncate">{sideLetterSelectedFile.name}</p>
                                          <p className="text-xs text-green-600">{(sideLetterSelectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                                        </div>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => setSideLetterSelectedFile(null)}
                                          className="h-6 w-6 p-0 hover:bg-green-100"
                                        >
                                          <X className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Document Upload Section */}
                        <div className="border-t pt-8 pb-6">
                          <h3 className="text-lg font-semibold mb-6">Executed Note</h3>
                          <div className="space-y-2">
                            {issuedNoteSelectedFile ? (
                              <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/50">
                                <span className="text-sm text-muted-foreground">{issuedNoteSelectedFile.name}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setIssuedNoteSelectedFile(null)}
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="flex items-center justify-center w-full">
                                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground">
                                      <span className="font-semibold">Click to upload</span> or drag and drop
                                    </p>
                                    <p className="text-xs text-muted-foreground">PDF or DOCX (MAX. 5MB)</p>
                                  </div>
                                  <input
                                    type="file"
                                    className="hidden"
                                    accept=".pdf,.docx"
                                    onChange={(e) => {
                                      const file = e.target.files?.[0];
                                      if (file) {
                                        // Validate file type and size
                                        const allowedTypes = [
                                          'application/pdf',
                                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                        ];
                                        
                                        if (!allowedTypes.includes(file.type)) {
                                          toast.error("Please select a PDF or DOCX file");
                                          return;
                                        }
                                        
                                        if (file.size > 5 * 1024 * 1024) {
                                          toast.error("File size must be less than 5MB");
                                          return;
                                        }
                                        
                                        setIssuedNoteSelectedFile(file);
                                      }
                                    }}
                                  />
                                </label>
                              </div>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-2">
                            Note: Documents uploaded here will not be sent out to investors. This is for internal record keeping only.
                          </p>
                        </div>



                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t bg-muted/20 px-6 -mx-6">
                          <Button 
                            variant="outline" 
                            onClick={handleClose}
                            className="w-full sm:w-auto"
                          >
                            Cancel
                          </Button>
                          <Button 
                            onClick={handleIssuedNoteSubmit}
                            disabled={!selectedRoundId || rounds.filter(round => round.isActive === true).length === 0 || isUploading || isRecordingNote}
                            className="w-full sm:w-auto"
                          >
                            {isUploading || isRecordingNote ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                {isUploading ? "Uploading..." : "Issuing Note..."}
                              </>
                            ) : (
                              "Issue Note"
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default RecordPopupDialog;
