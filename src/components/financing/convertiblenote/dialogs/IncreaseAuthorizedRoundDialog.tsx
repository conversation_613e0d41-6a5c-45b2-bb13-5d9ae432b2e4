import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { ConvertibleNoteRoundResponse } from "@/types/financing";
import { useIncreaseAuthorizedRound } from "@/hooks/financing/useIncreaseAuthorizedRound.hooks";
import { toast } from "sonner";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  rounds: ConvertibleNoteRoundResponse[];
  companyId: string;
}

interface FormData {
  selectedRoundId: string;
  authorizedAmount: string;
}

const IncreaseAuthorizedRoundDialog: React.FC<Props> = ({
  open,
  onOpenChange,
  rounds,
  companyId,
}) => {
  const [selectedRound, setSelectedRound] =
    useState<ConvertibleNoteRoundResponse | null>(null);
  const { requestRoundIncrease, isRequestingIncrease, error } =
    useIncreaseAuthorizedRound(companyId);

  const form = useForm<FormData>({
    defaultValues: {
      selectedRoundId: "",
      authorizedAmount: "",
    },
  });

  // Filter to show only approved rounds
  const approvedRounds = rounds.filter((round) => round.isActive === true);

  // Set the top row as selected by default when component mounts or rounds change
  useEffect(() => {
    if (approvedRounds.length > 0 && !selectedRound) {
      const firstRound = approvedRounds[0];
      setSelectedRound(firstRound);
      form.setValue("selectedRoundId", firstRound.id);
    }
  }, [approvedRounds, selectedRound, form]);

  const handleRoundSelection = (roundId: string) => {
    const round = approvedRounds.find((r) => r.id === roundId);
    setSelectedRound(round || null);
    form.setValue("selectedRoundId", roundId);
  };

  const handleSubmit = async (data: FormData) => {
    if (!data.selectedRoundId || !data.authorizedAmount) {
      toast.error("Please select a round and enter an increase amount");
      return;
    }

    const increaseAmount = parseFloat(data.authorizedAmount);
    if (isNaN(increaseAmount) || increaseAmount <= 0) {
      toast.error("Please enter a valid increase amount");
      return;
    }

    try {
      await requestRoundIncrease({
        authorizedRoundId: data.selectedRoundId,
        authorizedAmount: increaseAmount,
      });

      toast.success("Round increase request submitted successfully");
      onOpenChange(false);
      form.reset();
      setSelectedRound(null);
    } catch (error) {
      // Error is already handled by the service
      console.error("Failed to submit round increase:", error);
    }
  };

  const currentAmount = selectedRound?.totalAuthorizedAmountOfRound || 0;
  const increaseAmount = parseFloat(form.watch("authorizedAmount") || "0");
  const newTotal = currentAmount + increaseAmount;

  return (
    <div className="max-w-[900px] mx-auto">
      <div className="space-y-6">
        {/* Round Selection Table */}
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-semibold mb-4">
              Select Round to Increase
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Only approved rounds are available for increase requests.
            </p>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Select</TableHead>
                    <TableHead>Date Authorized</TableHead>
                    <TableHead>Maturity Date</TableHead>
                    <TableHead>Authorized Amount</TableHead>
                    <TableHead>Outstanding Principal</TableHead>
                    <TableHead>Interest Rate</TableHead>
                    <TableHead>Valuation Cap</TableHead>
                    <TableHead>Discount</TableHead>
                    <TableHead>MFN</TableHead>
                    <TableHead>QFT</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {approvedRounds.map((round) => (
                    <TableRow
                      key={round.id}
                      className={`cursor-pointer hover:bg-gray-50 ${
                        selectedRound?.id === round.id
                          ? "bg-blue-50 border-blue-200"
                          : ""
                      }`}
                      onClick={() => handleRoundSelection(round.id)}
                    >
                      <TableCell>
                        <RadioGroup
                          value={form.watch("selectedRoundId")}
                          onValueChange={handleRoundSelection}
                        >
                          <RadioGroupItem value={round.id} id={round.id} />
                        </RadioGroup>
                      </TableCell>
                      <TableCell>
                        {round.boardApprovedDate
                          ? format(new Date(round.boardApprovedDate), "PP")
                          : "N/A"}
                      </TableCell>
                      <TableCell>
                        {round.maturityDate
                          ? format(new Date(round.maturityDate), "PP")
                          : "N/A"}
                      </TableCell>
                      <TableCell>
                        ${round.totalAuthorizedAmountOfRound.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        ${round.outstandingPrincipal.toLocaleString()}
                      </TableCell>
                      <TableCell>{round.interestRate}%</TableCell>
                      <TableCell>
                        ${round.valuationCap.toLocaleString()}
                      </TableCell>
                      <TableCell>{round.discount}%</TableCell>
                      <TableCell>
                        {round.mostFavoredNation ? "Yes" : "No"}
                      </TableCell>
                      <TableCell>
                        ${round.qualifiedFinancingThreshold.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="default"
                          className="bg-green-100 text-green-800"
                        >
                          Approved
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {approvedRounds.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  No approved rounds available for increase.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Increase Amount Form */}
        {selectedRound && (
          <Card>
            <CardContent className="pt-6">
              <h3 className="text-lg font-semibold mb-4">Increase Amount</h3>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(handleSubmit)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        Current Amount
                      </Label>
                      <div className="p-3 bg-gray-50 rounded-md border">
                        <span className="text-lg font-semibold">
                          ${currentAmount.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <FormField
                        control={form.control}
                        name="authorizedAmount"
                        rules={{
                          required: "Increase amount is required",
                          min: {
                            value: 0.01,
                            message: "Amount must be greater than 0",
                          },
                          validate: (value) => {
                            const num = parseFloat(value);
                            if (isNaN(num))
                              return "Please enter a valid number";
                            if (num <= 0)
                              return "Amount must be greater than 0";
                            return true;
                          },
                        }}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Increase Amount</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                                  $
                                </span>
                                <Input
                                  {...field}
                                  type="number"
                                  placeholder="0.00"
                                  className="pl-8"
                                  step="0.01"
                                  min="0.01"
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">New Total</Label>
                      <div className="p-3 bg-blue-50 rounded-md border border-blue-200">
                        <span className="text-lg font-semibold text-blue-700">
                          ${newTotal.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="py-10">
                    <div className="flex justify-end space-x-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                        disabled={isRequestingIncrease}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={
                          isRequestingIncrease ||
                          !selectedRound ||
                          !form.watch("authorizedAmount")
                        }
                      >
                        {isRequestingIncrease
                          ? "Submitting..."
                          : "Submit for Board Approval"}
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* Error Display - Only show when there's an actual error */}
        {error && error !== "null" && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default IncreaseAuthorizedRoundDialog;
