import React, { useMemo, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/common/Card";
import AuthorizeRoundForm from "@/components/financing/convertiblenote/forms/AuthorizeRoundForm";
import RoundsTable from "@/components/financing/convertiblenote/tables/RoundsTable";
import IssueIndividualNotesDialog from "@/components/financing/convertiblenote/dialogs/IssueIndividualNotesDialog";
import IncreaseAuthorizedRoundDialog from "@/components/financing/convertiblenote/dialogs/IncreaseAuthorizedRoundDialog";
import { ViewIndividualNotesDialog } from "@/components/financing/convertiblenote/dialogs/ViewIndividualNotesDialog";
import RecordPopupDialog from "@/components/financing/convertiblenote/dialogs/RecordPopupDialog";
import ActionButton from "@/components/financing/convertiblenote/ActionButton";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useAuthorizedRoundList } from "../../../hooks/financing/useAuthorizedRoundList.hooks";
import { ConvertibleNoteRoundResponse } from "@/types/financing";
import { useAuth } from "@/contexts/AuthContext";

const ConvertibleNotesFlow: React.FC = () => {
  const { user } = useAuth();
  const [selectedRoundId, setSelectedRoundId] = useState<string | null>(null);
  const [authorizeOpen, setAuthorizeOpen] = useState(false);
  const [issueOpen, setIssueOpen] = useState(false);
  const [increaseOpen, setIncreaseOpen] = useState(false);
  const [recordOpen, setRecordOpen] = useState(false);
  const [viewNotesOpen, setViewNotesOpen] = useState(false);
  const [selectedRoundForNotes, setSelectedRoundForNotes] = useState<ConvertibleNoteRoundResponse | null>(null);

  // Use the new hook to fetch data from API with filtering
  const { 
    rounds, 
    filteredRounds, 
    isLoadingRounds, 
    error, 
    refetchRounds,
    filterType,
    setFilterType 
  } = useAuthorizedRoundList(user?.companyId || "");

  const selectedRound = useMemo(
    () => rounds.find((r) => r.id === selectedRoundId) || null,
    [rounds, selectedRoundId]
  );

  const handleCreateRound = () => {
    // Close the dialog and refetch data
    setAuthorizeOpen(false);
    refetchRounds();
  };

  const handleUpdateRound = (updated: ConvertibleNoteRoundResponse) => {
    // For now, just close the dialog and refetch data
    // In the future, this should call an API to update the round
    setIncreaseOpen(false);
    refetchRounds();
  };

  const handleViewNotes = (round: ConvertibleNoteRoundResponse) => {
    setSelectedRoundForNotes(round);
    setViewNotesOpen(true);
  };

  const handleCloseViewNotes = () => {
    setViewNotesOpen(false);
    setSelectedRoundForNotes(null);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Error loading rounds: {error}</p>
            <Button onClick={refetchRounds} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoadingRounds) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <p>Loading rounds...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Convertible Notes</CardTitle>
        <ActionButton
          onAuthorizeRound={() => setAuthorizeOpen(true)}
          onIssueIndividualNotes={() => setIssueOpen(true)}
          onIncreaseAuthorizedRound={() => setIncreaseOpen(true)}
          onRecordAuthorizedRound={() => setRecordOpen(true)}
        />
      </CardHeader>
      <CardContent className="space-y-8">
        <RoundsTable 
          rounds={filteredRounds} 
          filterType={filterType}
          onFilterChange={setFilterType}
          onRefresh={refetchRounds}
          onViewNotes={handleViewNotes}
        />

        <Dialog open={authorizeOpen} onOpenChange={setAuthorizeOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Authorize Round</DialogTitle>
            </DialogHeader>
            <div className="overflow-y-auto max-h-[calc(90vh-120px)] pr-2">
              <AuthorizeRoundForm onSuccess={handleCreateRound} companyId={user?.companyId || ""} />
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={increaseOpen} onOpenChange={setIncreaseOpen}>
          <DialogContent className="max-w-[900px] max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Increase Authorized Round</DialogTitle>
            </DialogHeader>
            <div className="overflow-y-auto max-h-[calc(90vh-120px)] pr-2">
              <IncreaseAuthorizedRoundDialog
                open={increaseOpen}
                onOpenChange={setIncreaseOpen}
                rounds={rounds}
                companyId={user?.companyId || ""}
              />
            </div>
          </DialogContent>
        </Dialog>

        <RecordPopupDialog
          open={recordOpen}
          onOpenChange={setRecordOpen}
          rounds={rounds}
          companyId={user?.companyId || ""}
        />

        <IssueIndividualNotesDialog
          open={issueOpen}
          onOpenChange={setIssueOpen}
          rounds={rounds}
          selectedRoundId={selectedRoundId}
          onSelectRound={setSelectedRoundId}
          companyId={user?.companyId || ""}
        />

        <ViewIndividualNotesDialog
          open={viewNotesOpen}
          onOpenChange={setViewNotesOpen}
          roundData={selectedRoundForNotes}
          companyId={user?.companyId || ""}
        />
      </CardContent>
    </Card>
  );
};

export default ConvertibleNotesFlow;


