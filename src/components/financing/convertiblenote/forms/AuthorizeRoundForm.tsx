import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent } from "@/components/common/Card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useAuthorizeRound } from "@/hooks/financing/useAuthorizeRound.hooks";
import { AuthorizeRoundRequest } from "@/types/financing";

interface Props {
  onSuccess: () => void;
  companyId: string;
}

// Form validation schema
const formSchema = z.object({
  totalAuthorizedAmountOfRound: z
    .string()
    .min(1, "Total Authorized Amount is required")
    .refine((val) => {
      const num = parseFloat(val.replace(/[$,]/g, ""));
      return !isNaN(num) && num > 0;
    }, "Must be a valid amount greater than 0"),
  interestRate: z
    .string()
    .min(1, "Interest Rate is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 100;
    }, "Must be a valid percentage between 0 and 100"),
  interestType: z.enum(["simple-interest", "compound-interest"], {
    required_error: "Interest Type is required",
  }),
  maturityDate: z
    .date({
      required_error: "Maturity Date is required",
    })
    .refine((date) => date > new Date(), "Maturity Date must be in the future"),
  valuationCap: z
    .string()
    .min(1, "Valuation Cap is required")
    .refine((val) => {
      const num = parseFloat(val.replace(/[$,]/g, ""));
      return !isNaN(num) && num > 0;
    }, "Must be a valid amount greater than 0"),
  discount: z
    .string()
    .min(1, "Discount is required")
    .refine((val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 0 && num <= 100;
    }, "Must be a valid percentage between 0 and 100"),
  mostFavoredNation: z.boolean(),
  qualifiedFinancingThreshold: z
    .string()
    .min(1, "Qualified Financing Threshold is required")
    .refine((val) => {
      const num = parseFloat(val.replace(/[$,]/g, ""));
      return !isNaN(num) && num >= 0;
    }, "Must be a valid amount greater than or equal to 0"),
});

type FormData = z.infer<typeof formSchema>;

const AuthorizeRoundForm: React.FC<Props> = ({ onSuccess, companyId }) => {
  const { createAuthorizeRound, isCreatingRound, error } =
    useAuthorizeRound(companyId);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      totalAuthorizedAmountOfRound: "",
      interestRate: "",
      interestType: "simple-interest",
      maturityDate: undefined,
      valuationCap: "",
      discount: "",
      mostFavoredNation: false,
      qualifiedFinancingThreshold: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      const payload: AuthorizeRoundRequest = {
        qualifiedFinancingThreshold: parseFloat(
          data.qualifiedFinancingThreshold.replace(/[$,]/g, "")
        ),
        totalAuthorizedAmountOfRound: parseFloat(
          data.totalAuthorizedAmountOfRound.replace(/[$,]/g, "")
        ),
        interestRate: parseFloat(data.interestRate),
        interestType: data.interestType,
        maturityDate: data.maturityDate.toISOString().split("T")[0], // ISO date string
        valuationCap: parseFloat(data.valuationCap.replace(/[$,]/g, "")),
        discount: parseFloat(data.discount),
        mostFavoredNation: data.mostFavoredNation,
      };

      await createAuthorizeRound(payload);
      form.reset();
      onSuccess();
    } catch (err) {
      // Error is already handled in the service with toast notifications
      console.error("Failed to create authorize round:", err);
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <h3 className="text-lg font-semibold mb-4">Round Details</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Please include the terms of the convertible note.
        </p>

        <Form {...(form as any)}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <fieldset disabled={isCreatingRound} className="space-y-6">
              <FormField
                control={form.control}
                name="totalAuthorizedAmountOfRound"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Authorized Amount of Round</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">
                          $
                        </span>
                        <Input
                          {...field}
                          type="text"
                          placeholder="0.00"
                          className="pl-8"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="interestRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Interest Rate</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type="text"
                            placeholder="0.00"
                            className="pr-8"
                          />
                          <span className="absolute right-3 top-1/2 -translate-y-1/2">
                            %
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="interestType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Interest Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select interest type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="simple-interest">
                            Simple Interest
                          </SelectItem>
                          <SelectItem value="compound-interest">
                            Compound Interest
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="maturityDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Maturity Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="valuationCap"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valuation Cap</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 -translate-y-1/2">
                            $
                          </span>
                          <Input
                            {...field}
                            type="text"
                            placeholder="0.00"
                            className="pl-8"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type="text"
                            placeholder="0.00"
                            className="pr-8"
                          />
                          <span className="absolute right-3 top-1/2 -translate-y-1/2">
                            %
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="mostFavoredNation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>MFN (Most Favored Nation)</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value === "yes")}
                      defaultValue={field.value ? "yes" : "no"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select yes or no" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="yes">Yes</SelectItem>
                        <SelectItem value="no">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="qualifiedFinancingThreshold"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Qualified Financing Threshold</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">
                          $
                        </span>
                        <Input
                          {...field}
                          type="text"
                          placeholder="0.00"
                          className="pl-8"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="p-4 rounded-md bg-red-50 border border-red-200">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <div className="py-10 flex justify-end space-x-3">
                <Button type="submit" disabled={isCreatingRound}>
                  {isCreatingRound && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {isCreatingRound
                    ? "Creating Round..."
                    : "Submit for Board Approval"}
                </Button>
              </div>
            </fieldset>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AuthorizeRoundForm;
