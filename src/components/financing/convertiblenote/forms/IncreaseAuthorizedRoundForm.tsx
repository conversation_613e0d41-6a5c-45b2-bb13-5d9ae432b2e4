import React from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ConvertibleNoteRoundResponse } from "@/types/financing";

interface Props {
  rounds: ConvertibleNoteRoundResponse[];
  onChanged: (round: ConvertibleNoteRoundResponse) => void;
}

const IncreaseAuthorizedRoundForm: React.FC<Props> = ({ rounds, onChanged }) => {
  const form = useForm({
    defaultValues: {
      roundId: rounds[0]?.id || "",
      increaseAmount: "",
    },
  });

  const onSubmit = (data: any) => {
    const r = rounds.find((x) => x.id === data.roundId);
    if (!r) return;
    // For now, we'll just call onChanged with the original round
    // In the future, this should call an API to request the increase
    onChanged(r);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <h3 className="text-lg font-semibold mb-4">Increase Authorized Round</h3>
        <Form {...(form as any)}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="roundId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Round</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select round" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rounds.map((r) => (
                        <SelectItem key={r.id} value={r.id}>
                          Round {r.id.slice(0, 8)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="increaseAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Increase Amount</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                      <Input {...field} type="text" placeholder="0.00" className="pl-8" />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="border p-4 rounded-md bg-gray-50">
              <h4 className="font-medium mb-2">Board Approval</h4>
              <Button type="submit">Submit Increase for Board Approval</Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default IncreaseAuthorizedRoundForm;


