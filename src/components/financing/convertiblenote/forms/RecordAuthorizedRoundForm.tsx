import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent } from "@/components/common/Card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Upload, X, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ConvertibleNoteRound } from "@/types/financing";
import { useAuth } from "@/contexts/AuthContext";
import { useRecordAuthorizedRound } from "@/hooks/financing/useRecordAuthorizedRound.hooks";
import { toast } from "sonner";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

interface Props {
  onCreated: (round: ConvertibleNoteRound) => void;
}

// Validation schema
const formSchema = z.object({
  authorizedAmount: z.string().min(1, "Authorized amount is required"),
  interestRate: z.string().min(1, "Interest rate is required"),
  interestType: z.enum(["simple-interest", "compound-interest"]),
  maturityDate: z.date({ required_error: "Maturity date is required" }),
  valuationCap: z.string().min(1, "Valuation cap is required"),
  discount: z.string().min(1, "Discount is required"),
  mfn: z.enum(["yes", "no"]),
  qualifiedFinancingThreshold: z.string().min(1, "Qualified financing threshold is required"),
});

type FormData = z.infer<typeof formSchema>;

const RecordAuthorizedRoundForm: React.FC<Props> = ({ onCreated }) => {
  const { user } = useAuth();
  const { recordRound, isRecording } = useRecordAuthorizedRound();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      authorizedAmount: "",
      interestRate: "",
      interestType: "simple-interest",
      maturityDate: undefined,
      valuationCap: "",
      discount: "",
      mfn: "no",
      qualifiedFinancingThreshold: "",
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }

    if (!user?.companyId) {
      toast.error("Company ID not found");
      return;
    }

    try {
      // Prepare form data without s3Key
      const formData = {
        qualifiedFinancingThreshold: parseFloat(data.qualifiedFinancingThreshold) || 0,
        totalAuthorizedAmountOfRound: parseFloat(data.authorizedAmount) || 0,
        interestRate: parseFloat(data.interestRate) || 0,
        interestType: data.interestType,
        maturityDate: format(data.maturityDate, "yyyy-MM-dd"),
        valuationCap: parseFloat(data.valuationCap) || 0,
        discount: parseFloat(data.discount) || 0,
        mostFavoredNation: data.mfn === "yes",
        boardApprovedDate: new Date().toISOString().split('T')[0],
      };

      // Record the authorized round (hook handles file upload + round creation)
      const result = await recordRound(selectedFile, formData, user.companyId);
      if (result) {
        onCreated(result as any);
        form.reset();
        setSelectedFile(null);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to record authorized round");
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-900 mb-6">
          Use this workflow to record any round authorized outside of the platform.
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="authorizedAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Authorized Amount of Round *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                      <Input {...field} type="text" placeholder="0.00" className="pl-8" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="interestRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Rate *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input {...field} type="text" placeholder="0.00" className="pr-8" />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2">%</span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="interestType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Type *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select interest type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="simple-interest">Simple Interest</SelectItem>
                        <SelectItem value="compound-interest">Compound Interest</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="maturityDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Maturity Date *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button variant="outline" className={cn("w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                        >
                          {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value || undefined}
                        onSelect={field.onChange}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="valuationCap"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valuation Cap *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                        <Input {...field} type="text" placeholder="0.00" className="pl-8" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Discount *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input {...field} type="text" placeholder="0.00" className="pr-8" />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2">%</span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="mfn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>MFN (Most Favored Nation) *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select yes or no" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="qualifiedFinancingThreshold"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Qualified Financing Threshold *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                      <Input {...field} type="text" placeholder="0.00" className="pl-8" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItem>
              <FormLabel>Upload Authorized Round Form *</FormLabel>
              <div className="space-y-2">
                {selectedFile ? (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/50">
                    <span className="text-sm text-muted-foreground">{selectedFile.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedFile(null)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center w-full">
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-muted-foreground">PDF or DOCX (MAX. 5MB)</p>
                      </div>
                      <input
                        type="file"
                        className="hidden"
                        accept=".pdf,.docx"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setSelectedFile(file);
                          }
                        }}
                      />
                    </label>
                  </div>
                )}
              </div>
            </FormItem>

            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={isRecording || !selectedFile}
                className="min-w-[120px]"
              >
                {isRecording ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Round"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default RecordAuthorizedRoundForm;


