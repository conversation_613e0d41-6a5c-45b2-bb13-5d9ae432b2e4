import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

type EntryMode = "authorize" | "issue" | "increase" | "record";

interface Props {
  value: EntryMode;
  onChange: (value: EntryMode) => void;
}

export const EntryModeSelector: React.FC<Props> = ({ value, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Entry Mode</h3>
      <div className="grid gap-3 md:grid-cols-2">
        <label className="flex items-start gap-3 rounded-md border p-4 cursor-pointer">
          <RadioGroup value={value} onValueChange={(v) => onChange(v as EntryMode)}>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="authorize" id="authorize" />
              <span className="font-medium">Authorize Round</span>
            </div>
          </RadioGroup>
        </label>

        <label className="flex items-start gap-3 rounded-md border p-4 cursor-pointer">
          <RadioGroup value={value} onValueChange={(v) => onChange(v as EntryMode)}>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="issue" id="issue" />
              <span className="font-medium">Issue Individual Notes</span>
            </div>
          </RadioGroup>
        </label>

        <label className="flex items-start gap-3 rounded-md border p-4 cursor-pointer">
          <RadioGroup value={value} onValueChange={(v) => onChange(v as EntryMode)}>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="increase" id="increase" />
              <span className="font-medium">Increase Authorized Round</span>
            </div>
          </RadioGroup>
        </label>

        <label className="flex items-start gap-3 rounded-md border p-4 cursor-pointer">
          <RadioGroup value={value} onValueChange={(v) => onChange(v as EntryMode)}>
            <div className="flex items-center gap-3">
              <RadioGroupItem value="record" id="record" />
              <span className="font-medium">Record Authorized Round</span>
            </div>
          </RadioGroup>
        </label>
      </div>
    </div>
  );
};


