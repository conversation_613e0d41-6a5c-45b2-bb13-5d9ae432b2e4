import React from "react";
import { Card, CardContent } from "@/components/common/Card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ConvertibleNoteRoundResponse } from "@/types/financing";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";

interface Props {
  rounds: ConvertibleNoteRoundResponse[];
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  filterType?: 'all' | 'approved' | 'pending';
  onFilterChange?: (filter: 'all' | 'approved' | 'pending') => void;
  onViewNotes?: (round: ConvertibleNoteRoundResponse) => void;
}

const RoundsTable: React.FC<Props> = ({ 
  rounds, 
  isLoading = false, 
  error = null, 
  onRefresh, 
  filterType = 'approved',
  onFilterChange,
  onViewNotes
}) => {
  const handleFilterChange = (value: string) => {
    if (onFilterChange) {
      onFilterChange(value as 'all' | 'approved' | 'pending');
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
          <div>
            <h3 className="text-lg font-semibold">Aggregate Note Rounds</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Click the eye icon (👁️) on any row to view individual notes for that round
            </p>
          </div>
          
          {/* Filter Controls */}
          {onFilterChange && (
            <div className="flex items-center space-x-4">
              <Label className="text-sm font-medium">Filter:</Label>
              <RadioGroup 
                value={filterType} 
                onValueChange={handleFilterChange}
                className="flex items-center space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="approved" id="approved" />
                  <Label htmlFor="approved" className="text-sm">Approved Only</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pending" id="pending" />
                  <Label htmlFor="pending" className="text-sm">Pending</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all" className="text-sm">Show All</Label>
                </div>
              </RadioGroup>
            </div>
          )}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
            {onRefresh && (
              <button 
                onClick={onRefresh}
                className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
              >
                Retry
              </button>
            )}
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Loading rounds...</p>
          </div>
        ) : rounds.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {filterType === 'approved' && 'No approved rounds found.'}
              {filterType === 'pending' && 'No pending rounds found.'}
              {filterType === 'all' && 'No rounds found.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date Authorized</TableHead>
                  <TableHead>Maturity Date</TableHead>
                  <TableHead>Authorized Amount</TableHead>
                  <TableHead>Outstanding Principal</TableHead>
                  <TableHead>Interest Rate</TableHead>
                  <TableHead>Valuation Cap</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>MFN</TableHead>
                  <TableHead>QFT</TableHead>
                  <TableHead>Board Approved</TableHead>
                  <TableHead className="w-16">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {rounds.map((r) => (
                  <TableRow key={r.id}>
                    <TableCell>
                      {r.boardApprovedDate ? format(new Date(r.boardApprovedDate), "PP") : "N/A"}
                    </TableCell>
                    <TableCell>
                      {r.maturityDate ? format(new Date(r.maturityDate), "PP") : "N/A"}
                    </TableCell>
                    <TableCell>${r.totalAuthorizedAmountOfRound.toLocaleString()}</TableCell>
                    <TableCell>${r.outstandingPrincipal.toLocaleString()}</TableCell>
                    <TableCell>{r.interestRate}%</TableCell>
                    <TableCell>${r.valuationCap.toLocaleString()}</TableCell>
                    <TableCell>{r.discount}%</TableCell>
                    <TableCell>{r.mostFavoredNation ? "Yes" : "No"}</TableCell>
                    <TableCell>${r.qualifiedFinancingThreshold.toLocaleString()}</TableCell>
                    <TableCell>
                      <Badge 
                        variant={r.isActive ? "default" : "secondary"}
                        className={r.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                      >
                        {r.isActive ? "Approved" : "Pending"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewNotes?.(r)}
                        className="h-8 w-8 p-0 hover:bg-muted"
                        title="View Individual Notes"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RoundsTable;


