import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Plus, FileText, TrendingUp, Upload } from "lucide-react";

interface ActionButtonProps {
  onAuthorizeRound: () => void;
  onIssueIndividualNotes: () => void;
  onIncreaseAuthorizedRound: () => void;
  onRecordAuthorizedRound: () => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  onAuthorizeRound,
  onIssueIndividualNotes,
  onIncreaseAuthorizedRound,
  onRecordAuthorizedRound,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="default" className="flex items-center gap-2">
          Action
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={onAuthorizeRound} className="cursor-pointer">
          <Plus className="mr-2 h-4 w-4" />
          Authorize Round
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onIssueIndividualNotes} className="cursor-pointer">
          <FileText className="mr-2 h-4 w-4" />
          Issue Individual Notes
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onIncreaseAuthorizedRound} className="cursor-pointer">
          <TrendingUp className="mr-2 h-4 w-4" />
          Increase Authorized Round
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onRecordAuthorizedRound} className="cursor-pointer">
          <Upload className="mr-2 h-4 w-4" />
          Record
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ActionButton;
