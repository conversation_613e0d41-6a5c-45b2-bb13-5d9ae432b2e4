"use client";

import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { useEditorStore } from "@/store/use-editor-store";

import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import {
  GitCommitIcon,
  ClockIcon,
  ChevronDownIcon,
  ChevronRight,
  HistoryIcon,
} from "lucide-react";

import { Commit } from "../extensions/change-tracker";
import { extractCommitTitle } from "../extensions/change-tracker/utils";

export const CommitsSheet: React.FC = () => {
  const { editor } = useEditorStore();

  const [open, setOpen] = useState(false);
  const [commits, setCommits] = useState<Commit[]>([]);
  const [highlightedCommit, setHighlightedCommit] = useState<Commit | null>(
    null
  );
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingCommit, setPendingCommit] = useState<Commit | null>(null);

  const loadCommits = () => {
    if (editor?.state) {
      editor.commands.getTrackState();
      const trackState = editor.storage.trackchange;
      const commits =
        trackState.checkedOutCommitIndex === -1
          ? trackState?.currentTrackState?.commits
          : trackState?.latestState?.trackState.commits;
      setCommits(commits || []);
    }
  };

  const handleOpenChange = (next: boolean) => {
    setOpen(next);
    if (next) loadCommits();
  };

  const handleContinueEditing = () => {
    return new Promise<void>((resolve) => {
      if (!editor) return resolve();
      editor.commands.returnToLatest();
      editor.setEditable(true);
      setHighlightedCommit(null);
      setOpen(false);
      setTimeout(() => {
        editor.commands.focus();
        resolve();
      }, 0);
    });
  };

  const checkOutCommit = (commit: Commit, force: boolean) => {
    editor?.commands.checkoutCommit(commit, force);
    editor?.setEditable(false);
    setHighlightedCommit(commit);
    setOpen(false);
  };

  const handleConfirmCheckout = () => {
    if (!editor || !pendingCommit) return;
    checkOutCommit(pendingCommit, true);
    setShowConfirmModal(false);
    setPendingCommit(null);
  };

  const handleCommitClick = async (commit: Commit) => {
    if (!editor) return;
    const hasUncommitted = editor.commands.hasUncommittedChanges();

    if (hasUncommitted) {
      setPendingCommit(commit);
      setShowConfirmModal(true);
      return;
    }

    if (highlightedCommit) {
      await handleContinueEditing();
    }

    checkOutCommit(commit, false);
  };

  const formatTimestamp = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "short",
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <>
      <Dialog open={showConfirmModal} onOpenChange={setShowConfirmModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Uncommitted Changes</DialogTitle>
            <DialogDescription>
              You have uncommitted changes. Continuing will discard these
              changes. Do you want to proceed?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowConfirmModal(false);
                setPendingCommit(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleConfirmCheckout}>Continue</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Sheet open={open} onOpenChange={handleOpenChange}>
        <SheetTrigger asChild>
          <button className="h-7 min-w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80 px-1.5 overflow-hidden text-sm">
            <HistoryIcon className="size-4" />
          </button>
        </SheetTrigger>
        <SheetContent
          side="right"
          className="w-[360px] sm:max-w-md p-0 flex flex-col h-full"
        >
          <SheetHeader className="px-4 py-3 border-b">
            <SheetTitle>Document History</SheetTitle>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            {commits.length === 0 ? (
              <div className="p-6 text-center text-sm text-gray-500">
                <ClockIcon className="size-8 mx-auto mb-2 text-gray-400" />
                <p>No commits yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Save changes to see commit history
                </p>
              </div>
            ) : (
              <div className="p-3">
                <div className="px-1 py-2 text-xs font-medium text-gray-500 border-b">
                  Document History ({commits.length} commits)
                </div>
                <div className="mt-2 space-y-1">
                  {commits.map((commit, index) => (
                    <div
                      key={index}
                      className={cn(
                        "flex flex-col items-start p-3 cursor-pointer rounded-md hover:bg-neutral-50",
                        highlightedCommit === commit &&
                          "bg-yellow-50 border-l-2 border-l-yellow-400"
                      )}
                      onClick={() => handleCommitClick(commit)}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm font-medium text-gray-900 truncate flex-1">
                          {extractCommitTitle(commit)}
                        </span>
                        {highlightedCommit === commit && (
                          <span className="text-xs text-yellow-600 ml-2">
                            Viewing
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 mt-1">
                        {formatTimestamp(commit.time)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {highlightedCommit && (
            <div className="border-t p-3">
              <Button
                variant="default"
                className="w-full justify-center"
                onClick={handleContinueEditing}
              >
                <div>Continue editing document</div>
                <ChevronRight />
              </Button>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
};
