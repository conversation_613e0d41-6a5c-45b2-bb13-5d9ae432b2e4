import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

export interface ToolbarButtonProps {
  onClick: () => void;
  isActive?: boolean;
  icon: LucideIcon;
  label: string;
  className?: string;
  disabled?: boolean; // Added disabled prop
}

export const ToolbarButton = ({
  onClick,
  isActive,
  icon: Icon,
  label,
  className,
  disabled = false, // Default to false
}: ToolbarButtonProps) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "h-7 min-w-7 shrink-0 flex items-center justify-center rounded-sm hover:bg-neutral-200/80",
        isActive && "bg-neutral-200/80",
        disabled && "opacity-50 pointer-events-none", // Style for disabled
        className
      )}
      title={label}
      disabled={disabled} // Pass disabled prop to button
    >
      <Icon className="size-4" />
    </button>
  );
};
