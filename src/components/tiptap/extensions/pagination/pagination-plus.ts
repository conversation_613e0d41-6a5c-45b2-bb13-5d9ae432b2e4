import { Extension } from "@tiptap/core";
import { EditorState, Plugin, Plugin<PERSON>ey } from "@tiptap/pm/state";
import { Decoration, DecorationSet, EditorView } from "@tiptap/pm/view";

export interface MarginConfig {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

export type PaperSize = "A4" | "Letter" | "Legal" | "A3" | "A5";

// Paper size dimensions in millimeters (width x height)
const PAPER_SIZES: Record<PaperSize, { width: number; height: number }> = {
  A4: { width: 210, height: 310 },
  Letter: { width: 215.9, height: 279.4 },
  Legal: { width: 215.9, height: 355.6 },
  A3: { width: 297, height: 420 },
  A5: { width: 148, height: 210 },
};

// Helper function to get paper dimensions
export const getPaperDimensions = (paperSize: PaperSize) =>
  PAPER_SIZES[paperSize];

// Helper function to convert mm to pixels (assuming 96 DPI)
export const mmToPixels = (mm: number) => (mm * 96) / 25.4;

// Helper function to convert pixels to mm
export const pixelsToMm = (pixels: number) => (pixels * 25.4) / 96;

interface PaginationPlusOptions {
  pageHeight: number;
  pageGap: number;
  pageBreakBackground: string;
  pageHeaderHeight: number;
  pageGapBorderSize: number;
  footerRight: string;
  footerLeft: string;
  headerRight: string;
  headerLeft: string;
  defaultMarginConfig: MarginConfig;
  defaultPaperSize: PaperSize;
}
const page_count_meta_key = "PAGE_COUNT_META_KEY";
export const PaginationPlus = Extension.create<PaginationPlusOptions>({
  name: "PaginationPlus",
  addOptions() {
    return {
      pageHeight: 800,
      pageGap: 50,
      pageGapBorderSize: 1,
      pageBreakBackground: "#e2e8f0",
      pageHeaderHeight: 10,
      footerRight: "{page}",
      footerLeft: "",
      headerRight: "",
      headerLeft: "",
      defaultMarginConfig: {
        top: 24.85,
        right: 24.85,
        bottom: 25.4,
        left: 25.4,
      },
      defaultPaperSize: "A4" as PaperSize,
    };
  },
  onCreate() {
    const targetNode = this.editor.view.dom;
    targetNode.classList.add("rm-with-pagination");
    const config = { attributes: true };
    const _pageHeaderHeight = this.options.pageHeaderHeight;
    const _pageHeight = this.options.pageHeight - _pageHeaderHeight * 2;
    const _marginConfig = this.options.defaultMarginConfig;

    const style = document.createElement("style");
    style.dataset.rmPaginationStyle = "";

    style.textContent = `
      .rm-with-pagination {
        padding-top: ${_marginConfig.top}mm;
        padding-bottom: ${_marginConfig.bottom}mm;
        padding-left: ${_marginConfig.left}mm;
        padding-right: ${_marginConfig.right}mm;
        background-color: #ffffff;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        width: 210mm;
        position: relative;
      }

      .rm-with-pagination .rm-page-break:last-child .rm-pagination-gap {
        display: none;
      }
      .rm-with-pagination .rm-page-break:last-child .rm-page-header {
        display: none;
      }

      .rm-with-pagination table tr td,
      .rm-with-pagination table tr th {
        word-break: break-word;
      }
      .rm-with-pagination table > tr {
        display: grid;
        min-width: 100%;
      }
      .rm-with-pagination table {
        border-collapse: collapse;
        width: 100%;
        display: contents;
      }
      .rm-with-pagination table tbody{
        display: table;
        max-height: 300px;
        overflow-y: auto;
      }
      .rm-with-pagination table tbody > tr{
        display: table-row !important;
      }
      .rm-with-pagination p:has(br.ProseMirror-trailingBreak:only-child) {
        display: table;
        width: 100%;
      }
      .rm-with-pagination .table-row-group {
        max-height: ${_pageHeight}px;
        overflow-y: auto;
        width: 100%;
      }
      .rm-with-pagination .rm-page-footer-left,
      .rm-with-pagination .rm-page-footer-right,
      .rm-with-pagination .rm-page-header-left,
      .rm-with-pagination .rm-page-header-right {
        display: inline-block;
      }
      .rm-with-pagination .rm-page-header-left,
      .rm-with-pagination .rm-page-header-right{
        padding-top: 15px !important;
      }

      .rm-with-pagination .rm-page-header-left,
      .rm-with-pagination .rm-page-footer-left{
        float: left;
        margin-left: ${_marginConfig.left}mm;
      }
      .rm-with-pagination .rm-page-header-right,
      .rm-with-pagination .rm-page-footer-right{
        float: right;
        margin-right: ${_marginConfig.right}mm;
      }

      .rm-with-pagination .rm-first-page-header{
        display: inline-flex;
        justify-content: space-between;
        width: 100%;
      }
    `;
    document.head.appendChild(style);

    const refreshPage = (targetNode: HTMLElement) => {
      const paginationElement = targetNode.querySelector(
        "[data-rm-pagination]"
      );
      if (paginationElement) {
        const lastPageBreak = paginationElement.lastElementChild?.querySelector(
          ".breaker"
        ) as HTMLElement;
        if (lastPageBreak) {
          const minHeight =
            lastPageBreak.offsetTop + lastPageBreak.offsetHeight;
          targetNode.style.minHeight = `${minHeight}px`;
        }
      }
    };

    const callback = (
      mutationList: MutationRecord[],
      observer: MutationObserver
    ) => {
      if (mutationList.length > 0 && mutationList[0].target) {
        const _target = mutationList[0].target as HTMLElement;
        if (_target.classList.contains("rm-with-pagination")) {
          const currentPageCount = getExistingPageCount(this.editor.view);
          const pageCount = calculatePageCount(this.editor.view, this.options);
          if (currentPageCount !== pageCount) {
            const tr = this.editor.view.state.tr.setMeta(
              page_count_meta_key,
              Date.now()
            );
            this.editor.view.dispatch(tr);
          }

          refreshPage(_target);
        }
      }
    };
    const observer = new MutationObserver(callback);
    observer.observe(targetNode, config);
    refreshPage(targetNode);
  },
  addProseMirrorPlugins() {
    const pageOptions = this.options;
    const editor = this.editor;
    return [
      new Plugin({
        key: new PluginKey("pagination"),

        state: {
          init(_, state) {
            const widgetList = createDecoration(state, pageOptions);
            return DecorationSet.create(state.doc, widgetList);
          },
          apply(tr, oldDeco, oldState, newState) {
            // Handle document restoration from backend
            if (
              tr.getMeta("isRestoration") ||
              tr.getMeta("forcePaginationUpdate")
            ) {
              const widgetList = createDecoration(newState, pageOptions);
              return DecorationSet.create(newState.doc, [...widgetList]);
            }

            if (!tr.docChanged) {
              return oldDeco;
            }

            const currentPageCount = getExistingPageCount(editor.view);
            const pageCount = calculatePageCount(editor.view, pageOptions);
            if ((pageCount > 1 ? pageCount : 1) !== currentPageCount) {
              const widgetList = createDecoration(newState, pageOptions);
              return DecorationSet.create(newState.doc, [...widgetList]);
            }
            return oldDeco;
          },
        },

        props: {
          decorations(state: EditorState) {
            return this.getState(state) as DecorationSet;
          },
        },
      }),
    ];
  },
});

const getExistingPageCount = (view: EditorView) => {
  const editorDom = view.dom;
  const paginationElement = editorDom.querySelector("[data-rm-pagination]");
  if (paginationElement) {
    return paginationElement.children.length;
  }
  return 0;
};
const calculatePageCount = (
  view: EditorView,
  pageOptions: PaginationPlusOptions
) => {
  const editorDom = view.dom;

  // Calculate effective page content area by subtracting headers and margins
  const topMarginPixels = mmToPixels(pageOptions.defaultMarginConfig.top);
  const bottomMarginPixels = mmToPixels(pageOptions.defaultMarginConfig.bottom);
  const pageContentAreaHeight =
    pageOptions.pageHeight -
    pageOptions.pageHeaderHeight * 2 -
    topMarginPixels -
    bottomMarginPixels;
  console.log("pageContentAreaHeight", pageContentAreaHeight);
  const paginationElement = editorDom.querySelector("[data-rm-pagination]");
  const currentPageCount = getExistingPageCount(view);

  if (paginationElement) {
    const lastElementOfEditor = editorDom.lastElementChild;
    const lastPageBreak =
      paginationElement.lastElementChild?.querySelector(".breaker");
    if (lastElementOfEditor && lastPageBreak) {
      const lastElementRect = lastElementOfEditor.getBoundingClientRect();
      const lastPageBreakRect = lastPageBreak.getBoundingClientRect();

      // Check if measurements are valid (not zero/NaN)
      if (lastElementRect.bottom === 0 || lastPageBreakRect.bottom === 0) {
        return currentPageCount; // Return current count if measurements are invalid
      }

      const lastPageGap = lastElementRect.bottom - lastPageBreakRect.bottom;

      if (lastPageGap > 0) {
        const addPage = Math.ceil(lastPageGap / pageContentAreaHeight);
        return currentPageCount + addPage;
      } else {
        // Account for margins in page removal calculations
        const lpFrom = -(pageOptions.pageHeaderHeight + topMarginPixels);
        const lpTo = -(
          pageOptions.pageHeight -
          pageOptions.pageHeaderHeight -
          bottomMarginPixels
        );

        if (lastPageGap > lpTo && lastPageGap < lpFrom) {
          return currentPageCount;
        } else if (lastPageGap < lpTo) {
          const pageHeightOnRemove =
            pageOptions.pageHeight +
            pageOptions.pageGap +
            topMarginPixels +
            bottomMarginPixels;
          const removePage = Math.floor(lastPageGap / pageHeightOnRemove);
          return currentPageCount + removePage;
        } else {
          return currentPageCount;
        }
      }
    }
    return 1;
  } else {
    const editorHeight = editorDom.scrollHeight;
    const pageCount = Math.ceil(editorHeight / pageContentAreaHeight);
    return pageCount <= 0 ? 1 : pageCount;
  }
};

function createDecoration(
  state: EditorState,
  pageOptions: PaginationPlusOptions,
  isInitial: boolean = false
): Decoration[] {
  const pageWidget = Decoration.widget(
    0,
    (view) => {
      const _pageGap = pageOptions.pageGap;
      const _pageHeaderHeight = pageOptions.pageHeaderHeight;

      // Calculate effective page height accounting for headers and margins
      const topMarginPixels = mmToPixels(pageOptions.defaultMarginConfig.top);
      const bottomMarginPixels = mmToPixels(
        pageOptions.defaultMarginConfig.bottom
      );
      const _pageHeight =
        pageOptions.pageHeight -
        _pageHeaderHeight * 2 -
        topMarginPixels -
        bottomMarginPixels -
        100;
      const _pageBreakBackground = pageOptions.pageBreakBackground;

      const breakerWidth = view.dom.clientWidth;

      const el = document.createElement("div");
      el.dataset.rmPagination = "true";

      const pageBreakDefinition = ({
        firstPage = false,
        lastPage = false,
      }: {
        firstPage: boolean;
        lastPage: boolean;
      }) => {
        const pageContainer = document.createElement("div");
        pageContainer.classList.add("rm-page-break");

        const page = document.createElement("div");
        page.classList.add("page");
        page.style.position = "relative";
        page.style.float = "left";
        page.style.clear = "both";

        // Calculate margin top including the effective page height and top margin
        const topMarginPixels = mmToPixels(pageOptions.defaultMarginConfig.top);
        page.style.marginTop = firstPage
          ? `calc(${_pageHeaderHeight}px + ${_pageHeight}px + ${topMarginPixels}px)`
          : `calc(${_pageHeight}px + ${topMarginPixels}px)`;

        const pageBreak = document.createElement("div");
        pageBreak.classList.add("breaker");
        pageBreak.style.width = `calc(${breakerWidth}px)`;
        pageBreak.style.marginLeft = `calc(calc(calc(${breakerWidth}px - 100%) / 2) - calc(${breakerWidth}px - 100%))`;
        pageBreak.style.marginRight = `calc(calc(calc(${breakerWidth}px - 100%) / 2) - calc(${breakerWidth}px - 100%))`;
        pageBreak.style.position = "relative";
        pageBreak.style.float = "left";
        pageBreak.style.clear = "both";
        pageBreak.style.left = "0px";
        pageBreak.style.right = "0px";
        pageBreak.style.zIndex = "2";

        const pageFooter = document.createElement("div");
        pageFooter.classList.add("rm-page-footer");
        pageFooter.style.height = _pageHeaderHeight + "px";

        const footerRight = pageOptions.footerRight.replace(
          "{page}",
          `<span class="rm-page-number"></span>`
        );
        const footerLeft = pageOptions.footerLeft.replace(
          "{page}",
          `<span class="rm-page-number"></span>`
        );

        const pageFooterLeft = document.createElement("div");
        pageFooterLeft.classList.add("rm-page-footer-left");
        pageFooterLeft.innerHTML = footerLeft;

        const pageFooterRight = document.createElement("div");
        pageFooterRight.classList.add("rm-page-footer-right");
        pageFooterRight.innerHTML = footerRight;

        pageFooter.append(pageFooterLeft);
        pageFooter.append(pageFooterRight);

        const pageSpace = document.createElement("div");
        pageSpace.classList.add("rm-pagination-gap");
        pageSpace.style.height = _pageGap + "px";
        pageSpace.style.borderLeft = "1px solid #e2e8f0";
        pageSpace.style.borderRight = "1px solid #e2e8f0";
        pageSpace.style.position = "relative";
        pageSpace.style.setProperty("width", "calc(100% + 2px)", "important");
        pageSpace.style.left = "-1px";
        pageSpace.style.marginTop = pageOptions.defaultMarginConfig.top + "mm";
        pageSpace.style.marginBottom =
          pageOptions.defaultMarginConfig.bottom + "mm";
        pageSpace.style.backgroundColor = _pageBreakBackground;
        pageSpace.style.borderLeftColor = "#e2e8f0";
        pageSpace.style.borderRightColor = "#e2e8f0";

        const pageHeader = document.createElement("div");
        pageHeader.classList.add("rm-page-header");
        pageHeader.style.height = _pageHeaderHeight + "px";

        const pageHeaderLeft = document.createElement("div");
        pageHeaderLeft.classList.add("rm-page-header-left");
        pageHeaderLeft.innerHTML = pageOptions.headerLeft;

        const pageHeaderRight = document.createElement("div");
        pageHeaderRight.classList.add("rm-page-header-right");
        pageHeaderRight.innerHTML = pageOptions.headerRight;

        pageHeader.append(pageHeaderLeft, pageHeaderRight);
        pageBreak.append(pageFooter, pageSpace, pageHeader);
        pageContainer.append(page, pageBreak);

        return pageContainer;
      };

      const page = pageBreakDefinition({ firstPage: false, lastPage: false });
      const firstPage = pageBreakDefinition({
        firstPage: true,
        lastPage: false,
      });
      const fragment = document.createDocumentFragment();

      const pageCount = calculatePageCount(view, pageOptions);

      for (let i = 0; i < pageCount; i++) {
        if (i === 0) {
          fragment.appendChild(firstPage.cloneNode(true));
        } else {
          fragment.appendChild(page.cloneNode(true));
        }
      }
      el.append(fragment);
      el.id = "pages";

      return el;
    },
    { side: -1 }
  );

  return !isInitial ? [pageWidget] : [pageWidget];
}
