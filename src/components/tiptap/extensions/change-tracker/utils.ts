import { Editor } from "@tiptap/core";
import { Step, StepMap, Transform } from "@tiptap/pm/transform";
import { Fragment, DOMParser } from "@tiptap/pm/model";
import { Decoration } from "@tiptap/pm/view";

// Import types from the main index file
export interface ReplaceStep extends Step {
  jsonID: string;
  from: number;
  to: number;
  slice: {
    content: Fragment;
  };
}

export interface NodeLike {
  textContent?: string;
  text?: string;
}

// Data structures for tracking changes
export class Span {
  constructor(
    public from: number,
    public to: number,
    public commit: number | null
  ) {}
}

export class Commit {
  constructor(
    public message: string,
    public time: Date,
    public steps: Step[],
    public maps: StepMap[],
    public originalSteps: Step[],
    public hidden?: boolean,
    public deletions?: Array<{ pos: number; content: string }>
  ) {}
}

export class TrackState {
  constructor(
    public blameMap: Span[],
    public commits: Commit[],
    public uncommittedSteps: Step[],
    public uncommittedMaps: StepMap[],
    public uncommittedOriginalSteps: Step[]
  ) {}

  // Apply a transform to this state
  applyTransform(transform: Transform): TrackState {
    // Invert the steps in the transaction, to be able to save them in the next commit
    const inverted = transform.steps.map((step, i) =>
      step.invert(transform.docs[i])
    );

    const original = transform.steps.slice();

    const newBlame = updateBlameMap(
      this.blameMap,
      transform,
      this.commits.length
    );
    // Create a new state—since these are part of the editor state, a
    // persistent data structure, they must not be mutated.
    return new TrackState(
      newBlame,
      this.commits,
      this.uncommittedSteps.concat(inverted),
      this.uncommittedMaps.concat(transform.mapping.maps),
      this.uncommittedOriginalSteps.concat(original)
    );
  }

  // When a transaction is marked as a commit, this is used to put any
  // uncommitted steps into a new commit.
  applyCommit(message: string, time: Date): TrackState {
    if (this.uncommittedSteps.length == 0) return this;

    // Extract deletion information from the steps
    const deletions = [];
    this.uncommittedSteps.forEach((step: ReplaceStep) => {
      // Check if this step represents a replacement that includes deletions
      if (step.jsonID === "replace" && step.slice && step.slice.content) {
        // This step represents a replacement that includes deletions
        const deletedContent = step.slice.content;
        if (deletedContent.size > 0) {
          // Extract text content from the deleted slice
          let textContent = "";
          try {
            // NEW: Preserve ALL structure including newlines
            textContent = deletedContent.textBetween(
              0,
              deletedContent.size,
              "\n",
              "\n"
            );
          } catch (e) {
            // Fallback with newline preservation
            deletedContent.forEach((node) => {
              if (node.isText) {
                textContent += node.text;
              } else if (node.isBlock) {
                // ADD NEWLINES for block elements!
                if (textContent && !textContent.endsWith("\n")) {
                  textContent += "\n";
                }
              }
            });
          }
          if (textContent) {
            deletions.push({
              pos: step.from,
              content: textContent,
            });
          }
        }
      }
    });

    // Group consecutive deletions
    const groupedDeletions = groupConsecutiveDeletions(deletions);

    let commit = new Commit(
      message,
      time,
      this.uncommittedSteps, // inverted steps
      this.uncommittedMaps,
      this.uncommittedOriginalSteps, // original steps
      false,
      groupedDeletions // Use grouped deletions instead
    );

    return new TrackState(
      this.blameMap,
      this.commits.concat(commit),
      [],
      [],
      [] // clear uncommitted original steps
    );
  }
}

// Serialization utilities
export function serializeStepMap(stepMap: StepMap): any {
  const ranges: number[] = [];
  stepMap.forEach(
    (oldStart: number, oldEnd: number, newStart: number, newEnd: number) => {
      ranges.push(oldStart, oldEnd, newStart, newEnd);
    }
  );
  return { ranges };
}

export function deserializeStepMap(mapData: any): StepMap {
  if (!mapData || !mapData.ranges) {
    return StepMap.empty;
  }
  return new StepMap(mapData.ranges);
}

export function serializeTrackStateToAPI(trackState: TrackState): any {
  return {
    blameMap: trackState.blameMap.map((span) => ({
      from: span.from,
      to: span.to,
      commit: span.commit,
    })),
    commits: trackState.commits.map((commit) => ({
      message: commit.message,
      timestamp: commit.time.toISOString(),
      steps: commit.steps.map((s) => s.toJSON()),
      originalSteps: commit.originalSteps.map((s) => s.toJSON()),
      maps: commit.maps.map((m) => serializeStepMap(m)),
      hidden: commit.hidden || false,
      deletions: commit.deletions || [],
    })),
  };
}

export function createTrackStateFromAPI(apiData: any, schema: any): TrackState {
  try {
    const data = apiData.data || apiData;

    const blameMap = (data.blameMap || []).map(
      (span: any) => new Span(span.from, span.to, span.commit)
    );

    const commits = (data.commits || []).map((commitData: any) => {
      const steps = (commitData.steps || []).map((s: any) =>
        Step.fromJSON(schema, s)
      );
      const originalSteps = (commitData.originalSteps || []).map((s: any) =>
        Step.fromJSON(schema, s)
      );
      const maps = (commitData.maps || []).map((m: any) =>
        deserializeStepMap(m)
      );

      return new Commit(
        commitData.message,
        new Date(commitData.timestamp),
        steps,
        maps,
        originalSteps,
        commitData.hidden || false,
        commitData.deletions || []
      );
    });

    return new TrackState(
      blameMap,
      commits,
      [], // uncommittedSteps - always empty for saved documents
      [], // uncommittedMaps - always empty for saved documents
      [] // uncommittedOriginalSteps - always empty for saved documents
    );
  } catch (error) {
    console.error("Error parsing TrackState from API:", error);
    return new TrackState([], [], [], [], []);
  }
}

export function groupConsecutiveDeletions(deletions: any[]): any[] {
  if (deletions.length <= 1) return deletions;

  const sorted = deletions.slice().sort((a, b) => b.pos - a.pos);

  const grouped = [];
  let currentGroup = null;

  for (const deletion of sorted) {
    if (!currentGroup) {
      currentGroup = {
        pos: deletion.pos,
        content: deletion.content,
        endPos: deletion.pos + deletion.content.length,
      };
    } else {
      if (deletion.pos + deletion.content.length === currentGroup.pos) {
        currentGroup.content = deletion.content + currentGroup.content;
        currentGroup.pos = deletion.pos;
      } else {
        grouped.push({
          pos: currentGroup.pos,
          content: currentGroup.content,
        });
        currentGroup = {
          pos: deletion.pos,
          content: deletion.content,
          endPos: deletion.pos + deletion.content.length,
        };
      }
    }
  }

  if (currentGroup) {
    grouped.push({
      pos: currentGroup.pos,
      content: currentGroup.content,
    });
  }

  return grouped;
}

// Blame map utilities
export function updateBlameMap(
  map: Span[],
  transform: Transform,
  id: number
): Span[] {
  const result = [];
  const mapping = transform.mapping;
  for (let i = 0; i < map.length; i++) {
    const span = map[i];
    const from = mapping.map(span.from, 1),
      to = mapping.map(span.to, -1);
    if (from < to) result.push(new Span(from, to, span.commit));
  }

  for (let i = 0; i < mapping.maps.length; i++) {
    const map = mapping.maps[i],
      after = mapping.slice(i + 1);
    map.forEach((_s: number, _e: number, start: number, end: number) => {
      insertIntoBlameMap(result, after.map(start, 1), after.map(end, -1), id);
    });
  }

  return result;
}

export function insertIntoBlameMap(
  map: Span[],
  from: number,
  to: number,
  commit: number
): void {
  if (from >= to) return;
  let pos = 0;
  let next: Span;
  for (; pos < map.length; pos++) {
    next = map[pos];
    if (next.commit == commit) {
      if (next.to >= from) break;
    } else if (next.to > from) {
      // Different commit, not before
      if (next.from < from) {
        // Sticks out to the left (loop below will handle right side)
        const left = new Span(next.from, from, next.commit);
        if (next.to > to) map.splice(pos++, 0, left);
        else map[pos++] = left;
      }
      break;
    }
  }

  while ((next = map[pos])) {
    if (next.commit == commit) {
      if (next.from > to) break;
      from = Math.min(from, next.from);
      to = Math.max(to, next.to);
      map.splice(pos, 1);
    } else {
      if (next.from >= to) break;
      if (next.to > to) {
        map[pos] = new Span(to, next.to, next.commit);
        break;
      } else {
        map.splice(pos, 1);
      }
    }
  }

  map.splice(pos, 0, new Span(from, to, commit));
}

// Helper function to calculate diff between two commits
export function calculateCommitDiff(
  trackState: TrackState,
  currentCommit: Commit
): Decoration[] {
  const commits = trackState.commits;
  const currentIndex = commits.indexOf(currentCommit);

  if (currentIndex === -1) return [];

  const decorations: Decoration[] = [];

  // Show only additions for the selected commit (content that belongs to this commit only)
  const currentSpans = trackState.blameMap.filter(
    (span) => span.commit === currentIndex
  );

  // Show additions (content that belongs to current commit)
  currentSpans.forEach((span) => {
    decorations.push(
      Decoration.inline(span.from, span.to, {
        class: "commit-diff-addition",
        "data-commit-message": currentCommit.message,
        "data-commit-time": currentCommit.time.toISOString(),
      })
    );
  });

  // Show deletions for this commit
  if (currentCommit.deletions && currentCommit.deletions.length > 0) {
    currentCommit.deletions.forEach((deletion) => {
      decorations.push(
        Decoration.widget(deletion.pos, () => {
          const span = document.createElement("span");
          span.className = "commit-diff-deletion";
          span.setAttribute("data-commit-message", currentCommit.message);
          span.setAttribute(
            "data-commit-time",
            currentCommit.time.toISOString()
          );
          span.textContent = deletion.content;
          span.title = `Deleted: "${deletion.content}"`;
          return span;
        })
      );
    });
  }

  return decorations;
}

// Utility functions for change tracking

/**
 * Get all commits from the track state
 */
export function getCommits(editor: Editor) {
  const trackState = editor.commands.getTrackState();
  return trackState ? [] : []; // Will be populated when getTrackState returns actual state
}

/**
 * Check if there are uncommitted changes
 */
export function hasUncommittedChanges(editor: Editor): boolean {
  return editor.commands.hasUncommittedChanges();
}

/**
 * Save document with optional message
 */
export function saveDocument(editor: Editor, message?: string): boolean {
  return editor.commands.saveDocument(message);
}

/**
 * Commit changes with a message
 */
export function commitChanges(editor: Editor, message: string): boolean {
  return editor.commands.commitChanges(message);
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(date: Date): string {
  return date.toLocaleString();
}

/**
 * Generate a default commit message
 */
export function generateDefaultCommitMessage(): string {
  const now = new Date();
  return `Saved at ${now.toLocaleTimeString()}`;
}

/**
 * Extract meaningful content from commit steps to generate a descriptive title
 */
export function extractCommitTitle(commit: any): string {
  // If it's a custom message (not auto-generated), use it but truncate if too long
  if (
    !commit.message.includes("Saved at") &&
    !commit.message.includes("Changes saved")
  ) {
    return commit.message.length > 30
      ? commit.message.substring(0, 30) + "..."
      : commit.message;
  }

  // Try to extract meaningful content from the commit steps
  if (commit.steps && commit.steps.length > 0) {
    for (const step of commit.steps) {
      // Check for text insertion/replacement steps
      if (step.jsonID === "replace" && step.slice) {
        // Extract text content from the slice
        let textContent = "";
        if (step.slice.content && step.slice.content.content) {
          step.slice.content.content.forEach((node: any) => {
            if (node.text) {
              textContent += node.text;
            } else if (node.content) {
              // Handle nested content
              node.content.forEach((childNode: any) => {
                if (childNode.text) {
                  textContent += childNode.text;
                }
              });
            }
          });
        }

        if (textContent.trim()) {
          // Return first few words of the added text
          const words = textContent.trim().split(/\s+/).slice(0, 4);
          return words.length > 0
            ? `Added "${words.join(" ")}..."`
            : "Text changes";
        }
      }

      // Check for other step types
      if (step.jsonID === "addMark") {
        return "Formatting changes";
      }

      if (step.jsonID === "removeMark") {
        return "Removed formatting";
      }
    }
  }

  // Fallback for unrecognized changes
  return "Document changes";
}

/**
 * Add keyboard shortcut for saving (Ctrl+S)
 */
export function addSaveShortcut(editor: Editor) {
  const handleKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === "s") {
      event.preventDefault();
      saveDocument(editor, generateDefaultCommitMessage());
    }
  };

  document.addEventListener("keydown", handleKeyDown);

  // Return cleanup function
  return () => {
    document.removeEventListener("keydown", handleKeyDown);
  };
}

export function convertHTMLToJSON(htmlContent: string, schema: any): any {
  try {
    // Create a temporary DOM element to parse the HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;

    const parser = DOMParser.fromSchema(schema);
    const doc = parser.parse(tempDiv, {
      preserveWhitespace: "full",
    });

    return doc.toJSON();
  } catch (error) {
    console.error("Error converting HTML to JSON:", error);
    return null;
  }
}
