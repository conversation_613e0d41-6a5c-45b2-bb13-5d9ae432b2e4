import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Check, X, Trash2, Edit } from "lucide-react";
import { PromisedGrant } from "@/services/service-providers/promisedGrants.service";

interface PromisedGrantsTableProps {
  grants: PromisedGrant[];
  selectedGrants: Set<string>;
  editingGrantId?: string | null;
  editedGrantData?: Partial<PromisedGrant> | null;
  isUpdating?: boolean;
  showCheckboxes?: boolean;
  actionType?: 'edit' | 'delete';
  onSelectAll?: (grantIds: string[], checked: boolean) => void;
  onSelectGrant?: (grantId: string, checked: boolean) => void;
  onEditClick?: (grant: PromisedGrant) => void;
  onCancelClick?: () => void;
  onSaveClick?: () => void;
  onDeleteClick?: (grant: PromisedGrant) => void;
  onFieldChange?: (grantId: string, field: keyof PromisedGrant, value: string | number) => void;
  onCustomVestingClick?: (grantId: string, grantName: string) => void;
  grantTypeOptions: Array<{ value: string; label: string }>;
  vestingScheduleOptions: Array<{ value: string; label: string }>;
  saveResults?: Array<{ grantId: string; success: boolean; error?: any }>;
  getStatusIcon?: (grantId: string) => React.ReactNode;
  isViewMode?: boolean;
}

const PromisedGrantsTable: React.FC<PromisedGrantsTableProps> = ({
  grants,
  selectedGrants,
  editingGrantId,
  editedGrantData,
  isUpdating,
  showCheckboxes = true,
  actionType = 'delete',
  onSelectAll,
  onSelectGrant,
  onEditClick,
  onCancelClick,
  onSaveClick,
  onDeleteClick,
  onFieldChange,
  onCustomVestingClick,
  grantTypeOptions,
  vestingScheduleOptions,
  saveResults,
  getStatusIcon,
  isViewMode = false,
}) => {
  return (
    <Table>
      <TableHeader className="bg-[#2E2F3A] sticky top-0 z-10">
        <TableRow>
          {showCheckboxes && (
            <TableHead className="w-[50px]">
              <Checkbox
                checked={selectedGrants.size === grants.filter(g => g.boardApproved === 'no').length && grants.filter(g => g.boardApproved === 'no').length > 0}
                onCheckedChange={(checked) => onSelectAll?.(grants.filter(g => g.boardApproved === 'no').map((grant) => grant.id), checked === true)}
              />
            </TableHead>
          )}
          <TableHead className="text-[#F2F2F5]">Name</TableHead>
          <TableHead className="text-[#F2F2F5]">Service Provider Status</TableHead>
          <TableHead className="text-[#F2F2F5]">Shares</TableHead>
          <TableHead className="text-[#F2F2F5]">Grant Type</TableHead>
          <TableHead className="text-[#F2F2F5]">Vesting Schedule</TableHead>
          <TableHead className="text-[#F2F2F5]">Vesting Commencement Date</TableHead>
          <TableHead className="text-[#F2F2F5]">Price per Share</TableHead>
          <TableHead className="text-[#F2F2F5]">Address</TableHead>
          <TableHead className="text-[#F2F2F5]">10% Holder</TableHead>
          <TableHead className="text-[#F2F2F5]">Board Approved</TableHead>
          <TableHead className="w-[50px]">
            {actionType === 'edit' && saveResults && saveResults.length > 0 ? 'Status' : ''}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {grants.map((grant) => (
          <TableRow
            key={grant.id}
            className="border-t border-[#2E2F3A] hover:bg-[#2E2F3A]/50"
          >
            {showCheckboxes && (
              <TableCell>
                <Checkbox
                  checked={selectedGrants.has(grant.id)}
                  onCheckedChange={(checked) => onSelectGrant?.(grant.id, checked === true)}
                  disabled={grant.boardApproved !== 'no'}
                />
              </TableCell>
            )}
            <TableCell className="text-[#F2F2F5] w-[150px]">
              {actionType === 'edit' && !isViewMode ? (
                <Input
                  type="text"
                  value={grant.name}
                  onChange={(e) =>
                    onFieldChange?.(grant.id, "name", e.target.value)
                  }
                  className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50 w-full"
                  placeholder="Enter name"
                />
              ) : (
                grant.name
              )}
            </TableCell>
            <TableCell className="text-[#F2F2F5] capitalize w-[140px]">
              {grant.type.replace(/-/g, " ")}
            </TableCell>
            <TableCell className="text-[#F2F2F5]">
              {actionType === 'edit' && !isViewMode ? (
                <Input
                  type="number"
                  value={grant.shares}
                  onChange={(e) =>
                    onFieldChange?.(grant.id, "shares", Number(e.target.value))
                  }
                  className="w-16 bg-transparent text-right focus:ring-1 focus:ring-[#9B51E0]"
                />
              ) : (
                grant.shares.toLocaleString()
              )}
            </TableCell>
            <TableCell className="text-[#F2F2F5] w-[180px]">
              {actionType === 'edit' && !isViewMode ? (
                <Select
                  value={grant.grantType}
                  onValueChange={(value) =>
                    onFieldChange?.(grant.id, "grantType", value)
                  }
                >
                  <SelectTrigger className="bg-[#2E2F3A] border-[#9B51E0]">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {grantTypeOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                grant.grantType
              )}
            </TableCell>
                                    <TableCell className="text-[#F2F2F5] w-[160px]">
                          {actionType === 'edit' && !isViewMode ? (
                            <Select
                              value={grant.vestingSchedule || ""}
                              onValueChange={(value) => {
                                if (value === "custom") {
                                  onCustomVestingClick?.(grant.id, grant.name);
                                }
                                onFieldChange?.(grant.id, "vestingSchedule", value);
                              }}
                            >
                              <SelectTrigger className="bg-[#2E2F3A] border-[#9B51E0]">
                                <SelectValue placeholder="Select schedule" />
                              </SelectTrigger>
                              <SelectContent>
                                {vestingScheduleOptions.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            grant.vestingSchedule
                          )}
                        </TableCell>
            <TableCell className="text-[#F2F2F5]">
              {new Date(grant.vestingCommencementDate).toLocaleDateString()}
            </TableCell>
              <TableCell className="text-[#F2F2F5]">
                {`$${grant.pricePerShare}`}
              </TableCell>
            <TableCell className="text-[#F2F2F5]">
              {grant.address}
            </TableCell>
            <TableCell className="text-[#F2F2F5]">
              {grant.tenPercentHolder ? "Yes" : "No"}
            </TableCell>
            <TableCell className="text-[#F2F2F5] capitalize">
              {grant.boardApproved}
            </TableCell>
            <TableCell>
              {actionType === 'edit' && saveResults && saveResults.length > 0 ? (
                // Modal: Show status indicators (✅❌) only when there are results
                getStatusIcon && getStatusIcon(grant.id)
              ) : actionType === 'edit' ? (
                // Modal: Show empty cell when in edit mode but no results yet
                null
              ) : (
                // Main page: Show delete icon (🗑️)
                onDeleteClick && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteClick(grant)}
                    className="h-8 w-8 p-0 text-red-400 hover:text-red-300 hover:bg-red-400/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )
              )}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default PromisedGrantsTable; 