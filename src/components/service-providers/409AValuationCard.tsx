import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Upload, DollarSign, Calendar } from "lucide-react";
import { Active409AValuation } from "@/services/service-providers/promisedGrants.service";
import { toast } from "sonner";

interface Active409AValuationCardProps {
  valuation: Active409AValuation | null;
  isLoading: boolean;
}

const Active409AValuationCard: React.FC<Active409AValuationCardProps> = ({
  valuation,
  isLoading,
}) => {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [fairMarketValue, setFairMarketValue] = useState("");
  const [valuationDate, setValuationDate] = useState("");

  const handleUpload = () => {
    if (!fairMarketValue || !valuationDate) {
      toast.error("Please fill in all fields");
      return;
    }

    // TODO: Implement actual upload API call
    toast.success("409A Valuation uploaded successfully!");
    setIsUploadDialogOpen(false);
    setFairMarketValue("");
    setValuationDate("");
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Card className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
      <CardHeader className="pb-3">
        <CardTitle className="text-[#F2F2F5] text-lg font-semibold">
          409A Valuation
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#9B51E0] mx-auto"></div>
            <p className="text-[#F2F2F5] opacity-60 mt-2">Loading...</p>
          </div>
        ) : valuation ? (
          <div className="space-y-3 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-[#9B51E0]" />
                <span className="text-sm text-[#F2F2F5] opacity-80">
                  Fair Market Value
                </span>
              </div>
              <span className="text-lg font-semibold text-[#F2F2F5]">
                {formatCurrency(valuation.fairMarketValue)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-[#9B51E0]" />
                <span className="text-sm text-[#F2F2F5] opacity-80">
                  Valuation Date
                </span>
              </div>
              <span className="text-sm text-[#F2F2F5]">
                {formatDate(valuation.appraisalDate)}
              </span>
            </div>
            <div className="pt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsUploadDialogOpen(true)}
                className="w-full border-[#9B51E0] text-[#9B51E0] hover:bg-[#9B51E0] hover:text-[#F2F2F5]"
              >
                <Upload className="h-4 w-4 mr-2" />
                Update Valuation
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="mb-3">
              <DollarSign className="h-8 w-8 text-[#F2F2F5] opacity-40 mx-auto" />
            </div>
            <p className="text-[#F2F2F5] opacity-60 mb-3">
              No 409A valuation found
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsUploadDialogOpen(true)}
              className="border-[#9B51E0] text-[#9B51E0] hover:bg-[#9B51E0] hover:text-[#F2F2F5]"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload 409A Valuation Report
            </Button>
          </div>
        )}
      </CardContent>

      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
          <DialogHeader>
            <DialogTitle className="text-[#F2F2F5]">
              Upload 409A Valuation Report
            </DialogTitle>
            <DialogDescription className="text-[#F2F2F5] opacity-80">
              Enter the valuation details from your 409A report.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="fairMarketValue" className="text-[#F2F2F5]">
                Fair Market Value ($)
              </Label>
              <Input
                id="fairMarketValue"
                type="number"
                value={fairMarketValue}
                onChange={(e) => setFairMarketValue(e.target.value)}
                placeholder="Enter fair market value"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0]"
              />
            </div>
            <div>
              <Label htmlFor="valuationDate" className="text-[#F2F2F5]">
                Valuation Date
              </Label>
              <Input
                id="valuationDate"
                type="date"
                value={valuationDate}
                onChange={(e) => setValuationDate(e.target.value)}
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsUploadDialogOpen(false)}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              Upload Valuation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default Active409AValuationCard; 