import { ColumnDef } from "@tanstack/react-table";

export interface TerminatedServiceProvider {
  id: string;
  name: string;
  serviceProviderStatus: "advisor" | "employee" | "independent-contractor-consultant";
  shares: number;
  typeOfGrant: string;
  vestedAmount: number;
  unvestedAmount: number;
  vestingProgress: number;
  severanceAmount: number | null;
}

export const terminatedServiceProviderColumns: ColumnDef<TerminatedServiceProvider>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "serviceProviderStatus",
    header: "Type",
    cell: ({ row }) => {
      const status = row.getValue("serviceProviderStatus") as string;
      const displayStatus = status
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
      return <div className="capitalize">{displayStatus}</div>;
    },
  },
  {
    accessorKey: "shares",
    header: "Shares",
    cell: ({ row }) => {
      const shares = row.getValue("shares") as number;
      return <div>{shares.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "typeOfGrant",
    header: "Grant Type",
    cell: ({ row }) => {
      const grantType = row.getValue("typeOfGrant") as string;
      const displayGrantType = grantType
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
      return <div className="capitalize">{displayGrantType}</div>;
    },
  },
  {
    accessorKey: "vestedAmount",
    header: "Vested",
    cell: ({ row }) => {
      const vested = row.getValue("vestedAmount") as number;
      return <div>{vested.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "unvestedAmount",
    header: "Unvested",
    cell: ({ row }) => {
      const unvested = row.getValue("unvestedAmount") as number;
      return <div>{unvested.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "vestingProgress",
    header: "Progress",
    cell: ({ row }) => {
      const progress = row.getValue("vestingProgress") as number;
      return (
        <div className="flex items-center">
          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="text-sm">{progress}%</span>
        </div>
      );
    },
  },
  {
    accessorKey: "severanceAmount",
    header: "Severance",
    cell: ({ row }) => {
      const severance = row.getValue("severanceAmount") as number | null;
      return (
        <div>
          {severance ? `$${severance.toLocaleString()}` : "N/A"}
        </div>
      );
    },
  },
];
