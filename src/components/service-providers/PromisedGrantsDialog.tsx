import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, ClipboardCheckIcon } from "lucide-react";
import { useServiceProviders } from "@/hooks/serviceProviders";
import { PromisedGrant } from "@/types/serviceProvider";
import EquityIssueDialog from "./EquityIssueDialog";
import { promisedGrantService } from "@/services/promisedGrantService";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useStockOptionInformation } from "@/hooks/service-providers/usePromisedGrants.hooks";

interface PromisedGrantsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const PromisedGrantsDialog: React.FC<PromisedGrantsDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { promisedGrants } = useServiceProviders();
  const { user } = useAuth();
  const companyId = user?.companyId;
  
  const { data: stockOptionInfo, isLoading: isLoadingStockInfo } = useStockOptionInformation(companyId || "");
  
  const [selectedGrant, setSelectedGrant] = useState<PromisedGrant | null>(
    null
  );
  const [isIssueDialogOpen, setIsIssueDialogOpen] = useState(false);
  const [isBoardApprovalDialogOpen, setIsBoardApprovalDialogOpen] =
    useState(false);
  const [isSubmittingApproval, setIsSubmittingApproval] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleIssueClick = (grant: PromisedGrant) => {
    setSelectedGrant(grant);
    setIsIssueDialogOpen(true);
  };

  const handleBoardApprovalClick = () => {
    setIsBoardApprovalDialogOpen(true);
  };

  const handleSubmitForBoardApproval = async () => {
    setIsSubmittingApproval(true);
    try {
      // Simulate submitting all promised grants for board approval
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success(
        "Successfully submitted for board approval. Directors will be notified."
      );
      setIsBoardApprovalDialogOpen(false);
    } catch (error) {
      console.error("Error submitting for board approval:", error);
      toast.error("Failed to submit for board approval. Please try again.");
    } finally {
      setIsSubmittingApproval(false);
    }
  };

  const handleIssueDialogClose = () => {
    setIsIssueDialogOpen(false);
    setSelectedGrant(null);
  };

  const pendingGrantsCount = promisedGrants.filter(
    (grant) => grant.status === "Promised"
  ).length;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
          <DialogHeader>
            <DialogTitle className="text-[#F2F2F5]">
              Promised Grants
            </DialogTitle>
            <DialogDescription className="text-[#F2F2F5] opacity-80">
              View and manage promised equity grants for your team.
            </DialogDescription>
          </DialogHeader>

          {/* Summary information */}
          <Alert className="bg-[#2E2F3A] border-[#9B51E0] mb-4">
            <InfoIcon className="h-4 w-4 text-[#9B51E0]" />
            <AlertTitle className="text-[#F2F2F5]">
              Stock Option Plan Summary
            </AlertTitle>
            <AlertDescription className="text-[#F2F2F5] opacity-90">
              <div className="grid grid-cols-3 gap-4 mt-2">
                <div>
                  <p className="text-sm font-medium">Total Pool Size</p>
                  <p className="text-lg font-semibold">
                    {stockOptionInfo?.totalPoolSize?.toLocaleString() || 0} shares
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Allocated</p>
                  <p className="text-lg font-semibold">
                    {stockOptionInfo?.allocated?.toLocaleString() || 0} shares
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Remaining</p>
                  <p className="text-lg font-semibold">
                    {stockOptionInfo?.remaining?.toLocaleString() || 0} shares
                  </p>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Board Approval Action - This section clearly displays when there are pending grants */}
          {pendingGrantsCount > 0 && (
            <div className="bg-[#2E2F3A] border border-[#E1467C] rounded-md p-4 mb-4 flex items-center justify-between">
              <div className="flex items-center">
                <ClipboardCheckIcon className="h-5 w-5 text-[#E1467C] mr-3" />
                <div>
                  <h3 className="font-medium text-[#F2F2F5]">
                    Board Approval Required
                  </h3>
                  <p className="text-sm text-[#F2F2F5] opacity-80">
                    {pendingGrantsCount} promised grant
                    {pendingGrantsCount > 1 ? "s" : ""}{" "}
                    {pendingGrantsCount > 1 ? "require" : "requires"} board
                    approval.
                  </p>
                </div>
              </div>
              <Button
                variant="default"
                onClick={handleBoardApprovalClick}
                className="bg-[#E1467C] hover:bg-[#E1467C]/90 text-[#F2F2F5]"
              >
                Submit for Board Approval
              </Button>
            </div>
          )}

          {isLoadingStockInfo ? (
            <div className="text-center py-10">
              <p className="text-[#F2F2F5] opacity-60">
                Loading stock option information...
              </p>
            </div>
          ) : promisedGrants.length > 0 ? (
            <div className="border border-[#2E2F3A] rounded-md">
              <Table>
                <TableHeader className="bg-[#2E2F3A]">
                  <TableRow>
                    <TableHead className="text-[#F2F2F5]">Name</TableHead>
                    <TableHead className="text-[#F2F2F5]">
                      Service Provider Type
                    </TableHead>
                    <TableHead className="text-[#F2F2F5]">
                      Vesting Commencement Date
                    </TableHead>
                    <TableHead className="text-[#F2F2F5]">
                      Type of Grant
                    </TableHead>
                    <TableHead className="text-[#F2F2F5]">
                      Number of Shares
                    </TableHead>
                    <TableHead className="text-[#F2F2F5]">
                      Vesting Schedule
                    </TableHead>
                    <TableHead className="text-[#F2F2F5]">Status</TableHead>
                    <TableHead className="text-[#F2F2F5]">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {promisedGrants.map((grant) => (
                    <TableRow
                      key={grant.id}
                      className="border-t border-[#2E2F3A] hover:bg-[#2E2F3A]/50"
                    >
                      <TableCell className="text-[#F2F2F5]">
                        {grant.serviceProviderName}
                      </TableCell>
                      <TableCell className="text-[#F2F2F5]">
                        {grant.serviceProviderType}
                      </TableCell>
                      <TableCell className="text-[#F2F2F5]">
                        {new Date(
                          grant.vestingCommencementDate
                        ).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-[#F2F2F5]">
                        {grant.grantType}
                        {grant.optionType && ` (${grant.optionType})`}
                      </TableCell>
                      <TableCell className="text-[#F2F2F5]">
                        {grant.shares.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-[#F2F2F5]">
                        {grant.vestingSchedule}
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            grant.status === "Promised"
                              ? "bg-[#9B51E0]/20 text-[#9B51E0]"
                              : grant.status === "Board Approved"
                                ? "bg-[#9B51E0]/40 text-[#9B51E0]"
                                : "bg-[#E1467C]/20 text-[#E1467C]"
                          }`}
                        >
                          {grant.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {grant.status === "Promised" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleIssueClick(grant)}
                            className="border-[#9B51E0] text-[#9B51E0] hover:bg-[#9B51E0]/10"
                          >
                            Issue Equity
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-[#F2F2F5] opacity-60">
                No promised grants found.
              </p>
              <p className="text-sm text-[#F2F2F5] opacity-40 mt-1">
                Add service providers with equity grants to see them here.
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Board Approval Alert Dialog */}
      <AlertDialog
        open={isBoardApprovalDialogOpen}
        onOpenChange={setIsBoardApprovalDialogOpen}
      >
        <AlertDialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5]">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-[#F2F2F5]">
              Submit for Board Approval
            </AlertDialogTitle>
            <AlertDialogDescription className="text-[#F2F2F5] opacity-80">
              This will submit all pending promised grants for board approval.
              Your board members will be notified and asked to review and
              approve these equity grants.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isSubmittingApproval}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSubmitForBoardApproval}
              disabled={isSubmittingApproval}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              {isSubmittingApproval ? "Submitting..." : "Submit for Approval"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {selectedGrant && (
        <EquityIssueDialog
          isOpen={isIssueDialogOpen}
          onClose={handleIssueDialogClose}
          grant={selectedGrant}
        />
      )}
    </>
  );
};

export default PromisedGrantsDialog;
