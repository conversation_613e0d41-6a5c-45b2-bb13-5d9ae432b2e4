import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Upload, FileText, Calendar, DollarSign } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useSetFairMarketValue, use409aUpload, useSubmit409a } from "@/hooks/service-providers/usePromisedGrants.hooks";

interface Dialog409ValuationProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGrantsCount: number;
  mode?: 'set-fair-market-value' | 'update-409a' | 'use-current-409a' | 'issue-workflow';
  currentFairMarketValue?: number;
  currentAppraisalDate?: string;
  onNext?: () => void;
}

type DialogStep = "initial" | "has409A" | "upload409A" | "confirmValue";

const Dialog409Valuation: React.FC<Dialog409ValuationProps> = ({
  isOpen,
  onClose,
  selectedGrantsCount,
  mode = 'issue-workflow',
  currentFairMarketValue,
  currentAppraisalDate,
  onNext,
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId || "";
  const { setFMV, isSettingFMV } = useSetFairMarketValue(companyId);
  const { getPresignedUrl, uploadToS3 } = use409aUpload(companyId);
  const submit409a = useSubmit409a(companyId);
  const [currentStep, setCurrentStep] = useState<DialogStep>("initial");
  const [has409A, setHas409A] = useState<boolean | null>(null);
  const [formData, setFormData] = useState({
    fairMarketValue: "",
    pricePerShare: "",
    appraiserName: "",
    appraisalDate: "",
    valuationFile: null as File | null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStepChange = (step: DialogStep) => {
    setCurrentStep(step);
  };

  const handleYesClick = () => {
    setHas409A(true);
    handleStepChange("upload409A");
  };

  const handleNoClick = () => {
    setHas409A(false);
    handleStepChange("confirmValue");
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size must be less than 5MB");
        return;
      }
      setFormData(prev => ({ ...prev, valuationFile: file }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Validate required fields based on mode
    if (mode === 'set-fair-market-value') {
      if (!formData.pricePerShare) {
        toast.error("Please fill in all required fields");
        setIsSubmitting(false);
        return;
      }
      try {
        await setFMV(Number(formData.pricePerShare));
        setIsSubmitting(false);
        // Call onNext if available (for issue workflow), otherwise close
        if (onNext) {
          onNext();
        } else {
          onClose();
        }
        return;
      } catch (e) {
        setIsSubmitting(false);
        return;
      }
    } else if (mode === 'update-409a') {
      if (!formData.fairMarketValue || !formData.appraiserName || !formData.appraisalDate || !formData.valuationFile) {
        toast.error("Please fill in all required fields");
        setIsSubmitting(false);
        return;
      }
    } else if (has409A && (!formData.fairMarketValue || !formData.appraiserName || !formData.appraisalDate || !formData.valuationFile)) {
      toast.error("Please fill in all required fields");
      setIsSubmitting(false);
      return;
    } else if (!has409A && !formData.fairMarketValue) {
      toast.error("Please enter the fair market value");
      setIsSubmitting(false);
      return;
    }

    try {
      // Upload + submit (used in update-409a mode or the upload step of issue workflow)
      if (mode === 'update-409a' || currentStep === 'upload409A') {
        const file = formData.valuationFile as File;
        const { uploadUrl, key } = await getPresignedUrl(file);
        await uploadToS3(uploadUrl, file, file.type || 'application/octet-stream');
        await submit409a.mutateAsync({
          appraiserName: formData.appraiserName,
          appraisalDate: formData.appraisalDate,
          fairMarketValue: Number(formData.fairMarketValue),
          confirmationStatus: 'yes',
          s3Key: key,
        });
        setIsSubmitting(false);
        // Call onNext if available (for issue workflow), otherwise close
        if (onNext) {
          onNext();
        } else {
          handleClose();
        }
        return;
      }

      // Fallback: simple success (kept for other modes not covered here)
      toast.success("Successfully saved!");
      handleClose();
    } catch (error: any) {
      toast.error(error?.message || "Failed to save. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setCurrentStep("initial");
    setHas409A(null);
    setFormData({
      fairMarketValue: "",
      pricePerShare: "",
      appraiserName: "",
      appraisalDate: "",
      valuationFile: null,
    });
    setIsSubmitting(false);
    onClose();
  };

  const renderStepContent = () => {
    // Use Current 409A Valuation Mode
    if (mode === 'use-current-409a') {
      return (
        <>
          <DialogHeader>
            <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
              Current 409A Valuation
            </DialogTitle>
            <DialogDescription className="text-[#F2F2F5] opacity-80">
              Current 409A valuation on file for the selected grants.
            </DialogDescription>
          </DialogHeader>
          <div className="py-6">
            <div className="bg-[#2E2F3A] rounded-lg p-6 border border-[#2E2F3A] hover:border-[#E1467C]/30 transition-colors">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-md bg-[#E1467C]/20 flex items-center justify-center">
                      <span className="text-[#E1467C] font-bold text-sm">$</span>
                    </div>
                    <div>
                      <p className="text-sm text-[#F2F2F5] opacity-80">Fair Market Value</p>
                      <p className="text-xl font-bold text-[#F2F2F5]">
                        {currentFairMarketValue ? new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: "USD",
                        }).format(currentFairMarketValue) : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-[#2E2F3A]">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-md bg-[#9B51E0]/20 flex items-center justify-center">
                      <span className="text-[#9B51E0] font-bold text-sm">📅</span>
                    </div>
                    <div>
                      <p className="text-sm text-[#F2F2F5] opacity-80">Valuation Date</p>
                      <p className="text-sm font-medium text-[#F2F2F5]">
                        {currentAppraisalDate ? new Date(currentAppraisalDate).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }) : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={onNext || handleClose}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              Next
            </Button>
          </DialogFooter>
        </>
      );
    }

    // Set Fair Market Value Mode
    if (mode === 'set-fair-market-value') {
      return (
        <>
          <DialogHeader>
            <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
              Set Fair Market Value
            </DialogTitle>
            <DialogDescription className="text-[#F2F2F5] opacity-80">
              Enter the price per share for the selected grants.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                <DollarSign className="h-4 w-4" />
                <span>Price per Share</span>
              </Label>
              <Input
                type="number"
                value={formData.pricePerShare}
                onChange={(e) => setFormData(prev => ({ ...prev, pricePerShare: e.target.value }))}
                placeholder="Enter price per share"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting || isSettingFMV}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || isSettingFMV}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              {isSubmitting || isSettingFMV ? "Saving..." : "Save and Continue"}
            </Button>
          </DialogFooter>
        </>
      );
    }

    // Update 409A Valuation Mode - Direct upload
    if (mode === 'update-409a') {
      return (
        <>
          <DialogHeader>
            <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
              Upload 409A Valuation Report
            </DialogTitle>
            <DialogDescription className="text-[#F2F2F5] opacity-80">
              Please provide the 409A valuation details and upload the report.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6 py-4">
            {/* File Upload */}
            <div className="space-y-3">
              <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                <Upload className="h-4 w-4" />
                <span>Upload 409A Valuation Report</span>
              </Label>
              <div className="border-2 border-dashed border-[#2E2F3A] rounded-lg p-6 hover:border-[#9B51E0]/30 transition-colors">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileChange}
                  className="hidden"
                  id="valuation-file"
                />
                <label
                  htmlFor="valuation-file"
                  className="cursor-pointer flex flex-col items-center space-y-3"
                >
                  <div className="w-12 h-12 rounded-full bg-[#9B51E0]/20 flex items-center justify-center">
                    <FileText className="h-6 w-6 text-[#9B51E0]" />
                  </div>
                  <div className="text-center">
                    <p className="text-[#F2F2F5] font-medium">
                      {formData.valuationFile ? formData.valuationFile.name : "Click to upload"}
                    </p>
                    <p className="text-sm text-[#F2F2F5] opacity-60">
                      PDF, DOC, or DOCX (max 5MB)
                    </p>
                  </div>
                </label>
              </div>
            </div>

            {/* Appraiser Name */}
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                <span className="w-4 h-4">👤</span>
                <span>Confirm Appraiser Name</span>
              </Label>
              <Input
                value={formData.appraiserName}
                onChange={(e) => setFormData(prev => ({ ...prev, appraiserName: e.target.value }))}
                placeholder="Enter appraiser name"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>

            {/* Appraisal Date */}
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Confirm Appraisal Date</span>
              </Label>
              <Input
                type="date"
                value={formData.appraisalDate}
                onChange={(e) => setFormData(prev => ({ ...prev, appraisalDate: e.target.value }))}
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>

            {/* Fair Market Value */}
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                <DollarSign className="h-4 w-4" />
                <span>Confirm the fair market value of the Common Stock</span>
              </Label>
              <Input
                type="number"
                value={formData.fairMarketValue}
                onChange={(e) => setFormData(prev => ({ ...prev, fairMarketValue: e.target.value }))}
                placeholder="Enter fair market value"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
            >
              {isSubmitting ? "Saving..." : "Save and Continue"}
            </Button>
          </DialogFooter>
        </>
      );
    }

    // Original Issue Workflow Mode
    switch (currentStep) {
      case "initial":
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
                409A Valuation Confirmation
              </DialogTitle>
              <DialogDescription className="text-[#F2F2F5] opacity-80 text-base">
                Have you obtained a 409A valuation with a measurement date within 12 months of these grants?
              </DialogDescription>
            </DialogHeader>
            <div className="py-6">
              <div className="bg-[#2E2F3A] rounded-lg p-4 mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-[#9B51E0]/20 flex items-center justify-center">
                    <span className="text-[#9B51E0] font-bold text-sm">📋</span>
                  </div>
                  <div>
                    <p className="text-[#F2F2F5] font-medium">
                      {selectedGrantsCount} grant{selectedGrantsCount > 1 ? 's' : ''} selected
                    </p>
                    <p className="text-sm text-[#F2F2F5] opacity-60">
                      Ready to issue equity grants
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleNoClick}
                className="flex-1 bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5] border-[#2E2F3A]"
              >
                No
              </Button>
              <Button
                onClick={handleYesClick}
                className="flex-1 bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
              >
                Yes
              </Button>
            </DialogFooter>
          </>
        );

      case "upload409A":
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
                Upload 409A Valuation Report
              </DialogTitle>
              <DialogDescription className="text-[#F2F2F5] opacity-80">
                Please provide the 409A valuation details and upload the report.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              {/* File Upload */}
              <div className="space-y-3">
                <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                  <Upload className="h-4 w-4" />
                  <span>Upload 409A Valuation Report</span>
                </Label>
                <div className="border-2 border-dashed border-[#2E2F3A] rounded-lg p-6 hover:border-[#9B51E0]/30 transition-colors">
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileChange}
                    className="hidden"
                    id="valuation-file"
                  />
                  <label
                    htmlFor="valuation-file"
                    className="cursor-pointer flex flex-col items-center space-y-3"
                  >
                    <div className="w-12 h-12 rounded-full bg-[#9B51E0]/20 flex items-center justify-center">
                      <FileText className="h-6 w-6 text-[#9B51E0]" />
                    </div>
                    <div className="text-center">
                      <p className="text-[#F2F2F5] font-medium">
                        {formData.valuationFile ? formData.valuationFile.name : "Click to upload"}
                      </p>
                      <p className="text-sm text-[#F2F2F5] opacity-60">
                        PDF, DOC, or DOCX (max 5MB)
                      </p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Appraiser Name */}
              <div className="space-y-2">
                <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                  <span className="w-4 h-4">👤</span>
                  <span>Confirm Appraiser Name</span>
                </Label>
                <Input
                  value={formData.appraiserName}
                  onChange={(e) => setFormData(prev => ({ ...prev, appraiserName: e.target.value }))}
                  placeholder="Enter appraiser name"
                  className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
                />
              </div>

              {/* Appraisal Date */}
              <div className="space-y-2">
                <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Confirm Appraisal Date</span>
                </Label>
                <Input
                  type="date"
                  value={formData.appraisalDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, appraisalDate: e.target.value }))}
                  className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
                />
              </div>

              {/* Fair Market Value */}
              <div className="space-y-2">
                <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                  <DollarSign className="h-4 w-4" />
                  <span>Confirm the fair market value of the Common Stock</span>
                </Label>
                <Input
                  type="number"
                  value={formData.fairMarketValue}
                  onChange={(e) => setFormData(prev => ({ ...prev, fairMarketValue: e.target.value }))}
                  placeholder="Enter fair market value"
                  className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => handleStepChange("initial")}
                disabled={isSubmitting}
                className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
              >
                Back
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
              >
                {isSubmitting ? "Saving..." : "Save and Continue"}
              </Button>
            </DialogFooter>
          </>
        );

      case "confirmValue":
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
                Confirm Fair Market Value
              </DialogTitle>
              <DialogDescription className="text-[#F2F2F5] opacity-80">
                Please confirm the fair market value of the Common Stock for these grants.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              <div className="bg-[#2E2F3A] rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 rounded-full bg-[#E1467C]/20 flex items-center justify-center">
                    <span className="text-[#E1467C] font-bold text-sm">⚠️</span>
                  </div>
                  <div>
                    <p className="text-[#F2F2F5] font-medium">No 409A Valuation</p>
                    <p className="text-sm text-[#F2F2F5] opacity-60">
                      You'll need to obtain a 409A valuation within 12 months
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-[#F2F2F5] font-medium flex items-center space-x-2">
                  <DollarSign className="h-4 w-4" />
                  <span>Confirm the fair market value of the Common Stock</span>
                </Label>
                <Input
                  type="number"
                  value={formData.fairMarketValue}
                  onChange={(e) => setFormData(prev => ({ ...prev, fairMarketValue: e.target.value }))}
                  placeholder="Enter fair market value"
                  className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => handleStepChange("initial")}
                disabled={isSubmitting}
                className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
              >
                Back
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
              >
                {isSubmitting ? "Saving..." : "Save and Continue"}
              </Button>
            </DialogFooter>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) handleClose(); }}>
      <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5] max-w-md">
        {renderStepContent()}
      </DialogContent>
    </Dialog>
  );
};

export default Dialog409Valuation;
