import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import PromisedGrantsTable from "@/components/service-providers/PromisedGrantsTable";
import CustomVestingDialog from "@/components/service-providers/dialog/CustomVestingDialog";
import { PromisedGrant } from "@/services/service-providers/promisedGrants.service";

interface ModifyGrantsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGrants: PromisedGrant[];
  localEdits?: Record<string, Partial<PromisedGrant>>;
  onFieldChange: (grantId: string, field: keyof PromisedGrant, value: string | number) => void;
  onCustomVestingClick: (grantId: string, grantName: string) => void;
  onCustomVestingSave: (grantId: string, data: { vestingPeriod: string; cliff: string; compensation: string }) => void;
  getCustomVestingData: (grantId: string) => { vestingPeriod: string; cliff: string; compensation: string } | undefined;
  grantTypeOptions: Array<{ value: string; label: string }>;
  vestingScheduleOptions: Array<{ value: string; label: string }>;
  onSaveAndClose: (grants: PromisedGrant[]) => Promise<{ success: boolean; results: any[] }>;
  isSaving: boolean;
  saveResults?: Array<{ grantId: string; success: boolean; error?: any }>;
  getStatusIcon: (grantId: string) => React.ReactNode;
  isViewMode?: boolean;
  onIssueGrants?: (grants: PromisedGrant[]) => Promise<{ success: boolean; results: any[] }>;
  isIssuing?: boolean;
  issueResults?: Array<{ grantId: string; success: boolean; error?: any }>;
}

const ModifyGrantsDialog: React.FC<ModifyGrantsDialogProps> = ({
  isOpen,
  onClose,
  selectedGrants,
  localEdits = {},
  onFieldChange,
  onCustomVestingClick,
  onCustomVestingSave,
  getCustomVestingData,
  grantTypeOptions,
  vestingScheduleOptions,
  onSaveAndClose,
  isSaving,
  saveResults = [],
  getStatusIcon,
  isViewMode = false,
  onIssueGrants,
  isIssuing = false,
  issueResults = [],
}) => {
  const mergedGrants = selectedGrants.map((g) => ({ ...g, ...(localEdits[g.id] || {}) }));

  const handleSaveAndClose = async () => {
    if (isViewMode && onIssueGrants) {
      const result = await onIssueGrants(selectedGrants);
      if (result.success) {
        onClose();
      }
    } else {
      const result = await onSaveAndClose(selectedGrants);
      if (result.success) {
        onClose();
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5] max-w-7xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
            {isViewMode ? 'Review Grants for Issuance' : 'Modify Selected Grants'}
          </DialogTitle>
          <DialogDescription className="text-[#F2F2F5] opacity-80">
            {isViewMode 
              ? `Review the details of ${selectedGrants.length} selected grant${selectedGrants.length > 1 ? 's' : ''} before issuance.`
              : `Edit the details of ${selectedGrants.length} selected grant${selectedGrants.length > 1 ? 's' : ''}.`
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-y-auto">
          <div className="border border-[#2E2F3A] rounded-md max-h-[500px] overflow-y-auto">
            <PromisedGrantsTable
              grants={mergedGrants}
              selectedGrants={new Set()}
              showCheckboxes={false}
              actionType="edit"
              onFieldChange={onFieldChange}
              onCustomVestingClick={onCustomVestingClick}
              grantTypeOptions={grantTypeOptions}
              vestingScheduleOptions={vestingScheduleOptions}
              saveResults={isViewMode ? issueResults : saveResults}
              getStatusIcon={getStatusIcon}
              isViewMode={isViewMode}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
          >
            Close
          </Button>
          <Button
            onClick={handleSaveAndClose}
            disabled={isSaving || isIssuing}
            className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
          >
            {isViewMode 
              ? (isIssuing ? "Issuing..." : "Issue Grants")
              : (isSaving ? "Saving..." : "Save and Close")
            }
          </Button>
        </DialogFooter>
      </DialogContent>
      
      <CustomVestingDialog
        isOpen={false}
        onClose={() => {}}
        onSave={(data) => onCustomVestingSave("", data)}
        grantId=""
        grantName=""
      />
    </Dialog>
  );
};

export default ModifyGrantsDialog; 