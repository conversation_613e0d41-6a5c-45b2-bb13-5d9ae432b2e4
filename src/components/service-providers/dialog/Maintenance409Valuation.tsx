import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Upload, FileText, Calendar, DollarSign } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { use409aUpload, useSubmit409a } from "@/hooks/service-providers/usePromisedGrants.hooks";

interface Maintenance409ValuationProps {
  isOpen: boolean;
  onClose: () => void;
}

const Maintenance409Valuation: React.FC<Maintenance409ValuationProps> = ({
  isOpen,
  onClose,
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId || "";
  const { getPresignedUrl, uploadToS3 } = use409aUpload(companyId);
  const submitMutation = useSubmit409a(companyId);
  const [formData, setFormData] = useState({
    fairMarketValue: "",
    appraiserName: "",
    appraisalDate: "",
    valuationFile: null as File | null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size must be less than 5MB");
        return;
      }
      setFormData(prev => ({ ...prev, valuationFile: file }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Validate required fields
    if (!formData.fairMarketValue || !formData.appraiserName || !formData.appraisalDate || !formData.valuationFile) {
      toast.error("Please fill in all required fields");
      setIsSubmitting(false);
      return;
    }

    try {
      const file = formData.valuationFile as File;
      const { uploadUrl, key } = await getPresignedUrl(file);
      await uploadToS3(uploadUrl, file, file.type || 'application/octet-stream');
      await submitMutation.mutateAsync({
        appraiserName: formData.appraiserName,
        appraisalDate: formData.appraisalDate,
        fairMarketValue: Number(formData.fairMarketValue),
        confirmationStatus: 'yes',
        s3Key: key,
      });
      handleClose();
    } catch (error: any) {
      toast.error(error?.message || "Failed to upload 409A valuation. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      fairMarketValue: "",
      appraiserName: "",
      appraisalDate: "",
      valuationFile: null,
    });
    setIsSubmitting(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open) handleClose(); }}>
      <DialogContent className="bg-white border border-gray-200 text-gray-900 max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Upload 409A Valuation Report
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Please provide the 409A valuation details and upload the report.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* File Upload */}
          <div className="space-y-3">
            <Label className="text-gray-900 font-medium flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Upload 409A Valuation Report</span>
            </Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-500/30 transition-colors">
              <input
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleFileChange}
                className="hidden"
                id="valuation-file"
              />
              <label
                htmlFor="valuation-file"
                className="cursor-pointer flex flex-col items-center space-y-3"
              >
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-center">
                  <p className="text-gray-900 font-medium">
                    {formData.valuationFile ? formData.valuationFile.name : "Click to upload"}
                  </p>
                  <p className="text-sm text-gray-500">
                    PDF, DOC, or DOCX (max 5MB)
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* Appraiser Name */}
          <div className="space-y-2">
            <Label className="text-gray-900 font-medium flex items-center space-x-2">
              <span className="w-4 h-4">👤</span>
              <span>Confirm Appraiser Name</span>
            </Label>
            <Input
              value={formData.appraiserName}
              onChange={(e) => setFormData(prev => ({ ...prev, appraiserName: e.target.value }))}
              placeholder="Enter appraiser name"
              className="border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Appraisal Date */}
          <div className="space-y-2">
            <Label className="text-gray-900 font-medium flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Confirm Appraisal Date</span>
            </Label>
            <Input
              type="date"
              value={formData.appraisalDate}
              onChange={(e) => setFormData(prev => ({ ...prev, appraisalDate: e.target.value }))}
              className="border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Fair Market Value */}
          <div className="space-y-2">
            <Label className="text-gray-900 font-medium flex items-center space-x-2">
              <DollarSign className="h-4 w-4" />
              <span>Confirm the fair market value of the Common Stock</span>
            </Label>
            <Input
              type="number"
              value={formData.fairMarketValue}
              onChange={(e) => setFormData(prev => ({ ...prev, fairMarketValue: e.target.value }))}
              placeholder="Enter fair market value"
              className="border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSubmitting ? "Sending..." : "Send for Approval"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default Maintenance409Valuation;
