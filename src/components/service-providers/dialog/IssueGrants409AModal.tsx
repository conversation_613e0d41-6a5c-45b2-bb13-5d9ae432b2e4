import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileText, DollarSign, Upload } from "lucide-react";
import { PromisedGrant } from "@/services/service-providers/promisedGrants.service";

interface IssueGrants409AModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedGrants: PromisedGrant[];
  onOptionSelect: (option: 'use-current' | 'set-fmv' | 'update-409a') => void;
}

const IssueGrants409AModal: React.FC<IssueGrants409AModalProps> = ({
  isOpen,
  onClose,
  selectedGrants,
  onOptionSelect,
}) => {
  const handleOptionSelect = (option: 'use-current' | 'set-fmv' | 'update-409a') => {
    onOptionSelect(option);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5] max-w-md">
        <DialogHeader>
          <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
            Select 409A Valuation Method
          </DialogTitle>
          <DialogDescription className="text-[#F2F2F5] opacity-80">
            Choose how to handle fair market value for {selectedGrants.length} selected grant{selectedGrants.length > 1 ? 's' : ''}.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <Button
            onClick={() => handleOptionSelect('use-current')}
            className="w-full h-16 bg-gradient-to-r from-[#9B51E0] to-[#E1467C] text-[#F2F2F5] hover:opacity-90 flex items-center justify-start space-x-3 px-4"
          >
            <FileText className="h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">Use Current 409A Valuation</div>
              <div className="text-sm opacity-80">Use existing 409A without changes</div>
            </div>
          </Button>

          <Button
            onClick={() => handleOptionSelect('set-fmv')}
            className="w-full h-16 bg-[#2E2F3A] border border-[#9B51E0] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 flex items-center justify-start space-x-3 px-4"
          >
            <DollarSign className="h-5 w-5 text-[#9B51E0]" />
            <div className="text-left">
              <div className="font-medium">Set Fair Market Value</div>
              <div className="text-sm opacity-80">Set a new fair market value manually</div>
            </div>
          </Button>

          <Button
            onClick={() => handleOptionSelect('update-409a')}
            className="w-full h-16 bg-[#2E2F3A] border border-[#9B51E0] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 flex items-center justify-start space-x-3 px-4"
          >
            <Upload className="h-5 w-5 text-[#9B51E0]" />
            <div className="text-left">
              <div className="font-medium">Update 409A Valuation</div>
              <div className="text-sm opacity-80">Upload new 409A valuation report</div>
            </div>
          </Button>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IssueGrants409AModal;
