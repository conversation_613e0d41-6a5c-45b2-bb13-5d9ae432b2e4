import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface CustomVestingData {
  vestingPeriod: string;
  cliff: string;
  compensation: string;
}

interface CustomVestingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: CustomVestingData) => void;
  grantId: string;
  grantName: string;
  initialData?: CustomVestingData;
}

const CustomVestingDialog: React.FC<CustomVestingDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  grantId,
  grantName,
  initialData,
}) => {
  const [formData, setFormData] = useState<CustomVestingData>(
    initialData || {
      vestingPeriod: "",
      cliff: "",
      compensation: "",
    }
  );

  const handleInputChange = (field: keyof CustomVestingData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    onSave(formData);
    setFormData({
      vestingPeriod: "",
      cliff: "",
      compensation: "",
    });
    onClose();
  };

  const handleClose = () => {
    setFormData({
      vestingPeriod: "",
      cliff: "",
      compensation: "",
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-[#1B1D2A] border-[#2E2F3A] text-[#F2F2F5] max-w-md">
        <DialogHeader>
          <DialogTitle className="text-[#F2F2F5] text-xl font-semibold">
            Custom Vesting Schedule
          </DialogTitle>
          <DialogDescription className="text-[#F2F2F5] opacity-80">
            Set custom vesting details for {grantName}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium">
                Vesting Period (months)
              </Label>
              <Input
                type="number"
                value={formData.vestingPeriod}
                onChange={(e) => handleInputChange("vestingPeriod", e.target.value)}
                placeholder="Enter months"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-[#F2F2F5] font-medium">
                Cliff (months)
              </Label>
              <Input
                type="number"
                value={formData.cliff}
                onChange={(e) => handleInputChange("cliff", e.target.value)}
                placeholder="Enter months"
                className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label className="text-[#F2F2F5] font-medium">
              Compensation
            </Label>
            <Input
              type="number"
              value={formData.compensation}
              onChange={(e) => handleInputChange("compensation", e.target.value)}
              placeholder="Enter amount"
              className="bg-[#2E2F3A] border-[#9B51E0] text-[#F2F2F5] focus:ring-1 focus:ring-[#9B51E0] placeholder:text-[#F2F2F5]/50"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            className="bg-[#2E2F3A] text-[#F2F2F5] hover:bg-[#2E2F3A]/80 hover:text-[#F2F2F5]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="bg-gradient-to-r from-[#E1467C] to-[#9B51E0] text-[#F2F2F5] hover:opacity-90"
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CustomVestingDialog; 