import React, { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  ServiceProviderType,
  GrantType,
  OptionType,
  VestingScheduleType,
} from "@/types/serviceProvider";

interface ServiceProviderFormProps {
  type: ServiceProviderType;
  onSubmit: (data: any) => void;
  hasStockOptionPlan: boolean;
}

const baseSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  address: z.string().min(5, { message: "Please enter a valid address." }),
  services: z.string().optional(),
  grantType: z.enum(["Option", "Restricted Stock", "Restricted Stock Outside", "None"]),
  optionType: z.enum(["Statutory", "Non-Statutory"]).optional(),
  shares: z.number().min(1, "Number of shares is required and must be at least 1").optional(),
  startDate: z.date(),
  vestingSchedule: z.enum(["Standard", "Custom"]),
  vestingPeriod: z.number().min(1, "Vesting period must be at least 1 month").max(120, "Vesting period cannot exceed 10 years").optional(),
  cliff: z.number().min(0, "Cliff cannot be negative").max(60, "Cliff cannot exceed 5 years").optional(),
  compensation: z.number().min(0, "Compensation cannot be negative").optional(),
  compensationPeriod: z
    .enum(["Annually", "Monthly", "Hourly"])
    .optional(),
  term: z.number().int().min(1, { message: "Term must be at least 1 month." }).max(120, { message: "Term cannot exceed 120 months." }).optional(),
});

const ServiceProviderForm: React.FC<ServiceProviderFormProps> = ({
  type,
  onSubmit,
  hasStockOptionPlan,
}) => {
  const [isCustomVesting, setIsCustomVesting] = useState(false);

  const formSchema = baseSchema.superRefine((data, ctx) => {
    if (data.vestingSchedule === "Custom" && data.vestingPeriod && data.cliff) {
      if (data.cliff > data.vestingPeriod) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Cliff cannot exceed vesting period",
          path: ["cliff"],
        });
      }
    }
    // Require services for Advisor and Contractor
    if ((type === "Advisor" || type === "Contractor") && (!data.services || data.services.trim().length < 2)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter the services to be provided.",
        path: ["services"],
      });
    }
    // Require term for Contractor
    if (type === "Contractor" && (data.term === undefined || data.term === null)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter the contract term in months.",
        path: ["term"],
      });
    }
    // Require shares when grant type is not "None"
    if (data.grantType !== "None" && (data.shares === undefined || data.shares === null || data.shares < 1)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter the number of shares to be granted.",
        path: ["shares"],
      });
    }
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      address: "",
      services: type !== "Employee" ? "" : undefined,
      grantType: "Option",
      optionType: type !== "Employee" ? "Non-Statutory" : "Statutory",
      shares: undefined,
      startDate: new Date(),
      vestingSchedule: "Standard",
      vestingPeriod: undefined,
      cliff: undefined,
      compensation: undefined,
      compensationPeriod: type === "Employee" ? "Annually" : type === "Contractor" ? "Monthly" : "Hourly",
      term: type === "Contractor" ? undefined : undefined,
    },
  });

  const grantType = form.watch("grantType");
  const vestingSchedule = form.watch("vestingSchedule");

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    // Add the service provider type
    const formData = {
      ...values,
      type,
    };
    onSubmit(formData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{type} Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter full name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter full address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {type !== "Employee" && (
          <FormField
            control={form.control}
            name="services"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Services to be provided</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe the services to be provided"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="grantType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type of Grant</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Option" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Option{" "}
                      {type !== "Employee" &&
                        "(Default to Non-Statutory Option Grant)"}
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Restricted Stock" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Restricted Stock Grant
                      {hasStockOptionPlan && (
                        <span className="text-sm text-gray-500 ml-2">
                          (These shares will be issued from the Stock Option
                          Plan)
                        </span>
                      )}
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value="Restricted Stock Outside" />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Restricted Stock Grant
                      <span className="text-sm text-gray-500 ml-2">
                        (These shares will be issued from outside the Stock Option Plan)
                      </span>
                    </FormLabel>
                  </FormItem>
                  {type !== "Employee" && (
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="None" />
                      </FormControl>
                      <FormLabel className="font-normal">None</FormLabel>
                    </FormItem>
                  )}
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {grantType !== "None" && (
          <FormField
            control={form.control}
            name="shares"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Shares</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter number of shares"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Start Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date < new Date("1900-01-01") ||
                      date >
                        new Date(
                          new Date().setFullYear(new Date().getFullYear() + 1)
                        )
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {grantType !== "None" && (
          <FormField
            control={form.control}
            name="vestingSchedule"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Vesting Schedule</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      field.onChange(value);
                      setIsCustomVesting(value === "Custom");
                    }}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Standard" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Standard (
                        {type === "Employee"
                          ? "4 years with 1 year cliff"
                          : "2 years, monthly vesting"}
                        )
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="Custom" />
                      </FormControl>
                      <FormLabel className="font-normal">Custom</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {grantType !== "None" && isCustomVesting && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="vestingPeriod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vesting Period (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter months"
                      {...field}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseInt(e.target.value) : undefined
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="cliff"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cliff (months)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter months"
                      {...field}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value ? parseInt(e.target.value) : undefined
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <FormField
          control={form.control}
          name="compensation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Compensation</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter amount"
                  {...field}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {type === "Contractor" && (
          <FormField
            control={form.control}
            name="term"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Term (months)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter months"
                    {...field}
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Compensation Period: For Contractor, show as static text or disabled input */}
        {type === "Contractor" ? (
          <FormField
            control={form.control}
            name="compensationPeriod"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Compensation Period</FormLabel>
                <FormControl>
                  <div className="flex gap-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="monthly"
                        value="Monthly"
                        checked={true}
                        disabled
                        className="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 cursor-not-allowed"
                      />
                      <label htmlFor="monthly" className="text-sm font-medium text-gray-500 cursor-not-allowed">
                        Monthly
                      </label>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : (
          <FormField
            control={form.control}
            name="compensationPeriod"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Compensation Period</FormLabel>
                <FormControl>
                  <div className="flex gap-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="hourly"
                        value="Hourly"
                        checked={field.value === "Hourly"}
                        onChange={() => field.onChange("Hourly")}
                        className="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                      />
                      <label htmlFor="hourly" className="text-sm font-medium text-gray-900">
                        Hourly
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="monthly"
                        value="Monthly"
                        checked={field.value === "Monthly"}
                        onChange={() => field.onChange("Monthly")}
                        className="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                      />
                      <label htmlFor="monthly" className="text-sm font-medium text-gray-900">
                        Monthly
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="annually"
                        value="Annually"
                        checked={field.value === "Annually"}
                        onChange={() => field.onChange("Annually")}
                        className="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
                      />
                      <label htmlFor="annually" className="text-sm font-medium text-gray-900">
                        Annually
                      </label>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <Button type="submit">Save</Button>
        </div>
      </form>
    </Form>
  );
};

export default ServiceProviderForm;
