import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { usePendingServiceProviders, useTerminateServiceProvider } from "@/hooks/service-providers/useTerminate.hooks";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface TerminateDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const terminationTypes = [
  { value: 'involuntary_termination_without_cause', label: 'Involuntary Termination (Without Cause)' },
  { value: 'involuntary_termination_with_cause', label: 'Involuntary Termination (With Cause)' },
  { value: 'resignation', label: 'Resignation' },
];

const TerminateDialog: React.FC<TerminateDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { user } = useAuth();
  const companyId = user?.companyId || "";
  const { data: serviceProviders = [], isLoading } = usePendingServiceProviders(companyId, {
    enabled: isOpen && !!companyId,
  });
  const terminateMutation = useTerminateServiceProvider();
  
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [terminationDate, setTerminationDate] = useState<string>("");
  const [terminationType, setTerminationType] = useState<string>("");
  const [severanceAmount, setSeveranceAmount] = useState<string>("");
  const [step, setStep] = useState<"select" | "confirm">("select");

  // Filter active providers (terminated === false)
  const activeProviders = serviceProviders.filter(
    (p) => p.terminatedOn === null
  );

  const selectedProvider = serviceProviders.find(
    (p) => p.id === selectedProviderId
  );

  const handleTerminate = async () => {
    if (selectedProviderId && terminationDate && terminationType) {
      try {
        await terminateMutation.mutateAsync({ 
          id: selectedProviderId, 
          terminationDate,
          serviceProviderTerminationType: terminationType,
          severanceAmount: parseFloat(severanceAmount) || 0,
          reasonForTermination: reason || "No reason provided" 
        });
        toast.success("Termination has been sent for approval.");
        resetAndClose();
      } catch (error) {
        toast.error("Failed to terminate service provider");
        console.error("Termination error:", error);
      }
    }
  };

  const resetAndClose = () => {
    setSelectedProviderId("");
    setReason("");
    setTerminationDate("");
    setTerminationType("");
    setSeveranceAmount("");
    setStep("select");
    onClose();
  };

  const isFormValid = () => {
    if (!selectedProviderId || !terminationDate || !terminationType) {
      return false;
    }
    
    // Validate that termination date is not in the future
    const today = new Date();
    const selectedDate = new Date(terminationDate);
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);
    
    return selectedDate <= today;
  };

  const getValidationError = () => {
    if (!selectedProviderId) return "Please select a service provider";
    if (!terminationDate) return "Please select a termination date";
    if (!terminationType) return "Please select a termination type";
    
    const today = new Date();
    const selectedDate = new Date(terminationDate);
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);
    
    if (selectedDate > today) return "Termination date cannot be in the future";
    
    return null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {step === "select"
              ? "Terminate Service Provider"
              : "Confirm Termination"}
          </DialogTitle>
          <DialogDescription>
            {step === "select"
              ? "Select a service provider to terminate."
              : "Please review and confirm termination details."}
          </DialogDescription>
        </DialogHeader>

        {step === "select" ? (
          <>
            {isLoading ? (
              <div className="py-4 text-center">
                <p className="text-gray-500">Loading service providers...</p>
              </div>
            ) : activeProviders.length > 0 ? (
              <>
                <div className="py-4">
                  <Label className="text-base">Select Service Provider</Label>
                  <div className="mt-2 max-h-64 overflow-y-auto">
                    <RadioGroup
                      value={selectedProviderId}
                      onValueChange={setSelectedProviderId}
                      className="space-y-2"
                    >
                      {activeProviders.map((provider) => (
                        <div
                          key={provider.id}
                          className="flex items-center space-x-2 border p-3 rounded-md"
                        >
                          <RadioGroupItem value={provider.id} id={provider.id} />
                          <Label htmlFor={provider.id} className="flex-1">
                            <div className="font-medium">{provider.name}</div>
                            <div className="text-sm text-gray-500">
                              {provider.type} • {provider.email}
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                </div>

                <div className="space-y-4 py-2">
                  <div>
                    <Label htmlFor="terminationDate">
                      Termination Date *
                    </Label>
                    <Input
                      id="terminationDate"
                      type="date"
                      value={terminationDate}
                      onChange={(e) => setTerminationDate(e.target.value)}
                      max={new Date().toISOString().split('T')[0]}
                      className="mt-1"
                      required
                    />
                  </div>

                  <div>
                    <Label className="text-base">Termination Type *</Label>
                    <RadioGroup
                      value={terminationType}
                      onValueChange={setTerminationType}
                      className="mt-2 space-y-2"
                    >
                      {terminationTypes.map((type) => (
                        <div key={type.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={type.value} id={type.value} />
                          <Label htmlFor={type.value} className="text-sm">
                            {type.label}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>

                  <div>
                    <Label htmlFor="severanceAmount">
                      Severance Amount
                    </Label>
                    <Input
                      id="severanceAmount"
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      value={severanceAmount}
                      onChange={(e) => setSeveranceAmount(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="reason">
                      Reason for Termination (Optional)
                    </Label>
                    <Textarea
                      id="reason"
                      placeholder="Enter reason for termination"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>

                {!isFormValid() && getValidationError() && (
                  <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
                    {getValidationError()}
                  </div>
                )}

                <DialogFooter>
                  <Button variant="outline" onClick={resetAndClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={() => isFormValid() && setStep("confirm")}
                    disabled={!isFormValid()}
                  >
                    Continue
                  </Button>
                </DialogFooter>
              </>
            ) : (
              <div className="py-4 text-center">
                <p className="text-gray-500">
                  No active service providers found.
                </p>
                <Button
                  variant="outline"
                  onClick={resetAndClose}
                  className="mt-4"
                >
                  Close
                </Button>
              </div>
            )}
          </>
        ) : (
          <>
            {selectedProvider && (
              <div className="space-y-4 py-2">
                <div>
                  <h3 className="text-sm font-medium">
                    You are about to terminate:
                  </h3>
                  <div className="mt-2 p-3 border rounded-md">
                    <p className="font-medium">{selectedProvider.name}</p>
                    <p className="text-sm text-gray-500">
                      {selectedProvider.type}
                    </p>
                    <p className="text-sm text-gray-500">
                      {selectedProvider.email}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Termination Details:</h3>
                  <div className="mt-2 space-y-1 text-sm">
                    <p><span className="font-medium">Date:</span> {terminationDate}</p>
                    <p><span className="font-medium">Type:</span> {terminationTypes.find(t => t.value === terminationType)?.label}</p>
                    <p><span className="font-medium">Severance Amount:</span> ${severanceAmount || '0.00'}</p>
                    {reason && (
                      <p><span className="font-medium">Reason:</span> {reason}</p>
                    )}
                  </div>
                </div>

                <div className="text-sm text-amber-600 border-l-4 border-amber-500 pl-3 py-2 bg-amber-50 rounded-r-md">
                  <p className="font-medium">Important</p>
                  <p>
                    This action will terminate the service provider's
                    relationship with your company. It cannot be undone.
                  </p>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setStep("select")}>
                Back
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleTerminate}
                disabled={terminateMutation.isPending}
              >
                {terminateMutation.isPending ? "Terminating..." : "Terminate"}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TerminateDialog;
