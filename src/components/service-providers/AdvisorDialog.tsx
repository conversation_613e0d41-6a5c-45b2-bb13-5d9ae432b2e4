import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import ServiceProviderForm from "./ServiceProviderForm";
import { useServiceProviders } from "@/hooks/serviceProviders";
import { useAddCompanyAdvisor } from "@/hooks/service-providers/useAdvisor.hooks";
import { useAuth } from "@/contexts/AuthContext";

interface AdvisorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdvisorDialog: React.FC<AdvisorDialogProps> = ({ isOpen, onClose }) => {
  const { addServiceProvider, companyEquity } = useServiceProviders();
  const [step, setStep] = useState<"form" | "confirmation">("form");
  const [formData, setFormData] = useState<any>(null);

  const { user } = useAuth();
  const { mutate: addAdvisor, isPending } = useAddCompanyAdvisor();

  const handleSubmit = async (data: any) => {
    setFormData(data);
    setStep("confirmation");
  };

  // const handleFinish = async () => {
  //   const success = await addServiceProvider(formData);
  //   if (success) {
  //     toast({
  //       title: "Advisor Onboarding Initiated",
  //       description:
  //         "The advisor will receive an email with onboarding documentation.",
  //     });
  //     resetAndClose();
  //   }
  // };

  const handleFinish = () => {
    if (!user?.companyId) return;
    
    // Transform form data to match API expectations
    const transformedData: any = {
      name: formData.name,
      email: formData.email,
      address: formData.address,
      services: formData.services,
      grantType:
        formData.grantType === "Option"
          ? "option"
          : formData.grantType === "Restricted Stock"
          ? "restricted-stock-grant-inside-of-stock-option-plan"
          : formData.grantType === "Restricted Stock Outside"
          ? "restricted-stock-grant-outside-of-stock-option-plan"
          : "none",
      startDate: formData.startDate.toISOString().split('T')[0],
      compensation: formData.compensation,
      compensationPeriod: formData.compensationPeriod.toLowerCase() as "hourly" | "monthly" | "annually",
    };

    // Add conditional fields based on grant type
    if (formData.grantType !== "None") {
      transformedData.shares = formData.shares;
      transformedData.vestingSchedule = formData.vestingSchedule === "Standard" 
        ? "standard-two-years-monthly-vesting" 
        : "custom";
      
      // Add vesting period and cliff for custom vesting
      if (formData.vestingSchedule === "Custom") {
        transformedData.vestingPeriod = formData.vestingPeriod;
        transformedData.cliff = formData.cliff;
      }
    }
    
    addAdvisor(
      { companyId: user.companyId, payload: transformedData },
      {
        onSuccess: () => {
          resetAndClose();
        },
        onError: () => {
          // Error is handled in the hook (toast), dialog stays open
        },
      }
    );
  };

  const resetAndClose = () => {
    setStep("form");
    setFormData(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && resetAndClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {step === "form" ? "Onboard Advisor" : "Confirm Advisor Details"}
          </DialogTitle>
          <DialogDescription>
            {step === "form"
              ? "Add a new advisor to your company and set up their equity grant."
              : "Review the information before finalizing."}
          </DialogDescription>
        </DialogHeader>

        {step === "form" ? (
          <ServiceProviderForm
            type="Advisor"
            onSubmit={handleSubmit}
            hasStockOptionPlan={!!companyEquity.stockOptionPlan}
          />
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium">Advisor Name</h3>
                <p className="text-sm">{formData.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Email</h3>
                <p className="text-sm">{formData.email}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Address</h3>
                <p className="text-sm">{formData.address}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Services</h3>
                <p className="text-sm">{formData.services}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Grant Type</h3>
                <p className="text-sm">{formData.grantType}</p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Shares</h3>
                  <p className="text-sm">{formData.shares}</p>
                </div>
              )}
              <div>
                <h3 className="text-sm font-medium">Start Date</h3>
                <p className="text-sm">
                  {formData.startDate.toLocaleDateString()}
                </p>
              </div>
              {formData.grantType !== "None" && (
                <div>
                  <h3 className="text-sm font-medium">Vesting Schedule</h3>
                  <p className="text-sm">
                    {formData.vestingSchedule === "Standard"
                      ? "2 years monthly"
                      : `Custom (${formData.vestingPeriod} months, ${formData.cliff} months cliff)`}
                  </p>
                </div>
              )}
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Compensation</h3>
                <p className="text-sm">
                  ${formData.compensation}{" "}
                  {formData.compensationPeriod.toLowerCase()}
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setStep("form")}>
                Back
              </Button>
              <Button onClick={handleFinish} disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  "Finish"
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AdvisorDialog;
