"use client";

import { useEffect, useState } from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
  Row,
  Column,
  Table as TableType,
} from "@tanstack/react-table";

import TableSkeleton from "@/components/shared/data-table/table-skeleton";
import { DataTableManageColumns } from "@/components/shared/data-table/data-table-manage-columns";
import { Checkbox } from "@/components/ui/checkbox";

interface TotalColumn<TData> {
  columnId: keyof TData;
  format?: (value: number) => React.ReactNode;
}

interface TestCaseData {
  test_case_id?: string;
  [key: string]: any;
}

interface ColumnMetaType {
  sticky?: string;
}

interface DataTableProps<TData extends TestCaseData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[] | any;
  border?: boolean;
  hover?: boolean;
  loading?: boolean;
  height?: string;
  headerSticky?: boolean;
  lottieWidth?: number;
  lottieHeight?: number;
  total?: TotalColumn<TData>[];
  loadingDataNum?: number | 1;
  selectedId?: string | number;
  showManageColumn?: boolean;
  children?: React.ReactNode;
  module?: string;
  applyColumns?: (arg: string) => void;
  headerBgClass?: string;
  className?: string;
  showCheckbox?: boolean;
  onRowSelect?: (selectedRows: any[]) => void;
  isEditMode?: boolean;
  resetSelection?: boolean;
  dashboard?: boolean;
}

export function DataTable<TData extends TestCaseData, TValue>({
  columns,
  dashboard,
  data,
  border,
  hover,
  loading,
  height,
  headerSticky,
  lottieWidth,
  lottieHeight,
  total,
  loadingDataNum,
  selectedId,
  showManageColumn,
  children,
  module,
  applyColumns,
  headerBgClass,
  className,
  showCheckbox,
  onRowSelect,
  isEditMode,
  resetSelection,
}: DataTableProps<TData, TValue> & { isEditMode?: boolean }) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});

  // Reset row selection when resetSelection changes
  useEffect(() => {
    if (resetSelection) {
      setRowSelection({});
    }
  }, [resetSelection]);

  // Define selection column
  const selectionColumn: ColumnDef<TData, TValue> & { meta?: ColumnMetaType } =
    {
      id: "select",
      header: ({ table }: { table: TableType<TData> }) => {
        const isChecked = table.getIsAllPageRowsSelected();
        const isIndeterminate = table.getIsSomePageRowsSelected() && !isChecked;

        return (
          <Checkbox
            checked={isChecked}
            onClick={(e) => e.stopPropagation()}
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-[2px]"
            ref={(ref: any) => {
              if (ref) {
                ref.indeterminate = isIndeterminate;
              }
            }}
          />
        );
      },
      cell: ({ row }: { row: Row<TData> }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          onClick={(e) => e.stopPropagation()}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    };

  // Initialize column visibility based on enableHiding property
  const initialVisibility = columns.reduce((acc, column) => {
    if (column.id !== undefined) {
      acc[column.id] = !column.enableHiding;
    }
    return acc;
  }, {} as Record<string, boolean>);

  const [columnVisibility, setColumnVisibility] =
    useState<VisibilityState>(initialVisibility);

  // Only include selection column when in edit mode
  const finalColumns = isEditMode ? [selectionColumn, ...columns] : columns;

  const table = useReactTable({
    data,
    columns: finalColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const calculateTotals = (
    data: TData[],
    totalColumns: TotalColumn<TData>[]
  ) => {
    return totalColumns.map((totalColumn) => {
      const sum = data.reduce((acc, curr) => {
        const value = curr[totalColumn.columnId];
        return acc + Number(value);
      }, 0);
      return {
        columnId: totalColumn.columnId,
        value: totalColumn.format ? totalColumn.format(sum) : sum,
      };
    });
  };
  const showColumnHandler = (newVisibility?: VisibilityState) => {
    if (newVisibility) {
      const parsedColumns = Object.keys(newVisibility)
        .filter((key) => newVisibility[key] === true && key !== "sn")
        .join(",");
      setColumnVisibility(newVisibility);
      applyColumns && applyColumns(parsedColumns);
    } else {
      const storedVisibility = localStorage.getItem(
        `columnVisibility_${module}`
      );
      if (storedVisibility) {
        const parsedVisibility = JSON.parse(storedVisibility);
        setColumnVisibility((prev) => ({
          ...prev,
          ...parsedVisibility,
        }));
        applyColumns && applyColumns(parsedVisibility);
      }
    }
  };
  const totals = total ? calculateTotals(data, total) : [];

  // Load column visibility from localStorage
  useEffect(() => {
    if (module) {
      showColumnHandler();
    }
  }, [module]);

  useEffect(() => {
    if (onRowSelect) {
      const selectedRows = table
        .getRowModel()
        .rows.filter((row) => row.getIsSelected())
        .map((row) => row.original);
      onRowSelect(selectedRows);
    }
  }, [rowSelection]);

  const handleCellSave = (
    row: Row<TData>,
    column: Column<TValue>,
    value: string
  ) => {
    const updatedData = data.map((item: any) => {
      if (item.test_case_id === row.original.test_case_id) {
        return {
          ...item,
          [column.id]: value,
        };
      }
      return item;
    });
    // Update the state or perform any necessary actions with updatedData
  };

  return (
    <div className={cn(className)}>
      {(showManageColumn || children) && (
        <div className="flex justify-between items-center">
          {showManageColumn && (
            <DataTableManageColumns
              module={module ?? ""}
              table={table}
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              applyManageColumn={showColumnHandler}
            />
          )}
          {children}
        </div>
      )}
      <div
        className={cn(
          `relative ${dashboard ? "" : "overflow-auto"}`,
          border && "border-2 border-slate-100",
          height && height
        )}
      >
        <Table className="w-full">
          <TableHeader className="sticky top-0 z-10 bg-white border-b">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={cn(
                      border &&
                        "border-r-2 border-slate-100 last:border-r-0 text-zinc-800 font-medium",
                      "whitespace-nowrap bg-white",
                      headerBgClass,
                      header.column.columnDef.size &&
                        `max-w-[${header.column.columnDef.size}px] w-[${header.column.columnDef.size}px] min-w-[${header.column.columnDef.size}px]`
                    )}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {loading ? (
              Array.from(
                { length: loadingDataNum || 3 },
                (_, rowIndex) => (
                  <TableRow className="[&>*]:last:border-b-0" key={`skeleton-row-${rowIndex}`}>
                    {Array.from(
                      { length: columns.length },
                      (_, cellIndex) => (
                        <TableSkeleton key={`skeleton-cell-${rowIndex}-${cellIndex}`} />
                      )
                    )}
                  </TableRow>
                )
              )
            ) : table?.getRowModel().rows?.length ? (
              <>
                {table?.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={`${
                      row?.original?.status === "failed"
                        ? "!bg-destructive/20 "
                        : ""
                    }`}
                  >
                    {row?.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
                {totals.length > 0 && (
                  <TableRow className={cn("font-semibold !bg-slate-100")}>
                    {columns.map((column, index) => {
                      const totalColumn = totals.find(
                        (t) => t.columnId === column?.id
                      );
                      return (
                        <>
                          <TableCell key={index}>
                            {totalColumn ? totalColumn.value : ""}
                          </TableCell>
                        </>
                      );
                    })}
                  </TableRow>
                )}
              </>
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className={border ? "h-24 text-center" : "h-24 text-center"}
                >
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
