import React from "react";
import { Director } from "@/services/manage-directors/manageDirectors.service";
import DirectorsTable from "./DirectorsTable";
import EmptyDirectorsState from "./EmptyDirectorsState";

interface DirectorsListProps {
  directors: Director[];
}

const DirectorsList: React.FC<DirectorsListProps> = ({
  directors,
}) => {
  if (directors.length === 0) {
    return <EmptyDirectorsState />;
  }

  return (
    <DirectorsTable directors={directors}/>
  );
};

export default DirectorsList;
