import React from "react";
import { Calendar, Check, X } from "lucide-react";
import { format } from "date-fns";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Director } from "./types";

interface DirectorRowProps {
  director: Director;
  editingId: string | null;
  onEditClick: (director: Director) => void;
  onSaveClick: (id: string) => void;
  onDateChange: (id: string, date: Date | undefined) => void;
  onResignationDateChange: (id: string, date: Date | undefined) => void;
  onResignedChange: (id: string) => void;
  onRemoveChange: (id: string) => void;
  onRemovalDateChange: (id: string, date: Date | undefined) => void;
}

const DirectorRow: React.FC<DirectorRowProps> = ({
  director,
  editingId,
  onEditClick,
  onSaveClick,
  onDateChange,
  onResignationDateChange,
  onResignedChange,
  onRemoveChange,
  onRemovalDateChange,
}) => {
  const isEditing = editingId === director.id;

  return (
    <TableRow>
      <TableCell>{director.name}</TableCell>
      <TableCell>
        {isEditing ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                {director.appointmentDate
                  ? format(director.appointmentDate, "PPP")
                  : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={director.appointmentDate}
                onSelect={(date) => onDateChange(director.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          format(director.appointmentDate, "PPP")
        )}
      </TableCell>
      <TableCell>
        <Switch
          checked={director.resigned}
          onCheckedChange={() => onResignedChange(director.id)}
          disabled={editingId !== director.id}
        />
      </TableCell>
      <TableCell>
        {isEditing ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={!director.resigned}
                className={!director.resigned ? "opacity-50" : ""}
              >
                <Calendar className="h-4 w-4 mr-2" />
                {director.resignationDate
                  ? format(director.resignationDate, "PPP")
                  : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={director.resignationDate || undefined}
                onSelect={(date) => onResignationDateChange(director.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          <span className={!director.resigned ? "text-gray-400" : ""}>
            {director.resignationDate
              ? format(director.resignationDate, "PPP")
              : "N/A"}
          </span>
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          director.removalStatus === "yes" ? (
            <span className="text-blue-600 font-medium">Yes</span>
          ) : director.removalStatus === "pending" ? (
            <span className="text-orange-600 font-medium">Pending</span>
          ) : (
          <Switch
              checked={director.removed || false}
              onCheckedChange={() => onRemoveChange(director.id)}
              className="cursor-pointer"
          />
          )
        ) : director.removalStatus === "yes" ? (
          <span className="text-blue-600 font-medium">Yes</span>
        ) : director.removalStatus === "pending" ? (
          <span className="text-orange-600 font-medium">Pending</span>
        ) : (
          <span className="text-gray-500">No</span>
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={director.removalStatus !== "no"}
                className={director.removalStatus !== "no" ? "opacity-50" : "cursor-pointer"}
              >
                <Calendar className="h-4 w-4 mr-2" />
                {director.removalDate
                  ? format(director.removalDate, "PPP")
                  : "Select date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={director.removalDate || undefined}
                onSelect={(date) => onRemovalDateChange(director.id, date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        ) : (
          <span className={!director.removed ? "text-gray-400" : ""}>
            {director.removalDate
              ? format(director.removalDate, "PPP")
              : "N/A"}
          </span>
        )}
      </TableCell>
      <TableCell>
        {isEditing ? (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSaveClick(director.id)}
            disabled={
              director.removalStatus === "no" && 
              (!director.removed || !director.removalDate)
            }
          >
            Save
          </Button>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEditClick(director)}
          >
            Edit
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
};

export default DirectorRow;
