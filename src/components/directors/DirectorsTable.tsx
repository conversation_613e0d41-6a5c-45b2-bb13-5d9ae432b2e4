import React from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Director } from "@/services/manage-directors/manageDirectors.service";

interface DirectorsTableProps {
  directors: Director[];
}

const DirectorsTable: React.FC<DirectorsTableProps> = ({
  directors,
}) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMMM do, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const getResignedText = (resignationStatus: string) => {
    switch (resignationStatus) {
      case "yes":
        return "Yes";
      case "no":
        return "No";
      case "pending":
        return "Pending";
      default:
        return "No";
    }
  };

  const getResignedColor = (resignationStatus: string) => {
    switch (resignationStatus) {
      case "yes":
        return "bg-green-100 text-green-800";
      case "no":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRemovedText = (removalStatus: string) => {
    switch (removalStatus) {
      case "yes":
        return "Yes";
      case "no":
        return "No";
      case "pending":
        return "Pending";
      default:
        return "No";
    }
  };

  const getRemovedColor = (removalStatus: string) => {
    switch (removalStatus) {
      case "yes":
        return "bg-green-100 text-green-800";
      case "no":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };


  return (
    <div className="max-h-96 overflow-auto border rounded-lg">
      <Table>
        <TableHeader className="sticky top-0 bg-white z-10">
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Appointment Date</TableHead>
            <TableHead>Resigned</TableHead>
            <TableHead>Resignation Date</TableHead>
            <TableHead>Removed</TableHead>
            <TableHead>Removal Date</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {directors.map((director) => (
            <TableRow key={director.id}>
              <TableCell className="font-medium">{director.name}</TableCell>
              <TableCell>{formatDate(director.startDate)}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  getResignedColor(director.resignationStatus)
                }`}>
                  {getResignedText(director.resignationStatus)}
                </span>
              </TableCell>
              <TableCell>{formatDate(director.resignationDate)}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  getRemovedColor(director.removalStatus)
                }`}>
                  {getRemovedText(director.removalStatus)}
                </span>
              </TableCell>
              <TableCell>{formatDate(director.removalDate)}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  director.isActive
                    ? "bg-green-100 text-green-800" 
                    : "bg-red-100 text-red-800"
                }`}>
                  {director.isActive ? "Active" : "Inactive"}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DirectorsTable;
