import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/common/Card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useDirectorsManagement } from "./hooks/useDirectorsManagement";
import { useManageDirectors } from "@/hooks/manage-directors/useManageDirectors.hooks";
import { RemoveDirectorRequest } from "@/services/manage-directors/manageDirectors.service";
import BoardSizeDisplay from "./BoardSizeDisplay";
import DirectorsList from "./DirectorsList";
import { Director } from "./types";
import { Director as APIDirector } from "@/services/manage-directors/manageDirectors.service";

const DirectorsManagementForm: React.FC = () => {
  const navigate = useNavigate();

  const {
    directors,
    boardSize,
    loading,
    vacancies,
    addDirector,
    updateDirector,
    generateConsents,
    uploadResignationLetter,
  } = useDirectorsManagement();

  const { 
    director<PERSON><PERSON><PERSON>y, 
    directors: apiDirectors, 
    loading: directorS<PERSON>mary<PERSON>oading, 
    error: director<PERSON><PERSON>mary<PERSON>rror, 
    addDirector: addDirector<PERSON>romHook,
    removeDirector: removeDirectorFromHook,
    resignDirector: resignDirectorFromHook
  } = useManageDirectors();

  // Removed isAddingDirector and exceedsBoardSize state since we're not using the Add Director button anymore

  // Map API directors to component format
  const mappedDirectors = useMemo(() => {
    return apiDirectors.map((apiDirector: APIDirector) => {
      // Compute status based on resignation and removal status
      const isActive = apiDirector.resignationStatus === "no" && apiDirector.removalStatus === "no";
      const status: "Active" | "Inactive" = isActive ? "Active" : "Inactive";
      
      return {
        id: apiDirector.id,
        name: apiDirector.name,
        appointmentDate: new Date(apiDirector.startDate),
        resigned: apiDirector.resignationStatus === "yes",
        resignationDate: apiDirector.resignationDate ? new Date(apiDirector.resignationDate) : null,
        stockholderApproval: false, // Default value since API doesn't provide this
        status, // Add computed status
      };
    });
  }, [apiDirectors]);

  const handleSaveChanges = async () => {
    try {
      await generateConsents(false); // Assuming exceedsBoardSize is no longer a state
      toast.success(
        false // Assuming exceedsBoardSize is no longer a state
          ? "Board and stockholder consents have been generated and sent for signatures."
          : "Stockholder consent has been generated and sent for signature."
      );
    } catch (error) {
      toast.error("Failed to save changes. Please try again.");
    }
  };

  const handleAddDirector = async (directorData: Partial<Director>) => {
    try {
      await addDirectorFromHook({
        name: directorData.name || "",
        startDate: directorData.appointmentDate ? directorData.appointmentDate.toISOString().split('T')[0] : "",
        emailAddress: null,
        contactAddress: null,
      });
      toast.success("Director added successfully");
    } catch (error: any) {
      // Handle API validation errors
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = Object.values(validationErrors).flat();
        toast.error(errorMessages.join(", "));
      } else {
        toast.error(error?.message || "Failed to add director. Please try again.");
      }
    }
  };

  const handleAddDirectorFromDialog = async (name: string) => {
    try {
      await addDirectorFromHook({
        name: name,
        startDate: new Date().toISOString().split('T')[0], // Use current date as start date
        emailAddress: null,
        contactAddress: null,
      });
      toast.success("Director added successfully");
    } catch (error: any) {
      // Handle API validation errors
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = Object.values(validationErrors).flat();
        toast.error(errorMessages.join(", "));
      } else {
        toast.error(error?.message || "Failed to add director. Please try again.");
      }
      throw error; // Re-throw to let the dialog handle it
    }
  };

  const handleUpdateBoardSize = async (data: any) => {
    try {
      // TODO: Implement API call to update board size and trigger approval flows
      console.log("Update board size data:", data);
      
      // For now, just show a success message
      toast.success("Board size update submitted for approval");
      
      // TODO: Refetch director summary after successful update
      // await fetchDirectorSummary();
    } catch (error: any) {
      toast.error(error?.message || "Failed to update board size. Please try again.");
    }
  };

  const handleRemoveDirectorFromDialog = async (directorId: string) => {
    try {
      await removeDirectorFromHook(directorId);
      toast.success("Director removed successfully");
    } catch (error: any) {
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = Object.values(validationErrors).flat();
        toast.error(errorMessages.join(", "));
      } else {
        toast.error(error?.message || "Failed to remove director. Please try again.");
      }
      throw error; // Re-throw to let the dialog handle it
    }
  };

  const handleResignDirectorFromDialog = async (directorId: string, resignationDate: string) => {
    try {
      await resignDirectorFromHook(directorId, resignationDate);
      toast.success("Director resigned successfully");
    } catch (error: any) {
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = Object.values(validationErrors).flat();
        toast.error(errorMessages.join(", "));
      } else {
        toast.error(error?.message || "Failed to resign director. Please try again.");
      }
      throw error; // Re-throw to let the dialog handle it
    }
  };

  // Removed handleUpdateDirector and handleSaveRemovalChanges since we're not editing in the table anymore

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <BoardSizeDisplay 
          directorSummary={directorSummary}
          loading={directorSummaryLoading}
          error={directorSummaryError}
          onUpdateBoardSize={handleUpdateBoardSize}
          onAddDirector={handleAddDirectorFromDialog}
          onRemoveDirector={handleRemoveDirectorFromDialog}
          onResignDirector={handleResignDirectorFromDialog}
          directors={apiDirectors}
        />

        <h3 className="text-lg font-medium mt-6 mb-3">Current Directors</h3>

        <DirectorsList
          directors={apiDirectors}
        />

        {/* Save Changes button for removal changes */}
        {/* Removed removalChanges.size > 0 && Array.from(removalChanges.values()).some(changes => changes.removed) && ( */}
        {/*   <div className="mt-6 flex justify-end"> */}
        {/*     <Button  */}
        {/*       onClick={() => { */}
        {/*         handleSaveRemovalChanges(); */}
        {/*       }}  */}
        {/*       disabled={loading} */}
        {/*     > */}
        {/*       Save Changes */}
        {/*     </Button> */}
        {/*   </div> */}
        {/* ) */}

        {/* Removed isAddingDirector && ( */}
        {/*   <div className="mt-6 flex justify-end"> */}
        {/*     <Button onClick={handleSaveChanges} disabled={loading}> */}
        {/*       Save Changes */}
        {/*     </Button> */}
        {/*   </div> */}
        {/* ) */}
      </Card>
    </div>
  );
};

export default DirectorsManagementForm;
