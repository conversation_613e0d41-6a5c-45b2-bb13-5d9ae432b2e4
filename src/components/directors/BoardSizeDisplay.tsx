import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Plus, Settings, UserMinus, UserX } from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Director } from "@/services/manage-directors/manageDirectors.service";
import UpdateBoardSizeDialog from "./dialog/UpdateBoardSizeDialog";
import AddDirectorDialog from "./dialog/AddDirectorDialog";
import RemoveDirectorDialog from "./dialog/RemoveDirectorDialog";
import ResignationDialog from "./dialog/ResignationDialog";

interface BoardSizeDisplayProps {
  directorSummary: DirectorSummary;
  loading: boolean;
  error: string | null;
  onUpdateBoardSize?: (data: any) => Promise<void>;
  onAddDirector?: (name: string) => Promise<void>;
  onRemoveDirector?: (directorId: string) => Promise<void>;
  onResignDirector?: (directorId: string, resignationDate: string) => Promise<void>;
  directors: Director[];
}

const BoardSizeDisplay: React.FC<BoardSizeDisplayProps> = ({
  directorSummary,
  loading,
  error,
  onUpdateBoardSize,
  onAddDirector,
  onRemoveDirector,
  onResignDirector,
  directors
}) => {
  const [isUpdateBoardSizeDialogOpen, setIsUpdateBoardSizeDialogOpen] = useState(false);
  const [isAddDirectorDialogOpen, setIsAddDirectorDialogOpen] = useState(false);
  const [isRemoveDirectorDialogOpen, setIsRemoveDirectorDialogOpen] = useState(false);
  const [isResignationDialogOpen, setIsResignationDialogOpen] = useState(false);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="mb-8">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600 text-center">Failed to load director summary: {error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!directorSummary) {
    return null;
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Authorized Size Card */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Authorized Size</CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsUpdateBoardSizeDialogOpen(true)}>
                    <Settings className="mr-2 h-4 w-4" />Update Authorized Size
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{directorSummary.authorizeSize}</div>
          </CardContent>
        </Card>

        {/* Current Directors Card */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Current Directors</CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsRemoveDirectorDialogOpen(true)}>
                    <UserMinus className="mr-2 h-4 w-4" />Remove Director
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setIsResignationDialogOpen(true)}>
                    <UserX className="mr-2 h-4 w-4" />Update Resignation
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{directorSummary.currentSize}</div>
          </CardContent>
        </Card>

        {/* Vacant Seats Card */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Vacant Seats</CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsAddDirectorDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />Add Director
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{directorSummary.vacantSize}</div>
          </CardContent>
        </Card>
      </div>

      {/* Dialogs */}
      <UpdateBoardSizeDialog
        isOpen={isUpdateBoardSizeDialogOpen}
        onClose={() => setIsUpdateBoardSizeDialogOpen(false)}
        currentAuthorizedSize={directorSummary.authorizeSize}
        currentVacantSeats={directorSummary.vacantSize}
        onSubmit={onUpdateBoardSize}
      />

      <AddDirectorDialog
        isOpen={isAddDirectorDialogOpen}
        onClose={() => setIsAddDirectorDialogOpen(false)}
        vacantSeats={directorSummary.vacantSize}
        onSubmit={onAddDirector}
      />

      <RemoveDirectorDialog
        isOpen={isRemoveDirectorDialogOpen}
        onClose={() => setIsRemoveDirectorDialogOpen(false)}
        directors={directors}
        onRemoveDirector={onRemoveDirector}
      />

      <ResignationDialog
        isOpen={isResignationDialogOpen}
        onClose={() => setIsResignationDialogOpen(false)}
        directors={directors}
        onResignDirector={onResignDirector}
      />
    </>
  );
};

export default BoardSizeDisplay;
