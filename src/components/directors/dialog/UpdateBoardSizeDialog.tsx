import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface UpdateBoardSizeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentAuthorizedSize: number;
  currentVacantSeats: number;
  onSubmit: (data: UpdateBoardSizeFormData) => void;
}

interface UpdateBoardSizeFormData {
  newAuthorizedSize: number;
  appointDirectors: "yes" | "no";
  directors: { name: string }[];
}

const UpdateBoardSizeDialog: React.FC<UpdateBoardSizeDialogProps> = ({
  isOpen,
  onClose,
  currentAuthorizedSize,
  currentVacantSeats,
  onSubmit,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    control,
    setValue,
    formState: { errors },
    reset,
  } = useForm<UpdateBoardSizeFormData>({
    defaultValues: {
      newAuthorizedSize: currentAuthorizedSize + 1,
      appointDirectors: "no",
      directors: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "directors",
  });

  const watchAppointDirectors = watch("appointDirectors");
  const watchNewAuthorizedSize = watch("newAuthorizedSize");

  // Calculate max directors that can be appointed
  // According to the doc: "vacant amount will be the amount that they propose to increase the board size to"
  // Example: If they want to increase to 17 from 15, and all seats are currently filled, the amount of names will be up to 2
  const maxDirectors = watchNewAuthorizedSize - currentAuthorizedSize;

  const handleFormSubmit = async (data: UpdateBoardSizeFormData) => {
    setIsSubmitting(true);

    try {
      // Validate new authorized size
      if (data.newAuthorizedSize <= currentAuthorizedSize) {
        toast.error("New authorized size must be greater than current size");
        return;
      }

      // Validate directors if appointing
      if (data.appointDirectors === "yes") {
        if (data.directors.length === 0) {
          toast.error("Please add at least one director");
          return;
        }
        if (data.directors.length > maxDirectors) {
          toast.error(`Cannot appoint more than ${maxDirectors} directors`);
          return;
        }
      }

      await onSubmit(data);
      toast.success("Board size update submitted for approval");
      handleClose();
    } catch (error) {
      toast.error("Failed to submit. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleAddDirector = () => {
    if (fields.length < maxDirectors) {
      append({ name: "" });
    }
  };

  const handleRemoveDirector = (index: number) => {
    remove(index);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Update Authorized Size of the Board</DialogTitle>
          <DialogDescription>
            Increase the authorized size of the board and optionally appoint new directors.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Current Size Display */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Current Authorized Size</Label>
            <div className="text-2xl font-bold text-gray-900">{currentAuthorizedSize}</div>
          </div>

          {/* New Authorized Size Input */}
          <div className="space-y-2">
            <Label htmlFor="newAuthorizedSize">New Authorized Size</Label>
            <Input
              id="newAuthorizedSize"
              type="number"
              min={currentAuthorizedSize + 1}
              {...register("newAuthorizedSize", {
                required: "New size is required",
                min: {
                  value: currentAuthorizedSize + 1,
                  message: `Must be greater than ${currentAuthorizedSize}`,
                },
              })}
            />
            {errors.newAuthorizedSize && (
              <p className="text-sm text-red-600">{errors.newAuthorizedSize.message}</p>
            )}
          </div>

          {/* Appoint Directors Question */}
          <div className="space-y-3">
            <Label>Appoint Director(s) to the Board?</Label>
            <RadioGroup
              value={watchAppointDirectors}
              onValueChange={(value) => setValue("appointDirectors", value as "yes" | "no")}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="yes" />
                <Label htmlFor="yes">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="no" />
                <Label htmlFor="no">No</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Directors Input (if Yes) */}
          {watchAppointDirectors === "yes" && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Directors to Appoint</Label>
                <Badge variant="secondary">
                  {fields.length} / {maxDirectors}
                </Badge>
              </div>
              
              <div className="space-y-2">
                {fields.map((field, index) => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <Input
                      placeholder={`Director ${index + 1} name`}
                      {...register(`directors.${index}.name` as const, {
                        required: "Director name is required",
                      })}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveDirector(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                {fields.length < maxDirectors && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddDirector}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Director
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Approval Status */}
          <div className="space-y-2">
            <Label>Approval Status</Label>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                <span className="text-sm font-medium">Board Approved</span>
                <Badge variant="secondary">Pending</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                <span className="text-sm font-medium">Stockholder Approved</span>
                <Badge variant="secondary">Pending</Badge>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit for Approval"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateBoardSizeDialog; 