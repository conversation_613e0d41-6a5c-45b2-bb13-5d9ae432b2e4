import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Director } from "@/services/manage-directors/manageDirectors.service";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Trash2 } from "lucide-react";
import { format } from "date-fns";

interface RemoveDirectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  directors: Director[];
  onRemoveDirector: (directorId: string) => Promise<void>;
}

const RemoveDirectorDialog: React.FC<RemoveDirectorDialogProps> = ({
  isOpen,
  onClose,
  directors,
  onRemoveDirector,
}) => {
  // Filter only active directors
  const activeDirectors = directors.filter(
    (director) => director.isActive
  );

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM do, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const handleRemoveDirector = async (directorId: string) => {
    try {
      await onRemoveDirector(directorId);
    } catch (error) {
      // Error handling is done in the parent component
      console.error("Failed to remove director:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Remove Director</DialogTitle>
          <DialogDescription>
            Select a director to remove from the board. Only active directors are shown.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {activeDirectors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No active directors available for removal.
            </div>
          ) : (
            <div className="max-h-96 overflow-auto border rounded-lg">
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Appointment Date</TableHead>
                    <TableHead className="w-20">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activeDirectors.map((director) => (
                    <TableRow key={director.id}>
                      <TableCell className="font-medium">
                        {director.name}
                      </TableCell>
                      <TableCell>
                        {formatDate(director.startDate)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveDirector(director.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RemoveDirectorDialog; 