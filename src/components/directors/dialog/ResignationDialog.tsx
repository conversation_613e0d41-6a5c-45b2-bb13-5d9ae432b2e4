import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Director } from "@/services/manage-directors/manageDirectors.service";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Calendar, UserX } from "lucide-react";
import { format } from "date-fns";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface ResignationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  directors: Director[];
  onResignDirector: (directorId: string, resignationDate: string) => Promise<void>;
}

const ResignationDialog: React.FC<ResignationDialogProps> = ({
  isOpen,
  onClose,
  directors,
  onResignDirector,
}) => {
  const [selectedDates, setSelectedDates] = useState<Record<string, Date | undefined>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter only active directors
  const activeDirectors = directors.filter(
    (director) => director.isActive
  );

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM do, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  const handleDateSelect = (directorId: string, date: Date | undefined) => {
    setSelectedDates(prev => ({
      ...prev,
      [directorId]: date
    }));
  };

  const handleResignDirector = async (directorId: string) => {
    const selectedDate = selectedDates[directorId];
    if (!selectedDate) {
      return; // No date selected
    }

    setIsSubmitting(true);
    try {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      await onResignDirector(directorId, formattedDate);
      
      // Clear the selected date after successful resignation
      setSelectedDates(prev => {
        const newDates = { ...prev };
        delete newDates[directorId];
        return newDates;
      });
    } catch (error) {
      console.error("Failed to resign director:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Resign Director</DialogTitle>
          <DialogDescription>
            Select a director and resignation date to process their resignation.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {activeDirectors.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No active directors available for resignation.
            </div>
          ) : (
            <div className="max-h-96 overflow-auto border rounded-lg">
              <Table>
                <TableHeader className="sticky top-0 bg-white z-10">
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Appointment Date</TableHead>
                    <TableHead>Resignation Date</TableHead>
                    <TableHead className="w-20">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activeDirectors.map((director) => (
                    <TableRow key={director.id}>
                      <TableCell className="font-medium">
                        {director.name}
                      </TableCell>
                      <TableCell>
                        {formatDate(director.startDate)}
                      </TableCell>
                      <TableCell>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !selectedDates[director.id] && "text-muted-foreground"
                              )}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {selectedDates[director.id] ? (
                                format(selectedDates[director.id]!, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={selectedDates[director.id]}
                              onSelect={(date) => handleDateSelect(director.id, date)}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleResignDirector(director.id)}
                          disabled={!selectedDates[director.id] || isSubmitting}
                          className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                        >
                          <UserX className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResignationDialog; 