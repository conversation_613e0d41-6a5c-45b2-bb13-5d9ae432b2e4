import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface AddDirectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  vacantSeats: number;
  onSubmit: (name: string) => Promise<void>;
}

interface AddDirectorFormData {
  name: string;
  emailAddress: string;
}

const AddDirectorDialog: React.FC<AddDirectorDialogProps> = ({
  isOpen,
  onClose,
  vacantSeats,
  onSubmit,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<AddDirectorFormData>({
    defaultValues: {
      name: "",
      emailAddress: "",
    },
  });

  const handleFormSubmit = async (data: AddDirectorFormData) => {
    if (vacantSeats <= 0) {
      toast.error("No vacant seats available");
      return;
    }

    setIsSubmitting(true);

    try {
      // Only submit the name to the API, email is for UI display only
      await onSubmit(data.name);
      toast.success("Director added successfully");
      reset();
      onClose();
    } catch (error: any) {
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = Object.values(validationErrors).flat();
        toast.error(errorMessages.join(", "));
      } else {
        toast.error(error?.message || "Failed to add director. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add Director</DialogTitle>
          <DialogDescription>
            Add a new director to fill a vacant seat on the board.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Vacant Seats Display */}
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Available Vacant Seats</Label>
              <Badge variant="secondary">{vacantSeats}</Badge>
            </div>
          </div>

          {/* Director Name Input */}
          <div className="space-y-2">
            <Label htmlFor="name">Director Name</Label>
            <Input
              id="name"
              placeholder="Enter director's full name"
              {...register("name", {
                required: "Director name is required",
                minLength: {
                  value: 2,
                  message: "Name must be at least 2 characters",
                },
              })}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Director Email Input */}
          <div className="space-y-2">
            <Label htmlFor="emailAddress">Email Address</Label>
            <Input
              id="emailAddress"
              type="email"
              placeholder="Enter director's email address"
              {...register("emailAddress", {
                required: "Email address is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Please enter a valid email address",
                },
              })}
            />
            {errors.emailAddress && (
              <p className="text-sm text-red-600">{errors.emailAddress.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || vacantSeats <= 0}
            >
              {isSubmitting ? "Adding..." : "Add Director"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddDirectorDialog; 