import React, { useContext, useEffect, useState } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DocumentCommentsContext } from "@/components/signature/DocumentSigningContainer";
import { useEditorComments } from "@/contexts/EditorCommentsContext";
import { Comment } from "./types";
import CommentsHeader from "./CommentsHeader";
import CommentList from "./CommentList";
import CommentForm from "./CommentForm";
import { useComments } from "./hooks/useComments";
import { useAuth } from "@/contexts/AuthContext";
import { useCompanyComments } from "@/integrations/legal-concierge/hooks/useDocumentComments";

interface DocumentCommentsProps {
  isDashboard?: boolean;
  loading?: boolean;
  documentId?: string;
  useEditorIntegration?: boolean;
}

const DocumentComments: React.FC<DocumentCommentsProps> = ({
  isDashboard = false,
  loading: externalLoading,
  documentId,
  useEditorIntegration = false,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(externalLoading ?? true);

  // Get editor context if available (always call hook)
  const editorContext = useEditorComments();

  // Try to get comments from document context, if not available, use local state
  let contextComments: Comment[] | undefined;
  let contextAddComment:
    | ((data: {
        content: string;
        position?: { x: number; y: number };
      }) => Promise<void>)
    | undefined;
  let contextResolveComment: ((id: string) => Promise<void>) | undefined;

  try {
    const context = useContext(DocumentCommentsContext);
    if (!isDashboard && !useEditorIntegration) {
      contextComments = context?.comments;
      contextAddComment = context?.addComment;
      contextResolveComment = context?.resolveComment;
    }
  } catch (error) {
    // Context not available, will use local state instead
  }

  // Use React Query for company comments when in dashboard mode
  const { data: companyCommentsData, isLoading: isCompanyCommentsLoading } =
    useCompanyComments(user?.companyId || "", documentId, isDashboard);

  // Transform company comments data
  const transformedCompanyComments: Comment[] = React.useMemo(() => {
    if (!companyCommentsData) return [];

    return companyCommentsData.map((comment) => {
      const timestamp = (() => {
        try {
          const date = new Date(comment.createdAt);
          return isNaN(date.getTime()) ? new Date() : date;
        } catch {
          return new Date();
        }
      })();

      return {
        id: comment.id,
        userId: comment.userId,
        userName: comment.userName || comment.userId || "Anonymous",
        documentId: comment.documentIdentifier,
        content: comment.comment,
        timestamp,
        position: comment.position,
        resolved: comment.resolved,
        statusText: comment.statusText,
      };
    });
  }, [companyCommentsData]);

  // Update local state when React Query data changes
  useEffect(() => {
    if (isDashboard && transformedCompanyComments) {
      setLoading(isCompanyCommentsLoading);
    }
  }, [isDashboard, transformedCompanyComments, isCompanyCommentsLoading]);

  // Handle external loading prop changes
  useEffect(() => {
    if (externalLoading !== undefined) {
      setLoading(externalLoading);
    }
  }, [externalLoading]);

  // Determine which comments and functions to use
  const commentsToUse =
    useEditorIntegration && editorContext
      ? editorContext.comments
      : isDashboard
        ? transformedCompanyComments
        : contextComments;

  const addCommentFn =
    useEditorIntegration && editorContext
      ? async (data: {
          content: string;
          position?: { x: number; y: number };
        }) => {
          await editorContext.addComment(data);
        }
      : contextAddComment;

  const resolveCommentFn =
    useEditorIntegration && editorContext
      ? editorContext.resolveComment
      : contextResolveComment;

  const {
    comments: hookComments,
    showResolved,
    toggleShowResolved,
    handleAddComment,
    handleResolveComment,
    isUserLoggedIn,
  } = useComments(commentsToUse, addCommentFn, resolveCommentFn);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <CommentsHeader
          comments={hookComments}
          showResolved={showResolved}
          toggleShowResolved={toggleShowResolved}
        />
      </CardHeader>

      <CardContent className="flex flex-col">
        <div className="space-y-4 flex flex-col">
          {!isDashboard && (
            <>
              <CommentForm
                onAddComment={handleAddComment}
                isUserLoggedIn={isUserLoggedIn}
              />
              <Separator />
            </>
          )}

          <CommentList
            comments={hookComments}
            onResolve={handleResolveComment}
            isUserLoggedIn={isUserLoggedIn}
            loading={loading}
            showResolved={showResolved}
            isDashboard={isDashboard}
            useEditorIntegration={useEditorIntegration}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentComments;
