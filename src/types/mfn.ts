// MFN (Most Favored Nation) Types

export interface MfnCurrentIssue {
  isTriggerUpdateData: boolean;
  id: string;
  roundId: string;
  companyId: string;
  roundType: "authorized-round" | "authorized-safe";
  interestRate: number | null;
  valuationCap: number;
  discountRate: number;
  investorName: string;
  principalAmount: number;
  dateOfInvestment: string;
  approvedDate: string;
  email: string;
}

export interface MfnNewTerm {
  roundId: string;
  companyId: string;
  roundType: "authorized-round" | "authorized-safe";
  interestRate: number | null;
  valuationCap: number;
  discountRate: number;
  approvedDate: string;
  oldMfnRoundType: string;
  oldMfnRoundId: string;
}

export interface MfnSummaryResponse {
  message: string;
  data: {
    currentIssues: MfnCurrentIssue[];
    newTerm: MfnNewTerm;
  };
  error: string | null;
  validationErrors: unknown | null;
}

export interface MfnNotifyResponse {
  message: string;
  data: unknown;
  error: string | null;
  validationErrors: unknown | null;
}

// Legacy types for backward compatibility (can be removed after migration)
export interface MfnConvertibleNoteRound {
  id: string;
  maturityDate: string;
  authorizedAmount: number;
  outstandingPrincipal: number;
  interestRate: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

export interface MfnConvertibleNoteTerms {
  id: string;
  maturityDate: string;
  authorizedAmount: number;
  outstandingPrincipal: number;
  interestRate: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

export interface MfnSafeRound {
  id: string;
  authorizedAmount: number;
  outstandingPrincipal: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

export interface MfnSafeTerms {
  id: string;
  authorizedAmount: number;
  outstandingPrincipal: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

export interface MfnConvertibleNotesResponse {
  message: string;
  data: {
    currentConvertibleNoteRounds: MfnConvertibleNoteRound[];
    newConvertibleNoteTerms: MfnConvertibleNoteTerms;
  };
  error: string | null;
  validationErrors: unknown | null;
}

export interface MfnSafesResponse {
  message: string;
  data: {
    currentSafeRound: MfnSafeRound[];
    newSafeTerms: MfnSafeTerms;
  };
  error: string | null;
  validationErrors: unknown | null;
}
