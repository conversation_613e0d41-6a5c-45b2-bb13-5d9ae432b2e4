export type ServiceProviderType = "Advisor" | "Contractor" | "Employee";

export type GrantType = "Option" | "Restricted Stock" | "None" | "Restricted Stock Outside";

export type OptionType = "Statutory" | "Non-Statutory";

export type VestingScheduleType = "Standard" | "Custom";

export type ServiceProvider = {
  id: string;
  name: string;
  email: string;
  address: string;
  services?: string; // For Advisor and Consultant only
  grantType: GrantType;
  optionType?: OptionType;
  shares?: number;
  startDate: Date;
  vestingSchedule: VestingScheduleType;
  vestingPeriod?: number; // In months
  cliff?: number; // In months
  compensation?: number;
  compensationPeriod?: "Annually" | "Monthly" | "Weekly" | "Hourly";
  type: ServiceProviderType;
  status: "Pending" | "Active" | "Terminated" | "Promised" | "Issued";
  documents?: string[];
  company_id?: string; // Add this property to match database schema
};

export type PromisedGrant = {
  id: string;
  serviceProviderId: string;
  serviceProviderName: string;
  serviceProviderType: ServiceProviderType;
  vestingCommencementDate: Date;
  grantType: GrantType;
  optionType?: OptionType;
  shares: number;
  vestingSchedule: string;
  status: "Promised" | "Issued" | "Board Approved" | "Completed";
};

export type CompanyEquityInfo = {
  authorizedShares: number;
  issuedShares: number;
  stockOptionPlan?: {
    totalShares: number;
    allocatedShares: number;
  };
};

export interface AddAdvisorPayload {
  name: string;
  email: string;
  address: string;
  services: string;
  grantType: "option" | "restricted-stock-grant-inside-of-stock-option-plan" | "restricted-stock-grant-outside-of-stock-option-plan" | "none";
  shares?: number;
  startDate: string; // ISO date string
  vestingSchedule?: string;
  compensation: number;
  compensationPeriod: "hourly" | "monthly" | "annually";
  vestingPeriod?: number;
  cliff?: number;
}

export interface AddEmployeePayload {
  name: string;
  email: string;
  address: string;
  services: string;
  grantType: "option" | "restricted-stock-grant-inside-of-stock-option-plan" | "restricted-stock-grant-outside-of-stock-option-plan" | "none";
  shares?: number;
  startDate: string; // ISO date string
  vestingSchedule?: string;
  compensation: number;
  compensationPeriod: "hourly" | "monthly" | "annually";
  vestingPeriod?: number;
  cliff?: number;
}

export interface AddContractorPayload {
  name: string;
  email: string;
  address: string;
  services: string;
  grantType: "option" | "restricted-stock-grant-inside-of-stock-option-plan" | "restricted-stock-grant-outside-of-stock-option-plan" | "none";
  shares?: number;
  startDate: string; // ISO date string
  vestingSchedule?: string;
  compensation: number;
  compensationPeriod: "monthly";
  vestingPeriod?: number;
  cliff?: number;
  term: number;
}
