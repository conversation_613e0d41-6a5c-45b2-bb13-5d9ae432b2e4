export type InterestType = "Simple Interest" | "Compound Interest";

export interface ConvertibleNote {
  id: string;
  dateAuthorized: Date | null;
  authorizedAmount: number;
  outstandingPrincipal: number;
  interestRate: number;
  interestType: InterestType;
  maturityDate: Date | null;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  boardApproved: boolean;
}

export interface Safe {
  id: string;
  dateAuthorized: Date | null;
  authorizedAmount: number;
  outstandingSafeAmount: number;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  boardApproved: boolean;
}

export interface SafeRepurchase {
  id: string;
  authorizedRoundId: string; // From API response
  selected: boolean; // Local state for UI
  repurchaseAmount: number; // Local state for UI
  boardApproved: 'yes' | 'no' | 'pending'; // String values from API
  investorName: string;
  dateIssued: Date | null; // Converted from string
  purchaseAmount: number;
  valuationCap: number;
  discount: number;
  mfn: boolean; // Static value for now (not in API response)
}

// Helper functions for board approval validation
export const isSafeSelectable = (safe: SafeRepurchase): boolean => {
  return safe.boardApproved === 'no';
};

export const isSafeDisabled = (safe: SafeRepurchase): boolean => {
  return safe.boardApproved === 'yes' || safe.boardApproved === 'pending';
};

export interface MfnConvertibleNoteTrigger {
  currentNote: ConvertibleNote;
  newNoteTerms: ConvertibleNote;
}

export interface MfnSafeTrigger {
  currentSafe: Safe;
  newSafeTerms: Safe;
}

// New types for revamped Convertible Notes flows
export type RoundSource = "platform" | "external";
export type RoundStatus = "draft" | "active" | "closed";
export type ApprovalStatus = "not_requested" | "pending" | "approved" | "rejected";

export interface ConvertibleNoteRound {
  id: string;
  roundName: string;
  dateAuthorized: Date | null;
  maturityDate: Date | null;
  interestRate: number;
  interestType: InterestType;
  valuationCap: number;
  discount: number;
  mfn: boolean;
  authorizedAmount: number;
  outstandingPrincipal: number;
  qualifiedFinancingThreshold: number;
  boardApproved: boolean;
  approvalStatus: ApprovalStatus;
  pendingIncreaseAmount?: number;
  source: RoundSource;
  status: RoundStatus;
}

export type NoteIssuanceStatus =
  | "saved"
  | "pending_circulation"
  | "circulating"
  | "signed"
  | "void";

export interface ConvertibleNoteIssue {
  id: string;
  roundId: string;
  roundName: string;
  principalAmount: number;
  investorName: string;
  investorEmail: string;
  investmentDate: Date | null;
  includeProRataSideLetter: boolean;
  status: NoteIssuanceStatus;
}

// New types for API response
export interface ApiResponse<T> {
  message: string;
  data: T;
  error: string | null;
  validationErrors: unknown | null;
}

export interface ConvertibleNoteRoundResponse {
  id: string;
  companyId: string;
  qualifiedFinancingThreshold: number;
  totalAuthorizedAmountOfRound: number;
  increaseAuthorizeRoundId: string | null;
  interestRate: number;
  interestType: 'simple-interest' | 'compound-interest';
  maturityDate: string; // ISO date string
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null; // ISO date string
  outstandingPrincipal: number; // NEW: Outstanding principal amount from API
  isActive: boolean;
}

// Types for Authorize Round POST API
export interface AuthorizeRoundRequest {
  qualifiedFinancingThreshold: number;        // USD, required, >= 0
  totalAuthorizedAmountOfRound: number;       // USD, required, > 0  
  interestRate: number;                       // Percentage, required, 0-100
  interestType: "simple-interest" | "compound-interest"; // Required
  maturityDate: string;                       // ISO date string, required
  valuationCap: number;                       // USD, required, > 0
  discount: number;                           // Percentage, required, 0-100
  mostFavoredNation: boolean;                 // Required
}

export interface AuthorizeRoundResponse {
  id: string;                               // Generated round ID
  companyId: string;
  qualifiedFinancingThreshold: number;
  totalAuthorizedAmountOfRound: number;
  interestRate: number;
  interestType: "simple-interest" | "compound-interest";
  maturityDate: string;                     // ISO date string
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null;         // null until approved
  isActive: boolean;                        // false until board approved
  createdAt: string;                        // ISO timestamp
}

// Types for Issue Individual Notes POST API
export interface IssueIndividualNoteRequest {
  authorizedRoundId: string;                // ID of the selected round
  principalAmount: number;                  // Amount to issue, required, > 0
  name: string;                             // Investor name, required
  email: string;                            // Investor email, required
  dateOfInvestment: string;                 // Date of investment, ISO date string, required
  includeProRataSideLetter: boolean;        // Whether to include prorata sideletter
  includeSideLetter: boolean;               // NEW: Whether to include side letter
  prorataS3Key?: string;                    // S3 key when prorata sideletter is included
  s3Key?: string;                           // NEW: S3 key when side letter is included
}

export interface IssueIndividualNoteResponse {
  id: string;                               // Generated note ID
  authorizedRoundId: string;
  principalAmount: number;
  investorName: string;
  investorEmail: string;
  investmentDate: string;                   // ISO date string
  status: string;                           // Note status
  createdAt: string;                        // ISO timestamp
}

// Types for Increase Authorized Round POST API
export interface IncreaseAuthorizedRoundRequest {
  authorizedRoundId: string;                // ID of the selected round
  authorizedAmount: number;                 // Amount to increase, required, > 0
}

export interface IncreaseAuthorizedRoundResponse {
  id: string;                               // Generated request ID
  companyId: string;
  authorizedRoundId: string;
  increaseAmount: number;
  status: string;                           // Request status
  createdAt: string;                        // ISO timestamp
  boardApprovedDate: string | null;         // null until approved
}

// New types for View Individual Notes feature
export interface IndividualNote {
  id: string;
  companyId: string;
  authorizedRoundId: string;
  interestType: "simple-interest" | "compound-interest";
  principalAmount: number;
  accruedInterest: number; // NEW: Accrued interest amount from API
  interestRate: number;
  name: string;
  email: string;
  dateOfInvestment: string; // ISO date string
  approvedDate: string | null; // ISO date string or null
  isActive: boolean;
}

export interface IndividualNotesResponse {
  message: string;
  data: IndividualNote[];
  error: string | null;
  validationErrors: unknown | null;
}

// File upload types for convertible notes
export interface FileUploadRequest {
  key: string;                    // Required, filename (e.g., "name.doc")
  contentType: string;            // Required, MIME type of file
}

export interface FileUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: string | null;
  validationErrors: unknown | null;
}

// Record Authorized Round types
export interface RecordAuthorizedRoundRequest {
  qualifiedFinancingThreshold: number;        // USD, required, >= 0
  totalAuthorizedAmountOfRound: number;       // USD, required, > 0  
  interestRate: number;                       // Percentage, required, 0-100
  interestType: "simple-interest" | "compound-interest"; // Required
  maturityDate: string;                       // ISO date string, required
  valuationCap: number;                       // USD, required, > 0
  discount: number;                           // Percentage, required, 0-100
  mostFavoredNation: boolean;                 // Required
}

export interface RecordAuthorizedRoundResponse {
  message: string;
  data: {
    id: string;                               // Generated round ID
    companyId: string;
    qualifiedFinancingThreshold: number;
    totalAuthorizedAmountOfRound: number;
    interestRate: number;
    interestType: "simple-interest" | "compound-interest";
    maturityDate: string;                     // ISO date string
    valuationCap: number;
    discount: number;
    mostFavoredNation: boolean;
    boardApprovedDate: string;                // ISO date string
    isActive: boolean;                        // true since already board approved
    createdAt: string;                        // ISO timestamp
  };
  error: string | null;
  validationErrors: unknown | null;
}

// SAFE-related types for the new implementation
export interface SafeRoundResponse {
  id: string;
  companyId: string;
  totalAuthorizedAmountOfRound: number;
  outstandingPrincipal: number; // NEW: Outstanding principal amount from API
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null; // ISO date string
  isActive: boolean;
}

export interface AuthorizeSafeRequest {
  totalAuthorizedAmountOfRound: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
}

export interface AuthorizeSafeResponse {
  id: string;
  companyId: string;
  totalAuthorizedAmountOfRound: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string | null;
  isActive: boolean;
  createdAt: string;
}

export interface AuthorizeSafeApiResponse {
  message: string;
  data: AuthorizeSafeResponse;
  error: string | null;
  validationErrors: unknown | null;
}

export interface IncreaseAuthorizedSafeRequest {
  authorizedRoundId: string;
  authorizedAmount: number;
}

export interface IncreaseAuthorizedSafeResponse {
  id: string;
  companyId: string;
  authorizedRoundId: string;
  authorizedAmount: number;
  status: "pending_approval";
  createdAt: string;
  boardApprovedDate: string | null;
}

export interface IssueIndividualSafeRequest {
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  includeProRataSideLetter: boolean;    // Whether to include prorata sideletter
  includeSideLetter: boolean;           // NEW: Whether to include side letter
  prorataS3Key?: string;                // S3 key when prorata sideletter is included
  s3Key?: string;                       // NEW: S3 key when side letter is included
}

export interface IssueIndividualSafeResponse {
  id: string;
  companyId: string;
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  status: "pending_circulation";
  createdAt: string;
}

export interface IssueIndividualSafeApiResponse {
  message: string;
  data: IssueIndividualSafeResponse | string; // Can be success data or error message
  error: string | null;
  validationErrors: unknown | null;
}

export interface RecordAuthorizedSafeRequest {
  totalAuthorizedAmountOfRound: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string;
  s3Key: string;
}

export interface RecordAuthorizedSafeResponse {
  id: string;
  companyId: string;
  totalAuthorizedAmountOfRound: number;
  valuationCap: number;
  discount: number;
  mostFavoredNation: boolean;
  boardApprovedDate: string;
  isActive: boolean;
  createdAt: string;
}

// Individual SAFEs list types
export interface IndividualSafe {
  id: string;
  companyId: string;
  authorizedRoundId: string;
  principalAmount: number;
  accruedInterest: number; // NEW: Accrued interest amount from API
  name: string;
  email: string;
  dateOfInvestment: string; // ISO date string
  approvedDate: string | null; // ISO date string or null
  isActive: boolean;
}

export interface IndividualSafesResponse {
  message: string;
  data: IndividualSafe[];
  error: string | null;
  validationErrors: unknown | null;
}

// NEW: Prorata sideletter upload types
export interface ProrataSideletterUploadRequest {
  key: string;                    // filename with extension
  contentType: string;            // MIME type of file
}

export interface ProrataSideletterUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: string | null;
  validationErrors: unknown | null;
}

// NEW: Record Issue Individual Note types for the updated API
export interface RecordIssueIndividualNoteRequest {
  authorizedRoundId: string;
  principalAmount: number;
  name: string;
  email: string;
  dateOfInvestment: string;
  approvedDate: string;
  prorataS3Key?: string;        // Optional: for prorata sideletter
  s3Key?: string;               // Optional: for side letter
  optionalS3Key?: string;       // Optional: for optional document
}

export interface RecordIssueIndividualNoteResponse {
  message: string;
  data: RecordIssueIndividualNoteRequest & {
    id: string;
    companyId: string;
    status: string;
    createdAt: string;
  };
  error: string | null;
  validationErrors: unknown | null;
}

// NEW: Side letter upload types
export interface SideLetterUploadRequest {
  key: string;                    // filename with extension
  contentType: string;            // MIME type of file
}

export interface SideLetterUploadResponse {
  message: string;
  data: {
    uploadUrl: string;            // Pre-signed S3 upload URL
    key: string;                  // S3 key for the uploaded file
  };
  error: string | null;
  validationErrors: unknown | null;
}
