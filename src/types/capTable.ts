export type ShareholderRole =
  | "Founder"
  | "Investor"
  | "Employee"
  | "Advisor"
  | "Other";

export type ShareClass =
  | "Common"
  | "Series A Preferred"
  | "Series B Preferred"
  | "Stock Option Pool";

export type ShareClassDetails = {
  name: ShareClass;
  type: "Common" | "Preferred" | "Option";
  conversionRatio?: number;
  liquidationPreference?: number;
};

export type Shareholder = {
  id: string;
  name: string;
  role: ShareholderRole;
  holdings: {
    [key in ShareClass]?: number;
  };
  totalShares: number;
  percentage: number;
  fullyDilutedPercentage: number;
};

export type InvestmentRound = {
  roundName: string;
  investmentAmount: number;
  preMoneyValuation: number;
  postMoneyValuation?: number;
  sharesIssued?: number;
  pricePerShare?: number;
  date?: Date;
};

export type Convertible = {
  instrument: "SAFE" | "Note";
  valuationCap?: number;
  discount?: number;
  principal: number;
  interestRate?: number;
  issueDate?: Date;
  maturityDate?: Date;
};

export type StockGrant = {
  recipient: string;
  optionsGranted: number;
  vestingSchedule: string;
  grantDate?: Date;
};

export type StockOptionPlan = {
  poolSizePercentage: number;
  totalShares: number;
  grants: StockGrant[];
};

export type CapTableSummary = {
  id?: string;
  name?: string;
  totalShares: number;
  totalFullyDiluted: number;
  shareClasses: ShareClassDetails[];
  shareholders: Shareholder[];
  lastUpdated: Date;
  snapshots: {
    id: string;
    name: string;
    date: Date;
  }[];
  companyName?: string;
  isUserProvided?: boolean;
  investmentRounds?: InvestmentRound[];
  convertibles?: Convertible[];
  stockOptionPlan?: StockOptionPlan;
};

// Updated ProFormaInvestment type with user-editable flag for investment amount
export type ProFormaInvestment = {
  id: string;
  name: string;
  amount: number;
  pricePerShare: number;
  shareClass: ShareClass;
  date?: Date;
  preMoneyValuation: number;
  postMoneyValuation?: number;
  dilutedPoolPercentage?: number;
  isUserEdited?: boolean;
  isAmountEdited?: boolean; // New flag for editing investment amount
};

export type ProFormaSummary = {
  preMoney: number;
  postMoney: number;
  newShares: number;
  newPercentage: number;
  dilutedPoolPercentage: number;
};

// Convertible Note types
export type ConvertibleNote = {
  id: string;
  investor: string;
  principal: number;
  interestRate: number;
  issueDate: Date;
  maturityDate: Date;
  valuationCap?: number;
  discount?: number;
  conversionPrice?: number;
  estimatedShares?: number;
};

// Stock Option Pool types
export type OptionGrant = {
  id: string;
  recipient: string;
  role: ShareholderRole;
  grantDate: Date;
  optionsGranted: number;
  vestingStart: Date;
  vestingPeriod: number; // months
  cliff: number; // months
  percentVested: number;
  // REMOVED: currentValue: number; // TO BE REMOVED as per documentation
  isEditing?: boolean; // New property to track edit state
  
  // NEW FIELDS
  grantStatus?: 'Promised' | 'Granted';
  grantType?: 'Restricted Stock' | 'Option';
  issueStatus?: 'Active' | 'Terminated' | 'Exercised';
  stateOfResidency?: string;
  exerciseDate?: Date;
  exercisedShares?: number;
  remainingShares?: number;
  isExercised?: boolean;
};

export type SOPSummary = {
  totalPool: number;
  allocated: number;
  remaining: number;
  grants: OptionGrant[];
  lastUpdated?: string; // NEW FIELD
};

// Pro Forma Voting rights
export type VotingRights = {
  shareholder: string;
  votingShares: number;
  votingPercentage: number;
  votingPower: "High" | "Medium" | "Low";
};

export type ProFormaVoting = {
  votingRights: VotingRights[];
  threshold: {
    majority: number;
    supermajority: number;
  };
};

// Stock Option Plan Types (Enhanced)
export interface SOPGrant {
  id: string;
  fullName: string;
  role: string;
  issueStatus: 'Active' | 'Terminated' | 'Exercised';
  grantStatus: 'Promised' | 'Granted';
  vestingSchedule: string;
  stateOfResidency: string;
  grantType: 'Restricted Stock' | 'Option';
  shares: number;
  grantDate?: string;
  exerciseDate?: string;
  exercisedShares?: number;
  remainingShares?: number;
  isExercised?: boolean;
}

export interface SOPPoolMetrics {
  totalPlanSize: number;
  allocatedShares: number;
  remainingPlan: number;
  grantedShares: number;
  promisedShares: number;
  exercisedShares: number;
}

export interface SOPData {
  poolMetrics: SOPPoolMetrics;
  grants: SOPGrant[];
  lastUpdated: string;
}

// Edit Modal Types
export interface EditSOPGrant {
  id: string;
  fullName: string;
  role: string;
  issueStatus: 'Active' | 'Terminated' | 'Exercised';
  grantStatus: 'Promised' | 'Granted';
  vestingSchedule: string;
  stateOfResidency: string;
  grantType: 'Restricted Stock' | 'Option';
  shares: number;
  grantDate?: string;
  exerciseDate?: string;
  exercisedShares?: number;
  remainingShares?: number;
}

export interface ExerciseGrantData {
  grantId: string;
  fullName: string;
  shares: number;
  exercisedShares: number;
  exerciseDate: string;
  grantDate: string;
}

// Voting Rights Types (Enhanced)
export interface VotingShareholder {
  id: string;
  name: string;
  email: string;
  shareType: 'Common Stock' | 'Restricted Stock' | 'Exercised Options';
  votingShares: number;
  totalShares: number;
  vestedShares: number;
  terminationDate?: string;
  isTerminated: boolean;
  votingPercentage: number;
  votingPower: 'High' | 'Medium' | 'Low';
  canBlockMajority: boolean;
  sendEmail: boolean; // Email inclusion/exclusion status
  role: string;
  grantDate?: string;
  vestingSchedule?: string;
  companyServiceProviderId?: string | null;
  officerId?: string | null;
}

export interface EmailExclusionSummary {
  includedVotingShares: number;
  excludedVotingShares: number;
  includedVotingPercentage: number;
  excludedVotingPercentage: number;
  isEmailExclusionValid: boolean;
  includedShareholders: VotingShareholder[];
  excludedShareholders: VotingShareholder[];
}

export interface VotingRightsData {
  shareholders: VotingShareholder[];
  totalVotingShares: number;
  majorityThreshold: number;
  majorityThresholdPercentage: number;
  lastUpdated: string;
  emailExclusionSummary: EmailExclusionSummary;
}

export interface VotingPowerAnalysis {
  highPowerShareholders: VotingShareholder[];
  mediumPowerShareholders: VotingShareholder[];
  lowPowerShareholders: VotingShareholder[];
  majorityBlockers: VotingShareholder[];
  totalHighPowerPercentage: number;
  totalMediumPowerPercentage: number;
  totalLowPowerPercentage: number;
}

// Add Investor Modal Types
export interface AddInvestorModalData {
  investorName: string;
  investmentAmount: number;
  role?: string;
}

// Pro Rata Rights Table Types
export interface ProRataRightsItem {
  id: string;
  investorName: string;
  currentOwnershipPercentage: number;
  proRataAmount: number; // Maps to prorataAmount from API (renamed from investmentAmount)
  numberOfSeriesAShares: number; // Auto-calculated based on round and pro rata amount
  isExistingInvestor: boolean;
}

export interface ProRataRightsData {
  items: ProRataRightsItem[];
  totalProRataAmount: number;
  roundType: PreferredStockSeries;
  pricePerShare: number;
}

export interface ProFormaModelData {
  inputs: ProFormaInputs;
  summary: ProFormaSummaryMetrics;
  capTable: ProFormaCapTableData;
  votingRights: ProFormaVotingRightsData;
  stockOptionPlan: ProFormaStockOptionPlanData;
  proRataRights: ProRataRightsData;
  ownershipDistribution: PieChartItem[];
  lastUpdated: string;
  // Export functionality
  exportOptions: {
    pdfEnabled: boolean;
    excelEnabled: boolean;
  };
}

// New types for user data handling
export type UserCapTableData = {
  capTables: CapTableSummary[];
  currentCapTableId?: string;
};

export interface CapTableDataService {
  loadUserData: () => Promise<UserCapTableData>;
  saveCapTable: (capTable: CapTableSummary) => Promise<CapTableSummary>;
  deleteCapTable: (id: string) => Promise<boolean>;
  getCapTableById: (id: string) => Promise<CapTableSummary | null>;
}

// Document types for the Data Room
export type Document = {
  id: string;
  name: string;
  path: string;
  type: string;
  size: number;
  createdAt: Date;
  updatedAt: Date;
  url?: string;
};

export type Subfolder = {
  documents: Document[];
  subfolders?: Record<string, Subfolder>;
};

export type Folder = {
  documents: Document[];
  subfolders?: Record<string, Subfolder>;
};

export interface DocumentFile {
  id: string;
  fileName: string;
  url: string;
  thumbnailUrl: string;
}

export interface FolderStructure {
  [key: string]: {
    documents: DocumentFile[];
    subfolders?: Record<
      string,
      {
        documents: DocumentFile[];
        isCompleted?: boolean;
      }
    >;
    isCompleted?: boolean;
  };
}

// Cap Table Summary API Types
export interface PieChartItem {
  name: string;
  percentage: number;
}

export interface CapTableSummaryData {
  shareholders: number;
  shareClasses: number;
  totalShares: number;
  fullyDiluted: number;
  pieChartData: PieChartItem[];
}

export interface CapTableSummaryResponse {
  message: string;
  data: CapTableSummaryData;
  error: null;
  validationErrors: null;
}

export interface CapTableSummaryErrorResponse {
  message: string;
  data: null;
  error: string;
  validationErrors: {
    field: string;
    message: string;
  }[] | null;
}

// Cap Table List API Types
export interface CapTableListItem {
  name: string;
  role: string;
  common: number;
  totalShares: number;
  ownership: number;
}

export interface CapTableListResponse {
  message: string;
  data: CapTableListItem[];
  error: null;
  validationErrors: null;
}

export interface CapTableListErrorResponse {
  message: string;
  data: null;
  error: string;
  validationErrors: {
    field: string;
    message: string;
  }[] | null;
}

// Convertible Notes API Types
export interface ConvertibleNoteItem {
  investor: string;
  principal: number;
  interestRate: number;
  issueDate: string;
  maturity: string;
  valuationCap: number;
  discount: number;
  interestAccrued: number;
  totalValue: number;
  convPrice: number;
  estShares: number;
}

export interface ConvertibleNotesResponse {
  message: string;
  data: ConvertibleNoteItem[];
  error: null;
  validationErrors: null;
}

export interface ConvertibleNotesErrorResponse {
  message: string;
  data: null;
  error: string;
  validationErrors: {
    field: string;
    message: string;
  }[] | null;
}

// Actual API Response Types (from getCapTableList endpoint)
export interface CapTableListApiResponse {
  message: string;
  data: {
    latestUpdatedDate: string | null;
    data: CapTableListItemApi[];
    outstandingStockOptionPlanShares: SOPPoolItemApi;
    promisedStockOptionPlanShares: SOPPoolItemApi;
    stockOptionPlanSharesAvailable: SOPPoolItemApi;
  };
  error: null;
  validationErrors: null;
}

export interface CapTableListItemApi {
  serviceProviderId: string | null;
  officerId: string | null;
  name: string;
  role: string;
  commonStock: number;
  sopCommonStock: number;
  totalShares: number;
  fullyDilutedOwnership: number;
  vestedShare: number | null;
  unvestedShare: number | null;
  currentAvailableVestedShare: number | null;
  isTerminated: boolean;
}

export interface SOPPoolItemApi {
  stockOptionPlan: number;
  totalShares: number;
  fullyDilutedOwnership: number;
}

// UI Component Types (for Enhanced Cap Table)
export interface EnhancedCapTableListItem {
  id: string;
  name: string;
  role: string;
  commonStock: number;
  stockOptionPlan: number;
  totalShares: number;
  fullyDilutedOwnership: number;
  isTerminated: boolean;
  unvestedShares: string; // "N/A" - placeholder for future
  vestedShares: string; // "N/A" - placeholder for future (NEW)
  officerId: string; // For API operations
  serviceProviderId: string | null; // For API operations
}

export interface SOPPoolSummary {
  outstandingSOPShares: number;
  promisedSOPShares: number;
  availableSOPShares: number;
  totalAuthorizedSOP: number;
  outstandingOwnership: number;
  promisedOwnership: number;
  availableOwnership: number;
}

export interface EnhancedCapTableData {
  stockholders: EnhancedCapTableListItem[];
  sopPool: SOPPoolSummary;
  lastUpdated: string;
}

export interface EditCapTableItem {
  id: string;
  name: string;
  commonStock: number;
  stockOptionPlan: number;
}

export interface EditSOPPoolItem {
  outstandingSOPShares: number;
  promisedSOPShares: number;
  availableSOPShares: number;
}

export type CapTableViewMode = 'summary' | 'detailed';

// API Response Types for Convertible Securities
export interface ConvertibleSecuritiesApiResponse {
  totalPrincipal: number;
  safes: number;
  convertibleNotes: number;
  totalInvestors: number;
  latestUpdatedDate: string;
  convertibles: ConvertibleSecurityApiItem[];
}

export interface ConvertibleSecurityApiItem {
  id: string | null;
  officerId: string | null;
  serviceProviderId: string | null;
  name: string;
  issueRoundType: "authorized-safe" | "authorized-round";
  principal: number;
  interestRate: number;
  investment: number | null;
  issueDate: string;
  maturityDate: string | null;
  valuationCap: number;
  discount: number;
  interestAccrued: number;
  totalValue: number;
  mfn: boolean;
  qft: number;
}

// Convertible Securities Types (Enhanced)
export interface ConvertibleSecurity {
  id: string;
  investor: string;
  securityType: 'SAFE' | 'Convertible Note';
  principal: number;
  interestRate: number;
  issueDate: string;
  maturityDate?: string; // Optional for SAFEs
  valuationCap: number;
  discount: number;
  interestAccrued: number;
  totalValue: number;
  conversionPrice: number;
  estimatedShares: number;
  // Additional fields for backend processing (not displayed in UI)
  mfn: boolean;
  qft: number;
  issueRoundType: string;
}

export interface ConvertibleSecuritiesSummary {
  totalPrincipal: number;
  safes: number;
  convertibleNotes: number;
  totalInvestors: number;
  lastUpdated: string;
}

export interface ConvertibleSecuritiesData {
  securities: ConvertibleSecurity[];
  summary: ConvertibleSecuritiesSummary;
}

// Pro Forma Model Types
export type ValuationMode = 'Fixed Pre-Money' | 'Fixed Post-Money';
export type PreferredStockSeries = 'Series Seed' | 'Series A';

// Pro Forma Filter Types
export type ProFormaRoleFilter = 'All' | 'Founders' | 'Investors' | 'Advisors' | 'Contractors' | 'Employees';
export type ProFormaShareClassFilter = 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';

export interface ProFormaInputs {
  designation: ValuationMode;
  fixedValuation: number;
  investmentAmount: number;
  optionPoolPercentage: number;
  preferredStockDesignation: PreferredStockSeries;
  commonStockBuffer: number;
}

export interface ProFormaFilters {
  roleFilter: ProFormaRoleFilter;
  shareClassFilter: ProFormaShareClassFilter;
  shareholderNameSearch: string;
}

export interface ProFormaSummaryMetrics {
  effectivePreMoneyValuation?: number;
  effectivePostMoneyValuation?: number;
  seriesPricing: SeriesPricing[];
  authorizedStock: AuthorizedStockBreakdown;
  totalAuthorizedStock: number;
  totalPool: number; // Total option pool size
  poolRemainingForIssuance: number; // Available option pool for future grants
  notes: string[];
}

export interface SeriesPricing {
  seriesName: string;
  pricePerShare: number;
  sharesIssued: number;
  totalValue: number;
}

export interface AuthorizedStockBreakdown {
  commonStock: number;
  preferredStock: number;
  seriesStock: { [series: string]: number };
}

export interface ProFormaCapTableItem {
  id: string;
  name: string;
  investmentAmount: number; // Maps to cashInvestment from API
  commonStockShares: number;
  stockOptionPlanShares: number;
  seriesAShares?: number; // New field for Series A/Series Seed column
  // Dynamic series columns - will be populated based on API response
  [key: string]: any; // Allow dynamic series columns like seriesA1Shares, seriesA2Shares, etc.
  totalShares: number;
  fullyDilutedOwnership: number;
  ownershipChange: number;
  catchInvestment: number;
  isStockOptionPoolRow?: boolean; // Flag for Stock Option Pool Increase row
  isSection3Row?: boolean; // Flag for Section 3 rows (Additional Investor, Unallocated New Money)
  hasValidId?: boolean; // Flag to track if row has valid ID for editing
  // New ID fields for different investor types
  officerId?: string; // For officer type investors
  serviceProviderId?: string; // For service provider type investors
}

export interface ProFormaCapTableData {
  items: ProFormaCapTableItem[];
  stockOptionPoolIncrease: number;
  totalAggregateShares: number;
}

export interface ProFormaVotingRightsItem {
  id: string;
  name: string;
  overallVotingPercentage: number;
  commonStockVotingPercentage: number;
  preferredStockVotingPercentage: number;
  overallVotingPower: 'High' | 'Medium' | 'Low';
  commonStockVotingPower: 'High' | 'Medium' | 'Low';
  preferredStockVotingPower: 'High' | 'Medium' | 'Low';
}

export interface ProFormaVotingRightsData {
  items: ProFormaVotingRightsItem[];
  totalVotingShares: number;
  overallMajorityThreshold: number;
  preferredStockMajorityThreshold: number;
  commonStockMajorityThreshold: number;
  infoMessage: string; // Static message for info capsule
}

export interface ProFormaStockOptionPlanData {
  totalPlanSize: number;
  allocatedShares: number;
  remainingPlan: number;
  postMoneyAllocation: number;
  grants: ProFormaSOPGrant[];
  lastUpdated: string;
}

export interface ProFormaSOPGrant {
  id: string;
  fullName: string;
  role: string;
  currentShares: number;
  newShares: number;
  totalShares: number;
  vestingSchedule: string;
  grantStatus: 'Promised' | 'Granted';
  grantType: 'Restricted Stock' | 'Option';
}

export interface ProFormaModelData {
  inputs: ProFormaInputs;
  summary: ProFormaSummaryMetrics;
  capTable: ProFormaCapTableData;
  votingRights: ProFormaVotingRightsData;
  stockOptionPlan: ProFormaStockOptionPlanData;
  proRataRights: ProRataRightsData;
  ownershipDistribution: PieChartItem[];
  lastUpdated: string;
  // Export functionality
  exportOptions: {
    pdfEnabled: boolean;
    excelEnabled: boolean;
  };
}

// API Response Types for Stock Option Plan
export interface StockOptionPlanSummaryApiResponse {
  totalAuthorizedSize: number;
  totalPoolSize: number;
  allocated: number;
  remaining: number;
  totalRemaining: number;
  promiseGrant: number;
  pendingDetail: {
    additionalShare: number;
    isBoardApproved: string;
    isStockHolderApproved: string;
    workflowId: string | null;
  };
  stockOptionPoolPercentage: {
    totalPoolSizePercentage: number;
    poolAllocatedPercentage: number;
    remainingPoolPercentage: number;
  };
}

export interface StockOptionPlanTableApiResponse {
  latestUpdatedDate: string;
  optionPools: StockOptionPoolApiItem[];
}

export interface StockOptionPoolApiItem {
  id: string;
  recipient: string;
  role: string;
  grantType: string;
  grantStatus: string;
  grantDate: string;
  options: number;
  percentageOfPool: number;
  currentValue: number;
  vestingProgress: number;
  // ENHANCED VESTING FIELDS
  vestedShare: number;
  unVestedShare: number;
  vestingCommencementDate: string;
  term: number;
  vestingSchedule: string;
  compensation: number;
  compensationPeriod: string;
  vestingPeriod: number | null;
  cliff: number | null;
}

// Enhanced Stock Option Plan Types
export interface StockOptionPlanSummary {
  totalPool: number;
  allocated: number;
  remaining: number;
  lastUpdated: string;
  percentages: {
    totalPoolSizePercentage: number;
    poolAllocatedPercentage: number;
    remainingPoolPercentage: number;
  };
}

export interface StockOptionGrant {
  id: string;
  recipient: string;
  role: string;
  grantDate: Date;
  optionsGranted: number;
  vestingStart: Date;
  percentVested: number;
  // NEW FIELDS
  grantStatus: 'Promised' | 'Granted';
  grantType: 'Restricted Stock' | 'Option' | 'Exercised Option';
  issueStatus: 'Active' | 'Terminated' | 'Exercised';
  stateOfResidency: string;
  exerciseDate?: Date;
  exercisedShares?: number;
  remainingShares?: number;
  isExercised?: boolean;
  // ENHANCED VESTING FIELDS
  vestedShares: number;
  unvestedShares: number;
  vestingSchedule: string;
  vestingPeriod: number | null;
  cliff: number | null;
  vestingCommencementDate: string;
  term: number;
  // Additional fields for backend processing
  percentageOfPool: number;
  currentValue: number;
}

// API Request/Response Types for Grant Management
export interface UpdateGrantRequest {
  id: string;
  role: string;
  grantStatus: string;
  grantType: string;
  optionsGranted: number;
}

export interface UpdateGrantResponse {
  id: string;
  updatedAt: string;
}

export interface ExerciseOptionRequest {
  id: string;
  exerciseDate: string;
  sharesToExercise: number;
}

export interface ExerciseOptionResponse {
  id: string;
  exerciseDate: string;
  sharesExercised: number;
  updatedAt: string;
}

export interface DeleteGrantRequest {
  id: string;
  grantType: string;
}

export interface DeleteGrantResponse {
  id: string;
  deletedAt: string;
}

export interface StockOptionPlanData {
  summary: StockOptionPlanSummary;
  grants: StockOptionGrant[];
}

// API Response Types for Voting Rights
export interface VotingRightsApiResponse {
  latestUpdatedDate: string;
  votingRights: VotingRightApiItem[];
}

export interface VotingRightApiItem {
  id: string;
  email?: string; // Optional for now, will be required when API includes email field
  sendEmail: boolean;
  shareHolder: string;
  shareType: string;
  votingShares: number;
  votingPercentage: number;
  votingPower: string;
  canBlockMajority: boolean;
  companyServiceProviderId: string | null;
  officerId: string | null;
}

// Email Update Types
export interface UpdateEmailRequest {
  id: string;
  email: string;
}

export interface UpdateEmailResponse {
  id: string;
  email: string;
  updatedAt: string;
}

// Email Status Update Types
export interface UpdateEmailStatusRequest {
  id: string;
  sendEmail: boolean;
}

export interface UpdateEmailStatusResponse {
  id: string;
  sendEmail: boolean;
  updatedAt: string;
}

// Enhanced Voting Rights Types


export interface VotingRightsData {
  shareholders: VotingShareholder[];
  totalVotingShares: number;
  majorityThreshold: number;
  majorityThresholdPercentage: number;
  lastUpdated: string;
}

export interface VotingPowerAnalysis {
  highPowerShareholders: VotingShareholder[];
  mediumPowerShareholders: VotingShareholder[];
  lowPowerShareholders: VotingShareholder[];
  majorityBlockers: VotingShareholder[];
  totalHighPowerPercentage: number;
  totalMediumPowerPercentage: number;
  totalLowPowerPercentage: number;
}

// Termination Types [NEW]
export interface TerminateCapTableItemPayload {
  officerId: string;
  serviceProviderId: string | null;
  terminationDate: string;
  severanceAmount: number;
  serviceProviderTerminationType: string;
}

export interface TerminateCapTableItem {
  id: string;
  name: string;
  role: string;
  officerId: string;
  serviceProviderId: string | null;
}

// Add Grant API Types
export interface AddGrantRequest {
  name: string;
  email: string;
  address: string;
  services: string;
  grantType: string;
  shares: number;
  startDate: string;
  vestingSchedule: string;
  compensation: number;
  compensationPeriod: string;
  vestingPeriod?: number;
  cliff?: number;
  term?: number;
  pricePerShare?: number;
  type: string;
}

export interface AddGrantResponse {
  id: string;
  name: string;
  email: string;
  createdAt: string;
}
