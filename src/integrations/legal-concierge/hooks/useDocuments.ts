import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import { Document } from "../types/Document";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Get all documents for a company
export const useCompanyDocuments = (companyId: string, enabled = true) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, QueryKey().documents],
    queryFn: async () => {
      const response = await api.getCompanyDocuments(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data as Document[];
    },
    enabled: !!companyId && enabled,
  });
};

// Get document by ID
export const useDocumentById = (
  companyId: string,
  documentId: string,
  enabled = true
) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, QueryKey().documents, documentId],
    queryFn: async () => {
      const response = await api.getDocument(documentId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    enabled: !!companyId && !!documentId && enabled,
  });
};

// Generate document for a company
export const useGenerateCompanyDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (companyId: string) => {
      const response = await api.generateCompanyDocument(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, companyId) => {
      // Invalidate company documents query
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, companyId, QueryKey().documents],
      });
    },
  });
};

// Get document content
export const useDocument = (documentId: string, enabled = true) => {
  return useQuery({
    queryKey: [QueryKey().documents, documentId],
    queryFn: async () => {
      const response = await api.getDocument(documentId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    enabled: !!documentId && enabled,
  });
};

// Get document token for viewing
export const useDocumentToken = () => {
  return useMutation({
    mutationFn: async (payload: { documentId: string; companyId: string }) => {
      const response = await api.getDocumentToken(payload);
      console.log("Document token response from mutation", response);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
  });
};

// Document server callback
export const useDocumentCallback = () => {
  return useMutation({
    mutationFn: async (payload: Record<string, unknown>) => {
      const response = await api.documentCallback(payload);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
  });
};

// Update document content
export const useUpdateDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      id: string;
      content: string;
      data?: {
        blameMap?: Array<{
          from: number;
          to: number;
          commit: number;
        }>;
        commits?: Array<{
          message: string;
          timestamp: string;
          steps: string;
          originalSteps: string;
          maps: string;
          hidden: boolean;
          deletions?: Array<{
            pos: number;
            content: string;
          }>;
        }>;
      };
    }) => {
      const { id: documentId, ...updatePayload } = payload;
      const response = await api.updateDocument(documentId, updatePayload);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant document queries
      queryClient.invalidateQueries({
        queryKey: [QueryKey().documents],
      });
    },
  });
};
