import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { APIClient } from "../client";
import { QueryKey } from "../keys";
import {
  DocumentCommentRequest,
  DocumentCommentResponse,
} from "../types/DocumentComment";

const api = new APIClient();

// Type guard to check if response has an error
function hasError<T>(
  response: { error: string } | { data: T }
): response is { error: string } {
  return "error" in response;
}

// Get comments for a specific document
export const useDocumentComments = (
  companyId: string,
  documentIdentifier: string,
  enabled = true
) => {
  return useQuery({
    queryKey: [
      QueryKey().company,
      companyId,
      QueryKey().document,
      documentIdentifier,
      "comments",
    ],
    queryFn: async () => {
      const response = await api.getDocumentComments(
        companyId,
        documentIdentifier
      );
      if (hasError(response)) {
        throw new Error(response.error);
      }

      // Ensure response.data is an array
      const commentsData = Array.isArray(response.data)
        ? response.data
        : [response.data].filter(Boolean);

      return commentsData as DocumentCommentResponse[];
    },
    enabled: !!companyId && !!documentIdentifier && enabled,
  });
};

// Get all comments for a company
export const useCompanyComments = (companyId: string, enabled = true) => {
  return useQuery({
    queryKey: [QueryKey().company, companyId, "comments"],
    queryFn: async () => {
      const response = await api.getCompanyComments(companyId);
      if (hasError(response)) {
        throw new Error(response.error);
      }

      // Ensure response.data is an array
      const commentsData = Array.isArray(response.data)
        ? response.data
        : [response.data].filter(Boolean);

      return commentsData as DocumentCommentResponse[];
    },
    enabled: !!companyId && enabled,
  });
};

// Create a new document comment
export const useCreateDocumentComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      companyId,
      documentIdentifier,
      comment,
    }: {
      companyId: string;
      documentIdentifier: string;
      comment: DocumentCommentRequest;
    }) => {
      const response = await api.createDocumentComment(
        companyId,
        documentIdentifier,
        comment
      );
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return (response.data as any).data as DocumentCommentResponse;
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch document comments
      queryClient.invalidateQueries({
        queryKey: [
          QueryKey().company,
          variables.companyId,
          QueryKey().document,
          variables.documentIdentifier,
          "comments",
        ],
      });

      // Also invalidate company comments
      queryClient.invalidateQueries({
        queryKey: [QueryKey().company, variables.companyId, "comments"],
      });
    },
  });
};

// Update a document comment
export const useUpdateDocumentComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      commentId,
      comment,
      companyId,
      documentIdentifier,
    }: {
      commentId: string;
      comment: DocumentCommentRequest;
      companyId?: string;
      documentIdentifier?: string;
    }) => {
      const response = await api.updateDocumentComment(commentId, comment);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data as DocumentCommentResponse;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries if we have the context
      if (variables.companyId && variables.documentIdentifier) {
        queryClient.invalidateQueries({
          queryKey: [
            QueryKey().company,
            variables.companyId,
            QueryKey().document,
            variables.documentIdentifier,
            "comments",
          ],
        });

        queryClient.invalidateQueries({
          queryKey: [QueryKey().company, variables.companyId, "comments"],
        });
      } else {
        // Invalidate all comment queries if we don't have specific context
        queryClient.invalidateQueries({
          queryKey: ["comments"],
        });
      }
    },
  });
};

// Delete a document comment
export const useDeleteDocumentComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      commentId,
      companyId,
      documentIdentifier,
    }: {
      commentId: string;
      companyId?: string;
      documentIdentifier?: string;
    }) => {
      const response = await api.deleteDocumentComment(commentId);
      if (hasError(response)) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries if we have the context
      if (variables.companyId && variables.documentIdentifier) {
        queryClient.invalidateQueries({
          queryKey: [
            QueryKey().company,
            variables.companyId,
            QueryKey().document,
            variables.documentIdentifier,
            "comments",
          ],
        });

        queryClient.invalidateQueries({
          queryKey: [QueryKey().company, variables.companyId, "comments"],
        });
      } else {
        // Invalidate all comment queries if we don't have specific context
        queryClient.invalidateQueries({
          queryKey: ["comments"],
        });
      }
    },
  });
};
