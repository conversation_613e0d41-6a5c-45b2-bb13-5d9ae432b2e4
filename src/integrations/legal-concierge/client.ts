import { User } from "@/contexts/auth/types";
import {
  Company,
  ForgotPasswordRequest,
  LoginRequest,
  OfficerUpdateRequest,
  RegisterRequest,
  ResetPasswordRequest,
  VerifyOtpRequest,
} from "./types";
import { TechnologyRequest } from "./types/Technology";
import {
  RegisteredAgent,
  UpdateRegisteredAgentRequest,
} from "./types/RegisteredAgent";
import {
  IntellectualPropertyRequest,
  IntellectualPropertyUpdateRequest,
} from "./types/IntellectualProperty";
import { StockOptionPlanRequest, VestingRequest } from "./types/Stocks";
import { OfficerRequest } from "./types/Officer";
import {
  AuthorizedSharesRequest,
  CompanyAddressRequest,
  CompanyDetails,
  CompanyNameRequest,
} from "./types/Company";
import { Document } from "./types/Document";
import { DocumentFile } from "@/types/capTable";
import {
  DocumentPreparationResponse,
  DocumentSignatureResponse,
  DocumentSignatureSQSResponse,
} from "./types/Document";
import { DocumentCommentRequest } from "./types/DocumentComment";

const LEGAL_CONCIERGE_BASE_URL = "https://apidev.foundersform.com";

export class APIClient {
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: unknown) => void;
    reject: (reason?: unknown) => void;
    config: () => Promise<unknown>;
  }> = [];

  constructor(private baseUrl = LEGAL_CONCIERGE_BASE_URL) {}

  // Helper method to parse error response
  private parseErrorResponse(errorResponse: any): string {
    // Handle the case where data contains the error message directly
    if (errorResponse?.data && typeof errorResponse.data === "string") {
      return errorResponse.data;
    }

    // Handle the case where data is an array with description
    if (errorResponse?.data && Array.isArray(errorResponse.data)) {
      const error = errorResponse.data[0];
      return error.description || "An error occurred. Please try again.";
    }

    // Handle the case where message contains the error
    if (errorResponse?.message && typeof errorResponse.message === "string") {
      return errorResponse.message;
    }

    return "An unexpected error occurred. Please try again.";
  }

  // Helper method to handle errors consistently
  private handleError(error: unknown): { error: string } {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: "An unexpected error occurred" };
  }

  // Process the queue of failed requests
  private processQueue(error: Error | null = null) {
    this.failedQueue.forEach((promise) => {
      if (error) {
        promise.reject(error);
      } else {
        promise.resolve();
      }
    });

    this.failedQueue = [];
  }

  // Handle 401 Unauthorized errors by refreshing the token
  private async handleUnauthorizedError<T>(
    config: () => Promise<T>
  ): Promise<T> {
    // If already refreshing, add to queue
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({
          resolve,
          reject,
          config,
        });
      })
        .then(() => {
          return config();
        })
        .catch((err) => {
          throw err;
        });
    }

    this.isRefreshing = true;

    try {
      // Try to refresh the token
      const refreshResult = await this.post<
        unknown,
        { data?: unknown; error?: string }
      >("/api/auth/refresh");

      if (refreshResult.data == null || refreshResult.error) {
        // If refresh fails, process queue with error
        this.processQueue(new Error("Token refresh failed"));
        throw new Error("Authentication failed. Please log in again.");
      }
      // If refresh succeeds, process queue and retry the original request
      this.processQueue();
      return await config();
    } catch (error) {
      this.processQueue(error);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  // The post method
  private async post<TRequest, TResponse>(
    path: string,
    body?: TRequest
  ): Promise<TResponse> {
    // Skip token refresh for the refresh endpoint to avoid infinite loops
    const isRefreshEndpoint = path === "/api/auth/refresh";

    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!res.ok) {
        // If unauthorized and not already trying to refresh, attempt token refresh
        if (res.status === 401 && !isRefreshEndpoint) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.post(path, body));
        }

        const errorResponse = await res.json();
        const errorMessage = this.parseErrorResponse(errorResponse);
        throw new Error(errorMessage);
      }

      try {
        const response = await res.json();
        
        // Check if the response indicates an error even with 200 status
        if (response && typeof response === 'object') {
          // If data is a string and contains error message, treat as error
          if (response.data && typeof response.data === "string" && 
              (response.data.includes("Not enough") || 
               response.data.includes("error") || 
               response.data.includes("Error"))) {
            throw new Error(response.data);
          }
          
          // If there's an error field with content, treat as error
          if (response.error && response.error !== null && response.error !== '') {
            throw new Error(response.error);
          }
        }
        
        return response;
      } catch (error) {
        // If the response is not JSON, fallback to an empty object
        return {} as TResponse;
      }
    };

    return executeRequest();
  }

  private async get<TResponse>(path: string): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        credentials: "include",
      });

      if (!res.ok) {
        console.error(`Request failed with status: ${res.status}`);

        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.get(path));
        }

        throw new Error(`Request failed: ${res.status}`);
      }

      try {
        const jsonResponse = await res.json();

        // Check if the response has a data property
        if (jsonResponse.data !== undefined) {
          return jsonResponse.data;
        } else {
          // If there's no data property, return the whole response
          console.log(`No data property found, returning whole response`);
          return jsonResponse as TResponse;
        }
      } catch (error) {
        console.error("Error parsing json:", error);
        throw error;
      }
    };

    return executeRequest();
  }

  private async delete<TRequest, TResponse>(path: string, body?: TRequest): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "DELETE",
        headers: body ? {
          "Content-Type": "application/json",
        } : undefined,
        credentials: "include",
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!res.ok) {
        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.delete(path));
        }

        const errorResponse = await res.json();
        const errorMessage = this.parseErrorResponse(errorResponse);
        throw new Error(errorMessage);
      }

      try {
        return await res.json();
      } catch (error) {
        console.error("Error parsing json");
        throw new Error("Failed to parse response");
      }
    };

    return executeRequest();
  }

  private async put<TRequest, TResponse>(
    path: string,
    body?: TRequest
  ): Promise<TResponse> {
    const executeRequest = async (): Promise<TResponse> => {
      const res = await fetch(`${this.baseUrl}${path}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: body ? JSON.stringify(body) : undefined,
      });

      if (!res.ok) {
        // If unauthorized, attempt token refresh
        if (res.status === 401) {
          console.log(
            `401 Unauthorized received for ${path}, attempting token refresh...`
          );
          return this.handleUnauthorizedError(() => this.put(path, body));
        }

        const errorResponse = await res.json();
        // Create error with original response data preserved
        const error = new Error(`HTTP ${res.status}: ${res.statusText}`);
        (error as any).response = { data: errorResponse };
        throw error;
      }

      try {
        return await res.json();
      } catch (error) {
        console.error("Error parsing json");
        throw new Error("Failed to parse response");
      }
    };

    return executeRequest();
  }

  // Individual API methods with proper error handling
  async register(data: RegisterRequest) {
    try {
      const response = await this.post("/api/auth/register", data);
      return { data: response };
    } catch (error) {
      return { error: error.message }; // Return the error message from the post method
    }
  }

  async login(data: LoginRequest) {
    try {
      const response = await this.post("/api/auth/login", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async verifyOtp(data: VerifyOtpRequest) {
    try {
      const response = await this.post("/api/auth/verify-otp-get-token", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async refresh() {
    try {
      const response = await this.post("/api/auth/refresh");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async logout() {
    try {
      const response = await this.post("/api/auth/logout");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async forgotPassword(data: ForgotPasswordRequest) {
    try {
      const response = await this.post("/api/auth/forgot-password", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async resetPassword(data: ResetPasswordRequest) {
    try {
      const response = await this.post("/api/auth/reset-password", data);
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async profile() {
    try {
      const response = await this.get<User>("/api/auth/me");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  async getCompanies() {
    try {
      const response = await this.get<Company[]>("/api/auth/companies");
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }
  async selectCompany(companyId: string) {
    try {
      const response = await this.post("/api/auth/select-company", {
        companyId: companyId,
      });
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // GET: Get company by ID
  async getCompanyById(id: string) {
    try {
      const response = await this.get<CompanyDetails>(`/api/companies/${id}`);
      return response;
    } catch (error) {
      console.error(`Error getting company with ID ${id}:`, error);
      throw error;
    }
  }

  // PUT: Update company name
  async updateCompanyRegistrationMode(id: string, mode: number) {
    return this.put(`/api/companies/${id}/registermode/${mode}`, null);
  }

  // PUT: Update company name
  async updateCompanyName(id: string, payload: CompanyNameRequest) {
    return this.put(`/api/companies/${id}/name`, payload);
  }

  // PUT: Update company address
  async updateCompanyAddress(id: string, payload: CompanyAddressRequest) {
    return this.put(`/api/companies/${id}/address`, payload);
  }

  // PUT: Update authorized shares
  async updateAuthorizedShares(id: string, payload: AuthorizedSharesRequest) {
    return this.put(`/api/companies/${id}/authorizedshares`, payload);
  }

  // POST: Add officer
  async addOfficer(companyId: string, payload: OfficerRequest) {
    return this.post(`/api/companies/${companyId}/officers`, payload);
  }

  // PUT: Update officer
  async updateOfficer(officerId: string, payload: OfficerUpdateRequest) {
    return this.put(`/api/officers/${officerId}`, payload);
  }

  // DELETE: Delete officer
  async deleteOfficer(officerId: string) {
    return this.delete(`/api/officers/${officerId}`);
  }

  // PUT: Update stock option plan
  async updateStockOptionPlan(id: string, payload: StockOptionPlanRequest) {
    return this.put(`/api/companies/${id}/stockoption`, payload);
  }

  // PUT: Update vesting info
  async updateVesting(id: string, payload: VestingRequest) {
    return this.put(`/api/companies/${id}/vesting`, payload);
  }

  // POST: Add officer IP
  async addOfficerIP(officerId: string, payload: IntellectualPropertyRequest) {
    return this.post(`/api/officers/${officerId}/ips`, payload);
  }

  // PUT: Update officer IP
  async updateOfficerIP(
    ipId: string,
    payload: IntellectualPropertyUpdateRequest
  ) {
    return this.put(`/api/ips/${ipId}`, payload);
  }

  // DELETE: Delete officer IP
  async deleteOfficerIP(ipId: string) {
    return this.delete(`/api/ips/${ipId}`);
  }

  // PUT: Update technology for a company
  async updateTechnology(id: string, payload: TechnologyRequest) {
    return this.put(`/api/companies/${id}/technology`, payload);
  }

  // GET: getRegisteredAgents
  async getRegisteredAgents(companyId: string) {
    try {
      const response = await this.get<RegisteredAgent>(
        `/api/companies/${companyId}/agent`
      );
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // POST: Update registered agent
  async updateRegisteredAgent(
    id: string,
    payload: UpdateRegisteredAgentRequest
  ) {
    return this.post(`/api/companies/${id}/agent`, payload);
  }

  // Collaborator management
  async getCompanyCollaborators(companyId: string) {
    try {
      const response = await this.get(
        `/api/auth/companies/${companyId}/collaborators`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteCollaborator(collaboratorId: string) {
    try {
      const response = await this.delete(
        `/api/auth/collaborators/${collaboratorId}`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async sendInvite(payload: { email: string; role: string }) {
    try {
      const response = await this.post(`/api/auth/send-invite`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async inviteCollaborator(
    companyId: string,
    payload: { email: string; role: string }
  ) {
    try {
      const response = await this.post(
        `/api/auth/companies/${companyId}/collaborators`,
        payload
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateCollaboratorRole(
    collaboratorId: string,
    payload: { role: string }
  ) {
    try {
      const response = await this.put(
        `/api/auth/collaborators/${collaboratorId}`,
        payload
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm company form completion
  async confirmCompanyForm(id: string) {
    try {
      const response = await this.put(`/api/companies/${id}/comfirm`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document management methods

  // GET: Get all documents for a company
  async getCompanyDocuments(companyId: string) {
    try {
      // Using the endpoint from OpenAPI spec: /api/{id}
      const response = await this.get<{ message: string; data: Document[] }>(
        `/api/v2/companies/${companyId}/documents`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get document by ID
  async getDocumentById(companyId: string, documentId: string) {
    try {
      const response = await this.get<Document>(
        `/api/v2/companies/${companyId}/documents/${documentId}`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm document review
  async confirmReview(companyId: string) {
    try {
      const response = await this.put<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/comfirmreview`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Sign company documents
  async signCompanyDocuments(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/documents/sign`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Prepare documents for signature
  async prepareDocumentForSignature(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentPreparationResponse
      >(`/api/companies/${companyId}/documents/preparesign`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Send document for signature to all signers
  async sendDocumentForSignature(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentSignatureResponse
      >(`/api/companies/${companyId}/signature/send`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Send document for signature using SQS (asynchronous)
  async sendDocumentForSignatureSQS(companyId: string) {
    try {
      const response = await this.post<
        Record<string, never>,
        DocumentSignatureSQSResponse
      >(`/api/companies/${companyId}/signature/sendsqs`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get signature status for SQS workflow
  async getSignatureStatus(companyId: string) {
    try {
      const response = await this.get<number>(
        `/api/companies/${companyId}/signature/sendstatus`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Generate document for a company
  async generateCompanyDocument(companyId: string) {
    try {
      // Using the endpoint from OpenAPI spec: /api/generate/{id}
      const response = await this.post<
        Record<string, never>,
        { message: string; data: Document[] }
      >(`/api/companies/${companyId}/documents/generate`);
      return { data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get document token for viewing
  async getDocumentToken(payload: { documentId: string; companyId: string }) {
    try {
      // Using the endpoint from OpenAPI spec: /api/documents/token
      const response = await this.post<
        { documentId: string },
        { message: string; token: string }
      >(`/api/companies/${payload.companyId}/documents/token`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Callback for document server
  async documentCallback(payload: Record<string, unknown>) {
    try {
      // Using the endpoint from OpenAPI spec: /api/document-callback
      const response = await this.post<
        Record<string, unknown>,
        { message: string }
      >(`/api/document-callback`, payload);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document upload methods
  async getDocumentUploadUrl(
    companyId: string,
    payload: {
      key: string;
      contentType: string;
    }
  ) {
    try {
      const response = await this.post<
        typeof payload,
        { data: { uploadUrl: string; key: string } }
      >(`/api/companies/${companyId}/documents/presigned-url`, payload);
      return { data: response.data };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async saveDocumentMetadata(
    companyId: string,
    payload: {
      name: string;
      key: string;
      size: number;
      type: string;
    }
  ) {
    try {
      const response = await this.post<
        typeof payload,
        {
          name: string;
          key: string;
          size: number;
          type: string;
          uploadedAt: string;
          url: string;
        }
      >(`/api/companies/${companyId}/documents/metadata`, payload);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // get company comments
  async getDocumentComments(companyId: string, documentId: string) {
    try {
      const response = await this.get(
        `/api/companies/${companyId}/documents/${documentId}/comments`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

    // Get all comments for a company
  async getCompanyComments(companyId: string) {
    try {
       const response = await this.get(
        `/api/companies/${companyId}/documents/comments`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // create comment
  async createDocumentComment(
    companyId: string,
    documentIdentifier: string,
    comment: DocumentCommentRequest
  ) {
    try {
      const response = await this.post(
        `/api/companies/${companyId}/documents/${encodeURIComponent(
          documentIdentifier
        )}/comments`,
        comment
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  //deleteDocumentComment
  async deleteDocumentComment(commentId: string) {
    try {
      const response = await this.delete(`/api/comments/${commentId}`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // update comment
  async updateDocumentComment(
    commentId: string,
    comment: DocumentCommentRequest
  ) {
    try {
      const response = await this.put(`/api/comments/${commentId}`, comment);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get document content
  async getDocument(documentId: string) {
    try {
      const response = await this.get(`/api/v2/documents/${documentId}`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Update document content
  async updateDocument(
    documentId: string,
    payload: {
      content: string;
      data?: {
        blameMap?: Array<{
          from: number;
          to: number;
          commit: number;
        }>;
        commits?: Array<{
          message: string;
          timestamp: string;
          steps: string;
          originalSteps: string;
          maps: string;
          hidden: boolean;
          deletions?: Array<{
            pos: number;
            content: string;
          }>;
        }>;
      };
    }
  ) {
    try {
      const response = await this.put(
        `/api/v2/documents/${documentId}`,
        payload
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Document methods
  async getIncorporationDocuments(companyId: string) {
    try {
      const response = await this.get<{
        message: string;
        data: Record<string, DocumentFile[]>;
        error: string | null;
        validationErrors: unknown | null;
      }>(`/api/companies/${companyId}/final-documents/incorporation-documents`);
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PostIncorporation API methods

  // PUT: Complete EIN task
  async completeEIN(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completeein`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Complete business account task
  async completeBusinessAccount(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completebusinessaccount`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Complete foreign qualification task
  async completeForeignQualification(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/completeforeignqualification`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Add foreign qualification state
  async addForeignQualificationState(companyId: string, state: string) {
    try {
      const response = await this.post(
        `/api/companies/${companyId}/foreignqualification/states`,
        state
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // DELETE: Remove foreign qualification state
  async removeForeignQualificationState(id: string) {
    try {
      const response = await this.delete(
        `/api/companies/foreignqualification/states/${id}`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Confirm post-incorporation completion
  async confirmPostIncorporation(companyId: string) {
    try {
      const response = await this.put(
        `/api/companies/${companyId}/postincorporation/confirm`
      );
      return { data: response };
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Add new company advisor
  async addCompanyAdvisor(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/advisors`,
        payload
      );
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // POST: Add new company employee
  async addCompanyEmployee(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/employees`,
        payload
      );
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // POST: Add new company contractor
  async addCompanyContractor(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/contractors`,
        payload
      );
      return { data: response };
    } catch (error) {
      return { error: error.message };
    }
  }

  // Service Provider Status API
  async getServiceProviderStatus(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/status`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get active advisors
  async getActiveAdvisors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/activeadvisors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get pending advisors
  async getPendingAdvisors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/pendingadvisors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get terminated advisors
  async getTerminatedAdvisors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/terminatedadvisors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get active employees
  async getActiveEmployees(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/activeemployees`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get pending employees
  async getPendingEmployees(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/pendingemployees`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get terminated employees
  async getTerminatedEmployees(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/terminatedemployees`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get active contractors
  async getActiveContractors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/activecontractors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get pending contractors
  async getPendingContractors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/pendingcontractors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get terminated contractors
  async getTerminatedContractors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/terminatedcontractors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get stock option information
  async getStockOptionInformation(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/stockoptionsplansummary`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get stock option plan table data
  async getStockOptionPlanTable(companyId: string, filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.proFormaRoleFilter) {
        queryParams.append('proFormaRoleFilter', filters.proFormaRoleFilter);
      }
      if (filters?.proFormaShareClassFilter) {
        queryParams.append('proFormaShareClassFilter', filters.proFormaShareClassFilter);
      }
      if (filters?.shareHolderName) {
        queryParams.append('shareHolderName', filters.shareHolderName);
      }
      
      const queryString = queryParams.toString();
      const url = `/api/p2/companies/${companyId}/captable/optionpool${queryString ? `?${queryString}` : ''}`;
      
      const response = await this.get(url);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Update stock option grant
  async updateStockOptionGrant(companyId: string, payload: { id: string; role: string; grantStatus: string; grantType: string; optionsGranted: number }) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/captable/optionpool/update`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Exercise stock option
  async exerciseStockOption(companyId: string, payload: { id: string; exerciseDate: string; sharesToExercise: number }) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/captable/optionpool/exercisestockoption`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // DELETE: Delete stock option grant
  async deleteStockOptionGrant(companyId: string, payload: { id: string; grantType: string }) {
    try {
      const response = await this.delete(
        `/api/p2/companies/${companyId}/captable/optionpool`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get voting rights data
  async getVotingRights(companyId: string, filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }) {
    try {
      const queryParams = new URLSearchParams();
      if (filters?.proFormaRoleFilter) {
        queryParams.append('proFormaRoleFilter', filters.proFormaRoleFilter);
      }
      if (filters?.proFormaShareClassFilter) {
        queryParams.append('proFormaShareClassFilter', filters.proFormaShareClassFilter);
      }
      if (filters?.shareHolderName) {
        queryParams.append('shareHolderName', filters.shareHolderName);
      }
      const queryString = queryParams.toString();
      const url = `/api/p2/companies/${companyId}/captable/votingrights${queryString ? `?${queryString}` : ''}`;
      const response = await this.get(url);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Update voting right email
  async updateVotingRightEmail(companyId: string, payload: { id: string; email: string }) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/captable/votingrights/updateemail`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Update voting right email status
  async updateVotingRightEmailStatus(companyId: string, payload: { id: string; sendEmail: boolean }) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/captable/votingrights/updatesendemail`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get promised grants by company
  async getPromisedGrants(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/promisegrants`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Update promised grant
  async updatePromisedGrant(
    id: string,
    payload: {
      name: string;
      grantType: string;
      shares: number;
      vestingSchedule: string | null;
      pricePerShare: number;
      compensation?: number;
      vestingPeriod?: number;
      cliff?: number;
      term?: number;
    }
  ) {
    try {
      const response = await this.put(`/api/p2/promisegrants/${id}`, payload);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // DELETE: Delete promised grant
  async deletePromisedGrant(id: string) {
    try {
      const response = await this.delete(`/api/p2/promisegrants/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  }

  // POST: Issue promised grant
  async issuePromisedGrant(companyId: string, promiseGrantId: string) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/promisegrants/issue`,
        {
          promiseGrantId,
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  // GET: Get active 409A valuation information
  async getActive409AValuation(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/active409avaluation`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: 409A presigned URL for upload
  async get409aPresignedUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/409avaluation/presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Submit 409A valuation (send for approval)
  async submit409aValuation(
    companyId: string,
    payload: {
      appraiserName: string;
      appraisalDate: string;
      fairMarketValue: number;
      confirmationStatus: "yes" | "no";
      s3Key: string;
    }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/409avaluation`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Set Fair Market Value for 409A
  async set409aFairMarketValue(
    companyId: string,
    payload: { fairMarketValue: number }
  ) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/409avaluation/setfairmarketvalue`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get pending service providers for termination
  async getPendingServiceProviders(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/serviceproviders/pendings`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Terminate service provider
  async terminateServiceProvider(id: string, payload: {
    terminationDate: string;
    serviceProviderTerminationType: string;
    severanceAmount: number;
    reasonForTermination: string;
  }) {
    try {
      const response = await this.put(
        `/api/p2/serviceproviders/${id}/terminate`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get terminated service providers list
  async getTerminatedServiceProviders(companyId: string) {
    try {
      const response = await this.get(`/api/p2/serviceproviders/${companyId}/terminatedlist`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get pending terminated service providers list
  async getPendingTerminatedServiceProviders(companyId: string) {
    try {
      const response = await this.get(`/api/p2/serviceproviders/${companyId}/pendingterminatedlist`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get director summary
  async getDirectorSummary(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/directorsummary`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Add director
  async addDirector(
    companyId: string,
    payload: {
      name: string;
      startDate: string;
      emailAddress?: string | null;
      contactAddress?: string | null;
    }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/directors`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get directors list
  async getDirectors(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/directors`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Remove director
  async removeDirector(
    companyId: string,
    payload: {
      officerId: string;
      removalDate: string;
    }
  ) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/directors/remove`,
        payload
      );
      return response;
    } catch (error) {
      throw error; // Preserve the original error with validation data
    }
  }

  // PUT: Resignation director
  async resignationDirector(
    companyId: string,
    payload: {
      officerId: string;
      resignationDate: string;
    }
  ) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/directors/resignation`,
        payload
      );
      return response;
    } catch (error) {
      throw error; // Preserve the original error with validation data
    }
  }

  // Authorized Shares API Methods

  // GET: Get current pending authorized shares
  async getCurrentPendingAuthorizedShares(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/authorizedshares/currentpending`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Submit for board approval
  async submitForBoardApproval(companyId: string, additionalShares: number) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/authorizedshares`,
        {
          additionalShares,
        }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Submit for stockholder approval
  async submitForStockholderApproval(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/authorizedshares/submitforstockholderapproval`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Abandon process
  async abandonAuthorizedSharesProcess(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/authorizedshares/abandoned`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for file upload
  async getAmendmentPresignedUrl(
    companyId: string,
    key: string,
    contentType: string
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/authorized-share/amendment-presigned-url`,
        {
          key,
          contentType,
        }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for convertible notes file upload
  async getConvertibleNotesPresignedUrl(
    companyId: string,
    key: string,
    contentType: string
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/authorized-round/presigned-url`,
        {
          key,
          contentType,
        }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Record authorized round (external)
  async recordAuthorizedRound(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/recordauthorizedround`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Submit file amendment
  async submitFileAmendment(companyId: string, s3Key: string) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/authorizedshares/submitfileamendment`,
        {
          s3Key,
        }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Stock Option Plan APIs ---
  // GET: SOP summary
  async getStockOptionPlanSummary(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/stockoptionsplansummary`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Increase SOP (submit for board approval)
  async increaseStockOptionPlan(companyId: string, additionalShares: number) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/increasestockoptionplan`,
        { additionalShares }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Submit SOP for stockholder approval
  async submitSopForStockholderApproval(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/increasestockoptionplan/${companyId}/submitforstockholderapproval`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Abandon SOP increase
  async abandonIncreaseStockOptionPlan(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/increasestockoptionplan/abandoned`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Create Stock Option Plan APIs ---
  // GET: Create SOP current pending state
  async getCreateSopCurrentPending(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/createstockoptionplan/currentpending`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Create SOP submit for board approval
  async createStockOptionPlan(companyId: string, additionalShares: number) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/createstockoptionplan`,
        { additionalShares }
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Create SOP submit for stockholder approval
  async submitCreateSopForStockholderApproval(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/createstockoptionplan/${companyId}/submitforstockholderapproval`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Abandon Create SOP process
  async abandonCreateStockOptionPlan(companyId: string) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/createstockoptionplan/abandoned`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Financing APIs ---
  // GET: Authorized rounds for convertible notes
  async getAuthorizedRounds(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/preseedfinance/authorizedroundlist`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Create new authorized round for convertible notes
  async createAuthorizeRound(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/authorizedround`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Issue individual convertible notes
  async issueIndividualConvertibleNote(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/issueindividualnote`,
        payload
      );
      return response;
    } catch (error) {
      throw error; // Throw the error instead of returning it
    }
  }

  // POST: Get presigned URL for prorata sideletter upload
  async getProrataSideletterUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-note/prorata-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for SAFE prorata sideletter upload
  async getSafeProrataSideletterUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-safe-note/prorata-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for SAFE side letter upload
  async getSafeSideLetterUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-safe-note/side-letter-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for SAFE optional document upload
  async getSafeOptionalDocumentUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/record-issue-individual-safe-note/optional-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for side letter upload
  async getSideLetterUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-note/side-letter-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for optional document upload
  async getOptionalDocumentUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/record-issue-individual-note/optional-presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for Issue Individual Note document upload
  async getIssueIndividualNoteUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-note/presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Record Issue Individual Note
  async recordIssueIndividualNote(
    companyId: string,
    payload: {
      authorizedRoundId: string;
      principalAmount: number;
      name: string;
      email: string;
      dateOfInvestment: string;
      approvedDate: string;
      prorataS3Key?: string;
      s3Key?: string;
      optionalS3Key?: string;
    }
  ) {
    return this.post(
      `/api/p2/companies/${companyId}/preseedfinance/recordissueindividualnote`,
      payload
    );
  }

  // POST: Increase authorized round
  async increaseAuthorizedRound(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/increaseauthorizedround`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get individual notes list for a specific authorized round
  async getIndividualNotesList(companyId: string, authorizedRoundId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/preseedfinance/issueindividualnotelist/${authorizedRoundId}`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- SAFE APIs ---
  // GET: Authorized SAFEs list
  async getAuthorizedSafes(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/preseedfinance/authorizedsafelist`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Create new authorized SAFE round
  async createAuthorizeSafe(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/authorizedsafe`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Issue individual SAFE
  async issueIndividualSafe(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/issueindividualsafenote`,
        payload
      );
      return response;
    } catch (error) {
      throw error; // Throw the error instead of returning it
    }
  }

  // POST: Increase authorized SAFE round
  async increaseAuthorizedSafe(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/increaseauthorizedsafe`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Record authorized SAFE round
  async recordAuthorizedSafe(companyId: string, payload: any) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/preseedfinance/recordauthorizedsafe`,
        payload
      );
      return response;
    } catch (error) {
      throw error; // Throw the error instead of returning it
    }
  }

  // GET: Individual SAFEs list by round ID
  async getIndividualSafesList(companyId: string, authorizedRoundId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/preseedfinance/issueindividualsafelist/${authorizedRoundId}`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for SAFE document upload
  async getSafeDocumentUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/authorized-safe-round/presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Get presigned URL for Issue SAFE Note document upload
  async getIssueSafeNoteUploadUrl(
    companyId: string,
    payload: { key: string; contentType: string }
  ) {
    try {
      const response = await this.post(
        `/api/p2/companies/${companyId}/documents/issue-individual-safe-note/presigned-url`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Record Issue Individual SAFE Note
  async recordIssueIndividualSafeNote(
    companyId: string,
    payload: {
      authorizedRoundId: string;
      principalAmount: number;
      name: string;
      email: string;
      dateOfInvestment: string;
      approvedDate: string;
      prorataS3Key?: string;
      s3Key?: string; // Changed to optional
      optionalS3Key?: string; // Added
    }
  ) {
    return this.post(
      `/api/p2/companies/${companyId}/preseedfinance/recordissueindividualsafenote`,
      payload
    );
  }

  // --- SAFE Repurchase APIs ---
  // GET: Get SAFE repurchase list
  async getSafeRepurchaseList(companyId: string) {
    try {
      const response = await this.get(
        `/api/p2/companies/${companyId}/preseedfinance/saferepurchaselist`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // PUT: Submit SAFE repurchase
  async submitSafeRepurchase(
    companyId: string,
    payload: { id: string; repurchaseAmount: number }
  ) {
    try {
      const response = await this.put(
        `/api/p2/companies/${companyId}/preseedfinance/saferepurchase`,
        payload
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- MFN APIs ---
  // GET: Get MFN summary
  async getMfnSummary(companyId: string) {
    try {
      const response = await this.get(`/api/p2/companies/${companyId}/preseedfinance/mfnsummary`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST: Notify MFN holders
  async notifyMfnHolders(companyId: string) {
    try {
      const response = await this.post(`/api/p2/companies/${companyId}/preseedfinance/notifymfn`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Legacy MFN APIs (deprecated)
  // GET: Get MFN details for convertible notes
  async getMfnConvertibleNotes(companyId: string) {
    try {
      const response = await this.get(`/api/p2/companies/${companyId}/preseedfinance/authorizedround/mfndetail`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET: Get MFN details for SAFEs
  async getMfnSafes(companyId: string) {
    try {
      const response = await this.get(`/api/p2/companies/${companyId}/preseedfinance/authorizedsafe/mfndetail`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Cap Table Summary API ---
  // GET: Get cap table summary
  async getCapTableSummary(companyId: string) {
    try {
      const response = await this.get(`/api/p2/companies/${companyId}/captable/summary`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Cap Table List API ---
  // GET: Get cap table list
  async getCapTableList(companyId: string, filters?: {
    proFormaRoleFilter?: string;
    proFormaShareClassFilter?: string;
    shareHolderName?: string;
  }) {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.proFormaRoleFilter) {
        queryParams.append('proFormaRoleFilter', filters.proFormaRoleFilter);
      }
      if (filters?.proFormaShareClassFilter) {
        queryParams.append('proFormaShareClassFilter', filters.proFormaShareClassFilter);
      }
      if (filters?.shareHolderName) {
        queryParams.append('shareHolderName', filters.shareHolderName);
      }
      
      const queryString = queryParams.toString();
      const url = `/api/p2/companies/${companyId}/captable/list${queryString ? `?${queryString}` : ''}`;
      
      const response = await this.get(url);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Convertible Securities API ---
  // GET: Get convertible securities (notes and SAFEs)
  async getConvertibleSecurities(companyId: string) {
    try {
      const response = await this.get(`/api/p2/companies/${companyId}/captable/convertiblenotes`);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Cap Table Edit API ---
  // POST: Update cap table item
  async updateCapTableItem(companyId: string, payload: any) {
    try {
      const response = await this.post(`/api/p2/companies/${companyId}/captable`, payload);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Cap Table Termination API ---
  // POST: Terminate cap table item
  async terminateCapTableItem(companyId: string, payload: any) {
    try {
      const response = await this.post(`/api/p2/companies/${companyId}/captable/terminate`, payload);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Stock Option Plan Add Grant API ---
  // POST: Promise grant without approval
  async promiseGrantWithoutApproval(companyId: string, payload: any) {
    try {
      const response = await this.post(`/api/p2/companies/${companyId}/promisegrantswithoutapproval`, payload);
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

  // --- Pro Forma API ---
  // GET: Get proforma data
  async getProformaData(companyId: string, params: {
    PrePostMoneyValuation?: number;
    InvestmentAmount?: number;
    OptionPoolAvailableForIssuance?: number;
    CommonStockBuffer?: number;
    ValuationModeType?: 'fixed_pre_money' | 'fixed_post_money';
    PreferredStockDesignation?: 'SeriesA' | 'SeriesSeed';
    // Filter Parameters
    ProFormaRoleFilter?: 'All' | 'Founders' | 'Investors' | 'Advisors' | 'Contractors' | 'Employees';
    ProFormaShareClassFilter?: 'All' | 'Common' | 'SeriesAPreferred' | 'StockOptionPool';
    ShareHolderName?: string;
  }) {
    try {
      const queryParams = new URLSearchParams();
      // Add CompanyId as first parameter
      queryParams.append('CompanyId', companyId);
      // Add all mandatory parameters (default to 0 if not provided)
      queryParams.append('PrePostMoneyValuation', (params.PrePostMoneyValuation ?? 0).toString());
      queryParams.append('InvestmentAmount', (params.InvestmentAmount ?? 0).toString());
      queryParams.append('OptionPoolAvailableForIssuance', (params.OptionPoolAvailableForIssuance ?? 0).toString());
      queryParams.append('CommonStockBuffer', (params.CommonStockBuffer ?? 0).toString());
      queryParams.append('ValuationModeType', params.ValuationModeType ?? 'fixed_pre_money');
      queryParams.append('PreferredStockDesignation', params.PreferredStockDesignation ?? 'SeriesA');
      
      // Add filter parameters with defaults
      queryParams.append('ProFormaRoleFilter', params.ProFormaRoleFilter ?? 'All');
      queryParams.append('ProFormaShareClassFilter', params.ProFormaShareClassFilter ?? 'All');
      queryParams.append('ShareHolderName', params.ShareHolderName ?? '');
      
      const response = await this.get(
        `/api/p2/companies/captable/proforma?${queryParams.toString()}`
      );
      return response;
    } catch (error) {
      return this.handleError(error);
    }
  }

// POST: Update investor investment amount
async updateInvestorInvestmentAmount(companyId: string, data: {
  id?: string; // For investor type
  officerId?: string; // For officer type
  serviceProviderId?: string; // For service provider type
  shareholderName: string;
  investmentAmount: number;
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}) {
  try {
    // Build payload based on which ID is present
    const payload: any = {
      shareholderName: data.shareholderName,
      investmentAmount: data.investmentAmount,
      valuationModeType: data.valuationModeType
    };

    // Add the appropriate ID field based on what's available
    if (data.id) {
      payload.id = data.id;
    } else if (data.officerId) {
      payload.officerId = data.officerId;
    } else if (data.serviceProviderId) {
      payload.serviceProviderId = data.serviceProviderId;
    }
    
    const response = await this.post(
      `/api/p2/companies/${companyId}/captable/proforma`,
      payload
    );
    
    // Check if the response contains an error even on successful HTTP status
    if (response && typeof response === 'object' && 'error' in response) {
      // Only throw if the error is not null/undefined and not empty
      if (response.error && response.error !== null && response.error !== '') {
        throw new Error(response.error as string);
      }
    }
    
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}

// DELETE: Delete investor from proforma
async deleteInvestorFromProforma(companyId: string, data: {
  id?: string; // For investor type
  officerId?: string; // For officer type
  serviceProviderId?: string; // For service provider type
  valuationModeType: 'fixed_pre_money' | 'fixed_post_money';
}) {
  try {
    // Build payload based on which ID is present
    const payload: any = {
      valuationModeType: data.valuationModeType
    };

    // Add the appropriate ID field based on what's available
    if (data.id) {
      payload.id = data.id;
    } else if (data.officerId) {
      payload.officerId = data.officerId;
    } else if (data.serviceProviderId) {
      payload.serviceProviderId = data.serviceProviderId;
    }
    
    const response = await this.delete(
      `/api/p2/companies/${companyId}/captable/proforma/delete`,
      payload
    );
    return response;
  } catch (error) {
    return this.handleError(error);
  }
}
}

// Create and export a singleton instance
export const apiClient = new APIClient();
